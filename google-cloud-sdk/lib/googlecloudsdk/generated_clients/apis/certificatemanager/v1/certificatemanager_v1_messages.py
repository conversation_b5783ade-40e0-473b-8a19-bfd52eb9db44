"""Generated message classes for certificatemanager version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'certificatemanager'


class AuthorizationAttemptInfo(_messages.Message):
  r"""State of the latest attempt to authorize a domain for certificate
  issuance.

  Enums:
    FailureReasonValueValuesEnum: Output only. Reason for failure of the
      authorization attempt for the domain.
    StateValueValuesEnum: Output only. State of the domain for managed
      certificate issuance.

  Fields:
    details: Output only. Human readable explanation for reaching the state.
      Provided to help address the configuration issues. Not guaranteed to be
      stable. For programmatic access use FailureReason enum.
    domain: Domain name of the authorization attempt.
    failureReason: Output only. Reason for failure of the authorization
      attempt for the domain.
    state: Output only. State of the domain for managed certificate issuance.
  """

  class FailureReasonValueValuesEnum(_messages.Enum):
    r"""Output only. Reason for failure of the authorization attempt for the
    domain.

    Values:
      FAILURE_REASON_UNSPECIFIED: FailureReason is unspecified.
      CONFIG: There was a problem with the user's DNS or load balancer
        configuration for this domain.
      CAA: Certificate issuance forbidden by an explicit CAA record for the
        domain or a failure to check CAA records for the domain.
      RATE_LIMITED: Reached a CA or internal rate-limit for the domain, e.g.
        for certificates per top-level private domain.
    """
    FAILURE_REASON_UNSPECIFIED = 0
    CONFIG = 1
    CAA = 2
    RATE_LIMITED = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the domain for managed certificate issuance.

    Values:
      STATE_UNSPECIFIED: State is unspecified.
      AUTHORIZING: Certificate provisioning for this domain is under way.
        Google Cloud will attempt to authorize the domain.
      AUTHORIZED: A managed certificate can be provisioned, no issues for this
        domain.
      FAILED: Attempt to authorize the domain failed. This prevents the
        Managed Certificate from being issued. See `failure_reason` and
        `details` fields for more information.
    """
    STATE_UNSPECIFIED = 0
    AUTHORIZING = 1
    AUTHORIZED = 2
    FAILED = 3

  details = _messages.StringField(1)
  domain = _messages.StringField(2)
  failureReason = _messages.EnumField('FailureReasonValueValuesEnum', 3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Certificate(_messages.Message):
  r"""Defines TLS certificate.

  Enums:
    ScopeValueValuesEnum: Immutable. The scope of the certificate.

  Messages:
    LabelsValue: Set of labels associated with a Certificate.

  Fields:
    createTime: Output only. The creation timestamp of a Certificate.
    description: One or more paragraphs of text description of a certificate.
    expireTime: Output only. The expiry timestamp of a Certificate.
    labels: Set of labels associated with a Certificate.
    managed: If set, contains configuration and state of a managed
      certificate.
    name: A user-defined name of the certificate. Certificate names must be
      unique globally and match pattern
      `projects/*/locations/*/certificates/*`.
    pemCertificate: Output only. The PEM-encoded certificate chain.
    sanDnsnames: Output only. The list of Subject Alternative Names of dnsName
      type defined in the certificate (see RFC 5280 *******). Managed
      certificates that haven't been provisioned yet have this field populated
      with a value of the managed.domains field.
    scope: Immutable. The scope of the certificate.
    selfManaged: If set, defines data of a self-managed certificate.
    updateTime: Output only. The last update timestamp of a Certificate.
  """

  class ScopeValueValuesEnum(_messages.Enum):
    r"""Immutable. The scope of the certificate.

    Values:
      DEFAULT: Certificates with default scope are served from core Google
        data centers. If unsure, choose this option.
      EDGE_CACHE: Certificates with scope EDGE_CACHE are special-purposed
        certificates, served from Edge Points of Presence. See
        https://cloud.google.com/vpc/docs/edge-locations.
      ALL_REGIONS: Certificates with ALL_REGIONS scope are served from all GCP
        regions. See https://cloud.google.com/compute/docs/regions-zones.
    """
    DEFAULT = 0
    EDGE_CACHE = 1
    ALL_REGIONS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of labels associated with a Certificate.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  expireTime = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  managed = _messages.MessageField('ManagedCertificate', 5)
  name = _messages.StringField(6)
  pemCertificate = _messages.StringField(7)
  sanDnsnames = _messages.StringField(8, repeated=True)
  scope = _messages.EnumField('ScopeValueValuesEnum', 9)
  selfManaged = _messages.MessageField('SelfManagedCertificate', 10)
  updateTime = _messages.StringField(11)


class CertificateAuthorityConfig(_messages.Message):
  r"""The CA that issues the workload certificate. It includes CA address,
  type, authentication to CA service, etc.

  Fields:
    certificateAuthorityServiceConfig: Defines a
      CertificateAuthorityServiceConfig.
  """

  certificateAuthorityServiceConfig = _messages.MessageField('CertificateAuthorityServiceConfig', 1)


class CertificateAuthorityServiceConfig(_messages.Message):
  r"""Contains information required to contact CA service.

  Fields:
    caPool: Required. A CA pool resource used to issue a certificate. The CA
      pool string has a relative resource path following the form
      "projects/{project}/locations/{location}/caPools/{ca_pool}".
  """

  caPool = _messages.StringField(1)


class CertificateIssuanceConfig(_messages.Message):
  r"""CertificateIssuanceConfig specifies how to issue and manage a
  certificate.

  Enums:
    KeyAlgorithmValueValuesEnum: Required. The key algorithm to use when
      generating the private key.

  Messages:
    LabelsValue: Set of labels associated with a CertificateIssuanceConfig.

  Fields:
    certificateAuthorityConfig: Required. The CA that issues the workload
      certificate. It includes the CA address, type, authentication to CA
      service, etc.
    createTime: Output only. The creation timestamp of a
      CertificateIssuanceConfig.
    description: One or more paragraphs of text description of a
      CertificateIssuanceConfig.
    keyAlgorithm: Required. The key algorithm to use when generating the
      private key.
    labels: Set of labels associated with a CertificateIssuanceConfig.
    lifetime: Required. Workload certificate lifetime requested.
    name: A user-defined name of the certificate issuance config.
      CertificateIssuanceConfig names must be unique globally and match
      pattern `projects/*/locations/*/certificateIssuanceConfigs/*`.
    rotationWindowPercentage: Required. Specifies the percentage of elapsed
      time of the certificate lifetime to wait before renewing the
      certificate. Must be a number between 1-99, inclusive.
    updateTime: Output only. The last update timestamp of a
      CertificateIssuanceConfig.
  """

  class KeyAlgorithmValueValuesEnum(_messages.Enum):
    r"""Required. The key algorithm to use when generating the private key.

    Values:
      KEY_ALGORITHM_UNSPECIFIED: Unspecified key algorithm.
      RSA_2048: Specifies RSA with a 2048-bit modulus.
      ECDSA_P256: Specifies ECDSA with curve P256.
    """
    KEY_ALGORITHM_UNSPECIFIED = 0
    RSA_2048 = 1
    ECDSA_P256 = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of labels associated with a CertificateIssuanceConfig.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  certificateAuthorityConfig = _messages.MessageField('CertificateAuthorityConfig', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  keyAlgorithm = _messages.EnumField('KeyAlgorithmValueValuesEnum', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  lifetime = _messages.StringField(6)
  name = _messages.StringField(7)
  rotationWindowPercentage = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  updateTime = _messages.StringField(9)


class CertificateMap(_messages.Message):
  r"""Defines a collection of certificate configurations.

  Messages:
    LabelsValue: Set of labels associated with a Certificate Map.

  Fields:
    createTime: Output only. The creation timestamp of a Certificate Map.
    description: One or more paragraphs of text description of a certificate
      map.
    gclbTargets: Output only. A list of GCLB targets that use this Certificate
      Map. A Target Proxy is only present on this list if it's attached to a
      Forwarding Rule.
    labels: Set of labels associated with a Certificate Map.
    name: A user-defined name of the Certificate Map. Certificate Map names
      must be unique globally and match pattern
      `projects/*/locations/*/certificateMaps/*`.
    updateTime: Output only. The update timestamp of a Certificate Map.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of labels associated with a Certificate Map.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  gclbTargets = _messages.MessageField('GclbTarget', 3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class CertificateMapEntry(_messages.Message):
  r"""Defines a certificate map entry.

  Enums:
    MatcherValueValuesEnum: A predefined matcher for particular cases, other
      than SNI selection.
    StateValueValuesEnum: Output only. A serving state of this Certificate Map
      Entry.

  Messages:
    LabelsValue: Set of labels associated with a Certificate Map Entry.

  Fields:
    certificates: A set of Certificates defines for the given `hostname`.
      There can be defined up to four certificates in each Certificate Map
      Entry. Each certificate must match pattern
      `projects/*/locations/*/certificates/*`.
    createTime: Output only. The creation timestamp of a Certificate Map
      Entry.
    description: One or more paragraphs of text description of a certificate
      map entry.
    hostname: A Hostname (FQDN, e.g. `example.com`) or a wildcard hostname
      expression (`*.example.com`) for a set of hostnames with common suffix.
      Used as Server Name Indication (SNI) for selecting a proper certificate.
    labels: Set of labels associated with a Certificate Map Entry.
    matcher: A predefined matcher for particular cases, other than SNI
      selection.
    name: A user-defined name of the Certificate Map Entry. Certificate Map
      Entry names must be unique globally and match pattern
      `projects/*/locations/*/certificateMaps/*/certificateMapEntries/*`.
    state: Output only. A serving state of this Certificate Map Entry.
    updateTime: Output only. The update timestamp of a Certificate Map Entry.
  """

  class MatcherValueValuesEnum(_messages.Enum):
    r"""A predefined matcher for particular cases, other than SNI selection.

    Values:
      MATCHER_UNSPECIFIED: A matcher has't been recognized.
      PRIMARY: A primary certificate that is served when SNI wasn't specified
        in the request or SNI couldn't be found in the map.
    """
    MATCHER_UNSPECIFIED = 0
    PRIMARY = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. A serving state of this Certificate Map Entry.

    Values:
      SERVING_STATE_UNSPECIFIED: The status is undefined.
      ACTIVE: The configuration is serving.
      PENDING: Update is in progress. Some frontends may serve this
        configuration.
    """
    SERVING_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PENDING = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of labels associated with a Certificate Map Entry.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  certificates = _messages.StringField(1, repeated=True)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  hostname = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  matcher = _messages.EnumField('MatcherValueValuesEnum', 6)
  name = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class CertificatemanagerProjectsLocationsCertificateIssuanceConfigsCreateRequest(_messages.Message):
  r"""A
  CertificatemanagerProjectsLocationsCertificateIssuanceConfigsCreateRequest
  object.

  Fields:
    certificateIssuanceConfig: A CertificateIssuanceConfig resource to be
      passed as the request body.
    certificateIssuanceConfigId: Required. A user-provided name of the
      certificate config.
    parent: Required. The parent resource of the certificate issuance config.
      Must be in the format `projects/*/locations/*`.
  """

  certificateIssuanceConfig = _messages.MessageField('CertificateIssuanceConfig', 1)
  certificateIssuanceConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CertificatemanagerProjectsLocationsCertificateIssuanceConfigsDeleteRequest(_messages.Message):
  r"""A
  CertificatemanagerProjectsLocationsCertificateIssuanceConfigsDeleteRequest
  object.

  Fields:
    name: Required. A name of the certificate issuance config to delete. Must
      be in the format `projects/*/locations/*/certificateIssuanceConfigs/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsCertificateIssuanceConfigsGetRequest(_messages.Message):
  r"""A
  CertificatemanagerProjectsLocationsCertificateIssuanceConfigsGetRequest
  object.

  Fields:
    name: Required. A name of the certificate issuance config to describe.
      Must be in the format
      `projects/*/locations/*/certificateIssuanceConfigs/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsCertificateIssuanceConfigsListRequest(_messages.Message):
  r"""A
  CertificatemanagerProjectsLocationsCertificateIssuanceConfigsListRequest
  object.

  Fields:
    filter: Filter expression to restrict the Certificates Configs returned.
    orderBy: A list of Certificate Config field names used to specify the
      order of the returned results. The default sorting order is ascending.
      To specify descending order for a field, add a suffix " desc".
    pageSize: Maximum number of certificate configs to return per call.
    pageToken: The value returned by the last
      `ListCertificateIssuanceConfigsResponse`. Indicates that this is a
      continuation of a prior `ListCertificateIssuanceConfigs` call, and that
      the system should return the next page of data.
    parent: Required. The project and location from which the certificate
      should be listed, specified in the format `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntriesCreateRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntrie
  sCreateRequest object.

  Fields:
    certificateMapEntry: A CertificateMapEntry resource to be passed as the
      request body.
    certificateMapEntryId: Required. A user-provided name of the certificate
      map entry.
    parent: Required. The parent resource of the certificate map entry. Must
      be in the format `projects/*/locations/*/certificateMaps/*`.
  """

  certificateMapEntry = _messages.MessageField('CertificateMapEntry', 1)
  certificateMapEntryId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntriesDeleteRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntrie
  sDeleteRequest object.

  Fields:
    name: Required. A name of the certificate map entry to delete. Must be in
      the format
      `projects/*/locations/*/certificateMaps/*/certificateMapEntries/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntriesGetRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntrie
  sGetRequest object.

  Fields:
    name: Required. A name of the certificate map entry to describe. Must be
      in the format
      `projects/*/locations/*/certificateMaps/*/certificateMapEntries/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntriesListRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntrie
  sListRequest object.

  Fields:
    filter: Filter expression to restrict the returned Certificate Map
      Entries.
    orderBy: A list of Certificate Map Entry field names used to specify the
      order of the returned results. The default sorting order is ascending.
      To specify descending order for a field, add a suffix " desc".
    pageSize: Maximum number of certificate map entries to return. The service
      may return fewer than this value. If unspecified, at most 50 certificate
      map entries will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: The value returned by the last
      `ListCertificateMapEntriesResponse`. Indicates that this is a
      continuation of a prior `ListCertificateMapEntries` call, and that the
      system should return the next page of data.
    parent: Required. The project, location and certificate map from which the
      certificate map entries should be listed, specified in the format
      `projects/*/locations/*/certificateMaps/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntriesPatchRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsCertificateMapEntrie
  sPatchRequest object.

  Fields:
    certificateMapEntry: A CertificateMapEntry resource to be passed as the
      request body.
    name: A user-defined name of the Certificate Map Entry. Certificate Map
      Entry names must be unique globally and match pattern
      `projects/*/locations/*/certificateMaps/*/certificateMapEntries/*`.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask.
  """

  certificateMapEntry = _messages.MessageField('CertificateMapEntry', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CertificatemanagerProjectsLocationsCertificateMapsCreateRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsCreateRequest
  object.

  Fields:
    certificateMap: A CertificateMap resource to be passed as the request
      body.
    certificateMapId: Required. A user-provided name of the certificate map.
    parent: Required. The parent resource of the certificate map. Must be in
      the format `projects/*/locations/*`.
  """

  certificateMap = _messages.MessageField('CertificateMap', 1)
  certificateMapId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CertificatemanagerProjectsLocationsCertificateMapsDeleteRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsDeleteRequest
  object.

  Fields:
    name: Required. A name of the certificate map to delete. Must be in the
      format `projects/*/locations/*/certificateMaps/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsCertificateMapsGetRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsGetRequest object.

  Fields:
    name: Required. A name of the certificate map to describe. Must be in the
      format `projects/*/locations/*/certificateMaps/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsCertificateMapsListRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsListRequest object.

  Fields:
    filter: Filter expression to restrict the Certificates Maps returned.
    orderBy: A list of Certificate Map field names used to specify the order
      of the returned results. The default sorting order is ascending. To
      specify descending order for a field, add a suffix " desc".
    pageSize: Maximum number of certificate maps to return per call.
    pageToken: The value returned by the last `ListCertificateMapsResponse`.
      Indicates that this is a continuation of a prior `ListCertificateMaps`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the certificate maps
      should be listed, specified in the format `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CertificatemanagerProjectsLocationsCertificateMapsPatchRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificateMapsPatchRequest object.

  Fields:
    certificateMap: A CertificateMap resource to be passed as the request
      body.
    name: A user-defined name of the Certificate Map. Certificate Map names
      must be unique globally and match pattern
      `projects/*/locations/*/certificateMaps/*`.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask.
  """

  certificateMap = _messages.MessageField('CertificateMap', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CertificatemanagerProjectsLocationsCertificatesCreateRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificatesCreateRequest object.

  Fields:
    certificate: A Certificate resource to be passed as the request body.
    certificateId: Required. A user-provided name of the certificate.
    parent: Required. The parent resource of the certificate. Must be in the
      format `projects/*/locations/*`.
  """

  certificate = _messages.MessageField('Certificate', 1)
  certificateId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CertificatemanagerProjectsLocationsCertificatesDeleteRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificatesDeleteRequest object.

  Fields:
    name: Required. A name of the certificate to delete. Must be in the format
      `projects/*/locations/*/certificates/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsCertificatesGetRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificatesGetRequest object.

  Fields:
    name: Required. A name of the certificate to describe. Must be in the
      format `projects/*/locations/*/certificates/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsCertificatesListRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificatesListRequest object.

  Fields:
    filter: Filter expression to restrict the Certificates returned.
    orderBy: A list of Certificate field names used to specify the order of
      the returned results. The default sorting order is ascending. To specify
      descending order for a field, add a suffix " desc".
    pageSize: Maximum number of certificates to return per call.
    pageToken: The value returned by the last `ListCertificatesResponse`.
      Indicates that this is a continuation of a prior `ListCertificates`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the certificate
      should be listed, specified in the format `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CertificatemanagerProjectsLocationsCertificatesPatchRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsCertificatesPatchRequest object.

  Fields:
    certificate: A Certificate resource to be passed as the request body.
    name: A user-defined name of the certificate. Certificate names must be
      unique globally and match pattern
      `projects/*/locations/*/certificates/*`.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask.
  """

  certificate = _messages.MessageField('Certificate', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CertificatemanagerProjectsLocationsDnsAuthorizationsCreateRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsDnsAuthorizationsCreateRequest
  object.

  Fields:
    dnsAuthorization: A DnsAuthorization resource to be passed as the request
      body.
    dnsAuthorizationId: Required. A user-provided name of the dns
      authorization.
    parent: Required. The parent resource of the dns authorization. Must be in
      the format `projects/*/locations/*`.
  """

  dnsAuthorization = _messages.MessageField('DnsAuthorization', 1)
  dnsAuthorizationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CertificatemanagerProjectsLocationsDnsAuthorizationsDeleteRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsDnsAuthorizationsDeleteRequest
  object.

  Fields:
    name: Required. A name of the dns authorization to delete. Must be in the
      format `projects/*/locations/*/dnsAuthorizations/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsDnsAuthorizationsGetRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsDnsAuthorizationsGetRequest object.

  Fields:
    name: Required. A name of the dns authorization to describe. Must be in
      the format `projects/*/locations/*/dnsAuthorizations/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsDnsAuthorizationsListRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsDnsAuthorizationsListRequest
  object.

  Fields:
    filter: Filter expression to restrict the Dns Authorizations returned.
    orderBy: A list of Dns Authorization field names used to specify the order
      of the returned results. The default sorting order is ascending. To
      specify descending order for a field, add a suffix " desc".
    pageSize: Maximum number of dns authorizations to return per call.
    pageToken: The value returned by the last `ListDnsAuthorizationsResponse`.
      Indicates that this is a continuation of a prior `ListDnsAuthorizations`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the dns
      authorizations should be listed, specified in the format
      `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CertificatemanagerProjectsLocationsDnsAuthorizationsPatchRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsDnsAuthorizationsPatchRequest
  object.

  Fields:
    dnsAuthorization: A DnsAuthorization resource to be passed as the request
      body.
    name: A user-defined name of the dns authorization. DnsAuthorization names
      must be unique globally and match pattern
      `projects/*/locations/*/dnsAuthorizations/*`.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask.
  """

  dnsAuthorization = _messages.MessageField('DnsAuthorization', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CertificatemanagerProjectsLocationsGetRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsListRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class CertificatemanagerProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class CertificatemanagerProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class CertificatemanagerProjectsLocationsTrustConfigsCreateRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsTrustConfigsCreateRequest object.

  Fields:
    parent: Required. The parent resource of the TrustConfig. Must be in the
      format `projects/*/locations/*`.
    trustConfig: A TrustConfig resource to be passed as the request body.
    trustConfigId: Required. A user-provided name of the TrustConfig. Must
      match the regexp `[a-z0-9-]{1,63}`.
  """

  parent = _messages.StringField(1, required=True)
  trustConfig = _messages.MessageField('TrustConfig', 2)
  trustConfigId = _messages.StringField(3)


class CertificatemanagerProjectsLocationsTrustConfigsDeleteRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsTrustConfigsDeleteRequest object.

  Fields:
    etag: The current etag of the TrustConfig. If an etag is provided and does
      not match the current etag of the resource, deletion will be blocked and
      an ABORTED error will be returned.
    name: Required. A name of the TrustConfig to delete. Must be in the format
      `projects/*/locations/*/trustConfigs/*`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class CertificatemanagerProjectsLocationsTrustConfigsGetRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsTrustConfigsGetRequest object.

  Fields:
    name: Required. A name of the TrustConfig to describe. Must be in the
      format `projects/*/locations/*/trustConfigs/*`.
  """

  name = _messages.StringField(1, required=True)


class CertificatemanagerProjectsLocationsTrustConfigsListRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsTrustConfigsListRequest object.

  Fields:
    filter: Filter expression to restrict the TrustConfigs returned.
    orderBy: A list of TrustConfig field names used to specify the order of
      the returned results. The default sorting order is ascending. To specify
      descending order for a field, add a suffix " desc".
    pageSize: Maximum number of TrustConfigs to return per call.
    pageToken: The value returned by the last `ListTrustConfigsResponse`.
      Indicates that this is a continuation of a prior `ListTrustConfigs`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the TrustConfigs
      should be listed, specified in the format `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CertificatemanagerProjectsLocationsTrustConfigsPatchRequest(_messages.Message):
  r"""A CertificatemanagerProjectsLocationsTrustConfigsPatchRequest object.

  Fields:
    name: A user-defined name of the trust config. TrustConfig names must be
      unique globally and match pattern
      `projects/*/locations/*/trustConfigs/*`.
    trustConfig: A TrustConfig resource to be passed as the request body.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask.
  """

  name = _messages.StringField(1, required=True)
  trustConfig = _messages.MessageField('TrustConfig', 2)
  updateMask = _messages.StringField(3)


class DnsAuthorization(_messages.Message):
  r"""A DnsAuthorization resource describes a way to perform domain
  authorization for certificate issuance.

  Messages:
    LabelsValue: Set of labels associated with a DnsAuthorization.

  Fields:
    createTime: Output only. The creation timestamp of a DnsAuthorization.
    description: One or more paragraphs of text description of a
      DnsAuthorization.
    dnsResourceRecord: Output only. DNS Resource Record that needs to be added
      to DNS configuration.
    domain: Required. Immutable. A domain that is being authorized. A
      DnsAuthorization resource covers a single domain and its wildcard, e.g.
      authorization for `example.com` can be used to issue certificates for
      `example.com` and `*.example.com`.
    labels: Set of labels associated with a DnsAuthorization.
    name: A user-defined name of the dns authorization. DnsAuthorization names
      must be unique globally and match pattern
      `projects/*/locations/*/dnsAuthorizations/*`.
    updateTime: Output only. The last update timestamp of a DnsAuthorization.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of labels associated with a DnsAuthorization.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  dnsResourceRecord = _messages.MessageField('DnsResourceRecord', 3)
  domain = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class DnsResourceRecord(_messages.Message):
  r"""The structure describing the DNS Resource Record that needs to be added
  to DNS configuration for the authorization to be usable by certificate.

  Fields:
    data: Output only. Data of the DNS Resource Record.
    name: Output only. Fully qualified name of the DNS Resource Record. e.g.
      `_acme-challenge.example.com`
    type: Output only. Type of the DNS Resource Record. Currently always set
      to "CNAME".
  """

  data = _messages.StringField(1)
  name = _messages.StringField(2)
  type = _messages.StringField(3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GclbTarget(_messages.Message):
  r"""Describes a Target Proxy that uses this Certificate Map.

  Fields:
    ipConfigs: Output only. IP configurations for this Target Proxy where the
      Certificate Map is serving.
    targetHttpsProxy: Output only. This field returns the resource name in the
      following format:
      `//compute.googleapis.com/projects/*/global/targetHttpsProxies/*`.
    targetSslProxy: Output only. This field returns the resource name in the
      following format:
      `//compute.googleapis.com/projects/*/global/targetSslProxies/*`.
  """

  ipConfigs = _messages.MessageField('IpConfig', 1, repeated=True)
  targetHttpsProxy = _messages.StringField(2)
  targetSslProxy = _messages.StringField(3)


class IntermediateCA(_messages.Message):
  r"""Defines an intermediate CA.

  Fields:
    pemCertificate: PEM intermediate certificate used for building up paths
      for validation. Each certificate provided in PEM format may occupy up to
      5kB.
  """

  pemCertificate = _messages.StringField(1)


class IpConfig(_messages.Message):
  r"""Defines IP configuration where this Certificate Map is serving.

  Fields:
    ipAddress: Output only. An external IP address.
    ports: Output only. Ports.
  """

  ipAddress = _messages.StringField(1)
  ports = _messages.IntegerField(2, repeated=True, variant=_messages.Variant.UINT32)


class ListCertificateIssuanceConfigsResponse(_messages.Message):
  r"""Response for the `ListCertificateIssuanceConfigs` method.

  Fields:
    certificateIssuanceConfigs: A list of certificate configs for the parent
      resource.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
  """

  certificateIssuanceConfigs = _messages.MessageField('CertificateIssuanceConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCertificateMapEntriesResponse(_messages.Message):
  r"""Response for the `ListCertificateMapEntries` method.

  Fields:
    certificateMapEntries: A list of certificate map entries for the parent
      resource.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
  """

  certificateMapEntries = _messages.MessageField('CertificateMapEntry', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCertificateMapsResponse(_messages.Message):
  r"""Response for the `ListCertificateMaps` method.

  Fields:
    certificateMaps: A list of certificate maps for the parent resource.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
  """

  certificateMaps = _messages.MessageField('CertificateMap', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCertificatesResponse(_messages.Message):
  r"""Response for the `ListCertificates` method.

  Fields:
    certificates: A list of certificates for the parent resource.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: A list of locations that could not be reached.
  """

  certificates = _messages.MessageField('Certificate', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDnsAuthorizationsResponse(_messages.Message):
  r"""Response for the `ListDnsAuthorizations` method.

  Fields:
    dnsAuthorizations: A list of dns authorizations for the parent resource.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
  """

  dnsAuthorizations = _messages.MessageField('DnsAuthorization', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListTrustConfigsResponse(_messages.Message):
  r"""Response for the `ListTrustConfigs` method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    trustConfigs: A list of TrustConfigs for the parent resource.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  trustConfigs = _messages.MessageField('TrustConfig', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ManagedCertificate(_messages.Message):
  r"""Configuration and state of a Managed Certificate. Certificate Manager
  provisions and renews Managed Certificates automatically, for as long as
  it's authorized to do so.

  Enums:
    StateValueValuesEnum: Output only. State of the managed certificate
      resource.

  Fields:
    authorizationAttemptInfo: Output only. Detailed state of the latest
      authorization attempt for each domain specified for managed certificate
      resource.
    dnsAuthorizations: Immutable. Authorizations that will be used for
      performing domain authorization.
    domains: Immutable. The domains for which a managed SSL certificate will
      be generated. Wildcard domains are only supported with DNS challenge
      resolution.
    issuanceConfig: Immutable. The resource name for a
      CertificateIssuanceConfig used to configure private PKI certificates in
      the format `projects/*/locations/*/certificateIssuanceConfigs/*`. If
      this field is not set, the certificates will instead be publicly signed
      as documented at https://cloud.google.com/load-balancing/docs/ssl-
      certificates/google-managed-certs#caa.
    provisioningIssue: Output only. Information about issues with provisioning
      a Managed Certificate.
    state: Output only. State of the managed certificate resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the managed certificate resource.

    Values:
      STATE_UNSPECIFIED: State is unspecified.
      PROVISIONING: Certificate Manager attempts to provision or renew the
        certificate. If the process takes longer than expected, consult the
        `provisioning_issue` field.
      FAILED: Multiple certificate provisioning attempts failed and
        Certificate Manager gave up. To try again, delete and create a new
        managed Certificate resource. For details see the `provisioning_issue`
        field.
      ACTIVE: The certificate management is working, and a certificate has
        been provisioned.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    FAILED = 2
    ACTIVE = 3

  authorizationAttemptInfo = _messages.MessageField('AuthorizationAttemptInfo', 1, repeated=True)
  dnsAuthorizations = _messages.StringField(2, repeated=True)
  domains = _messages.StringField(3, repeated=True)
  issuanceConfig = _messages.StringField(4)
  provisioningIssue = _messages.MessageField('ProvisioningIssue', 5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation. Output only.

  Fields:
    apiVersion: API version used to start the operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    requestedCancellation: Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    statusMessage: Human-readable status of the operation, if any.
    target: Server-defined resource path for the target of the operation.
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ProvisioningIssue(_messages.Message):
  r"""Information about issues with provisioning a Managed Certificate.

  Enums:
    ReasonValueValuesEnum: Output only. Reason for provisioning failures.

  Fields:
    details: Output only. Human readable explanation about the issue. Provided
      to help address the configuration issues. Not guaranteed to be stable.
      For programmatic access use Reason enum.
    reason: Output only. Reason for provisioning failures.
  """

  class ReasonValueValuesEnum(_messages.Enum):
    r"""Output only. Reason for provisioning failures.

    Values:
      REASON_UNSPECIFIED: Reason is unspecified.
      AUTHORIZATION_ISSUE: Certificate provisioning failed due to an issue
        with one or more of the domains on the certificate. For details of
        which domains failed, consult the `authorization_attempt_info` field.
      RATE_LIMITED: Exceeded Certificate Authority quotas or internal rate
        limits of the system. Provisioning may take longer to complete.
    """
    REASON_UNSPECIFIED = 0
    AUTHORIZATION_ISSUE = 1
    RATE_LIMITED = 2

  details = _messages.StringField(1)
  reason = _messages.EnumField('ReasonValueValuesEnum', 2)


class SelfManagedCertificate(_messages.Message):
  r"""Certificate data for a SelfManaged Certificate. SelfManaged Certificates
  are uploaded by the user. Updating such certificates before they expire
  remains the user's responsibility.

  Fields:
    pemCertificate: Input only. The PEM-encoded certificate chain. Leaf
      certificate comes first, followed by intermediate ones if any.
    pemPrivateKey: Input only. The PEM-encoded private key of the leaf
      certificate.
  """

  pemCertificate = _messages.StringField(1)
  pemPrivateKey = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TrustAnchor(_messages.Message):
  r"""Defines a trust anchor.

  Fields:
    pemCertificate: PEM root certificate of the PKI used for validation. Each
      certificate provided in PEM format may occupy up to 5kB.
  """

  pemCertificate = _messages.StringField(1)


class TrustConfig(_messages.Message):
  r"""Defines a trust config.

  Messages:
    LabelsValue: Set of labels associated with a TrustConfig.

  Fields:
    createTime: Output only. The creation timestamp of a TrustConfig.
    description: One or more paragraphs of text description of a TrustConfig.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    labels: Set of labels associated with a TrustConfig.
    name: A user-defined name of the trust config. TrustConfig names must be
      unique globally and match pattern
      `projects/*/locations/*/trustConfigs/*`.
    trustStores: Set of trust stores to perform validation against. This field
      is supported when TrustConfig is configured with Load Balancers,
      currently not supported for SPIFFE certificate validation. Only one
      TrustStore specified is currently allowed.
    updateTime: Output only. The last update timestamp of a TrustConfig.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of labels associated with a TrustConfig.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  trustStores = _messages.MessageField('TrustStore', 6, repeated=True)
  updateTime = _messages.StringField(7)


class TrustStore(_messages.Message):
  r"""Defines a trust store.

  Fields:
    intermediateCas: Set of intermediate CA certificates used for the path
      building phase of chain validation. The field is currently not supported
      if TrustConfig is used for the workload certificate feature.
    trustAnchors: List of Trust Anchors to be used while performing validation
      against a given TrustStore.
  """

  intermediateCas = _messages.MessageField('IntermediateCA', 1, repeated=True)
  trustAnchors = _messages.MessageField('TrustAnchor', 2, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

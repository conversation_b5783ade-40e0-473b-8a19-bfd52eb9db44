"""Generated message classes for privateca version v1beta1.

The Certificate Authority Service API is a highly-available, scalable service
that enables you to simplify and automate the management of private
certificate authorities (CAs) while staying in control of your private keys.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'privateca'


class AccessUrls(_messages.Message):
  r"""URLs where a CertificateAuthority will publish content.

  Fields:
    caCertificateAccessUrl: The URL where this CertificateAuthority's CA
      certificate is published. This will only be set for CAs that have been
      activated.
    crlAccessUrl: The URL where this CertificateAuthority's CRLs are
      published. This will only be set for CAs that have been activated.
  """

  caCertificateAccessUrl = _messages.StringField(1)
  crlAccessUrl = _messages.StringField(2)


class ActivateCertificateAuthorityRequest(_messages.Message):
  r"""Request message for
  CertificateAuthorityService.ActivateCertificateAuthority.

  Fields:
    pemCaCertificate: Required. The signed CA certificate issued from
      FetchCertificateAuthorityCsrResponse.pem_csr.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    subordinateConfig: Required. Must include information about the issuer of
      'pem_ca_certificate', and any further issuers until the self-signed CA.
  """

  pemCaCertificate = _messages.StringField(1)
  requestId = _messages.StringField(2)
  subordinateConfig = _messages.MessageField('SubordinateConfig', 3)


class AllowedConfigList(_messages.Message):
  r"""A AllowedConfigList object.

  Fields:
    allowedConfigValues: Required. All Certificates issued by the
      CertificateAuthority must match at least one listed
      ReusableConfigWrapper. If a ReusableConfigWrapper has an empty field,
      any value will be allowed for that field.
  """

  allowedConfigValues = _messages.MessageField('ReusableConfigWrapper', 1, repeated=True)


class AllowedSubjectAltNames(_messages.Message):
  r"""AllowedSubjectAltNames specifies the allowed values for SubjectAltNames
  by the CertificateAuthority when issuing Certificates.

  Fields:
    allowCustomSans: Optional. Specifies if to allow custom X509Extension
      values.
    allowGlobbingDnsWildcards: Optional. Specifies if glob patterns used for
      allowed_dns_names allow wildcard certificates. If this is set,
      certificate requests with wildcard domains will be permitted to match a
      glob pattern specified in allowed_dns_names. Otherwise, certificate
      requests with wildcard domains will be permitted only if
      allowed_dns_names contains a literal wildcard.
    allowedDnsNames: Optional. Contains valid, fully-qualified host names.
      Glob patterns are also supported. To allow an explicit wildcard
      certificate, escape with backlash (i.e. `\*`). E.g. for globbed entries:
      `*bar.com` will allow `foo.bar.com`, but not `*.bar.com`, unless the
      allow_globbing_dns_wildcards field is set. E.g. for wildcard entries:
      `\*.bar.com` will allow `*.bar.com`, but not `foo.bar.com`.
    allowedEmailAddresses: Optional. Contains valid RFC 2822 E-mail addresses.
      Glob patterns are also supported.
    allowedIps: Optional. Contains valid 32-bit IPv4 addresses and subnet
      ranges or RFC 4291 IPv6 addresses and subnet ranges. Subnet ranges are
      specified using the '/' notation (e.g. 10.0.0.0/8,
      2001:700:300:1800::/64). Glob patterns are supported only for ip address
      entries (i.e. not for subnet ranges).
    allowedUris: Optional. Contains valid RFC 3986 URIs. Glob patterns are
      also supported. To match across path seperators (i.e. '/') use the
      double star glob pattern (i.e. '**').
  """

  allowCustomSans = _messages.BooleanField(1)
  allowGlobbingDnsWildcards = _messages.BooleanField(2)
  allowedDnsNames = _messages.StringField(3, repeated=True)
  allowedEmailAddresses = _messages.StringField(4, repeated=True)
  allowedIps = _messages.StringField(5, repeated=True)
  allowedUris = _messages.StringField(6, repeated=True)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CaOptions(_messages.Message):
  r"""Describes values that are relevant in a CA certificate.

  Fields:
    isCa: Optional. Refers to the "CA" X.509 extension, which is a boolean
      value. When this value is missing, the extension will be omitted from
      the CA certificate.
    maxIssuerPathLength: Optional. Refers to the path length restriction X.509
      extension. For a CA certificate, this value describes the depth of
      subordinate CA certificates that are allowed. If this value is less than
      0, the request will fail. If this value is missing, the max path length
      will be omitted from the CA certificate.
  """

  isCa = _messages.BooleanField(1)
  maxIssuerPathLength = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Certificate(_messages.Message):
  r"""A Certificate corresponds to a signed X.509 certificate issued by a
  CertificateAuthority.

  Messages:
    LabelsValue: Optional. Labels with user-defined metadata.

  Fields:
    certificateDescription: Output only. A structured description of the
      issued X.509 certificate.
    config: Immutable. A description of the certificate and key that does not
      require X.509 or ASN.1.
    createTime: Output only. The time at which this Certificate was created.
    labels: Optional. Labels with user-defined metadata.
    lifetime: Required. Immutable. The desired lifetime of a certificate. Used
      to create the "not_before_time" and "not_after_time" fields inside an
      X.509 certificate. Note that the lifetime may be truncated if it would
      extend past the life of any certificate authority in the issuing chain.
    name: Output only. The resource path for this Certificate in the format
      `projects/*/locations/*/certificateAuthorities/*/certificates/*`.
    pemCertificate: Output only. The pem-encoded, signed X.509 certificate.
    pemCertificateChain: Output only. The chain that may be used to verify the
      X.509 certificate. Expected to be in issuer-to-root order according to
      RFC 5246.
    pemCsr: Immutable. A pem-encoded X.509 certificate signing request (CSR).
    revocationDetails: Output only. Details regarding the revocation of this
      Certificate. This Certificate is considered revoked if and only if this
      field is present.
    updateTime: Output only. The time at which this Certificate was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels with user-defined metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  certificateDescription = _messages.MessageField('CertificateDescription', 1)
  config = _messages.MessageField('CertificateConfig', 2)
  createTime = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  lifetime = _messages.StringField(5)
  name = _messages.StringField(6)
  pemCertificate = _messages.StringField(7)
  pemCertificateChain = _messages.StringField(8, repeated=True)
  pemCsr = _messages.StringField(9)
  revocationDetails = _messages.MessageField('RevocationDetails', 10)
  updateTime = _messages.StringField(11)


class CertificateAuthority(_messages.Message):
  r"""A CertificateAuthority represents an individual Certificate Authority. A
  CertificateAuthority can be used to create Certificates.

  Enums:
    StateValueValuesEnum: Output only. The State for this
      CertificateAuthority.
    TierValueValuesEnum: Required. Immutable. The Tier of this
      CertificateAuthority.
    TypeValueValuesEnum: Required. Immutable. The Type of this
      CertificateAuthority.

  Messages:
    LabelsValue: Optional. Labels with user-defined metadata.

  Fields:
    accessUrls: Output only. URLs for accessing content published by this CA,
      such as the CA certificate and CRLs.
    caCertificateDescriptions: Output only. A structured description of this
      CertificateAuthority's CA certificate and its issuers. Ordered as self-
      to-root.
    certificatePolicy: Optional. The CertificateAuthorityPolicy to enforce
      when issuing Certificates from this CertificateAuthority.
    config: Required. Immutable. The config used to create a self-signed X.509
      certificate or CSR.
    createTime: Output only. The time at which this CertificateAuthority was
      created.
    deleteTime: Output only. The time at which this CertificateAuthority will
      be deleted, if scheduled for deletion.
    gcsBucket: Immutable. The name of a Cloud Storage bucket where this
      CertificateAuthority will publish content, such as the CA certificate
      and CRLs. This must be a bucket name, without any prefixes (such as
      `gs://`) or suffixes (such as `.googleapis.com`). For example, to use a
      bucket named `my-bucket`, you would simply specify `my-bucket`. If not
      specified, a managed bucket will be created.
    issuingOptions: Optional. The IssuingOptions to follow when issuing
      Certificates from this CertificateAuthority.
    keySpec: Required. Immutable. Used when issuing certificates for this
      CertificateAuthority. If this CertificateAuthority is a self-signed
      CertificateAuthority, this key is also used to sign the self-signed CA
      certificate. Otherwise, it is used to sign a CSR.
    labels: Optional. Labels with user-defined metadata.
    lifetime: Required. The desired lifetime of the CA certificate. Used to
      create the "not_before_time" and "not_after_time" fields inside an X.509
      certificate.
    name: Output only. The resource name for this CertificateAuthority in the
      format `projects/*/locations/*/certificateAuthorities/*`.
    pemCaCertificates: Output only. This CertificateAuthority's certificate
      chain, including the current CertificateAuthority's certificate. Ordered
      such that the root issuer is the final element (consistent with RFC
      5246). For a self-signed CA, this will only list the current
      CertificateAuthority's certificate.
    state: Output only. The State for this CertificateAuthority.
    subordinateConfig: Optional. If this is a subordinate
      CertificateAuthority, this field will be set with the subordinate
      configuration, which describes its issuers. This may be updated, but
      this CertificateAuthority must continue to validate.
    tier: Required. Immutable. The Tier of this CertificateAuthority.
    type: Required. Immutable. The Type of this CertificateAuthority.
    updateTime: Output only. The time at which this CertificateAuthority was
      updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The State for this CertificateAuthority.

    Values:
      STATE_UNSPECIFIED: Not specified.
      ENABLED: Certificates can be issued from this CA. CRLs will be generated
        for this CA.
      DISABLED: Certificates cannot be issued from this CA. CRLs will still be
        generated.
      PENDING_ACTIVATION: Certificates cannot be issued from this CA. CRLs
        will not be generated.
      PENDING_DELETION: Certificates cannot be issued from this CA. CRLs will
        not be generated.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    PENDING_ACTIVATION = 3
    PENDING_DELETION = 4

  class TierValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The Tier of this CertificateAuthority.

    Values:
      TIER_UNSPECIFIED: Not specified.
      ENTERPRISE: Enterprise tier.
      DEVOPS: DevOps tier.
    """
    TIER_UNSPECIFIED = 0
    ENTERPRISE = 1
    DEVOPS = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The Type of this CertificateAuthority.

    Values:
      TYPE_UNSPECIFIED: Not specified.
      SELF_SIGNED: Self-signed CA.
      SUBORDINATE: Subordinate CA. Could be issued by a Private CA
        CertificateAuthority or an unmanaged CA.
    """
    TYPE_UNSPECIFIED = 0
    SELF_SIGNED = 1
    SUBORDINATE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels with user-defined metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessUrls = _messages.MessageField('AccessUrls', 1)
  caCertificateDescriptions = _messages.MessageField('CertificateDescription', 2, repeated=True)
  certificatePolicy = _messages.MessageField('CertificateAuthorityPolicy', 3)
  config = _messages.MessageField('CertificateConfig', 4)
  createTime = _messages.StringField(5)
  deleteTime = _messages.StringField(6)
  gcsBucket = _messages.StringField(7)
  issuingOptions = _messages.MessageField('IssuingOptions', 8)
  keySpec = _messages.MessageField('KeyVersionSpec', 9)
  labels = _messages.MessageField('LabelsValue', 10)
  lifetime = _messages.StringField(11)
  name = _messages.StringField(12)
  pemCaCertificates = _messages.StringField(13, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  subordinateConfig = _messages.MessageField('SubordinateConfig', 15)
  tier = _messages.EnumField('TierValueValuesEnum', 16)
  type = _messages.EnumField('TypeValueValuesEnum', 17)
  updateTime = _messages.StringField(18)


class CertificateAuthorityPolicy(_messages.Message):
  r"""The issuing policy for a CertificateAuthority. Certificates will not be
  successfully issued from this CertificateAuthority if they violate the
  policy.

  Fields:
    allowedCommonNames: Optional. If any value is specified here, then all
      Certificates issued by the CertificateAuthority must match at least one
      listed value. If no value is specified, all values will be allowed for
      this fied. Glob patterns are also supported.
    allowedConfigList: Optional. All Certificates issued by the
      CertificateAuthority must match at least one listed
      ReusableConfigWrapper in the list.
    allowedIssuanceModes: Optional. If specified, then only methods allowed in
      the IssuanceModes may be used to issue Certificates.
    allowedLocationsAndOrganizations: Optional. If any Subject is specified
      here, then all Certificates issued by the CertificateAuthority must
      match at least one listed Subject. If a Subject has an empty field, any
      value will be allowed for that field.
    allowedSans: Optional. If a AllowedSubjectAltNames is specified here, then
      all Certificates issued by the CertificateAuthority must match
      AllowedSubjectAltNames. If no value or an empty value is specified, any
      value will be allowed for the SubjectAltNames field.
    cloudFunctionPolicy: Optional. If specified, use a Cloud Function to
      implement custom certificate policy for certificate issuance.
    maximumLifetime: Optional. The maximum lifetime allowed by the
      CertificateAuthority. Note that if the any part if the issuing chain
      expires before a Certificate's requested maximum_lifetime, the effective
      lifetime will be explicitly truncated.
    overwriteConfigValues: Optional. All Certificates issued by the
      CertificateAuthority will use the provided configuration values,
      overwriting any requested configuration values.
  """

  allowedCommonNames = _messages.StringField(1, repeated=True)
  allowedConfigList = _messages.MessageField('AllowedConfigList', 2)
  allowedIssuanceModes = _messages.MessageField('IssuanceModes', 3)
  allowedLocationsAndOrganizations = _messages.MessageField('Subject', 4, repeated=True)
  allowedSans = _messages.MessageField('AllowedSubjectAltNames', 5)
  cloudFunctionPolicy = _messages.MessageField('CloudFunctionPolicy', 6)
  maximumLifetime = _messages.StringField(7)
  overwriteConfigValues = _messages.MessageField('ReusableConfigWrapper', 8)


class CertificateConfig(_messages.Message):
  r"""A CertificateConfig describes an X.509 certificate or CSR that is to be
  created, as an alternative to using ASN.1.

  Fields:
    publicKey: Optional. The public key that corresponds to this config. This
      is, for example, used when issuing Certificates, but not when creating a
      self-signed CertificateAuthority or CertificateAuthority CSR.
    reusableConfig: Required. Describes how some of the technical fields in a
      certificate should be populated.
    subjectConfig: Required. Specifies some of the values in a certificate
      that are related to the subject.
  """

  publicKey = _messages.MessageField('PublicKey', 1)
  reusableConfig = _messages.MessageField('ReusableConfigWrapper', 2)
  subjectConfig = _messages.MessageField('SubjectConfig', 3)


class CertificateDescription(_messages.Message):
  r"""A CertificateDescription describes an X.509 certificate or CSR that has
  been issued, as an alternative to using ASN.1 / X.509.

  Fields:
    aiaIssuingCertificateUrls: Describes lists of issuer CA certificate URLs
      that appear in the "Authority Information Access" extension in the
      certificate.
    authorityKeyId: Identifies the subject_key_id of the parent certificate,
      per https://tools.ietf.org/html/rfc5280#section-*******
    certFingerprint: The hash of the x.509 certificate.
    configValues: Describes some of the technical fields in a certificate.
    crlDistributionPoints: Describes a list of locations to obtain CRL
      information, i.e. the DistributionPoint.fullName described by
      https://tools.ietf.org/html/rfc5280#section-********
    publicKey: The public key that corresponds to an issued certificate.
    subjectDescription: Describes some of the values in a certificate that are
      related to the subject and lifetime.
    subjectKeyId: Provides a means of identifiying certificates that contain a
      particular public key, per
      https://tools.ietf.org/html/rfc5280#section-*******.
  """

  aiaIssuingCertificateUrls = _messages.StringField(1, repeated=True)
  authorityKeyId = _messages.MessageField('KeyId', 2)
  certFingerprint = _messages.MessageField('CertificateFingerprint', 3)
  configValues = _messages.MessageField('ReusableConfigValues', 4)
  crlDistributionPoints = _messages.StringField(5, repeated=True)
  publicKey = _messages.MessageField('PublicKey', 6)
  subjectDescription = _messages.MessageField('SubjectDescription', 7)
  subjectKeyId = _messages.MessageField('KeyId', 8)


class CertificateFingerprint(_messages.Message):
  r"""A group of fingerprints for the x509 certificate.

  Fields:
    sha256Hash: The SHA 256 hash, encoded in hexadecimal, of the DER x509
      certificate.
  """

  sha256Hash = _messages.StringField(1)


class CertificateRevocationList(_messages.Message):
  r"""A CertificateRevocationList corresponds to a signed X.509 certificate
  Revocation List (CRL). A CRL contains the serial numbers of certificates
  that should no longer be trusted.

  Enums:
    StateValueValuesEnum: Output only. The State for this
      CertificateRevocationList.

  Messages:
    LabelsValue: Optional. Labels with user-defined metadata.

  Fields:
    accessUrl: Output only. The location where 'pem_crl' can be accessed.
    createTime: Output only. The time at which this CertificateRevocationList
      was created.
    labels: Optional. Labels with user-defined metadata.
    name: Output only. The resource path for this CertificateRevocationList in
      the format `projects/*/locations/*/certificateAuthorities/*/
      certificateRevocationLists/*`.
    pemCrl: Output only. The PEM-encoded X.509 CRL.
    revokedCertificates: Output only. The revoked serial numbers that appear
      in pem_crl.
    sequenceNumber: Output only. The CRL sequence number that appears in
      pem_crl.
    state: Output only. The State for this CertificateRevocationList.
    updateTime: Output only. The time at which this CertificateRevocationList
      was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The State for this CertificateRevocationList.

    Values:
      STATE_UNSPECIFIED: Not specified.
      ACTIVE: The CertificateRevocationList is up to date.
      SUPERSEDED: The CertificateRevocationList is no longer current.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    SUPERSEDED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels with user-defined metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessUrl = _messages.StringField(1)
  createTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  pemCrl = _messages.StringField(5)
  revokedCertificates = _messages.MessageField('RevokedCertificate', 6, repeated=True)
  sequenceNumber = _messages.IntegerField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class CloudFunctionPolicy(_messages.Message):
  r"""CloudFunctionPolicy specifies the Cloud Function custom certificate
  policy for certificate issuance.

  Fields:
    cloudFunction: Required. The resource name of the Cloud Function to
      invoke, in the format `projects/*/locations/*/functions/*`.
  """

  cloudFunction = _messages.StringField(1)


class DisableCertificateAuthorityRequest(_messages.Message):
  r"""Request message for
  CertificateAuthorityService.DisableCertificateAuthority.

  Fields:
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  requestId = _messages.StringField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnableCertificateAuthorityRequest(_messages.Message):
  r"""Request message for
  CertificateAuthorityService.EnableCertificateAuthority.

  Fields:
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  requestId = _messages.StringField(1)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExtendedKeyUsageOptions(_messages.Message):
  r"""KeyUsage.ExtendedKeyUsageOptions has fields that correspond to certain
  common OIDs that could be specified as an extended key usage value.

  Fields:
    clientAuth: Corresponds to OID *******.*******.2. Officially described as
      "TLS WWW client authentication", though regularly used for non-WWW TLS.
    codeSigning: Corresponds to OID *******.*******.3. Officially described as
      "Signing of downloadable executable code client authentication".
    emailProtection: Corresponds to OID *******.*******.4. Officially
      described as "Email protection".
    ocspSigning: Corresponds to OID *******.*******.9. Officially described as
      "Signing OCSP responses".
    serverAuth: Corresponds to OID *******.*******.1. Officially described as
      "TLS WWW server authentication", though regularly used for non-WWW TLS.
    timeStamping: Corresponds to OID *******.*******.8. Officially described
      as "Binding the hash of an object to a time".
  """

  clientAuth = _messages.BooleanField(1)
  codeSigning = _messages.BooleanField(2)
  emailProtection = _messages.BooleanField(3)
  ocspSigning = _messages.BooleanField(4)
  serverAuth = _messages.BooleanField(5)
  timeStamping = _messages.BooleanField(6)


class FetchCertificateAuthorityCsrResponse(_messages.Message):
  r"""Response message for
  CertificateAuthorityService.FetchCertificateAuthorityCsr.

  Fields:
    pemCsr: Output only. The PEM-encoded signed certificate signing request
      (CSR).
  """

  pemCsr = _messages.StringField(1)


class IssuanceModes(_messages.Message):
  r"""IssuanceModes specifies the allowed ways in which Certificates may be
  requested from this CertificateAuthority.

  Fields:
    allowConfigBasedIssuance: Required. When true, allows callers to create
      Certificates by specifying a CertificateConfig.
    allowCsrBasedIssuance: Required. When true, allows callers to create
      Certificates by specifying a CSR.
  """

  allowConfigBasedIssuance = _messages.BooleanField(1)
  allowCsrBasedIssuance = _messages.BooleanField(2)


class IssuingOptions(_messages.Message):
  r"""Options that affect all certificates issued by a CertificateAuthority.

  Fields:
    includeCaCertUrl: Required. When true, includes a URL to the issuing CA
      certificate in the "authority information access" X.509 extension.
    includeCrlAccessUrl: Required. When true, includes a URL to the CRL
      corresponding to certificates issued from a CertificateAuthority. CRLs
      will expire 7 days from their creation. However, we will rebuild daily.
      CRLs are also rebuilt shortly after a certificate is revoked.
  """

  includeCaCertUrl = _messages.BooleanField(1)
  includeCrlAccessUrl = _messages.BooleanField(2)


class KeyId(_messages.Message):
  r"""A KeyId identifies a specific public key, usually by hashing the public
  key.

  Fields:
    keyId: Optional. The value of this KeyId encoded in lowercase hexadecimal.
      This is most likely the 160 bit SHA-1 hash of the public key.
  """

  keyId = _messages.StringField(1)


class KeyUsage(_messages.Message):
  r"""A KeyUsage describes key usage values that may appear in an X.509
  certificate.

  Fields:
    baseKeyUsage: Describes high-level ways in which a key may be used.
    extendedKeyUsage: Detailed scenarios in which a key may be used.
    unknownExtendedKeyUsages: Used to describe extended key usages that are
      not listed in the KeyUsage.ExtendedKeyUsageOptions message.
  """

  baseKeyUsage = _messages.MessageField('KeyUsageOptions', 1)
  extendedKeyUsage = _messages.MessageField('ExtendedKeyUsageOptions', 2)
  unknownExtendedKeyUsages = _messages.MessageField('ObjectId', 3, repeated=True)


class KeyUsageOptions(_messages.Message):
  r"""KeyUsage.KeyUsageOptions corresponds to the key usage values described
  in https://tools.ietf.org/html/rfc5280#section-*******.

  Fields:
    certSign: The key may be used to sign certificates.
    contentCommitment: The key may be used for cryptographic commitments. Note
      that this may also be referred to as "non-repudiation".
    crlSign: The key may be used sign certificate revocation lists.
    dataEncipherment: The key may be used to encipher data.
    decipherOnly: The key may be used to decipher only.
    digitalSignature: The key may be used for digital signatures.
    encipherOnly: The key may be used to encipher only.
    keyAgreement: The key may be used in a key agreement protocol.
    keyEncipherment: The key may be used to encipher other keys.
  """

  certSign = _messages.BooleanField(1)
  contentCommitment = _messages.BooleanField(2)
  crlSign = _messages.BooleanField(3)
  dataEncipherment = _messages.BooleanField(4)
  decipherOnly = _messages.BooleanField(5)
  digitalSignature = _messages.BooleanField(6)
  encipherOnly = _messages.BooleanField(7)
  keyAgreement = _messages.BooleanField(8)
  keyEncipherment = _messages.BooleanField(9)


class KeyVersionSpec(_messages.Message):
  r"""A Cloud KMS key configuration that a CertificateAuthority will use.

  Enums:
    AlgorithmValueValuesEnum: Required. The algorithm to use for creating a
      managed Cloud KMS key for a for a simplified experience. All managed
      keys will be have their ProtectionLevel as `HSM`.

  Fields:
    algorithm: Required. The algorithm to use for creating a managed Cloud KMS
      key for a for a simplified experience. All managed keys will be have
      their ProtectionLevel as `HSM`.
    cloudKmsKeyVersion: Required. The resource name for an existing Cloud KMS
      CryptoKeyVersion in the format
      `projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*`.
      This option enables full flexibility in the key's capabilities and
      properties.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""Required. The algorithm to use for creating a managed Cloud KMS key
    for a for a simplified experience. All managed keys will be have their
    ProtectionLevel as `HSM`.

    Values:
      SIGN_HASH_ALGORITHM_UNSPECIFIED: Not specified.
      RSA_PSS_2048_SHA256: maps to
        CryptoKeyVersionAlgorithm.RSA_SIGN_PSS_2048_SHA256
      RSA_PSS_3072_SHA256: maps to CryptoKeyVersionAlgorithm.
        RSA_SIGN_PSS_3072_SHA256
      RSA_PSS_4096_SHA256: maps to
        CryptoKeyVersionAlgorithm.RSA_SIGN_PSS_4096_SHA256
      RSA_PKCS1_2048_SHA256: maps to
        CryptoKeyVersionAlgorithm.RSA_SIGN_PKCS1_2048_SHA256
      RSA_PKCS1_3072_SHA256: maps to
        CryptoKeyVersionAlgorithm.RSA_SIGN_PKCS1_3072_SHA256
      RSA_PKCS1_4096_SHA256: maps to
        CryptoKeyVersionAlgorithm.RSA_SIGN_PKCS1_4096_SHA256
      EC_P256_SHA256: maps to CryptoKeyVersionAlgorithm.EC_SIGN_P256_SHA256
      EC_P384_SHA384: maps to CryptoKeyVersionAlgorithm.EC_SIGN_P384_SHA384
    """
    SIGN_HASH_ALGORITHM_UNSPECIFIED = 0
    RSA_PSS_2048_SHA256 = 1
    RSA_PSS_3072_SHA256 = 2
    RSA_PSS_4096_SHA256 = 3
    RSA_PKCS1_2048_SHA256 = 4
    RSA_PKCS1_3072_SHA256 = 5
    RSA_PKCS1_4096_SHA256 = 6
    EC_P256_SHA256 = 7
    EC_P384_SHA384 = 8

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  cloudKmsKeyVersion = _messages.StringField(2)


class ListCertificateAuthoritiesResponse(_messages.Message):
  r"""Response message for
  CertificateAuthorityService.ListCertificateAuthorities.

  Fields:
    certificateAuthorities: The list of CertificateAuthorities.
    nextPageToken: A token to retrieve next page of results. Pass this value
      in ListCertificateAuthoritiesRequest.next_page_token to retrieve the
      next page of results.
    unreachable: A list of locations (e.g. "us-west1") that could not be
      reached.
  """

  certificateAuthorities = _messages.MessageField('CertificateAuthority', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCertificateRevocationListsResponse(_messages.Message):
  r"""Response message for
  CertificateAuthorityService.ListCertificateRevocationLists.

  Fields:
    certificateRevocationLists: The list of CertificateRevocationLists.
    nextPageToken: A token to retrieve next page of results. Pass this value
      in ListCertificateRevocationListsRequest.next_page_token to retrieve the
      next page of results.
    unreachable: A list of locations (e.g. "us-west1") that could not be
      reached.
  """

  certificateRevocationLists = _messages.MessageField('CertificateRevocationList', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCertificatesResponse(_messages.Message):
  r"""Response message for CertificateAuthorityService.ListCertificates.

  Fields:
    certificates: The list of Certificates.
    nextPageToken: A token to retrieve next page of results. Pass this value
      in ListCertificatesRequest.next_page_token to retrieve the next page of
      results.
    unreachable: A list of locations (e.g. "us-west1") that could not be
      reached.
  """

  certificates = _messages.MessageField('Certificate', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReusableConfigsResponse(_messages.Message):
  r"""Response message for CertificateAuthorityService.ListReusableConfigs.

  Fields:
    nextPageToken: A token to retrieve next page of results. Pass this value
      in ListReusableConfigsRequest.next_page_token to retrieve the next page
      of results.
    reusableConfigs: The list of ReusableConfigs.
    unreachable: A list of locations (e.g. "us-west1") that could not be
      reached.
  """

  nextPageToken = _messages.StringField(1)
  reusableConfigs = _messages.MessageField('ReusableConfig', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ObjectId(_messages.Message):
  r"""An ObjectId specifies an object identifier (OID). These provide context
  and describe types in ASN.1 messages.

  Fields:
    objectIdPath: Required. The parts of an OID path. The most significant
      parts of the path come first.
  """

  objectIdPath = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PrivatecaProjectsLocationsCertificateAuthoritiesActivateRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesActivateRequest
  object.

  Fields:
    activateCertificateAuthorityRequest: A ActivateCertificateAuthorityRequest
      resource to be passed as the request body.
    name: Required. The resource name for this CertificateAuthority in the
      format `projects/*/locations/*/certificateAuthorities/*`.
  """

  activateCertificateAuthorityRequest = _messages.MessageField('ActivateCertificateAuthorityRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationListsGetIamPolicyRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationL
  istsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationListsGetRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationL
  istsGetRequest object.

  Fields:
    name: Required. The name of the CertificateRevocationList to get.
  """

  name = _messages.StringField(1, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationListsListRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationL
  istsListRequest object.

  Fields:
    filter: Optional. Only include resources that match the filter in the
      response.
    orderBy: Optional. Specify how the results should be sorted.
    pageSize: Optional. Limit on the number of CertificateRevocationLists to
      include in the response. Further CertificateRevocationLists can
      subsequently be obtained by including the
      ListCertificateRevocationListsResponse.next_page_token in a subsequent
      request. If unspecified, the server will pick an appropriate default.
    pageToken: Optional. Pagination token, returned earlier via
      ListCertificateRevocationListsResponse.next_page_token.
    parent: Required. The resource name of the location associated with the
      CertificateRevocationLists, in the format
      `projects/*/locations/*/certificateauthorities/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationListsPatchRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationL
  istsPatchRequest object.

  Fields:
    certificateRevocationList: A CertificateRevocationList resource to be
      passed as the request body.
    name: Output only. The resource path for this CertificateRevocationList in
      the format `projects/*/locations/*/certificateAuthorities/*/
      certificateRevocationLists/*`.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. A list of fields to be updated in this request.
  """

  certificateRevocationList = _messages.MessageField('CertificateRevocationList', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationListsSetIamPolicyRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationL
  istsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationListsTestIamPermissionsRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesCertificateRevocationL
  istsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesCreateRequest(_messages.Message):
  r"""A
  PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesCreateRequest
  object.

  Fields:
    certificate: A Certificate resource to be passed as the request body.
    certificateId: Optional. It must be unique within a location and match the
      regular expression `[a-zA-Z0-9_-]{1,63}`. This field is required when
      using a CertificateAuthority in the Enterprise
      CertificateAuthority.Tier, but is optional and its value is ignored
      otherwise.
    parent: Required. The resource name of the location and
      CertificateAuthority associated with the Certificate, in the format
      `projects/*/locations/*/certificateAuthorities/*`.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  certificate = _messages.MessageField('Certificate', 1)
  certificateId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesGetRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesGetRequest
  object.

  Fields:
    name: Required. The name of the Certificate to get.
  """

  name = _messages.StringField(1, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesListRequest(_messages.Message):
  r"""A
  PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesListRequest
  object.

  Fields:
    filter: Optional. Only include resources that match the filter in the
      response. For details on supported filters and syntax, see [Certificates
      Filtering documentation](https://cloud.google.com/certificate-authority-
      service/docs/sorting-filtering-certificates#filtering_support).
    orderBy: Optional. Specify how the results should be sorted. For details
      on supported fields and syntax, see [Certificates Sorting
      documentation](https://cloud.google.com/certificate-authority-
      service/docs/sorting-filtering-certificates#sorting_support).
    pageSize: Optional. Limit on the number of Certificates to include in the
      response. Further Certificates can subsequently be obtained by including
      the ListCertificatesResponse.next_page_token in a subsequent request. If
      unspecified, the server will pick an appropriate default.
    pageToken: Optional. Pagination token, returned earlier via
      ListCertificatesResponse.next_page_token.
    parent: Required. The resource name of the location associated with the
      Certificates, in the format
      `projects/*/locations/*/certificateauthorities/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesPatchRequest(_messages.Message):
  r"""A
  PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesPatchRequest
  object.

  Fields:
    certificate: A Certificate resource to be passed as the request body.
    name: Output only. The resource path for this Certificate in the format
      `projects/*/locations/*/certificateAuthorities/*/certificates/*`.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. A list of fields to be updated in this request.
  """

  certificate = _messages.MessageField('Certificate', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesRevokeRequest(_messages.Message):
  r"""A
  PrivatecaProjectsLocationsCertificateAuthoritiesCertificatesRevokeRequest
  object.

  Fields:
    name: Required. The resource name for this Certificate in the format
      `projects/*/locations/*/certificateAuthorities/*/certificates/*`.
    revokeCertificateRequest: A RevokeCertificateRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  revokeCertificateRequest = _messages.MessageField('RevokeCertificateRequest', 2)


class PrivatecaProjectsLocationsCertificateAuthoritiesCreateRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesCreateRequest object.

  Fields:
    certificateAuthority: A CertificateAuthority resource to be passed as the
      request body.
    certificateAuthorityId: Required. It must be unique within a location and
      match the regular expression `[a-zA-Z0-9_-]{1,63}`
    parent: Required. The resource name of the location associated with the
      CertificateAuthorities, in the format `projects/*/locations/*`.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  certificateAuthority = _messages.MessageField('CertificateAuthority', 1)
  certificateAuthorityId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class PrivatecaProjectsLocationsCertificateAuthoritiesDisableRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesDisableRequest object.

  Fields:
    disableCertificateAuthorityRequest: A DisableCertificateAuthorityRequest
      resource to be passed as the request body.
    name: Required. The resource name for this CertificateAuthority in the
      format `projects/*/locations/*/certificateAuthorities/*`.
  """

  disableCertificateAuthorityRequest = _messages.MessageField('DisableCertificateAuthorityRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesEnableRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesEnableRequest object.

  Fields:
    enableCertificateAuthorityRequest: A EnableCertificateAuthorityRequest
      resource to be passed as the request body.
    name: Required. The resource name for this CertificateAuthority in the
      format `projects/*/locations/*/certificateAuthorities/*`.
  """

  enableCertificateAuthorityRequest = _messages.MessageField('EnableCertificateAuthorityRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesFetchRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesFetchRequest object.

  Fields:
    name: Required. The resource name for this CertificateAuthority in the
      format `projects/*/locations/*/certificateAuthorities/*`.
  """

  name = _messages.StringField(1, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesGetIamPolicyRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesGetRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesGetRequest object.

  Fields:
    name: Required. The name of the CertificateAuthority to get.
  """

  name = _messages.StringField(1, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesListRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesListRequest object.

  Fields:
    filter: Optional. Only include resources that match the filter in the
      response.
    orderBy: Optional. Specify how the results should be sorted.
    pageSize: Optional. Limit on the number of CertificateAuthorities to
      include in the response. Further CertificateAuthorities can subsequently
      be obtained by including the
      ListCertificateAuthoritiesResponse.next_page_token in a subsequent
      request. If unspecified, the server will pick an appropriate default.
    pageToken: Optional. Pagination token, returned earlier via
      ListCertificateAuthoritiesResponse.next_page_token.
    parent: Required. The resource name of the location associated with the
      CertificateAuthorities, in the format `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivatecaProjectsLocationsCertificateAuthoritiesPatchRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesPatchRequest object.

  Fields:
    certificateAuthority: A CertificateAuthority resource to be passed as the
      request body.
    name: Output only. The resource name for this CertificateAuthority in the
      format `projects/*/locations/*/certificateAuthorities/*`.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. A list of fields to be updated in this request.
  """

  certificateAuthority = _messages.MessageField('CertificateAuthority', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class PrivatecaProjectsLocationsCertificateAuthoritiesRestoreRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesRestoreRequest object.

  Fields:
    name: Required. The resource name for this CertificateAuthority in the
      format `projects/*/locations/*/certificateAuthorities/*`.
    restoreCertificateAuthorityRequest: A RestoreCertificateAuthorityRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  restoreCertificateAuthorityRequest = _messages.MessageField('RestoreCertificateAuthorityRequest', 2)


class PrivatecaProjectsLocationsCertificateAuthoritiesScheduleDeleteRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesScheduleDeleteRequest
  object.

  Fields:
    name: Required. The resource name for this CertificateAuthority in the
      format `projects/*/locations/*/certificateAuthorities/*`.
    scheduleDeleteCertificateAuthorityRequest: A
      ScheduleDeleteCertificateAuthorityRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  scheduleDeleteCertificateAuthorityRequest = _messages.MessageField('ScheduleDeleteCertificateAuthorityRequest', 2)


class PrivatecaProjectsLocationsCertificateAuthoritiesSetIamPolicyRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsCertificateAuthoritiesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class PrivatecaProjectsLocationsCertificateAuthoritiesTestIamPermissionsRequest(_messages.Message):
  r"""A
  PrivatecaProjectsLocationsCertificateAuthoritiesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class PrivatecaProjectsLocationsGetRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class PrivatecaProjectsLocationsListRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PrivatecaProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivatecaProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class PrivatecaProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PrivatecaProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PrivatecaProjectsLocationsReusableConfigsCreateRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsReusableConfigsCreateRequest object.

  Fields:
    parent: Required. The resource name of the location associated with the
      ReusableConfig, in the format `projects/*/locations/*`.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    reusableConfig: A ReusableConfig resource to be passed as the request
      body.
    reusableConfigId: Required. It must be unique within a location and match
      the regular expression `[a-zA-Z0-9_-]{1,63}`
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  reusableConfig = _messages.MessageField('ReusableConfig', 3)
  reusableConfigId = _messages.StringField(4)


class PrivatecaProjectsLocationsReusableConfigsDeleteRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsReusableConfigsDeleteRequest object.

  Fields:
    name: Required. The resource name for this ReusableConfig in the format
      `projects/*/locations/*/reusableConfigs/*`.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class PrivatecaProjectsLocationsReusableConfigsGetIamPolicyRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsReusableConfigsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class PrivatecaProjectsLocationsReusableConfigsGetRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsReusableConfigsGetRequest object.

  Fields:
    name: Required. The name of the ReusableConfigs to get.
  """

  name = _messages.StringField(1, required=True)


class PrivatecaProjectsLocationsReusableConfigsListRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsReusableConfigsListRequest object.

  Fields:
    filter: Optional. Only include resources that match the filter in the
      response.
    orderBy: Optional. Specify how the results should be sorted.
    pageSize: Optional. Limit on the number of ReusableConfigs to include in
      the response. Further ReusableConfigs can subsequently be obtained by
      including the ListReusableConfigsResponse.next_page_token in a
      subsequent request. If unspecified, the server will pick an appropriate
      default.
    pageToken: Optional. Pagination token, returned earlier via
      ListReusableConfigsResponse.next_page_token.
    parent: Required. The resource name of the location associated with the
      ReusableConfigs, in the format `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivatecaProjectsLocationsReusableConfigsPatchRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsReusableConfigsPatchRequest object.

  Fields:
    name: Output only. The resource path for this ReusableConfig in the format
      `projects/*/locations/*/reusableConfigs/*`.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    reusableConfig: A ReusableConfig resource to be passed as the request
      body.
    updateMask: Required. A list of fields to be updated in this request.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  reusableConfig = _messages.MessageField('ReusableConfig', 3)
  updateMask = _messages.StringField(4)


class PrivatecaProjectsLocationsReusableConfigsSetIamPolicyRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsReusableConfigsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class PrivatecaProjectsLocationsReusableConfigsTestIamPermissionsRequest(_messages.Message):
  r"""A PrivatecaProjectsLocationsReusableConfigsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class PublicKey(_messages.Message):
  r"""A PublicKey describes a public key.

  Enums:
    TypeValueValuesEnum: Optional. The type of public key. If specified, it
      must match the public key used for the`key` field.

  Fields:
    key: Required. A public key. When this is specified in a request, the
      padding and encoding can be any of the options described by the
      respective 'KeyType' value. When this is generated by the service, it
      will always be an RFC 5280
      [SubjectPublicKeyInfo](https://tools.ietf.org/html/rfc5280#section-4.1)
      structure containing an algorithm identifier and a key.
    type: Optional. The type of public key. If specified, it must match the
      public key used for the`key` field.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of public key. If specified, it must match the
    public key used for the`key` field.

    Values:
      KEY_TYPE_UNSPECIFIED: Default unspecified value.
      PEM_RSA_KEY: A PEM-encoded PKCS#1/RFC 3447 RSAPublicKey structure, or an
        RFC 5280 [SubjectPublicKeyInfo](https://tools.ietf.org/html/rfc5280#se
        ction-4.1) structure containing the former.
      PEM_EC_KEY: An RFC 5280
        [SubjectPublicKeyInfo](https://tools.ietf.org/html/rfc5280#section-
        4.1) structure containing a PEM-encoded compressed NIST
        P-256/secp256r1/prime256v1 or P-384 key.
    """
    KEY_TYPE_UNSPECIFIED = 0
    PEM_RSA_KEY = 1
    PEM_EC_KEY = 2

  key = _messages.BytesField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class ReconciliationOperationMetadata(_messages.Message):
  r"""Operation metadata returned by the CLH during resource state
  reconciliation.

  Enums:
    ExclusiveActionValueValuesEnum: Excluisive action returned by the CLH.

  Fields:
    deleteResource: DEPRECATED. Use exclusive_action instead.
    exclusiveAction: Excluisive action returned by the CLH.
  """

  class ExclusiveActionValueValuesEnum(_messages.Enum):
    r"""Excluisive action returned by the CLH.

    Values:
      UNKNOWN_REPAIR_ACTION: Unknown repair action.
      DELETE: The resource has to be deleted. When using this bit, the CLH
        should fail the operation. DEPRECATED. Instead use DELETE_RESOURCE
        OperationSignal in SideChannel.
      RETRY: This resource could not be repaired but the repair should be
        tried again at a later time. This can happen if there is a dependency
        that needs to be resolved first- e.g. if a parent resource must be
        repaired before a child resource.
    """
    UNKNOWN_REPAIR_ACTION = 0
    DELETE = 1
    RETRY = 2

  deleteResource = _messages.BooleanField(1)
  exclusiveAction = _messages.EnumField('ExclusiveActionValueValuesEnum', 2)


class RestoreCertificateAuthorityRequest(_messages.Message):
  r"""Request message for
  CertificateAuthorityService.RestoreCertificateAuthority.

  Fields:
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  requestId = _messages.StringField(1)


class ReusableConfig(_messages.Message):
  r"""A ReusableConfig refers to a managed ReusableConfigValues. Those, in
  turn, are used to describe certain fields of an X.509 certificate, such as
  the key usage fields, fields specific to CA certificates, certificate policy
  extensions and custom extensions.

  Messages:
    LabelsValue: Optional. Labels with user-defined metadata.

  Fields:
    createTime: Output only. The time at which this ReusableConfig was
      created.
    description: Optional. A human-readable description of scenarios these
      ReusableConfigValues may be compatible with.
    labels: Optional. Labels with user-defined metadata.
    name: Output only. The resource path for this ReusableConfig in the format
      `projects/*/locations/*/reusableConfigs/*`.
    updateTime: Output only. The time at which this ReusableConfig was
      updated.
    values: Required. The config values.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels with user-defined metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)
  values = _messages.MessageField('ReusableConfigValues', 6)


class ReusableConfigValues(_messages.Message):
  r"""A ReusableConfigValues is used to describe certain fields of an X.509
  certificate, such as the key usage fields, fields specific to CA
  certificates, certificate policy extensions and custom extensions.

  Fields:
    additionalExtensions: Optional. Describes custom X.509 extensions.
    aiaOcspServers: Optional. Describes Online Certificate Status Protocol
      (OCSP) endpoint addresses that appear in the "Authority Information
      Access" extension in the certificate.
    caOptions: Optional. Describes options in this ReusableConfigValues that
      are relevant in a CA certificate.
    keyUsage: Optional. Indicates the intended use for keys that correspond to
      a certificate.
    policyIds: Optional. Describes the X.509 certificate policy object
      identifiers, per https://tools.ietf.org/html/rfc5280#section-*******.
  """

  additionalExtensions = _messages.MessageField('X509Extension', 1, repeated=True)
  aiaOcspServers = _messages.StringField(2, repeated=True)
  caOptions = _messages.MessageField('CaOptions', 3)
  keyUsage = _messages.MessageField('KeyUsage', 4)
  policyIds = _messages.MessageField('ObjectId', 5, repeated=True)


class ReusableConfigWrapper(_messages.Message):
  r"""A ReusableConfigWrapper describes values that may assist in creating an
  X.509 certificate, or a reference to a pre-defined set of values.

  Fields:
    reusableConfig: Required. A resource path to a ReusableConfig in the
      format `projects/*/locations/*/reusableConfigs/*`.
    reusableConfigValues: Required. A user-specified inline
      ReusableConfigValues.
  """

  reusableConfig = _messages.StringField(1)
  reusableConfigValues = _messages.MessageField('ReusableConfigValues', 2)


class RevocationDetails(_messages.Message):
  r"""Describes fields that are relavent to the revocation of a Certificate.

  Enums:
    RevocationStateValueValuesEnum: Indicates why a Certificate was revoked.

  Fields:
    revocationState: Indicates why a Certificate was revoked.
    revocationTime: The time at which this Certificate was revoked.
  """

  class RevocationStateValueValuesEnum(_messages.Enum):
    r"""Indicates why a Certificate was revoked.

    Values:
      REVOCATION_REASON_UNSPECIFIED: Default unspecified value. This value
        does indicate that a Certificate has been revoked, but that a reason
        has not been recorded.
      KEY_COMPROMISE: Key material for this Certificate may have leaked.
      CERTIFICATE_AUTHORITY_COMPROMISE: The key material for a certificate
        authority in the issuing path may have leaked.
      AFFILIATION_CHANGED: The subject or other attributes in this Certificate
        have changed.
      SUPERSEDED: This Certificate has been superseded.
      CESSATION_OF_OPERATION: This Certificate or entities in the issuing path
        have ceased to operate.
      CERTIFICATE_HOLD: This Certificate should not be considered valid, it is
        expected that it may become valid in the future.
      PRIVILEGE_WITHDRAWN: This Certificate no longer has permission to assert
        the listed attributes.
      ATTRIBUTE_AUTHORITY_COMPROMISE: The authority which determines
        appropriate attributes for a Certificate may have been compromised.
    """
    REVOCATION_REASON_UNSPECIFIED = 0
    KEY_COMPROMISE = 1
    CERTIFICATE_AUTHORITY_COMPROMISE = 2
    AFFILIATION_CHANGED = 3
    SUPERSEDED = 4
    CESSATION_OF_OPERATION = 5
    CERTIFICATE_HOLD = 6
    PRIVILEGE_WITHDRAWN = 7
    ATTRIBUTE_AUTHORITY_COMPROMISE = 8

  revocationState = _messages.EnumField('RevocationStateValueValuesEnum', 1)
  revocationTime = _messages.StringField(2)


class RevokeCertificateRequest(_messages.Message):
  r"""Request message for CertificateAuthorityService.RevokeCertificate.

  Enums:
    ReasonValueValuesEnum: Required. The RevocationReason for revoking this
      certificate.

  Fields:
    reason: Required. The RevocationReason for revoking this certificate.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  class ReasonValueValuesEnum(_messages.Enum):
    r"""Required. The RevocationReason for revoking this certificate.

    Values:
      REVOCATION_REASON_UNSPECIFIED: Default unspecified value. This value
        does indicate that a Certificate has been revoked, but that a reason
        has not been recorded.
      KEY_COMPROMISE: Key material for this Certificate may have leaked.
      CERTIFICATE_AUTHORITY_COMPROMISE: The key material for a certificate
        authority in the issuing path may have leaked.
      AFFILIATION_CHANGED: The subject or other attributes in this Certificate
        have changed.
      SUPERSEDED: This Certificate has been superseded.
      CESSATION_OF_OPERATION: This Certificate or entities in the issuing path
        have ceased to operate.
      CERTIFICATE_HOLD: This Certificate should not be considered valid, it is
        expected that it may become valid in the future.
      PRIVILEGE_WITHDRAWN: This Certificate no longer has permission to assert
        the listed attributes.
      ATTRIBUTE_AUTHORITY_COMPROMISE: The authority which determines
        appropriate attributes for a Certificate may have been compromised.
    """
    REVOCATION_REASON_UNSPECIFIED = 0
    KEY_COMPROMISE = 1
    CERTIFICATE_AUTHORITY_COMPROMISE = 2
    AFFILIATION_CHANGED = 3
    SUPERSEDED = 4
    CESSATION_OF_OPERATION = 5
    CERTIFICATE_HOLD = 6
    PRIVILEGE_WITHDRAWN = 7
    ATTRIBUTE_AUTHORITY_COMPROMISE = 8

  reason = _messages.EnumField('ReasonValueValuesEnum', 1)
  requestId = _messages.StringField(2)


class RevokedCertificate(_messages.Message):
  r"""Describes a revoked Certificate.

  Enums:
    RevocationReasonValueValuesEnum: The reason the Certificate was revoked.

  Fields:
    certificate: The resource path for the Certificate in the format
      `projects/*/locations/*/certificateAuthorities/*/certificates/*`.
    hexSerialNumber: The serial number of the Certificate.
    revocationReason: The reason the Certificate was revoked.
  """

  class RevocationReasonValueValuesEnum(_messages.Enum):
    r"""The reason the Certificate was revoked.

    Values:
      REVOCATION_REASON_UNSPECIFIED: Default unspecified value. This value
        does indicate that a Certificate has been revoked, but that a reason
        has not been recorded.
      KEY_COMPROMISE: Key material for this Certificate may have leaked.
      CERTIFICATE_AUTHORITY_COMPROMISE: The key material for a certificate
        authority in the issuing path may have leaked.
      AFFILIATION_CHANGED: The subject or other attributes in this Certificate
        have changed.
      SUPERSEDED: This Certificate has been superseded.
      CESSATION_OF_OPERATION: This Certificate or entities in the issuing path
        have ceased to operate.
      CERTIFICATE_HOLD: This Certificate should not be considered valid, it is
        expected that it may become valid in the future.
      PRIVILEGE_WITHDRAWN: This Certificate no longer has permission to assert
        the listed attributes.
      ATTRIBUTE_AUTHORITY_COMPROMISE: The authority which determines
        appropriate attributes for a Certificate may have been compromised.
    """
    REVOCATION_REASON_UNSPECIFIED = 0
    KEY_COMPROMISE = 1
    CERTIFICATE_AUTHORITY_COMPROMISE = 2
    AFFILIATION_CHANGED = 3
    SUPERSEDED = 4
    CESSATION_OF_OPERATION = 5
    CERTIFICATE_HOLD = 6
    PRIVILEGE_WITHDRAWN = 7
    ATTRIBUTE_AUTHORITY_COMPROMISE = 8

  certificate = _messages.StringField(1)
  hexSerialNumber = _messages.StringField(2)
  revocationReason = _messages.EnumField('RevocationReasonValueValuesEnum', 3)


class ScheduleDeleteCertificateAuthorityRequest(_messages.Message):
  r"""Request message for
  CertificateAuthorityService.ScheduleDeleteCertificateAuthority.

  Fields:
    deletePendingDuration: Optional. For PRIVATECA_INTERNAL callers only. Set
      a duration for scheduled deletion. This allows internal callers to
      override the default 30 day scheduled deletion time for faster deletion.
      A duration greater than the default 30 days is prohibited.
    ignoreActiveCertificates: Optional. This field allows the CA to be
      scheduled for deletion even if the CA has active certs. Active certs
      include both unrevoked and unexpired certs.
    requestId: Optional. An ID to identify requests. Specify a unique request
      ID so that if you must retry your request, the server will know to
      ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  deletePendingDuration = _messages.StringField(1)
  ignoreActiveCertificates = _messages.BooleanField(2)
  requestId = _messages.StringField(3)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Subject(_messages.Message):
  r"""Subject describes parts of a distinguished name that, in turn, describes
  the subject of the certificate.

  Fields:
    countryCode: The country code of the subject.
    locality: The locality or city of the subject.
    organization: The organization of the subject.
    organizationalUnit: The organizational_unit of the subject.
    postalCode: The postal code of the subject.
    province: The province, territory, or regional state of the subject.
    streetAddress: The street address of the subject.
  """

  countryCode = _messages.StringField(1)
  locality = _messages.StringField(2)
  organization = _messages.StringField(3)
  organizationalUnit = _messages.StringField(4)
  postalCode = _messages.StringField(5)
  province = _messages.StringField(6)
  streetAddress = _messages.StringField(7)


class SubjectAltNames(_messages.Message):
  r"""SubjectAltNames corresponds to a more modern way of listing what the
  asserted identity is in a certificate (i.e., compared to the "common name"
  in the distinguished name).

  Fields:
    customSans: Contains additional subject alternative name values.
    dnsNames: Contains only valid, fully-qualified host names.
    emailAddresses: Contains only valid RFC 2822 E-mail addresses.
    ipAddresses: Contains only valid 32-bit IPv4 addresses or RFC 4291 IPv6
      addresses.
    uris: Contains only valid RFC 3986 URIs.
  """

  customSans = _messages.MessageField('X509Extension', 1, repeated=True)
  dnsNames = _messages.StringField(2, repeated=True)
  emailAddresses = _messages.StringField(3, repeated=True)
  ipAddresses = _messages.StringField(4, repeated=True)
  uris = _messages.StringField(5, repeated=True)


class SubjectConfig(_messages.Message):
  r"""These values are used to create the distinguished name and subject
  alternative name fields in an X.509 certificate.

  Fields:
    commonName: Optional. The "common name" of the distinguished name.
    subject: Required. Contains distinguished name fields such as the location
      and organization.
    subjectAltName: Optional. The subject alternative name fields.
  """

  commonName = _messages.StringField(1)
  subject = _messages.MessageField('Subject', 2)
  subjectAltName = _messages.MessageField('SubjectAltNames', 3)


class SubjectDescription(_messages.Message):
  r"""These values describe fields in an issued X.509 certificate such as the
  distinguished name, subject alternative names, serial number, and lifetime.

  Fields:
    commonName: The "common name" of the distinguished name.
    hexSerialNumber: The serial number encoded in lowercase hexadecimal.
    lifetime: For convenience, the actual lifetime of an issued certificate.
      Corresponds to 'not_after_time' - 'not_before_time'.
    notAfterTime: The time at which the certificate expires.
    notBeforeTime: The time at which the certificate becomes valid.
    subject: Contains distinguished name fields such as the location and
      organization.
    subjectAltName: The subject alternative name fields.
  """

  commonName = _messages.StringField(1)
  hexSerialNumber = _messages.StringField(2)
  lifetime = _messages.StringField(3)
  notAfterTime = _messages.StringField(4)
  notBeforeTime = _messages.StringField(5)
  subject = _messages.MessageField('Subject', 6)
  subjectAltName = _messages.MessageField('SubjectAltNames', 7)


class SubordinateConfig(_messages.Message):
  r"""Describes a subordinate CA's issuers. This is either a resource path to
  a known issuing CertificateAuthority, or a PEM issuer certificate chain.

  Fields:
    certificateAuthority: Required. This can refer to a CertificateAuthority
      that was used to create a subordinate CertificateAuthority. This field
      is used for information and usability purposes only. The resource name
      is in the format `projects/*/locations/*/certificateAuthorities/*`.
    pemIssuerChain: Required. Contains the PEM certificate chain for the
      issuers of this CertificateAuthority, but not pem certificate for this
      CA itself.
  """

  certificateAuthority = _messages.StringField(1)
  pemIssuerChain = _messages.MessageField('SubordinateConfigChain', 2)


class SubordinateConfigChain(_messages.Message):
  r"""This message describes a subordinate CA's issuer certificate chain. This
  wrapper exists for compatibility reasons.

  Fields:
    pemCertificates: Required. Expected to be in leaf-to-root order according
      to RFC 5246.
  """

  pemCertificates = _messages.StringField(1, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class X509Extension(_messages.Message):
  r"""An X509Extension specifies an X.509 extension, which may be used in
  different parts of X.509 objects like certificates, CSRs, and CRLs.

  Fields:
    critical: Required. Indicates whether or not this extension is critical
      (i.e., if the client does not know how to handle this extension, the
      client should consider this to be an error).
    objectId: Required. The OID for this X.509 extension.
    value: Required. The value of this X.509 extension.
  """

  critical = _messages.BooleanField(1)
  objectId = _messages.MessageField('ObjectId', 2)
  value = _messages.BytesField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

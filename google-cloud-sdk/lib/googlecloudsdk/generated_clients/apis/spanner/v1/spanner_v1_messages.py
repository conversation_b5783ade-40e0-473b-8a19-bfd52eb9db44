"""Generated message classes for spanner version v1.

Cloud Spanner is a managed, mission-critical, globally consistent and scalable
relational database service.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'spanner'


class Backup(_messages.Message):
  r"""A backup of a Cloud Spanner database.

  Enums:
    DatabaseDialectValueValuesEnum: Output only. The database dialect
      information for the backup.
    StateValueValuesEnum: Output only. The current state of the backup.

  Fields:
    createTime: Output only. The time the CreateBackup request is received. If
      the request does not specify `version_time`, the `version_time` of the
      backup will be equivalent to the `create_time`.
    database: Required for the CreateBackup operation. Name of the database
      from which this backup was created. This needs to be in the same
      instance as the backup. Values are of the form
      `projects//instances//databases/`.
    databaseDialect: Output only. The database dialect information for the
      backup.
    encryptionInfo: Output only. The encryption information for the backup.
    expireTime: Required for the CreateBackup operation. The expiration time
      of the backup, with microseconds granularity that must be at least 6
      hours and at most 366 days from the time the CreateBackup request is
      processed. Once the `expire_time` has passed, the backup is eligible to
      be automatically deleted by Cloud Spanner to free the resources used by
      the backup.
    maxExpireTime: Output only. The max allowed expiration time of the backup,
      with microseconds granularity. A backup's expiration time can be
      configured in multiple APIs: CreateBackup, UpdateBackup, CopyBackup.
      When updating or copying an existing backup, the expiration time
      specified must be less than `Backup.max_expire_time`.
    name: Output only for the CreateBackup operation. Required for the
      UpdateBackup operation. A globally unique identifier for the backup
      which cannot be changed. Values are of the form
      `projects//instances//backups/a-z*[a-z0-9]` The final segment of the
      name must be between 2 and 60 characters in length. The backup is stored
      in the location(s) specified in the instance configuration of the
      instance containing the backup, identified by the prefix of the backup
      name of the form `projects//instances/`.
    referencingBackups: Output only. The names of the destination backups
      being created by copying this source backup. The backup names are of the
      form `projects//instances//backups/`. Referencing backups may exist in
      different instances. The existence of any referencing backup prevents
      the backup from being deleted. When the copy operation is done (either
      successfully completed or cancelled or the destination backup is
      deleted), the reference to the backup is removed.
    referencingDatabases: Output only. The names of the restored databases
      that reference the backup. The database names are of the form
      `projects//instances//databases/`. Referencing databases may exist in
      different instances. The existence of any referencing database prevents
      the backup from being deleted. When a restored database from the backup
      enters the `READY` state, the reference to the backup is removed.
    sizeBytes: Output only. Size of the backup in bytes.
    state: Output only. The current state of the backup.
    versionTime: The backup will contain an externally consistent copy of the
      database at the timestamp specified by `version_time`. If `version_time`
      is not specified, the system will set `version_time` to the
      `create_time` of the backup.
  """

  class DatabaseDialectValueValuesEnum(_messages.Enum):
    r"""Output only. The database dialect information for the backup.

    Values:
      DATABASE_DIALECT_UNSPECIFIED: Default value. This value will create a
        database with the GOOGLE_STANDARD_SQL dialect.
      GOOGLE_STANDARD_SQL: GoogleSQL supported SQL.
      POSTGRESQL: PostgreSQL supported SQL.
    """
    DATABASE_DIALECT_UNSPECIFIED = 0
    GOOGLE_STANDARD_SQL = 1
    POSTGRESQL = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the backup.

    Values:
      STATE_UNSPECIFIED: Not specified.
      CREATING: The pending backup is still being created. Operations on the
        backup may fail with `FAILED_PRECONDITION` in this state.
      READY: The backup is complete and ready for use.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2

  createTime = _messages.StringField(1)
  database = _messages.StringField(2)
  databaseDialect = _messages.EnumField('DatabaseDialectValueValuesEnum', 3)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 4)
  expireTime = _messages.StringField(5)
  maxExpireTime = _messages.StringField(6)
  name = _messages.StringField(7)
  referencingBackups = _messages.StringField(8, repeated=True)
  referencingDatabases = _messages.StringField(9, repeated=True)
  sizeBytes = _messages.IntegerField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  versionTime = _messages.StringField(12)


class BackupInfo(_messages.Message):
  r"""Information about a backup.

  Fields:
    backup: Name of the backup.
    createTime: The time the CreateBackup request was received.
    sourceDatabase: Name of the database the backup was created from.
    versionTime: The backup contains an externally consistent copy of
      `source_database` at the timestamp specified by `version_time`. If the
      CreateBackup request did not specify `version_time`, the `version_time`
      of the backup is equivalent to the `create_time`.
  """

  backup = _messages.StringField(1)
  createTime = _messages.StringField(2)
  sourceDatabase = _messages.StringField(3)
  versionTime = _messages.StringField(4)


class BatchCreateSessionsRequest(_messages.Message):
  r"""The request for BatchCreateSessions.

  Fields:
    sessionCount: Required. The number of sessions to be created in this batch
      call. The API may return fewer than the requested number of sessions. If
      a specific number of sessions are desired, the client can make
      additional calls to BatchCreateSessions (adjusting session_count as
      necessary).
    sessionTemplate: Parameters to be applied to each created session.
  """

  sessionCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  sessionTemplate = _messages.MessageField('Session', 2)


class BatchCreateSessionsResponse(_messages.Message):
  r"""The response for BatchCreateSessions.

  Fields:
    session: The freshly created sessions.
  """

  session = _messages.MessageField('Session', 1, repeated=True)


class BeginTransactionRequest(_messages.Message):
  r"""The request for BeginTransaction.

  Fields:
    options: Required. Options for the new transaction.
    requestOptions: Common options for this request. Priority is ignored for
      this request. Setting the priority in this request_options struct will
      not do anything. To set the priority for a transaction, set it on the
      reads and writes that are part of this transaction instead.
  """

  options = _messages.MessageField('TransactionOptions', 1)
  requestOptions = _messages.MessageField('RequestOptions', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class ChildLink(_messages.Message):
  r"""Metadata associated with a parent-child relationship appearing in a
  PlanNode.

  Fields:
    childIndex: The node to which the link points.
    type: The type of the link. For example, in Hash Joins this could be used
      to distinguish between the build child and the probe child, or in the
      case of the child being an output variable, to represent the tag
      associated with the output variable.
    variable: Only present if the child node is SCALAR and corresponds to an
      output variable of the parent node. The field carries the name of the
      output variable. For example, a `TableScan` operator that reads rows
      from a table will have child links to the `SCALAR` nodes representing
      the output variables created for each column that is read by the
      operator. The corresponding `variable` fields will be set to the
      variable names assigned to the columns.
  """

  childIndex = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  type = _messages.StringField(2)
  variable = _messages.StringField(3)


class CommitRequest(_messages.Message):
  r"""The request for Commit.

  Fields:
    mutations: The mutations to be executed when this transaction commits. All
      mutations are applied atomically, in the order they appear in this list.
    requestOptions: Common options for this request.
    returnCommitStats: If `true`, then statistics related to the transaction
      will be included in the CommitResponse. Default value is `false`.
    singleUseTransaction: Execute mutations in a temporary transaction. Note
      that unlike commit of a previously-started transaction, commit with a
      temporary transaction is non-idempotent. That is, if the `CommitRequest`
      is sent to Cloud Spanner more than once (for instance, due to retries in
      the application, or in the transport library), it is possible that the
      mutations are executed more than once. If this is undesirable, use
      BeginTransaction and Commit instead.
    transactionId: Commit a previously-started transaction.
  """

  mutations = _messages.MessageField('Mutation', 1, repeated=True)
  requestOptions = _messages.MessageField('RequestOptions', 2)
  returnCommitStats = _messages.BooleanField(3)
  singleUseTransaction = _messages.MessageField('TransactionOptions', 4)
  transactionId = _messages.BytesField(5)


class CommitResponse(_messages.Message):
  r"""The response for Commit.

  Fields:
    commitStats: The statistics about this Commit. Not returned by default.
      For more information, see CommitRequest.return_commit_stats.
    commitTimestamp: The Cloud Spanner timestamp at which the transaction
      committed.
  """

  commitStats = _messages.MessageField('CommitStats', 1)
  commitTimestamp = _messages.StringField(2)


class CommitStats(_messages.Message):
  r"""Additional statistics about a commit.

  Fields:
    mutationCount: The total number of mutations for the transaction. Knowing
      the `mutation_count` value can help you maximize the number of mutations
      in a transaction and minimize the number of API round trips. You can
      also monitor this value to prevent transactions from exceeding the
      system [limit](https://cloud.google.com/spanner/quotas#limits_for_creati
      ng_reading_updating_and_deleting_data). If the number of mutations
      exceeds the limit, the server returns [INVALID_ARGUMENT](https://cloud.g
      oogle.com/spanner/docs/reference/rest/v1/Code#ENUM_VALUES.INVALID_ARGUME
      NT).
  """

  mutationCount = _messages.IntegerField(1)


class ContextValue(_messages.Message):
  r"""A message representing context for a KeyRangeInfo, including a label,
  value, unit, and severity.

  Enums:
    SeverityValueValuesEnum: The severity of this context.

  Fields:
    label: The label for the context value. e.g. "latency".
    severity: The severity of this context.
    unit: The unit of the context value.
    value: The value for the context.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of this context.

    Values:
      SEVERITY_UNSPECIFIED: Required default value.
      INFO: Lowest severity level "Info".
      WARNING: Middle severity level "Warning".
      ERROR: Severity level signaling an error "Error"
      FATAL: Severity level signaling a non recoverable error "Fatal"
    """
    SEVERITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ERROR = 3
    FATAL = 4

  label = _messages.MessageField('LocalizedString', 1)
  severity = _messages.EnumField('SeverityValueValuesEnum', 2)
  unit = _messages.StringField(3)
  value = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class CopyBackupEncryptionConfig(_messages.Message):
  r"""Encryption configuration for the copied backup.

  Enums:
    EncryptionTypeValueValuesEnum: Required. The encryption type of the
      backup.

  Fields:
    encryptionType: Required. The encryption type of the backup.
    kmsKeyName: Optional. The Cloud KMS key that will be used to protect the
      backup. This field should be set only when encryption_type is
      `CUSTOMER_MANAGED_ENCRYPTION`. Values are of the form
      `projects//locations//keyRings//cryptoKeys/`.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Required. The encryption type of the backup.

    Values:
      ENCRYPTION_TYPE_UNSPECIFIED: Unspecified. Do not use.
      USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION: This is the default option for
        CopyBackup when encryption_config is not specified. For example, if
        the source backup is using `Customer_Managed_Encryption`, the backup
        will be using the same Cloud KMS key as the source backup.
      GOOGLE_DEFAULT_ENCRYPTION: Use Google default encryption.
      CUSTOMER_MANAGED_ENCRYPTION: Use customer managed encryption. If
        specified, either `kms_key_name` or `kms_key_names` must contain valid
        Cloud KMS key(s).
    """
    ENCRYPTION_TYPE_UNSPECIFIED = 0
    USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION = 1
    GOOGLE_DEFAULT_ENCRYPTION = 2
    CUSTOMER_MANAGED_ENCRYPTION = 3

  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 1)
  kmsKeyName = _messages.StringField(2)


class CopyBackupMetadata(_messages.Message):
  r"""Metadata type for the operation returned by CopyBackup.

  Fields:
    cancelTime: The time at which cancellation of CopyBackup operation was
      received. Operations.CancelOperation starts asynchronous cancellation on
      a long-running operation. The server makes a best effort to cancel the
      operation, but success is not guaranteed. Clients can use
      Operations.GetOperation or other methods to check whether the
      cancellation succeeded or whether the operation completed despite
      cancellation. On successful cancellation, the operation is not deleted;
      instead, it becomes an operation with an Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    name: The name of the backup being created through the copy operation.
      Values are of the form `projects//instances//backups/`.
    progress: The progress of the CopyBackup operation.
    sourceBackup: The name of the source backup that is being copied. Values
      are of the form `projects//instances//backups/`.
  """

  cancelTime = _messages.StringField(1)
  name = _messages.StringField(2)
  progress = _messages.MessageField('OperationProgress', 3)
  sourceBackup = _messages.StringField(4)


class CopyBackupRequest(_messages.Message):
  r"""The request for CopyBackup.

  Fields:
    backupId: Required. The id of the backup copy. The `backup_id` appended to
      `parent` forms the full backup_uri of the form
      `projects//instances//backups/`.
    encryptionConfig: Optional. The encryption configuration used to encrypt
      the backup. If this field is not specified, the backup will use the same
      encryption configuration as the source backup by default, namely
      encryption_type = `USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION`.
    expireTime: Required. The expiration time of the backup in microsecond
      granularity. The expiration time must be at least 6 hours and at most
      366 days from the `create_time` of the source backup. Once the
      `expire_time` has passed, the backup is eligible to be automatically
      deleted by Cloud Spanner to free the resources used by the backup.
    sourceBackup: Required. The source backup to be copied. The source backup
      needs to be in READY state for it to be copied. Once CopyBackup is in
      progress, the source backup cannot be deleted or cleaned up on
      expiration until CopyBackup is finished. Values are of the form:
      `projects//instances//backups/`.
  """

  backupId = _messages.StringField(1)
  encryptionConfig = _messages.MessageField('CopyBackupEncryptionConfig', 2)
  expireTime = _messages.StringField(3)
  sourceBackup = _messages.StringField(4)


class CreateBackupMetadata(_messages.Message):
  r"""Metadata type for the operation returned by CreateBackup.

  Fields:
    cancelTime: The time at which cancellation of this operation was received.
      Operations.CancelOperation starts asynchronous cancellation on a long-
      running operation. The server makes a best effort to cancel the
      operation, but success is not guaranteed. Clients can use
      Operations.GetOperation or other methods to check whether the
      cancellation succeeded or whether the operation completed despite
      cancellation. On successful cancellation, the operation is not deleted;
      instead, it becomes an operation with an Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    database: The name of the database the backup is created from.
    name: The name of the backup being created.
    progress: The progress of the CreateBackup operation.
  """

  cancelTime = _messages.StringField(1)
  database = _messages.StringField(2)
  name = _messages.StringField(3)
  progress = _messages.MessageField('OperationProgress', 4)


class CreateDatabaseMetadata(_messages.Message):
  r"""Metadata type for the operation returned by CreateDatabase.

  Fields:
    database: The database being created.
  """

  database = _messages.StringField(1)


class CreateDatabaseRequest(_messages.Message):
  r"""The request for CreateDatabase.

  Enums:
    DatabaseDialectValueValuesEnum: Optional. The dialect of the Cloud Spanner
      Database.

  Fields:
    createStatement: Required. A `CREATE DATABASE` statement, which specifies
      the ID of the new database. The database ID must conform to the regular
      expression `a-z*[a-z0-9]` and be between 2 and 30 characters in length.
      If the database ID is a reserved word or if it contains a hyphen, the
      database ID must be enclosed in backticks (`` ` ``).
    databaseDialect: Optional. The dialect of the Cloud Spanner Database.
    encryptionConfig: Optional. The encryption configuration for the database.
      If this field is not specified, Cloud Spanner will encrypt/decrypt all
      data at rest using Google default encryption.
    extraStatements: Optional. A list of DDL statements to run inside the
      newly created database. Statements can create tables, indexes, etc.
      These statements execute atomically with the creation of the database:
      if there is an error in any statement, the database is not created.
    protoDescriptors: Optional. Proto descriptors used by CREATE/ALTER PROTO
      BUNDLE statements in 'extra_statements' above. Contains a protobuf-
      serialized [google.protobuf.FileDescriptorSet](https://github.com/protoc
      olbuffers/protobuf/blob/main/src/google/protobuf/descriptor.proto). To
      generate it, [install](https://grpc.io/docs/protoc-installation/) and
      run `protoc` with --include_imports and --descriptor_set_out. For
      example, to generate for moon/shot/app.proto, run " " " $protoc
      --proto_path=/app_path --proto_path=/lib_path \ --include_imports \
      --descriptor_set_out=descriptors.data \ moon/shot/app.proto " " " For
      more details, see protobuffer [self
      description](https://developers.google.com/protocol-
      buffers/docs/techniques#self-description).
  """

  class DatabaseDialectValueValuesEnum(_messages.Enum):
    r"""Optional. The dialect of the Cloud Spanner Database.

    Values:
      DATABASE_DIALECT_UNSPECIFIED: Default value. This value will create a
        database with the GOOGLE_STANDARD_SQL dialect.
      GOOGLE_STANDARD_SQL: GoogleSQL supported SQL.
      POSTGRESQL: PostgreSQL supported SQL.
    """
    DATABASE_DIALECT_UNSPECIFIED = 0
    GOOGLE_STANDARD_SQL = 1
    POSTGRESQL = 2

  createStatement = _messages.StringField(1)
  databaseDialect = _messages.EnumField('DatabaseDialectValueValuesEnum', 2)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 3)
  extraStatements = _messages.StringField(4, repeated=True)
  protoDescriptors = _messages.BytesField(5)


class CreateInstanceConfigMetadata(_messages.Message):
  r"""Metadata type for the operation returned by CreateInstanceConfig.

  Fields:
    cancelTime: The time at which this operation was cancelled.
    instanceConfig: The target instance config end state.
    progress: The progress of the CreateInstanceConfig operation.
  """

  cancelTime = _messages.StringField(1)
  instanceConfig = _messages.MessageField('InstanceConfig', 2)
  progress = _messages.MessageField('InstanceOperationProgress', 3)


class CreateInstanceConfigRequest(_messages.Message):
  r"""The request for CreateInstanceConfigRequest.

  Fields:
    instanceConfig: Required. The InstanceConfig proto of the configuration to
      create. instance_config.name must be `/instanceConfigs/`.
      instance_config.base_config must be a Google managed configuration name,
      e.g. /instanceConfigs/us-east1, /instanceConfigs/nam3.
    instanceConfigId: Required. The ID of the instance config to create. Valid
      identifiers are of the form `custom-[-a-z0-9]*[a-z0-9]` and must be
      between 2 and 64 characters in length. The `custom-` prefix is required
      to avoid name conflicts with Google managed configurations.
    validateOnly: An option to validate, but not actually execute, a request,
      and provide the same response.
  """

  instanceConfig = _messages.MessageField('InstanceConfig', 1)
  instanceConfigId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class CreateInstanceMetadata(_messages.Message):
  r"""Metadata type for the operation returned by CreateInstance.

  Fields:
    cancelTime: The time at which this operation was cancelled. If set, this
      operation is in the process of undoing itself (which is guaranteed to
      succeed) and cannot be cancelled again.
    endTime: The time at which this operation failed or was completed
      successfully.
    instance: The instance being created.
    startTime: The time at which the CreateInstance request was received.
  """

  cancelTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  instance = _messages.MessageField('Instance', 3)
  startTime = _messages.StringField(4)


class CreateInstancePartitionMetadata(_messages.Message):
  r"""Metadata type for the operation returned by CreateInstancePartition.

  Fields:
    cancelTime: The time at which this operation was cancelled. If set, this
      operation is in the process of undoing itself (which is guaranteed to
      succeed) and cannot be cancelled again.
    endTime: The time at which this operation failed or was completed
      successfully.
    instancePartition: The instance partition being created.
    startTime: The time at which the CreateInstancePartition request was
      received.
  """

  cancelTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  instancePartition = _messages.MessageField('InstancePartition', 3)
  startTime = _messages.StringField(4)


class CreateInstancePartitionRequest(_messages.Message):
  r"""The request for CreateInstancePartition.

  Fields:
    instancePartition: Required. The instance partition to create. The
      instance_partition.name may be omitted, but if specified must be
      `/instancePartitions/`.
    instancePartitionId: Required. The ID of the instance partition to create.
      Valid identifiers are of the form `a-z*[a-z0-9]` and must be between 2
      and 64 characters in length.
  """

  instancePartition = _messages.MessageField('InstancePartition', 1)
  instancePartitionId = _messages.StringField(2)


class CreateInstanceRequest(_messages.Message):
  r"""The request for CreateInstance.

  Fields:
    instance: Required. The instance to create. The name may be omitted, but
      if specified must be `/instances/`.
    instanceId: Required. The ID of the instance to create. Valid identifiers
      are of the form `a-z*[a-z0-9]` and must be between 2 and 64 characters
      in length.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)


class CreateSessionRequest(_messages.Message):
  r"""The request for CreateSession.

  Fields:
    session: Required. The session to create.
  """

  session = _messages.MessageField('Session', 1)


class Database(_messages.Message):
  r"""A Cloud Spanner database.

  Enums:
    DatabaseDialectValueValuesEnum: Output only. The dialect of the Cloud
      Spanner Database.
    StateValueValuesEnum: Output only. The current database state.

  Fields:
    createTime: Output only. If exists, the time at which the database
      creation started.
    databaseDialect: Output only. The dialect of the Cloud Spanner Database.
    defaultLeader: Output only. The read-write region which contains the
      database's leader replicas. This is the same as the value of
      default_leader database option set using DatabaseAdmin.CreateDatabase or
      DatabaseAdmin.UpdateDatabaseDdl. If not explicitly set, this is empty.
    earliestVersionTime: Output only. Earliest timestamp at which older
      versions of the data can be read. This value is continuously updated by
      Cloud Spanner and becomes stale the moment it is queried. If you are
      using this value to recover data, make sure to account for the time from
      the moment when the value is queried to the moment when you initiate the
      recovery.
    enableDropProtection: Whether drop protection is enabled for this
      database. Defaults to false, if not set. For more details, please see
      how to [prevent accidental database
      deletion](https://cloud.google.com/spanner/docs/prevent-database-
      deletion).
    encryptionConfig: Output only. For databases that are using customer
      managed encryption, this field contains the encryption configuration for
      the database. For databases that are using Google default or other types
      of encryption, this field is empty.
    encryptionInfo: Output only. For databases that are using customer managed
      encryption, this field contains the encryption information for the
      database, such as all Cloud KMS key versions that are in use. The
      `encryption_status' field inside of each `EncryptionInfo` is not
      populated. For databases that are using Google default or other types of
      encryption, this field is empty. This field is propagated lazily from
      the backend. There might be a delay from when a key version is being
      used and when it appears in this field.
    name: Required. The name of the database. Values are of the form
      `projects//instances//databases/`, where `` is as specified in the
      `CREATE DATABASE` statement. This name can be passed to other API
      methods to identify the database.
    reconciling: Output only. If true, the database is being updated. If
      false, there are no ongoing update operations for the database.
    restoreInfo: Output only. Applicable only for restored databases. Contains
      information about the restore source.
    state: Output only. The current database state.
    versionRetentionPeriod: Output only. The period in which Cloud Spanner
      retains all versions of data for the database. This is the same as the
      value of version_retention_period database option set using
      UpdateDatabaseDdl. Defaults to 1 hour, if not set.
  """

  class DatabaseDialectValueValuesEnum(_messages.Enum):
    r"""Output only. The dialect of the Cloud Spanner Database.

    Values:
      DATABASE_DIALECT_UNSPECIFIED: Default value. This value will create a
        database with the GOOGLE_STANDARD_SQL dialect.
      GOOGLE_STANDARD_SQL: GoogleSQL supported SQL.
      POSTGRESQL: PostgreSQL supported SQL.
    """
    DATABASE_DIALECT_UNSPECIFIED = 0
    GOOGLE_STANDARD_SQL = 1
    POSTGRESQL = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current database state.

    Values:
      STATE_UNSPECIFIED: Not specified.
      CREATING: The database is still being created. Operations on the
        database may fail with `FAILED_PRECONDITION` in this state.
      READY: The database is fully created and ready for use.
      READY_OPTIMIZING: The database is fully created and ready for use, but
        is still being optimized for performance and cannot handle full load.
        In this state, the database still references the backup it was restore
        from, preventing the backup from being deleted. When optimizations are
        complete, the full performance of the database will be restored, and
        the database will transition to `READY` state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    READY_OPTIMIZING = 3

  createTime = _messages.StringField(1)
  databaseDialect = _messages.EnumField('DatabaseDialectValueValuesEnum', 2)
  defaultLeader = _messages.StringField(3)
  earliestVersionTime = _messages.StringField(4)
  enableDropProtection = _messages.BooleanField(5)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 6)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 7, repeated=True)
  name = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  restoreInfo = _messages.MessageField('RestoreInfo', 10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  versionRetentionPeriod = _messages.StringField(12)


class DatabaseRole(_messages.Message):
  r"""A Cloud Spanner database role.

  Fields:
    name: Required. The name of the database role. Values are of the form
      `projects//instances//databases//databaseRoles/` where `` is as
      specified in the `CREATE ROLE` DDL statement.
  """

  name = _messages.StringField(1)


class DdlStatementActionInfo(_messages.Message):
  r"""Action information extracted from a DDL statement. This proto is used to
  display the brief info of the DDL statement for the operation
  UpdateDatabaseDdl.

  Fields:
    action: The action for the DDL statement, e.g. CREATE, ALTER, DROP, GRANT,
      etc. This field is a non-empty string.
    entityNames: The entity name(s) being operated on the DDL statement. E.g.
      1. For statement "CREATE TABLE t1(...)", `entity_names` = ["t1"]. 2. For
      statement "GRANT ROLE r1, r2 ...", `entity_names` = ["r1", "r2"]. 3. For
      statement "ANALYZE", `entity_names` = [].
    entityType: The entity type for the DDL statement, e.g. TABLE, INDEX,
      VIEW, etc. This field can be empty string for some DDL statement, e.g.
      for statement "ANALYZE", `entity_type` = "".
  """

  action = _messages.StringField(1)
  entityNames = _messages.StringField(2, repeated=True)
  entityType = _messages.StringField(3)


class Delete(_messages.Message):
  r"""Arguments to delete operations.

  Fields:
    keySet: Required. The primary keys of the rows within table to delete. The
      primary keys must be specified in the order in which they appear in the
      `PRIMARY KEY()` clause of the table's equivalent DDL statement (the DDL
      statement used to create the table). Delete is idempotent. The
      transaction will succeed even if some or all rows do not exist.
    table: Required. The table whose rows will be deleted.
  """

  keySet = _messages.MessageField('KeySet', 1)
  table = _messages.StringField(2)


class DerivedMetric(_messages.Message):
  r"""A message representing a derived metric.

  Fields:
    denominator: The name of the denominator metric. e.g. "rows".
    numerator: The name of the numerator metric. e.g. "latency".
  """

  denominator = _messages.MessageField('LocalizedString', 1)
  numerator = _messages.MessageField('LocalizedString', 2)


class DiagnosticMessage(_messages.Message):
  r"""A message representing the key visualizer diagnostic messages.

  Enums:
    SeverityValueValuesEnum: The severity of the diagnostic message.

  Fields:
    info: Information about this diagnostic information.
    metric: The metric.
    metricSpecific: Whether this message is specific only for the current
      metric. By default Diagnostics are shown for all metrics, regardless
      which metric is the currently selected metric in the UI. However
      occasionally a metric will generate so many messages that the resulting
      visual clutter becomes overwhelming. In this case setting this to true,
      will show the diagnostic messages for that metric only if it is the
      currently selected metric.
    severity: The severity of the diagnostic message.
    shortMessage: The short message.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the diagnostic message.

    Values:
      SEVERITY_UNSPECIFIED: Required default value.
      INFO: Lowest severity level "Info".
      WARNING: Middle severity level "Warning".
      ERROR: Severity level signaling an error "Error"
      FATAL: Severity level signaling a non recoverable error "Fatal"
    """
    SEVERITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ERROR = 3
    FATAL = 4

  info = _messages.MessageField('LocalizedString', 1)
  metric = _messages.MessageField('LocalizedString', 2)
  metricSpecific = _messages.BooleanField(3)
  severity = _messages.EnumField('SeverityValueValuesEnum', 4)
  shortMessage = _messages.MessageField('LocalizedString', 5)


class DirectedReadOptions(_messages.Message):
  r"""The DirectedReadOptions can be used to indicate which replicas or
  regions should be used for non-transactional reads or queries.
  DirectedReadOptions may only be specified for a read-only transaction,
  otherwise the API will return an `INVALID_ARGUMENT` error.

  Fields:
    excludeReplicas: Exclude_replicas indicates that should be excluded from
      serving requests. Spanner will not route requests to the replicas in
      this list.
    includeReplicas: Include_replicas indicates the order of replicas (as they
      appear in this list) to process the request. If auto_failover_disabled
      is set to true and all replicas are exhausted without finding a healthy
      replica, Spanner will wait for a replica in the list to become
      available, requests may fail due to `DEADLINE_EXCEEDED` errors.
  """

  excludeReplicas = _messages.MessageField('ExcludeReplicas', 1)
  includeReplicas = _messages.MessageField('IncludeReplicas', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfig(_messages.Message):
  r"""Encryption configuration for a Cloud Spanner database.

  Fields:
    kmsKeyName: The Cloud KMS key to be used for encrypting and decrypting the
      database. Values are of the form
      `projects//locations//keyRings//cryptoKeys/`.
  """

  kmsKeyName = _messages.StringField(1)


class EncryptionInfo(_messages.Message):
  r"""Encryption information for a Cloud Spanner database or backup.

  Enums:
    EncryptionTypeValueValuesEnum: Output only. The type of encryption.

  Fields:
    encryptionStatus: Output only. If present, the status of a recent
      encrypt/decrypt call on underlying data for this database or backup.
      Regardless of status, data is always encrypted at rest.
    encryptionType: Output only. The type of encryption.
    kmsKeyVersion: Output only. A Cloud KMS key version that is being used to
      protect the database or backup.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of encryption.

    Values:
      TYPE_UNSPECIFIED: Encryption type was not specified, though data at rest
        remains encrypted.
      GOOGLE_DEFAULT_ENCRYPTION: The data is encrypted at rest with a key that
        is fully managed by Google. No key version or status will be
        populated. This is the default state.
      CUSTOMER_MANAGED_ENCRYPTION: The data is encrypted at rest with a key
        that is managed by the customer. The active version of the key.
        `kms_key_version` will be populated, and `encryption_status` may be
        populated.
    """
    TYPE_UNSPECIFIED = 0
    GOOGLE_DEFAULT_ENCRYPTION = 1
    CUSTOMER_MANAGED_ENCRYPTION = 2

  encryptionStatus = _messages.MessageField('Status', 1)
  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 2)
  kmsKeyVersion = _messages.StringField(3)


class ExcludeReplicas(_messages.Message):
  r"""An ExcludeReplicas contains a repeated set of ReplicaSelection that
  should be excluded from serving requests.

  Fields:
    replicaSelections: The directed read replica selector.
  """

  replicaSelections = _messages.MessageField('ReplicaSelection', 1, repeated=True)


class ExecuteBatchDmlRequest(_messages.Message):
  r"""The request for ExecuteBatchDml.

  Fields:
    requestOptions: Common options for this request.
    seqno: Required. A per-transaction sequence number used to identify this
      request. This field makes each request idempotent such that if the
      request is received multiple times, at most one will succeed. The
      sequence number must be monotonically increasing within the transaction.
      If a request arrives for the first time with an out-of-order sequence
      number, the transaction may be aborted. Replays of previously handled
      requests will yield the same response as the first execution.
    statements: Required. The list of statements to execute in this batch.
      Statements are executed serially, such that the effects of statement `i`
      are visible to statement `i+1`. Each statement must be a DML statement.
      Execution stops at the first failed statement; the remaining statements
      are not executed. Callers must provide at least one statement.
    transaction: Required. The transaction to use. Must be a read-write
      transaction. To protect against replays, single-use transactions are not
      supported. The caller must either supply an existing transaction ID or
      begin a new transaction.
  """

  requestOptions = _messages.MessageField('RequestOptions', 1)
  seqno = _messages.IntegerField(2)
  statements = _messages.MessageField('Statement', 3, repeated=True)
  transaction = _messages.MessageField('TransactionSelector', 4)


class ExecuteBatchDmlResponse(_messages.Message):
  r"""The response for ExecuteBatchDml. Contains a list of ResultSet messages,
  one for each DML statement that has successfully executed, in the same order
  as the statements in the request. If a statement fails, the status in the
  response body identifies the cause of the failure. To check for DML
  statements that failed, use the following approach: 1. Check the status in
  the response message. The google.rpc.Code enum value `OK` indicates that all
  statements were executed successfully. 2. If the status was not `OK`, check
  the number of result sets in the response. If the response contains `N`
  ResultSet messages, then statement `N+1` in the request failed. Example 1: *
  Request: 5 DML statements, all executed successfully. * Response: 5
  ResultSet messages, with the status `OK`. Example 2: * Request: 5 DML
  statements. The third statement has a syntax error. * Response: 2 ResultSet
  messages, and a syntax error (`INVALID_ARGUMENT`) status. The number of
  ResultSet messages indicates that the third statement failed, and the fourth
  and fifth statements were not executed.

  Fields:
    resultSets: One ResultSet for each statement in the request that ran
      successfully, in the same order as the statements in the request. Each
      ResultSet does not contain any rows. The ResultSetStats in each
      ResultSet contain the number of rows modified by the statement. Only the
      first ResultSet in the response contains valid ResultSetMetadata.
    status: If all DML statements are executed successfully, the status is
      `OK`. Otherwise, the error status of the first failed statement.
  """

  resultSets = _messages.MessageField('ResultSet', 1, repeated=True)
  status = _messages.MessageField('Status', 2)


class ExecuteSqlRequest(_messages.Message):
  r"""The request for ExecuteSql and ExecuteStreamingSql.

  Enums:
    QueryModeValueValuesEnum: Used to control the amount of debugging
      information returned in ResultSetStats. If partition_token is set,
      query_mode can only be set to QueryMode.NORMAL.

  Messages:
    ParamTypesValue: It is not always possible for Cloud Spanner to infer the
      right SQL type from a JSON value. For example, values of type `BYTES`
      and values of type `STRING` both appear in params as JSON strings. In
      these cases, `param_types` can be used to specify the exact SQL type for
      some or all of the SQL statement parameters. See the definition of Type
      for more information about SQL types.
    ParamsValue: Parameter names and values that bind to placeholders in the
      SQL string. A parameter placeholder consists of the `@` character
      followed by the parameter name (for example, `@firstName`). Parameter
      names must conform to the naming requirements of identifiers as
      specified at https://cloud.google.com/spanner/docs/lexical#identifiers.
      Parameters can appear anywhere that a literal value is expected. The
      same parameter name can be used more than once, for example: `"WHERE id
      > @msg_id AND id < @msg_id + 100"` It is an error to execute a SQL
      statement with unbound parameters.

  Fields:
    dataBoostEnabled: If this is for a partitioned query and this field is set
      to `true`, the request is executed with Spanner Data Boost independent
      compute resources. If the field is set to `true` but the request does
      not set `partition_token`, the API returns an `INVALID_ARGUMENT` error.
    directedReadOptions: Directed read options for this request.
    paramTypes: It is not always possible for Cloud Spanner to infer the right
      SQL type from a JSON value. For example, values of type `BYTES` and
      values of type `STRING` both appear in params as JSON strings. In these
      cases, `param_types` can be used to specify the exact SQL type for some
      or all of the SQL statement parameters. See the definition of Type for
      more information about SQL types.
    params: Parameter names and values that bind to placeholders in the SQL
      string. A parameter placeholder consists of the `@` character followed
      by the parameter name (for example, `@firstName`). Parameter names must
      conform to the naming requirements of identifiers as specified at
      https://cloud.google.com/spanner/docs/lexical#identifiers. Parameters
      can appear anywhere that a literal value is expected. The same parameter
      name can be used more than once, for example: `"WHERE id > @msg_id AND
      id < @msg_id + 100"` It is an error to execute a SQL statement with
      unbound parameters.
    partitionToken: If present, results will be restricted to the specified
      partition previously created using PartitionQuery(). There must be an
      exact match for the values of fields common to this message and the
      PartitionQueryRequest message used to create this partition_token.
    queryMode: Used to control the amount of debugging information returned in
      ResultSetStats. If partition_token is set, query_mode can only be set to
      QueryMode.NORMAL.
    queryOptions: Query optimizer configuration to use for the given query.
    requestOptions: Common options for this request.
    resumeToken: If this request is resuming a previously interrupted SQL
      statement execution, `resume_token` should be copied from the last
      PartialResultSet yielded before the interruption. Doing this enables the
      new SQL statement execution to resume where the last one left off. The
      rest of the request parameters must exactly match the request that
      yielded this token.
    seqno: A per-transaction sequence number used to identify this request.
      This field makes each request idempotent such that if the request is
      received multiple times, at most one will succeed. The sequence number
      must be monotonically increasing within the transaction. If a request
      arrives for the first time with an out-of-order sequence number, the
      transaction may be aborted. Replays of previously handled requests will
      yield the same response as the first execution. Required for DML
      statements. Ignored for queries.
    sql: Required. The SQL string.
    transaction: The transaction to use. For queries, if none is provided, the
      default is a temporary read-only transaction with strong concurrency.
      Standard DML statements require a read-write transaction. To protect
      against replays, single-use transactions are not supported. The caller
      must either supply an existing transaction ID or begin a new
      transaction. Partitioned DML requires an existing Partitioned DML
      transaction ID.
  """

  class QueryModeValueValuesEnum(_messages.Enum):
    r"""Used to control the amount of debugging information returned in
    ResultSetStats. If partition_token is set, query_mode can only be set to
    QueryMode.NORMAL.

    Values:
      NORMAL: The default mode. Only the statement results are returned.
      PLAN: This mode returns only the query plan, without any results or
        execution statistics information.
      PROFILE: This mode returns both the query plan and the execution
        statistics along with the results.
    """
    NORMAL = 0
    PLAN = 1
    PROFILE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamTypesValue(_messages.Message):
    r"""It is not always possible for Cloud Spanner to infer the right SQL
    type from a JSON value. For example, values of type `BYTES` and values of
    type `STRING` both appear in params as JSON strings. In these cases,
    `param_types` can be used to specify the exact SQL type for some or all of
    the SQL statement parameters. See the definition of Type for more
    information about SQL types.

    Messages:
      AdditionalProperty: An additional property for a ParamTypesValue object.

    Fields:
      additionalProperties: Additional properties of type ParamTypesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamTypesValue object.

      Fields:
        key: Name of the additional property.
        value: A Type attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Type', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamsValue(_messages.Message):
    r"""Parameter names and values that bind to placeholders in the SQL
    string. A parameter placeholder consists of the `@` character followed by
    the parameter name (for example, `@firstName`). Parameter names must
    conform to the naming requirements of identifiers as specified at
    https://cloud.google.com/spanner/docs/lexical#identifiers. Parameters can
    appear anywhere that a literal value is expected. The same parameter name
    can be used more than once, for example: `"WHERE id > @msg_id AND id <
    @msg_id + 100"` It is an error to execute a SQL statement with unbound
    parameters.

    Messages:
      AdditionalProperty: An additional property for a ParamsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  dataBoostEnabled = _messages.BooleanField(1)
  directedReadOptions = _messages.MessageField('DirectedReadOptions', 2)
  paramTypes = _messages.MessageField('ParamTypesValue', 3)
  params = _messages.MessageField('ParamsValue', 4)
  partitionToken = _messages.BytesField(5)
  queryMode = _messages.EnumField('QueryModeValueValuesEnum', 6)
  queryOptions = _messages.MessageField('QueryOptions', 7)
  requestOptions = _messages.MessageField('RequestOptions', 8)
  resumeToken = _messages.BytesField(9)
  seqno = _messages.IntegerField(10)
  sql = _messages.StringField(11)
  transaction = _messages.MessageField('TransactionSelector', 12)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Field(_messages.Message):
  r"""Message representing a single field of a struct.

  Fields:
    name: The name of the field. For reads, this is the column name. For SQL
      queries, it is the column alias (e.g., `"Word"` in the query `"SELECT
      'hello' AS Word"`), or the column name (e.g., `"ColName"` in the query
      `"SELECT ColName FROM Table"`). Some columns might have an empty name
      (e.g., `"SELECT UPPER(ColName)"`). Note that a query result can contain
      multiple fields with the same name.
    type: The type of the field.
  """

  name = _messages.StringField(1)
  type = _messages.MessageField('Type', 2)


class FreeInstanceMetadata(_messages.Message):
  r"""Free instance specific metadata that is kept even after an instance has
  been upgraded for tracking purposes.

  Enums:
    ExpireBehaviorValueValuesEnum: Specifies the expiration behavior of a free
      instance. The default of ExpireBehavior is `REMOVE_AFTER_GRACE_PERIOD`.
      This can be modified during or after creation, and before expiration.

  Fields:
    expireBehavior: Specifies the expiration behavior of a free instance. The
      default of ExpireBehavior is `REMOVE_AFTER_GRACE_PERIOD`. This can be
      modified during or after creation, and before expiration.
    expireTime: Output only. Timestamp after which the instance will either be
      upgraded or scheduled for deletion after a grace period. ExpireBehavior
      is used to choose between upgrading or scheduling the free instance for
      deletion. This timestamp is set during the creation of a free instance.
    upgradeTime: Output only. If present, the timestamp at which the free
      instance was upgraded to a provisioned instance.
  """

  class ExpireBehaviorValueValuesEnum(_messages.Enum):
    r"""Specifies the expiration behavior of a free instance. The default of
    ExpireBehavior is `REMOVE_AFTER_GRACE_PERIOD`. This can be modified during
    or after creation, and before expiration.

    Values:
      EXPIRE_BEHAVIOR_UNSPECIFIED: Not specified.
      FREE_TO_PROVISIONED: When the free instance expires, upgrade the
        instance to a provisioned instance.
      REMOVE_AFTER_GRACE_PERIOD: When the free instance expires, disable the
        instance, and delete it after the grace period passes if it has not
        been upgraded.
    """
    EXPIRE_BEHAVIOR_UNSPECIFIED = 0
    FREE_TO_PROVISIONED = 1
    REMOVE_AFTER_GRACE_PERIOD = 2

  expireBehavior = _messages.EnumField('ExpireBehaviorValueValuesEnum', 1)
  expireTime = _messages.StringField(2)
  upgradeTime = _messages.StringField(3)


class GetDatabaseDdlResponse(_messages.Message):
  r"""The response for GetDatabaseDdl.

  Fields:
    protoDescriptors: Proto descriptors stored in the database. Contains a
      protobuf-serialized [google.protobuf.FileDescriptorSet](https://github.c
      om/protocolbuffers/protobuf/blob/main/src/google/protobuf/descriptor.pro
      to). For more details, see protobuffer [self
      description](https://developers.google.com/protocol-
      buffers/docs/techniques#self-description).
    statements: A list of formatted DDL statements defining the schema of the
      database specified in the request.
  """

  protoDescriptors = _messages.BytesField(1)
  statements = _messages.StringField(2, repeated=True)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class IncludeReplicas(_messages.Message):
  r"""An IncludeReplicas contains a repeated set of ReplicaSelection which
  indicates the order in which replicas should be considered.

  Fields:
    autoFailoverDisabled: If true, Spanner will not route requests to a
      replica outside the include_replicas list when all of the specified
      replicas are unavailable or unhealthy. Default value is `false`.
    replicaSelections: The directed read replica selector.
  """

  autoFailoverDisabled = _messages.BooleanField(1)
  replicaSelections = _messages.MessageField('ReplicaSelection', 2, repeated=True)


class IndexedHotKey(_messages.Message):
  r"""A message representing a (sparse) collection of hot keys for specific
  key buckets.

  Messages:
    SparseHotKeysValue: A (sparse) mapping from key bucket index to the index
      of the specific hot row key for that key bucket. The index of the hot
      row key can be translated to the actual row key via the
      ScanData.VisualizationData.indexed_keys repeated field.

  Fields:
    sparseHotKeys: A (sparse) mapping from key bucket index to the index of
      the specific hot row key for that key bucket. The index of the hot row
      key can be translated to the actual row key via the
      ScanData.VisualizationData.indexed_keys repeated field.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SparseHotKeysValue(_messages.Message):
    r"""A (sparse) mapping from key bucket index to the index of the specific
    hot row key for that key bucket. The index of the hot row key can be
    translated to the actual row key via the
    ScanData.VisualizationData.indexed_keys repeated field.

    Messages:
      AdditionalProperty: An additional property for a SparseHotKeysValue
        object.

    Fields:
      additionalProperties: Additional properties of type SparseHotKeysValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SparseHotKeysValue object.

      Fields:
        key: Name of the additional property.
        value: A integer attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2, variant=_messages.Variant.INT32)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  sparseHotKeys = _messages.MessageField('SparseHotKeysValue', 1)


class IndexedKeyRangeInfos(_messages.Message):
  r"""A message representing a (sparse) collection of KeyRangeInfos for
  specific key buckets.

  Messages:
    KeyRangeInfosValue: A (sparse) mapping from key bucket index to the
      KeyRangeInfos for that key bucket.

  Fields:
    keyRangeInfos: A (sparse) mapping from key bucket index to the
      KeyRangeInfos for that key bucket.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class KeyRangeInfosValue(_messages.Message):
    r"""A (sparse) mapping from key bucket index to the KeyRangeInfos for that
    key bucket.

    Messages:
      AdditionalProperty: An additional property for a KeyRangeInfosValue
        object.

    Fields:
      additionalProperties: Additional properties of type KeyRangeInfosValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a KeyRangeInfosValue object.

      Fields:
        key: Name of the additional property.
        value: A KeyRangeInfos attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('KeyRangeInfos', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  keyRangeInfos = _messages.MessageField('KeyRangeInfosValue', 1)


class Instance(_messages.Message):
  r"""An isolated set of Cloud Spanner resources on which databases can be
  hosted.

  Enums:
    DefaultStorageTypeValueValuesEnum: The `StorageType` of the current
      instance. If unspecified, it will default to the first StorageType in
      the list of allowed_storage_types in the InstanceConfig for this
      instance.
    InstanceTypeValueValuesEnum: The `InstanceType` of the current instance.
    StateValueValuesEnum: Output only. The current instance state. For
      CreateInstance, the state must be either omitted or set to `CREATING`.
      For UpdateInstance, the state must be either omitted or set to `READY`.

  Messages:
    LabelsValue: Cloud Labels are a flexible and lightweight mechanism for
      organizing cloud resources into groups that reflect a customer's
      organizational needs and deployment strategies. Cloud Labels can be used
      to filter collections of resources. They can be used to control how
      resource metrics are aggregated. And they can be used as arguments to
      policy management rules (e.g. route, firewall, load balancing, etc.). *
      Label keys must be between 1 and 63 characters long and must conform to
      the following regular expression: `a-z{0,62}`. * Label values must be
      between 0 and 63 characters long and must conform to the regular
      expression `[a-z0-9_-]{0,63}`. * No more than 64 labels can be
      associated with a given resource. See https://goo.gl/xmQnxf for more
      information on and examples of labels. If you plan to use labels in your
      own code, please note that additional characters may be allowed in the
      future. And so you are advised to use an internal label representation,
      such as JSON, which doesn't rely upon specific characters being
      disallowed. For example, representing labels as the string: name + "_" +
      value would prove problematic if we were to allow "_" in a future
      release.

  Fields:
    config: Required. The name of the instance's configuration. Values are of
      the form `projects//instanceConfigs/`. See also InstanceConfig and
      ListInstanceConfigs.
    createTime: Output only. The time at which the instance was created.
    defaultStorageType: The `StorageType` of the current instance. If
      unspecified, it will default to the first StorageType in the list of
      allowed_storage_types in the InstanceConfig for this instance.
    displayName: Required. The descriptive name for this instance as it
      appears in UIs. Must be unique per project and between 4 and 30
      characters in length.
    endpointUris: Deprecated. This field is not populated.
    freeInstanceMetadata: Free instance metadata. Only populated for free
      instances.
    instanceType: The `InstanceType` of the current instance.
    labels: Cloud Labels are a flexible and lightweight mechanism for
      organizing cloud resources into groups that reflect a customer's
      organizational needs and deployment strategies. Cloud Labels can be used
      to filter collections of resources. They can be used to control how
      resource metrics are aggregated. And they can be used as arguments to
      policy management rules (e.g. route, firewall, load balancing, etc.). *
      Label keys must be between 1 and 63 characters long and must conform to
      the following regular expression: `a-z{0,62}`. * Label values must be
      between 0 and 63 characters long and must conform to the regular
      expression `[a-z0-9_-]{0,63}`. * No more than 64 labels can be
      associated with a given resource. See https://goo.gl/xmQnxf for more
      information on and examples of labels. If you plan to use labels in your
      own code, please note that additional characters may be allowed in the
      future. And so you are advised to use an internal label representation,
      such as JSON, which doesn't rely upon specific characters being
      disallowed. For example, representing labels as the string: name + "_" +
      value would prove problematic if we were to allow "_" in a future
      release.
    name: Required. A unique identifier for the instance, which cannot be
      changed after the instance is created. Values are of the form
      `projects//instances/a-z*[a-z0-9]`. The final segment of the name must
      be between 2 and 64 characters in length.
    nodeCount: The number of nodes allocated to this instance. At most one of
      either node_count or processing_units should be present in the message.
      Users can set the node_count field to specify the target number of nodes
      allocated to the instance. This may be zero in API responses for
      instances that are not yet in state `READY`. See [the
      documentation](https://cloud.google.com/spanner/docs/compute-capacity)
      for more information about nodes and processing units.
    processingUnits: The number of processing units allocated to this
      instance. At most one of processing_units or node_count should be
      present in the message. Users can set the processing_units field to
      specify the target number of processing units allocated to the instance.
      This may be zero in API responses for instances that are not yet in
      state `READY`. See [the
      documentation](https://cloud.google.com/spanner/docs/compute-capacity)
      for more information about nodes and processing units.
    state: Output only. The current instance state. For CreateInstance, the
      state must be either omitted or set to `CREATING`. For UpdateInstance,
      the state must be either omitted or set to `READY`.
    updateTime: Output only. The time at which the instance was most recently
      updated.
  """

  class DefaultStorageTypeValueValuesEnum(_messages.Enum):
    r"""The `StorageType` of the current instance. If unspecified, it will
    default to the first StorageType in the list of allowed_storage_types in
    the InstanceConfig for this instance.

    Values:
      STORAGE_TYPE_UNSPECIFIED: Storage type not specified.
      SSD: Flash (SSD) storage should be used.
      HDD: Magnetic drive (HDD) storage should be used.
    """
    STORAGE_TYPE_UNSPECIFIED = 0
    SSD = 1
    HDD = 2

  class InstanceTypeValueValuesEnum(_messages.Enum):
    r"""The `InstanceType` of the current instance.

    Values:
      INSTANCE_TYPE_UNSPECIFIED: Not specified.
      PROVISIONED: Provisioned instances have dedicated resources, standard
        usage limits and support.
      FREE_INSTANCE: Free instances provide no guarantee for dedicated
        resources, [node_count, processing_units] should be 0. They come with
        stricter usage limits and limited support.
    """
    INSTANCE_TYPE_UNSPECIFIED = 0
    PROVISIONED = 1
    FREE_INSTANCE = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current instance state. For CreateInstance, the state
    must be either omitted or set to `CREATING`. For UpdateInstance, the state
    must be either omitted or set to `READY`.

    Values:
      STATE_UNSPECIFIED: Not specified.
      CREATING: The instance is still being created. Resources may not be
        available yet, and operations such as database creation may not work.
      READY: The instance is fully created and ready to do work such as
        creating databases.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cloud Labels are a flexible and lightweight mechanism for organizing
    cloud resources into groups that reflect a customer's organizational needs
    and deployment strategies. Cloud Labels can be used to filter collections
    of resources. They can be used to control how resource metrics are
    aggregated. And they can be used as arguments to policy management rules
    (e.g. route, firewall, load balancing, etc.). * Label keys must be between
    1 and 63 characters long and must conform to the following regular
    expression: `a-z{0,62}`. * Label values must be between 0 and 63
    characters long and must conform to the regular expression
    `[a-z0-9_-]{0,63}`. * No more than 64 labels can be associated with a
    given resource. See https://goo.gl/xmQnxf for more information on and
    examples of labels. If you plan to use labels in your own code, please
    note that additional characters may be allowed in the future. And so you
    are advised to use an internal label representation, such as JSON, which
    doesn't rely upon specific characters being disallowed. For example,
    representing labels as the string: name + "_" + value would prove
    problematic if we were to allow "_" in a future release.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  config = _messages.StringField(1)
  createTime = _messages.StringField(2)
  defaultStorageType = _messages.EnumField('DefaultStorageTypeValueValuesEnum', 3)
  displayName = _messages.StringField(4)
  endpointUris = _messages.StringField(5, repeated=True)
  freeInstanceMetadata = _messages.MessageField('FreeInstanceMetadata', 6)
  instanceType = _messages.EnumField('InstanceTypeValueValuesEnum', 7)
  labels = _messages.MessageField('LabelsValue', 8)
  name = _messages.StringField(9)
  nodeCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  processingUnits = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  updateTime = _messages.StringField(13)


class InstanceConfig(_messages.Message):
  r"""A possible configuration for a Cloud Spanner instance. Configurations
  define the geographic placement of nodes and their replication.

  Enums:
    AllowedStorageTypesValueListEntryValuesEnum:
    ConfigTypeValueValuesEnum: Output only. Whether this instance config is a
      Google or User Managed Configuration.
    FreeInstanceAvailabilityValueValuesEnum: Output only. Describes whether
      free instances are available to be created in this instance config.
    StateValueValuesEnum: Output only. The current instance config state.
      Applicable only for USER_MANAGED configs.

  Messages:
    LabelsValue: Cloud Labels are a flexible and lightweight mechanism for
      organizing cloud resources into groups that reflect a customer's
      organizational needs and deployment strategies. Cloud Labels can be used
      to filter collections of resources. They can be used to control how
      resource metrics are aggregated. And they can be used as arguments to
      policy management rules (e.g. route, firewall, load balancing, etc.). *
      Label keys must be between 1 and 63 characters long and must conform to
      the following regular expression: `a-z{0,62}`. * Label values must be
      between 0 and 63 characters long and must conform to the regular
      expression `[a-z0-9_-]{0,63}`. * No more than 64 labels can be
      associated with a given resource. See https://goo.gl/xmQnxf for more
      information on and examples of labels. If you plan to use labels in your
      own code, please note that additional characters may be allowed in the
      future. Therefore, you are advised to use an internal label
      representation, such as JSON, which doesn't rely upon specific
      characters being disallowed. For example, representing labels as the
      string: name + "_" + value would prove problematic if we were to allow
      "_" in a future release.

  Fields:
    allowedStorageTypes: Output only. The allowed storage types for this
      config. The first storage type will be considered the default storage
      type for any instance that has its default_storage_type field unset or
      set to STORAGE_TYPE_UNSPECIFIED.
    baseConfig: Base configuration name, e.g. projects//instanceConfigs/nam3,
      based on which this configuration is created. Only set for user managed
      configurations. `base_config` must refer to a configuration of type
      GOOGLE_MANAGED in the same project as this configuration.
    configType: Output only. Whether this instance config is a Google or User
      Managed Configuration.
    displayName: The name of this instance configuration as it appears in UIs.
    etag: etag is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a instance config from overwriting each
      other. It is strongly suggested that systems make use of the etag in the
      read-modify-write cycle to perform instance config updates in order to
      avoid race conditions: An etag is returned in the response which
      contains instance configs, and systems are expected to put that etag in
      the request to update instance config to ensure that their change will
      be applied to the same version of the instance config. If no etag is
      provided in the call to update instance config, then the existing
      instance config is overwritten blindly.
    freeInstanceAvailability: Output only. Describes whether free instances
      are available to be created in this instance config.
    labels: Cloud Labels are a flexible and lightweight mechanism for
      organizing cloud resources into groups that reflect a customer's
      organizational needs and deployment strategies. Cloud Labels can be used
      to filter collections of resources. They can be used to control how
      resource metrics are aggregated. And they can be used as arguments to
      policy management rules (e.g. route, firewall, load balancing, etc.). *
      Label keys must be between 1 and 63 characters long and must conform to
      the following regular expression: `a-z{0,62}`. * Label values must be
      between 0 and 63 characters long and must conform to the regular
      expression `[a-z0-9_-]{0,63}`. * No more than 64 labels can be
      associated with a given resource. See https://goo.gl/xmQnxf for more
      information on and examples of labels. If you plan to use labels in your
      own code, please note that additional characters may be allowed in the
      future. Therefore, you are advised to use an internal label
      representation, such as JSON, which doesn't rely upon specific
      characters being disallowed. For example, representing labels as the
      string: name + "_" + value would prove problematic if we were to allow
      "_" in a future release.
    leaderOptions: Allowed values of the "default_leader" schema option for
      databases in instances that use this instance configuration.
    name: A unique identifier for the instance configuration. Values are of
      the form `projects//instanceConfigs/a-z*`.
    optionalReplicas: Output only. The available optional replicas to choose
      from for user managed configurations. Populated for Google managed
      configurations.
    reconciling: Output only. If true, the instance config is being created or
      updated. If false, there are no ongoing operations for the instance
      config.
    replicas: The geographic placement of nodes in this instance configuration
      and their replication properties.
    state: Output only. The current instance config state. Applicable only for
      USER_MANAGED configs.
  """

  class AllowedStorageTypesValueListEntryValuesEnum(_messages.Enum):
    r"""AllowedStorageTypesValueListEntryValuesEnum enum type.

    Values:
      STORAGE_TYPE_UNSPECIFIED: Storage type not specified.
      SSD: Flash (SSD) storage should be used.
      HDD: Magnetic drive (HDD) storage should be used.
    """
    STORAGE_TYPE_UNSPECIFIED = 0
    SSD = 1
    HDD = 2

  class ConfigTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Whether this instance config is a Google or User Managed
    Configuration.

    Values:
      TYPE_UNSPECIFIED: Unspecified.
      GOOGLE_MANAGED: Google managed configuration.
      USER_MANAGED: User managed configuration.
    """
    TYPE_UNSPECIFIED = 0
    GOOGLE_MANAGED = 1
    USER_MANAGED = 2

  class FreeInstanceAvailabilityValueValuesEnum(_messages.Enum):
    r"""Output only. Describes whether free instances are available to be
    created in this instance config.

    Values:
      FREE_INSTANCE_AVAILABILITY_UNSPECIFIED: Not specified.
      AVAILABLE: Indicates that free instances are available to be created in
        this instance config.
      UNSUPPORTED: Indicates that free instances are not supported in this
        instance config.
      DISABLED: Indicates that free instances are currently not available to
        be created in this instance config.
      QUOTA_EXCEEDED: Indicates that additional free instances cannot be
        created in this instance config because the project has reached its
        limit of free instances.
    """
    FREE_INSTANCE_AVAILABILITY_UNSPECIFIED = 0
    AVAILABLE = 1
    UNSUPPORTED = 2
    DISABLED = 3
    QUOTA_EXCEEDED = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current instance config state. Applicable only for
    USER_MANAGED configs.

    Values:
      STATE_UNSPECIFIED: Not specified.
      CREATING: The instance config is still being created.
      READY: The instance config is fully created and ready to be used to
        create instances.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cloud Labels are a flexible and lightweight mechanism for organizing
    cloud resources into groups that reflect a customer's organizational needs
    and deployment strategies. Cloud Labels can be used to filter collections
    of resources. They can be used to control how resource metrics are
    aggregated. And they can be used as arguments to policy management rules
    (e.g. route, firewall, load balancing, etc.). * Label keys must be between
    1 and 63 characters long and must conform to the following regular
    expression: `a-z{0,62}`. * Label values must be between 0 and 63
    characters long and must conform to the regular expression
    `[a-z0-9_-]{0,63}`. * No more than 64 labels can be associated with a
    given resource. See https://goo.gl/xmQnxf for more information on and
    examples of labels. If you plan to use labels in your own code, please
    note that additional characters may be allowed in the future. Therefore,
    you are advised to use an internal label representation, such as JSON,
    which doesn't rely upon specific characters being disallowed. For example,
    representing labels as the string: name + "_" + value would prove
    problematic if we were to allow "_" in a future release.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowedStorageTypes = _messages.EnumField('AllowedStorageTypesValueListEntryValuesEnum', 1, repeated=True)
  baseConfig = _messages.StringField(2)
  configType = _messages.EnumField('ConfigTypeValueValuesEnum', 3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  freeInstanceAvailability = _messages.EnumField('FreeInstanceAvailabilityValueValuesEnum', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  leaderOptions = _messages.StringField(8, repeated=True)
  name = _messages.StringField(9)
  optionalReplicas = _messages.MessageField('ReplicaInfo', 10, repeated=True)
  reconciling = _messages.BooleanField(11)
  replicas = _messages.MessageField('ReplicaInfo', 12, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 13)


class InstanceOperationProgress(_messages.Message):
  r"""Encapsulates progress related information for a Cloud Spanner long
  running instance operations.

  Fields:
    endTime: If set, the time at which this operation failed or was completed
      successfully.
    progressPercent: Percent completion of the operation. Values are between 0
      and 100 inclusive.
    startTime: Time the request was received.
  """

  endTime = _messages.StringField(1)
  progressPercent = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(3)


class InstancePartition(_messages.Message):
  r"""An isolated set of Cloud Spanner resources that databases can define
  placements on.

  Enums:
    StateValueValuesEnum: Output only. The current instance partition state.

  Fields:
    config: Required. The name of the instance partition's configuration.
      Values are of the form `projects//instanceConfigs/`. See also
      InstanceConfig and ListInstanceConfigs.
    createTime: Output only. The time at which the instance partition was
      created.
    displayName: Required. The descriptive name for this instance partition as
      it appears in UIs. Must be unique per project and between 4 and 30
      characters in length.
    etag: Used for optimistic concurrency control as a way to help prevent
      simultaneous updates of a instance partition from overwriting each
      other. It is strongly suggested that systems make use of the etag in the
      read-modify-write cycle to perform instance partition updates in order
      to avoid race conditions: An etag is returned in the response which
      contains instance partitions, and systems are expected to put that etag
      in the request to update instance partitions to ensure that their change
      will be applied to the same version of the instance partition. If no
      etag is provided in the call to update instance partition, then the
      existing instance partition is overwritten blindly.
    name: Required. A unique identifier for the instance partition. Values are
      of the form `projects//instances//instancePartitions/a-z*[a-z0-9]`. The
      final segment of the name must be between 2 and 64 characters in length.
      An instance partition's name cannot be changed after the instance
      partition is created.
    nodeCount: The number of nodes allocated to this instance partition. Users
      can set the node_count field to specify the target number of nodes
      allocated to the instance partition. This may be zero in API responses
      for instance partitions that are not yet in state `READY`.
    processingUnits: The number of processing units allocated to this instance
      partition. Users can set the processing_units field to specify the
      target number of processing units allocated to the instance partition.
      This may be zero in API responses for instance partitions that are not
      yet in state `READY`.
    referencingBackups: Output only. The names of the backups that reference
      this instance partition. Referencing backups should share the parent
      instance. The existence of any referencing backup prevents the instance
      partition from being deleted.
    referencingDatabases: Output only. The names of the databases that
      reference this instance partition. Referencing databases should share
      the parent instance. The existence of any referencing database prevents
      the instance partition from being deleted.
    state: Output only. The current instance partition state.
    updateTime: Output only. The time at which the instance partition was most
      recently updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current instance partition state.

    Values:
      STATE_UNSPECIFIED: Not specified.
      CREATING: The instance partition is still being created. Resources may
        not be available yet, and operations such as creating placements using
        this instance partition may not work.
      READY: The instance partition is fully created and ready to do work such
        as creating placements and using in databases.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2

  config = _messages.StringField(1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  etag = _messages.StringField(4)
  name = _messages.StringField(5)
  nodeCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  processingUnits = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  referencingBackups = _messages.StringField(8, repeated=True)
  referencingDatabases = _messages.StringField(9, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class KeyRange(_messages.Message):
  r"""KeyRange represents a range of rows in a table or index. A range has a
  start key and an end key. These keys can be open or closed, indicating if
  the range includes rows with that key. Keys are represented by lists, where
  the ith value in the list corresponds to the ith component of the table or
  index primary key. Individual values are encoded as described here. For
  example, consider the following table definition: CREATE TABLE UserEvents (
  UserName STRING(MAX), EventDate STRING(10) ) PRIMARY KEY(UserName,
  EventDate); The following keys name rows in this table: "Bob", "2014-09-23"
  Since the `UserEvents` table's `PRIMARY KEY` clause names two columns, each
  `UserEvents` key has two elements; the first is the `UserName`, and the
  second is the `EventDate`. Key ranges with multiple components are
  interpreted lexicographically by component using the table or index key's
  declared sort order. For example, the following range returns all events for
  user `"Bob"` that occurred in the year 2015: "start_closed": ["Bob",
  "2015-01-01"] "end_closed": ["Bob", "2015-12-31"] Start and end keys can
  omit trailing key components. This affects the inclusion and exclusion of
  rows that exactly match the provided key components: if the key is closed,
  then rows that exactly match the provided components are included; if the
  key is open, then rows that exactly match are not included. For example, the
  following range includes all events for `"Bob"` that occurred during and
  after the year 2000: "start_closed": ["Bob", "2000-01-01"] "end_closed":
  ["Bob"] The next example retrieves all events for `"Bob"`: "start_closed":
  ["Bob"] "end_closed": ["Bob"] To retrieve events before the year 2000:
  "start_closed": ["Bob"] "end_open": ["Bob", "2000-01-01"] The following
  range includes all rows in the table: "start_closed": [] "end_closed": []
  This range returns all users whose `UserName` begins with any character from
  A to C: "start_closed": ["A"] "end_open": ["D"] This range returns all users
  whose `UserName` begins with B: "start_closed": ["B"] "end_open": ["C"] Key
  ranges honor column sort order. For example, suppose a table is defined as
  follows: CREATE TABLE DescendingSortedTable { Key INT64, ... ) PRIMARY
  KEY(Key DESC); The following range retrieves all rows with key values
  between 1 and 100 inclusive: "start_closed": ["100"] "end_closed": ["1"]
  Note that 100 is passed as the start, and 1 is passed as the end, because
  `Key` is a descending column in the schema.

  Fields:
    endClosed: If the end is closed, then the range includes all rows whose
      first `len(end_closed)` key columns exactly match `end_closed`.
    endOpen: If the end is open, then the range excludes rows whose first
      `len(end_open)` key columns exactly match `end_open`.
    startClosed: If the start is closed, then the range includes all rows
      whose first `len(start_closed)` key columns exactly match
      `start_closed`.
    startOpen: If the start is open, then the range excludes rows whose first
      `len(start_open)` key columns exactly match `start_open`.
  """

  endClosed = _messages.MessageField('extra_types.JsonValue', 1, repeated=True)
  endOpen = _messages.MessageField('extra_types.JsonValue', 2, repeated=True)
  startClosed = _messages.MessageField('extra_types.JsonValue', 3, repeated=True)
  startOpen = _messages.MessageField('extra_types.JsonValue', 4, repeated=True)


class KeyRangeInfo(_messages.Message):
  r"""A message representing information for a key range (possibly one key).

  Fields:
    contextValues: The list of context values for this key range.
    endKeyIndex: The index of the end key in indexed_keys.
    info: Information about this key range, for all metrics.
    keysCount: The number of keys this range covers.
    metric: The name of the metric. e.g. "latency".
    startKeyIndex: The index of the start key in indexed_keys.
    timeOffset: The time offset. This is the time since the start of the time
      interval.
    unit: The unit of the metric. This is an unstructured field and will be
      mapped as is to the user.
    value: The value of the metric.
  """

  contextValues = _messages.MessageField('ContextValue', 1, repeated=True)
  endKeyIndex = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  info = _messages.MessageField('LocalizedString', 3)
  keysCount = _messages.IntegerField(4)
  metric = _messages.MessageField('LocalizedString', 5)
  startKeyIndex = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  timeOffset = _messages.StringField(7)
  unit = _messages.MessageField('LocalizedString', 8)
  value = _messages.FloatField(9, variant=_messages.Variant.FLOAT)


class KeyRangeInfos(_messages.Message):
  r"""A message representing a list of specific information for multiple key
  ranges.

  Fields:
    infos: The list individual KeyRangeInfos.
    totalSize: The total size of the list of all KeyRangeInfos. This may be
      larger than the number of repeated messages above. If that is the case,
      this number may be used to determine how many are not being shown.
  """

  infos = _messages.MessageField('KeyRangeInfo', 1, repeated=True)
  totalSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class KeySet(_messages.Message):
  r"""`KeySet` defines a collection of Cloud Spanner keys and/or key ranges.
  All the keys are expected to be in the same table or index. The keys need
  not be sorted in any particular way. If the same key is specified multiple
  times in the set (for example if two ranges, two keys, or a key and a range
  overlap), Cloud Spanner behaves as if the key were only specified once.

  Messages:
    KeysValueListEntry: Single entry in a KeysValue.

  Fields:
    all: For convenience `all` can be set to `true` to indicate that this
      `KeySet` matches all keys in the table or index. Note that any keys
      specified in `keys` or `ranges` are only yielded once.
    keys: A list of specific keys. Entries in `keys` should have exactly as
      many elements as there are columns in the primary or index key with
      which this `KeySet` is used. Individual key values are encoded as
      described here.
    ranges: A list of key ranges. See KeyRange for more information about key
      range specifications.
  """

  class KeysValueListEntry(_messages.Message):
    r"""Single entry in a KeysValue.

    Fields:
      entry: A extra_types.JsonValue attribute.
    """

    entry = _messages.MessageField('extra_types.JsonValue', 1, repeated=True)

  all = _messages.BooleanField(1)
  keys = _messages.MessageField('KeysValueListEntry', 2, repeated=True)
  ranges = _messages.MessageField('KeyRange', 3, repeated=True)


class ListBackupOperationsResponse(_messages.Message):
  r"""The response for ListBackupOperations.

  Fields:
    nextPageToken: `next_page_token` can be sent in a subsequent
      ListBackupOperations call to fetch more of the matching metadata.
    operations: The list of matching backup long-running operations. Each
      operation's name will be prefixed by the backup's name. The operation's
      metadata field type `metadata.type_url` describes the type of the
      metadata. Operations returned include those that are pending or have
      completed/failed/canceled within the last 7 days. Operations returned
      are ordered by `operation.metadata.value.progress.start_time` in
      descending order starting from the most recently started operation.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListBackupsResponse(_messages.Message):
  r"""The response for ListBackups.

  Fields:
    backups: The list of matching backups. Backups returned are ordered by
      `create_time` in descending order, starting from the most recent
      `create_time`.
    nextPageToken: `next_page_token` can be sent in a subsequent ListBackups
      call to fetch more of the matching backups.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDatabaseOperationsResponse(_messages.Message):
  r"""The response for ListDatabaseOperations.

  Fields:
    nextPageToken: `next_page_token` can be sent in a subsequent
      ListDatabaseOperations call to fetch more of the matching metadata.
    operations: The list of matching database long-running operations. Each
      operation's name will be prefixed by the database's name. The
      operation's metadata field type `metadata.type_url` describes the type
      of the metadata.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListDatabaseRolesResponse(_messages.Message):
  r"""The response for ListDatabaseRoles.

  Fields:
    databaseRoles: Database roles that matched the request.
    nextPageToken: `next_page_token` can be sent in a subsequent
      ListDatabaseRoles call to fetch more of the matching roles.
  """

  databaseRoles = _messages.MessageField('DatabaseRole', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDatabasesResponse(_messages.Message):
  r"""The response for ListDatabases.

  Fields:
    databases: Databases that matched the request.
    nextPageToken: `next_page_token` can be sent in a subsequent ListDatabases
      call to fetch more of the matching databases.
  """

  databases = _messages.MessageField('Database', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInstanceConfigOperationsResponse(_messages.Message):
  r"""The response for ListInstanceConfigOperations.

  Fields:
    nextPageToken: `next_page_token` can be sent in a subsequent
      ListInstanceConfigOperations call to fetch more of the matching
      metadata.
    operations: The list of matching instance config long-running operations.
      Each operation's name will be prefixed by the instance config's name.
      The operation's metadata field type `metadata.type_url` describes the
      type of the metadata.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListInstanceConfigsResponse(_messages.Message):
  r"""The response for ListInstanceConfigs.

  Fields:
    instanceConfigs: The list of requested instance configurations.
    nextPageToken: `next_page_token` can be sent in a subsequent
      ListInstanceConfigs call to fetch more of the matching instance
      configurations.
  """

  instanceConfigs = _messages.MessageField('InstanceConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInstancePartitionsResponse(_messages.Message):
  r"""The response for ListInstancePartitions.

  Fields:
    instancePartitions: The list of requested instancePartitions.
    nextPageToken: `next_page_token` can be sent in a subsequent
      ListInstancePartitions call to fetch more of the matching instance
      partitions.
  """

  instancePartitions = _messages.MessageField('InstancePartition', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInstancesResponse(_messages.Message):
  r"""The response for ListInstances.

  Fields:
    instances: The list of requested instances.
    nextPageToken: `next_page_token` can be sent in a subsequent ListInstances
      call to fetch more of the matching instances.
    unreachable: The list of unreachable instances. It includes the names of
      instances whose metadata could not be retrieved within
      instance_deadline.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListScansResponse(_messages.Message):
  r"""Response method from the ListScans method.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    scans: Available scans based on the list query parameters.
  """

  nextPageToken = _messages.StringField(1)
  scans = _messages.MessageField('Scan', 2, repeated=True)


class ListSessionsResponse(_messages.Message):
  r"""The response for ListSessions.

  Fields:
    nextPageToken: `next_page_token` can be sent in a subsequent ListSessions
      call to fetch more of the matching sessions.
    sessions: The list of requested sessions.
  """

  nextPageToken = _messages.StringField(1)
  sessions = _messages.MessageField('Session', 2, repeated=True)


class LocalizedString(_messages.Message):
  r"""A message representing a user-facing string whose value may need to be
  translated before being displayed.

  Messages:
    ArgsValue: A map of arguments used when creating the localized message.
      Keys represent parameter names which may be used by the localized
      version when substituting dynamic values.

  Fields:
    args: A map of arguments used when creating the localized message. Keys
      represent parameter names which may be used by the localized version
      when substituting dynamic values.
    message: The canonical English version of this message. If no token is
      provided or the front-end has no message associated with the token, this
      text will be displayed as-is.
    token: The token identifying the message, e.g. 'METRIC_READ_CPU'. This
      should be unique within the service.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ArgsValue(_messages.Message):
    r"""A map of arguments used when creating the localized message. Keys
    represent parameter names which may be used by the localized version when
    substituting dynamic values.

    Messages:
      AdditionalProperty: An additional property for a ArgsValue object.

    Fields:
      additionalProperties: Additional properties of type ArgsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ArgsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  args = _messages.MessageField('ArgsValue', 1)
  message = _messages.StringField(2)
  token = _messages.StringField(3)


class Metric(_messages.Message):
  r"""A message representing the actual monitoring data, values for each key
  bucket over time, of a metric.

  Enums:
    AggregationValueValuesEnum: The aggregation function used to aggregate
      each key bucket

  Messages:
    IndexedHotKeysValue: The (sparse) mapping from time index to an
      IndexedHotKey message, representing those time intervals for which there
      are hot keys.
    IndexedKeyRangeInfosValue: The (sparse) mapping from time interval index
      to an IndexedKeyRangeInfos message, representing those time intervals
      for which there are informational messages concerning key ranges.

  Fields:
    aggregation: The aggregation function used to aggregate each key bucket
    category: The category of the metric, e.g. "Activity", "Alerts", "Reads",
      etc.
    derived: The references to numerator and denominator metrics for a derived
      metric.
    displayLabel: The displayed label of the metric.
    hasNonzeroData: Whether the metric has any non-zero data.
    hotValue: The value that is considered hot for the metric. On a per metric
      basis hotness signals high utilization and something that might
      potentially be a cause for concern by the end user. hot_value is used to
      calibrate and scale visual color scales.
    indexedHotKeys: The (sparse) mapping from time index to an IndexedHotKey
      message, representing those time intervals for which there are hot keys.
    indexedKeyRangeInfos: The (sparse) mapping from time interval index to an
      IndexedKeyRangeInfos message, representing those time intervals for
      which there are informational messages concerning key ranges.
    info: Information about the metric.
    matrix: The data for the metric as a matrix.
    unit: The unit of the metric.
    visible: Whether the metric is visible to the end user.
  """

  class AggregationValueValuesEnum(_messages.Enum):
    r"""The aggregation function used to aggregate each key bucket

    Values:
      AGGREGATION_UNSPECIFIED: Required default value.
      MAX: Use the maximum of all values.
      SUM: Use the sum of all values.
    """
    AGGREGATION_UNSPECIFIED = 0
    MAX = 1
    SUM = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class IndexedHotKeysValue(_messages.Message):
    r"""The (sparse) mapping from time index to an IndexedHotKey message,
    representing those time intervals for which there are hot keys.

    Messages:
      AdditionalProperty: An additional property for a IndexedHotKeysValue
        object.

    Fields:
      additionalProperties: Additional properties of type IndexedHotKeysValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a IndexedHotKeysValue object.

      Fields:
        key: Name of the additional property.
        value: A IndexedHotKey attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('IndexedHotKey', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class IndexedKeyRangeInfosValue(_messages.Message):
    r"""The (sparse) mapping from time interval index to an
    IndexedKeyRangeInfos message, representing those time intervals for which
    there are informational messages concerning key ranges.

    Messages:
      AdditionalProperty: An additional property for a
        IndexedKeyRangeInfosValue object.

    Fields:
      additionalProperties: Additional properties of type
        IndexedKeyRangeInfosValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a IndexedKeyRangeInfosValue object.

      Fields:
        key: Name of the additional property.
        value: A IndexedKeyRangeInfos attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('IndexedKeyRangeInfos', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  aggregation = _messages.EnumField('AggregationValueValuesEnum', 1)
  category = _messages.MessageField('LocalizedString', 2)
  derived = _messages.MessageField('DerivedMetric', 3)
  displayLabel = _messages.MessageField('LocalizedString', 4)
  hasNonzeroData = _messages.BooleanField(5)
  hotValue = _messages.FloatField(6, variant=_messages.Variant.FLOAT)
  indexedHotKeys = _messages.MessageField('IndexedHotKeysValue', 7)
  indexedKeyRangeInfos = _messages.MessageField('IndexedKeyRangeInfosValue', 8)
  info = _messages.MessageField('LocalizedString', 9)
  matrix = _messages.MessageField('MetricMatrix', 10)
  unit = _messages.MessageField('LocalizedString', 11)
  visible = _messages.BooleanField(12)


class MetricMatrix(_messages.Message):
  r"""A message representing a matrix of floats.

  Fields:
    rows: The rows of the matrix.
  """

  rows = _messages.MessageField('MetricMatrixRow', 1, repeated=True)


class MetricMatrixRow(_messages.Message):
  r"""A message representing a row of a matrix of floats.

  Fields:
    cols: The columns of the row.
  """

  cols = _messages.FloatField(1, repeated=True, variant=_messages.Variant.FLOAT)


class Mutation(_messages.Message):
  r"""A modification to one or more Cloud Spanner rows. Mutations can be
  applied to a Cloud Spanner database by sending them in a Commit call.

  Fields:
    delete: Delete rows from a table. Succeeds whether or not the named rows
      were present.
    insert: Insert new rows in a table. If any of the rows already exist, the
      write or transaction fails with error `ALREADY_EXISTS`.
    insertOrUpdate: Like insert, except that if the row already exists, then
      its column values are overwritten with the ones provided. Any column
      values not explicitly written are preserved. When using
      insert_or_update, just as when using insert, all `NOT NULL` columns in
      the table must be given a value. This holds true even when the row
      already exists and will therefore actually be updated.
    replace: Like insert, except that if the row already exists, it is
      deleted, and the column values provided are inserted instead. Unlike
      insert_or_update, this means any values not explicitly written become
      `NULL`. In an interleaved table, if you create the child table with the
      `ON DELETE CASCADE` annotation, then replacing a parent row also deletes
      the child rows. Otherwise, you must delete the child rows before you
      replace the parent row.
    update: Update existing rows in a table. If any of the rows does not
      already exist, the transaction fails with error `NOT_FOUND`.
  """

  delete = _messages.MessageField('Delete', 1)
  insert = _messages.MessageField('Write', 2)
  insertOrUpdate = _messages.MessageField('Write', 3)
  replace = _messages.MessageField('Write', 4)
  update = _messages.MessageField('Write', 5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationProgress(_messages.Message):
  r"""Encapsulates progress related information for a Cloud Spanner long
  running operation.

  Fields:
    endTime: If set, the time at which this operation failed or was completed
      successfully.
    progressPercent: Percent completion of the operation. Values are between 0
      and 100 inclusive.
    startTime: Time the request was received.
  """

  endTime = _messages.StringField(1)
  progressPercent = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(3)


class OptimizeRestoredDatabaseMetadata(_messages.Message):
  r"""Metadata type for the long-running operation used to track the progress
  of optimizations performed on a newly restored database. This long-running
  operation is automatically created by the system after the successful
  completion of a database restore, and cannot be cancelled.

  Fields:
    name: Name of the restored database being optimized.
    progress: The progress of the post-restore optimizations.
  """

  name = _messages.StringField(1)
  progress = _messages.MessageField('OperationProgress', 2)


class PartialResultSet(_messages.Message):
  r"""Partial results from a streaming read or SQL query. Streaming reads and
  SQL queries better tolerate large result sets, large rows, and large values,
  but are a little trickier to consume.

  Fields:
    chunkedValue: If true, then the final value in values is chunked, and must
      be combined with more values from subsequent `PartialResultSet`s to
      obtain a complete field value.
    metadata: Metadata about the result set, such as row type information.
      Only present in the first response.
    resumeToken: Streaming calls might be interrupted for a variety of
      reasons, such as TCP connection loss. If this occurs, the stream of
      results can be resumed by re-sending the original request and including
      `resume_token`. Note that executing any other transaction in the same
      session invalidates the token.
    stats: Query plan and execution statistics for the statement that produced
      this streaming result set. These can be requested by setting
      ExecuteSqlRequest.query_mode and are sent only once with the last
      response in the stream. This field will also be present in the last
      response for DML statements.
    values: A streamed result set consists of a stream of values, which might
      be split into many `PartialResultSet` messages to accommodate large rows
      and/or large values. Every N complete values defines a row, where N is
      equal to the number of entries in metadata.row_type.fields. Most values
      are encoded based on type as described here. It is possible that the
      last value in values is "chunked", meaning that the rest of the value is
      sent in subsequent `PartialResultSet`(s). This is denoted by the
      chunked_value field. Two or more chunked values can be merged to form a
      complete value as follows: * `bool/number/null`: cannot be chunked *
      `string`: concatenate the strings * `list`: concatenate the lists. If
      the last element in a list is a `string`, `list`, or `object`, merge it
      with the first element in the next list by applying these rules
      recursively. * `object`: concatenate the (field name, field value)
      pairs. If a field name is duplicated, then apply these rules recursively
      to merge the field values. Some examples of merging: # Strings are
      concatenated. "foo", "bar" => "foobar" # Lists of non-strings are
      concatenated. [2, 3], [4] => [2, 3, 4] # Lists are concatenated, but the
      last and first elements are merged # because they are strings. ["a",
      "b"], ["c", "d"] => ["a", "bc", "d"] # Lists are concatenated, but the
      last and first elements are merged # because they are lists.
      Recursively, the last and first elements # of the inner lists are merged
      because they are strings. ["a", ["b", "c"]], [["d"], "e"] => ["a", ["b",
      "cd"], "e"] # Non-overlapping object fields are combined. {"a": "1"},
      {"b": "2"} => {"a": "1", "b": 2"} # Overlapping object fields are
      merged. {"a": "1"}, {"a": "2"} => {"a": "12"} # Examples of merging
      objects containing lists of strings. {"a": ["1"]}, {"a": ["2"]} => {"a":
      ["12"]} For a more complete example, suppose a streaming SQL query is
      yielding a result set whose rows contain a single string field. The
      following `PartialResultSet`s might be yielded: { "metadata": { ... }
      "values": ["Hello", "W"] "chunked_value": true "resume_token": "Af65..."
      } { "values": ["orl"] "chunked_value": true } { "values": ["d"]
      "resume_token": "Zx1B..." } This sequence of `PartialResultSet`s encodes
      two rows, one containing the field value `"Hello"`, and a second
      containing the field value `"World" = "W" + "orl" + "d"`. Not all
      `PartialResultSet`s contain a `resume_token`. Execution can only be
      resumed from a previously yielded `resume_token`. For the above sequence
      of `PartialResultSet`s, resuming the query with `"resume_token":
      "Af65..."` will yield results from the `PartialResultSet` with value
      `["orl"]`.
  """

  chunkedValue = _messages.BooleanField(1)
  metadata = _messages.MessageField('ResultSetMetadata', 2)
  resumeToken = _messages.BytesField(3)
  stats = _messages.MessageField('ResultSetStats', 4)
  values = _messages.MessageField('extra_types.JsonValue', 5, repeated=True)


class Partition(_messages.Message):
  r"""Information returned for each partition returned in a PartitionResponse.

  Fields:
    partitionToken: This token can be passed to Read, StreamingRead,
      ExecuteSql, or ExecuteStreamingSql requests to restrict the results to
      those identified by this partition token.
  """

  partitionToken = _messages.BytesField(1)


class PartitionOptions(_messages.Message):
  r"""Options for a PartitionQueryRequest and PartitionReadRequest.

  Fields:
    maxPartitions: **Note:** This hint is currently ignored by PartitionQuery
      and PartitionRead requests. The desired maximum number of partitions to
      return. For example, this may be set to the number of workers available.
      The default for this option is currently 10,000. The maximum value is
      currently 200,000. This is only a hint. The actual number of partitions
      returned may be smaller or larger than this maximum count request.
    partitionSizeBytes: **Note:** This hint is currently ignored by
      PartitionQuery and PartitionRead requests. The desired data size for
      each partition generated. The default for this option is currently 1
      GiB. This is only a hint. The actual size of each partition may be
      smaller or larger than this size request.
  """

  maxPartitions = _messages.IntegerField(1)
  partitionSizeBytes = _messages.IntegerField(2)


class PartitionQueryRequest(_messages.Message):
  r"""The request for PartitionQuery

  Messages:
    ParamTypesValue: It is not always possible for Cloud Spanner to infer the
      right SQL type from a JSON value. For example, values of type `BYTES`
      and values of type `STRING` both appear in params as JSON strings. In
      these cases, `param_types` can be used to specify the exact SQL type for
      some or all of the SQL query parameters. See the definition of Type for
      more information about SQL types.
    ParamsValue: Parameter names and values that bind to placeholders in the
      SQL string. A parameter placeholder consists of the `@` character
      followed by the parameter name (for example, `@firstName`). Parameter
      names can contain letters, numbers, and underscores. Parameters can
      appear anywhere that a literal value is expected. The same parameter
      name can be used more than once, for example: `"WHERE id > @msg_id AND
      id < @msg_id + 100"` It is an error to execute a SQL statement with
      unbound parameters.

  Fields:
    paramTypes: It is not always possible for Cloud Spanner to infer the right
      SQL type from a JSON value. For example, values of type `BYTES` and
      values of type `STRING` both appear in params as JSON strings. In these
      cases, `param_types` can be used to specify the exact SQL type for some
      or all of the SQL query parameters. See the definition of Type for more
      information about SQL types.
    params: Parameter names and values that bind to placeholders in the SQL
      string. A parameter placeholder consists of the `@` character followed
      by the parameter name (for example, `@firstName`). Parameter names can
      contain letters, numbers, and underscores. Parameters can appear
      anywhere that a literal value is expected. The same parameter name can
      be used more than once, for example: `"WHERE id > @msg_id AND id <
      @msg_id + 100"` It is an error to execute a SQL statement with unbound
      parameters.
    partitionOptions: Additional options that affect how many partitions are
      created.
    sql: Required. The query request to generate partitions for. The request
      will fail if the query is not root partitionable. The query plan of a
      root partitionable query has a single distributed union operator. A
      distributed union operator conceptually divides one or more tables into
      multiple splits, remotely evaluates a subquery independently on each
      split, and then unions all results. This must not contain DML commands,
      such as INSERT, UPDATE, or DELETE. Use ExecuteStreamingSql with a
      PartitionedDml transaction for large, partition-friendly DML operations.
    transaction: Read only snapshot transactions are supported, read/write and
      single use transactions are not.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamTypesValue(_messages.Message):
    r"""It is not always possible for Cloud Spanner to infer the right SQL
    type from a JSON value. For example, values of type `BYTES` and values of
    type `STRING` both appear in params as JSON strings. In these cases,
    `param_types` can be used to specify the exact SQL type for some or all of
    the SQL query parameters. See the definition of Type for more information
    about SQL types.

    Messages:
      AdditionalProperty: An additional property for a ParamTypesValue object.

    Fields:
      additionalProperties: Additional properties of type ParamTypesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamTypesValue object.

      Fields:
        key: Name of the additional property.
        value: A Type attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Type', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamsValue(_messages.Message):
    r"""Parameter names and values that bind to placeholders in the SQL
    string. A parameter placeholder consists of the `@` character followed by
    the parameter name (for example, `@firstName`). Parameter names can
    contain letters, numbers, and underscores. Parameters can appear anywhere
    that a literal value is expected. The same parameter name can be used more
    than once, for example: `"WHERE id > @msg_id AND id < @msg_id + 100"` It
    is an error to execute a SQL statement with unbound parameters.

    Messages:
      AdditionalProperty: An additional property for a ParamsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  paramTypes = _messages.MessageField('ParamTypesValue', 1)
  params = _messages.MessageField('ParamsValue', 2)
  partitionOptions = _messages.MessageField('PartitionOptions', 3)
  sql = _messages.StringField(4)
  transaction = _messages.MessageField('TransactionSelector', 5)


class PartitionReadRequest(_messages.Message):
  r"""The request for PartitionRead

  Fields:
    columns: The columns of table to be returned for each row matching this
      request.
    index: If non-empty, the name of an index on table. This index is used
      instead of the table primary key when interpreting key_set and sorting
      result rows. See key_set for further information.
    keySet: Required. `key_set` identifies the rows to be yielded. `key_set`
      names the primary keys of the rows in table to be yielded, unless index
      is present. If index is present, then key_set instead names index keys
      in index. It is not an error for the `key_set` to name rows that do not
      exist in the database. Read yields nothing for nonexistent rows.
    partitionOptions: Additional options that affect how many partitions are
      created.
    table: Required. The name of the table in the database to be read.
    transaction: Read only snapshot transactions are supported, read/write and
      single use transactions are not.
  """

  columns = _messages.StringField(1, repeated=True)
  index = _messages.StringField(2)
  keySet = _messages.MessageField('KeySet', 3)
  partitionOptions = _messages.MessageField('PartitionOptions', 4)
  table = _messages.StringField(5)
  transaction = _messages.MessageField('TransactionSelector', 6)


class PartitionResponse(_messages.Message):
  r"""The response for PartitionQuery or PartitionRead

  Fields:
    partitions: Partitions created by this request.
    transaction: Transaction created by this request.
  """

  partitions = _messages.MessageField('Partition', 1, repeated=True)
  transaction = _messages.MessageField('Transaction', 2)


class PartitionedDml(_messages.Message):
  r"""Message type to initiate a Partitioned DML transaction."""


class PlanNode(_messages.Message):
  r"""Node information for nodes appearing in a QueryPlan.plan_nodes.

  Enums:
    KindValueValuesEnum: Used to determine the type of node. May be needed for
      visualizing different kinds of nodes differently. For example, If the
      node is a SCALAR node, it will have a condensed representation which can
      be used to directly embed a description of the node in its parent.

  Messages:
    ExecutionStatsValue: The execution statistics associated with the node,
      contained in a group of key-value pairs. Only present if the plan was
      returned as a result of a profile query. For example, number of
      executions, number of rows/time per execution etc.
    MetadataValue: Attributes relevant to the node contained in a group of
      key-value pairs. For example, a Parameter Reference node could have the
      following information in its metadata: { "parameter_reference":
      "param1", "parameter_type": "array" }

  Fields:
    childLinks: List of child node `index`es and their relationship to this
      parent.
    displayName: The display name for the node.
    executionStats: The execution statistics associated with the node,
      contained in a group of key-value pairs. Only present if the plan was
      returned as a result of a profile query. For example, number of
      executions, number of rows/time per execution etc.
    index: The `PlanNode`'s index in node list.
    kind: Used to determine the type of node. May be needed for visualizing
      different kinds of nodes differently. For example, If the node is a
      SCALAR node, it will have a condensed representation which can be used
      to directly embed a description of the node in its parent.
    metadata: Attributes relevant to the node contained in a group of key-
      value pairs. For example, a Parameter Reference node could have the
      following information in its metadata: { "parameter_reference":
      "param1", "parameter_type": "array" }
    shortRepresentation: Condensed representation for SCALAR nodes.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Used to determine the type of node. May be needed for visualizing
    different kinds of nodes differently. For example, If the node is a SCALAR
    node, it will have a condensed representation which can be used to
    directly embed a description of the node in its parent.

    Values:
      KIND_UNSPECIFIED: Not specified.
      RELATIONAL: Denotes a Relational operator node in the expression tree.
        Relational operators represent iterative processing of rows during
        query execution. For example, a `TableScan` operation that reads rows
        from a table.
      SCALAR: Denotes a Scalar node in the expression tree. Scalar nodes
        represent non-iterable entities in the query plan. For example,
        constants or arithmetic operators appearing inside predicate
        expressions or references to column names.
    """
    KIND_UNSPECIFIED = 0
    RELATIONAL = 1
    SCALAR = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExecutionStatsValue(_messages.Message):
    r"""The execution statistics associated with the node, contained in a
    group of key-value pairs. Only present if the plan was returned as a
    result of a profile query. For example, number of executions, number of
    rows/time per execution etc.

    Messages:
      AdditionalProperty: An additional property for a ExecutionStatsValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExecutionStatsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Attributes relevant to the node contained in a group of key-value
    pairs. For example, a Parameter Reference node could have the following
    information in its metadata: { "parameter_reference": "param1",
    "parameter_type": "array" }

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  childLinks = _messages.MessageField('ChildLink', 1, repeated=True)
  displayName = _messages.StringField(2)
  executionStats = _messages.MessageField('ExecutionStatsValue', 3)
  index = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  kind = _messages.EnumField('KindValueValuesEnum', 5)
  metadata = _messages.MessageField('MetadataValue', 6)
  shortRepresentation = _messages.MessageField('ShortRepresentation', 7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class PrefixNode(_messages.Message):
  r"""A message representing a key prefix node in the key prefix hierarchy.
  for eg. Bigtable keyspaces are lexicographically ordered mappings of keys to
  values. Keys often have a shared prefix structure where users use the keys
  to organize data. Eg ///employee In this case Keysight will possibly use one
  node for a company and reuse it for all employees that fall under the
  company. Doing so improves legibility in the UI.

  Fields:
    dataSourceNode: Whether this corresponds to a data_source name.
    depth: The depth in the prefix hierarchy.
    endIndex: The index of the end key bucket of the range that this node
      spans.
    startIndex: The index of the start key bucket of the range that this node
      spans.
    word: The string represented by the prefix node.
  """

  dataSourceNode = _messages.BooleanField(1)
  depth = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  endIndex = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  startIndex = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  word = _messages.StringField(5)


class QueryOptions(_messages.Message):
  r"""Query optimizer configuration.

  Fields:
    optimizerStatisticsPackage: An option to control the selection of
      optimizer statistics package. This parameter allows individual queries
      to use a different query optimizer statistics package. Specifying
      `latest` as a value instructs Cloud Spanner to use the latest generated
      statistics package. If not specified, Cloud Spanner uses the statistics
      package set at the database level options, or the latest package if the
      database option is not set. The statistics package requested by the
      query has to be exempt from garbage collection. This can be achieved
      with the following DDL statement: ``` ALTER STATISTICS SET OPTIONS
      (allow_gc=false) ``` The list of available statistics packages can be
      queried from `INFORMATION_SCHEMA.SPANNER_STATISTICS`. Executing a SQL
      statement with an invalid optimizer statistics package or with a
      statistics package that allows garbage collection fails with an
      `INVALID_ARGUMENT` error.
    optimizerVersion: An option to control the selection of optimizer version.
      This parameter allows individual queries to pick different query
      optimizer versions. Specifying `latest` as a value instructs Cloud
      Spanner to use the latest supported query optimizer version. If not
      specified, Cloud Spanner uses the optimizer version set at the database
      level options. Any other positive integer (from the list of supported
      optimizer versions) overrides the default optimizer version for query
      execution. The list of supported optimizer versions can be queried from
      SPANNER_SYS.SUPPORTED_OPTIMIZER_VERSIONS. Executing a SQL statement with
      an invalid optimizer version fails with an `INVALID_ARGUMENT` error. See
      https://cloud.google.com/spanner/docs/query-optimizer/manage-query-
      optimizer for more information on managing the query optimizer. The
      `optimizer_version` statement hint has precedence over this setting.
  """

  optimizerStatisticsPackage = _messages.StringField(1)
  optimizerVersion = _messages.StringField(2)


class QueryPlan(_messages.Message):
  r"""Contains an ordered list of nodes appearing in the query plan.

  Fields:
    planNodes: The nodes in the query plan. Plan nodes are returned in pre-
      order starting with the plan root. Each PlanNode's `id` corresponds to
      its index in `plan_nodes`.
  """

  planNodes = _messages.MessageField('PlanNode', 1, repeated=True)


class ReadOnly(_messages.Message):
  r"""Message type to initiate a read-only transaction.

  Fields:
    exactStaleness: Executes all reads at a timestamp that is
      `exact_staleness` old. The timestamp is chosen soon after the read is
      started. Guarantees that all writes that have committed more than the
      specified number of seconds ago are visible. Because Cloud Spanner
      chooses the exact timestamp, this mode works even if the client's local
      clock is substantially skewed from Cloud Spanner commit timestamps.
      Useful for reading at nearby replicas without the distributed timestamp
      negotiation overhead of `max_staleness`.
    maxStaleness: Read data at a timestamp >= `NOW - max_staleness` seconds.
      Guarantees that all writes that have committed more than the specified
      number of seconds ago are visible. Because Cloud Spanner chooses the
      exact timestamp, this mode works even if the client's local clock is
      substantially skewed from Cloud Spanner commit timestamps. Useful for
      reading the freshest data available at a nearby replica, while bounding
      the possible staleness if the local replica has fallen behind. Note that
      this option can only be used in single-use transactions.
    minReadTimestamp: Executes all reads at a timestamp >=
      `min_read_timestamp`. This is useful for requesting fresher data than
      some previous read, or data that is fresh enough to observe the effects
      of some previously committed transaction whose timestamp is known. Note
      that this option can only be used in single-use transactions. A
      timestamp in RFC3339 UTC \"Zulu\" format, accurate to nanoseconds.
      Example: `"2014-10-02T15:01:23.045123456Z"`.
    readTimestamp: Executes all reads at the given timestamp. Unlike other
      modes, reads at a specific timestamp are repeatable; the same read at
      the same timestamp always returns the same data. If the timestamp is in
      the future, the read will block until the specified timestamp, modulo
      the read's deadline. Useful for large scale consistent reads such as
      mapreduces, or for coordinating many reads against a consistent snapshot
      of the data. A timestamp in RFC3339 UTC \"Zulu\" format, accurate to
      nanoseconds. Example: `"2014-10-02T15:01:23.045123456Z"`.
    returnReadTimestamp: If true, the Cloud Spanner-selected read timestamp is
      included in the Transaction message that describes the transaction.
    strong: Read at a timestamp where all previously committed transactions
      are visible.
  """

  exactStaleness = _messages.StringField(1)
  maxStaleness = _messages.StringField(2)
  minReadTimestamp = _messages.StringField(3)
  readTimestamp = _messages.StringField(4)
  returnReadTimestamp = _messages.BooleanField(5)
  strong = _messages.BooleanField(6)


class ReadRequest(_messages.Message):
  r"""The request for Read and StreamingRead.

  Fields:
    columns: Required. The columns of table to be returned for each row
      matching this request.
    dataBoostEnabled: If this is for a partitioned read and this field is set
      to `true`, the request is executed with Spanner Data Boost independent
      compute resources. If the field is set to `true` but the request does
      not set `partition_token`, the API returns an `INVALID_ARGUMENT` error.
    directedReadOptions: Directed read options for this request.
    index: If non-empty, the name of an index on table. This index is used
      instead of the table primary key when interpreting key_set and sorting
      result rows. See key_set for further information.
    keySet: Required. `key_set` identifies the rows to be yielded. `key_set`
      names the primary keys of the rows in table to be yielded, unless index
      is present. If index is present, then key_set instead names index keys
      in index. If the partition_token field is empty, rows are yielded in
      table primary key order (if index is empty) or index key order (if index
      is non-empty). If the partition_token field is not empty, rows will be
      yielded in an unspecified order. It is not an error for the `key_set` to
      name rows that do not exist in the database. Read yields nothing for
      nonexistent rows.
    limit: If greater than zero, only the first `limit` rows are yielded. If
      `limit` is zero, the default is no limit. A limit cannot be specified if
      `partition_token` is set.
    partitionToken: If present, results will be restricted to the specified
      partition previously created using PartitionRead(). There must be an
      exact match for the values of fields common to this message and the
      PartitionReadRequest message used to create this partition_token.
    requestOptions: Common options for this request.
    resumeToken: If this request is resuming a previously interrupted read,
      `resume_token` should be copied from the last PartialResultSet yielded
      before the interruption. Doing this enables the new read to resume where
      the last read left off. The rest of the request parameters must exactly
      match the request that yielded this token.
    table: Required. The name of the table in the database to be read.
    transaction: The transaction to use. If none is provided, the default is a
      temporary read-only transaction with strong concurrency.
  """

  columns = _messages.StringField(1, repeated=True)
  dataBoostEnabled = _messages.BooleanField(2)
  directedReadOptions = _messages.MessageField('DirectedReadOptions', 3)
  index = _messages.StringField(4)
  keySet = _messages.MessageField('KeySet', 5)
  limit = _messages.IntegerField(6)
  partitionToken = _messages.BytesField(7)
  requestOptions = _messages.MessageField('RequestOptions', 8)
  resumeToken = _messages.BytesField(9)
  table = _messages.StringField(10)
  transaction = _messages.MessageField('TransactionSelector', 11)


class ReadWrite(_messages.Message):
  r"""Message type to initiate a read-write transaction. Currently this
  transaction type has no options.

  Enums:
    ReadLockModeValueValuesEnum: Read lock mode for the transaction.

  Fields:
    readLockMode: Read lock mode for the transaction.
  """

  class ReadLockModeValueValuesEnum(_messages.Enum):
    r"""Read lock mode for the transaction.

    Values:
      READ_LOCK_MODE_UNSPECIFIED: Default value. If the value is not
        specified, the pessimistic read lock is used.
      PESSIMISTIC: Pessimistic lock mode. Read locks are acquired immediately
        on read.
      OPTIMISTIC: Optimistic lock mode. Locks for reads within the transaction
        are not acquired on read. Instead the locks are acquired on a commit
        to validate that read/queried data has not changed since the
        transaction started.
    """
    READ_LOCK_MODE_UNSPECIFIED = 0
    PESSIMISTIC = 1
    OPTIMISTIC = 2

  readLockMode = _messages.EnumField('ReadLockModeValueValuesEnum', 1)


class ReplicaInfo(_messages.Message):
  r"""A ReplicaInfo object.

  Enums:
    TypeValueValuesEnum: The type of replica.

  Fields:
    defaultLeaderLocation: If true, this location is designated as the default
      leader location where leader replicas are placed. See the [region types
      documentation](https://cloud.google.com/spanner/docs/instances#region_ty
      pes) for more details.
    location: The location of the serving resources, e.g. "us-central1".
    type: The type of replica.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of replica.

    Values:
      TYPE_UNSPECIFIED: Not specified.
      READ_WRITE: Read-write replicas support both reads and writes. These
        replicas: * Maintain a full copy of your data. * Serve reads. * Can
        vote whether to commit a write. * Participate in leadership election.
        * Are eligible to become a leader.
      READ_ONLY: Read-only replicas only support reads (not writes). Read-only
        replicas: * Maintain a full copy of your data. * Serve reads. * Do not
        participate in voting to commit writes. * Are not eligible to become a
        leader.
      WITNESS: Witness replicas don't support reads but do participate in
        voting to commit writes. Witness replicas: * Do not maintain a full
        copy of data. * Do not serve reads. * Vote whether to commit writes. *
        Participate in leader election but are not eligible to become leader.
    """
    TYPE_UNSPECIFIED = 0
    READ_WRITE = 1
    READ_ONLY = 2
    WITNESS = 3

  defaultLeaderLocation = _messages.BooleanField(1)
  location = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class ReplicaSelection(_messages.Message):
  r"""The directed read replica selector. Callers must provide one or more of
  the following fields for replica selection: * `location` - The location must
  be one of the regions within the multi-region configuration of your
  database. * `type` - The type of the replica. Some examples of using
  replica_selectors are: * `location:us-east1` --> The "us-east1" replica(s)
  of any available type will be used to process the request. *
  `type:READ_ONLY` --> The "READ_ONLY" type replica(s) in nearest . available
  location will be used to process the request. * `location:us-east1
  type:READ_ONLY` --> The "READ_ONLY" type replica(s) in location "us-east1"
  will be used to process the request.

  Enums:
    TypeValueValuesEnum: The type of replica.

  Fields:
    location: The location or region of the serving requests, e.g. "us-east1".
    type: The type of replica.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of replica.

    Values:
      TYPE_UNSPECIFIED: Not specified.
      READ_WRITE: Read-write replicas support both reads and writes.
      READ_ONLY: Read-only replicas only support reads (not writes).
    """
    TYPE_UNSPECIFIED = 0
    READ_WRITE = 1
    READ_ONLY = 2

  location = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class RequestOptions(_messages.Message):
  r"""Common request options for various APIs.

  Enums:
    PriorityValueValuesEnum: Priority for the request.

  Fields:
    priority: Priority for the request.
    requestTag: A per-request tag which can be applied to queries or reads,
      used for statistics collection. Both request_tag and transaction_tag can
      be specified for a read or query that belongs to a transaction. This
      field is ignored for requests where it's not applicable (e.g.
      CommitRequest). Legal characters for `request_tag` values are all
      printable characters (ASCII 32 - 126) and the length of a request_tag is
      limited to 50 characters. Values that exceed this limit are truncated.
      Any leading underscore (_) characters will be removed from the string.
    transactionTag: A tag used for statistics collection about this
      transaction. Both request_tag and transaction_tag can be specified for a
      read or query that belongs to a transaction. The value of
      transaction_tag should be the same for all requests belonging to the
      same transaction. If this request doesn't belong to any transaction,
      transaction_tag will be ignored. Legal characters for `transaction_tag`
      values are all printable characters (ASCII 32 - 126) and the length of a
      transaction_tag is limited to 50 characters. Values that exceed this
      limit are truncated. Any leading underscore (_) characters will be
      removed from the string.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""Priority for the request.

    Values:
      PRIORITY_UNSPECIFIED: `PRIORITY_UNSPECIFIED` is equivalent to
        `PRIORITY_HIGH`.
      PRIORITY_LOW: This specifies that the request is low priority.
      PRIORITY_MEDIUM: This specifies that the request is medium priority.
      PRIORITY_HIGH: This specifies that the request is high priority.
    """
    PRIORITY_UNSPECIFIED = 0
    PRIORITY_LOW = 1
    PRIORITY_MEDIUM = 2
    PRIORITY_HIGH = 3

  priority = _messages.EnumField('PriorityValueValuesEnum', 1)
  requestTag = _messages.StringField(2)
  transactionTag = _messages.StringField(3)


class RestoreDatabaseEncryptionConfig(_messages.Message):
  r"""Encryption configuration for the restored database.

  Enums:
    EncryptionTypeValueValuesEnum: Required. The encryption type of the
      restored database.

  Fields:
    encryptionType: Required. The encryption type of the restored database.
    kmsKeyName: Optional. The Cloud KMS key that will be used to
      encrypt/decrypt the restored database. This field should be set only
      when encryption_type is `CUSTOMER_MANAGED_ENCRYPTION`. Values are of the
      form `projects//locations//keyRings//cryptoKeys/`.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Required. The encryption type of the restored database.

    Values:
      ENCRYPTION_TYPE_UNSPECIFIED: Unspecified. Do not use.
      USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION: This is the default option when
        encryption_config is not specified.
      GOOGLE_DEFAULT_ENCRYPTION: Use Google default encryption.
      CUSTOMER_MANAGED_ENCRYPTION: Use customer managed encryption. If
        specified, `kms_key_name` must must contain a valid Cloud KMS key.
    """
    ENCRYPTION_TYPE_UNSPECIFIED = 0
    USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION = 1
    GOOGLE_DEFAULT_ENCRYPTION = 2
    CUSTOMER_MANAGED_ENCRYPTION = 3

  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 1)
  kmsKeyName = _messages.StringField(2)


class RestoreDatabaseMetadata(_messages.Message):
  r"""Metadata type for the long-running operation returned by
  RestoreDatabase.

  Enums:
    SourceTypeValueValuesEnum: The type of the restore source.

  Fields:
    backupInfo: Information about the backup used to restore the database.
    cancelTime: The time at which cancellation of this operation was received.
      Operations.CancelOperation starts asynchronous cancellation on a long-
      running operation. The server makes a best effort to cancel the
      operation, but success is not guaranteed. Clients can use
      Operations.GetOperation or other methods to check whether the
      cancellation succeeded or whether the operation completed despite
      cancellation. On successful cancellation, the operation is not deleted;
      instead, it becomes an operation with an Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    name: Name of the database being created and restored to.
    optimizeDatabaseOperationName: If exists, the name of the long-running
      operation that will be used to track the post-restore optimization
      process to optimize the performance of the restored database, and remove
      the dependency on the restore source. The name is of the form
      `projects//instances//databases//operations/` where the is the name of
      database being created and restored to. The metadata type of the long-
      running operation is OptimizeRestoredDatabaseMetadata. This long-running
      operation will be automatically created by the system after the
      RestoreDatabase long-running operation completes successfully. This
      operation will not be created if the restore was not successful.
    progress: The progress of the RestoreDatabase operation.
    sourceType: The type of the restore source.
  """

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""The type of the restore source.

    Values:
      TYPE_UNSPECIFIED: No restore associated.
      BACKUP: A backup was used as the source of the restore.
    """
    TYPE_UNSPECIFIED = 0
    BACKUP = 1

  backupInfo = _messages.MessageField('BackupInfo', 1)
  cancelTime = _messages.StringField(2)
  name = _messages.StringField(3)
  optimizeDatabaseOperationName = _messages.StringField(4)
  progress = _messages.MessageField('OperationProgress', 5)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 6)


class RestoreDatabaseRequest(_messages.Message):
  r"""The request for RestoreDatabase.

  Fields:
    backup: Name of the backup from which to restore. Values are of the form
      `projects//instances//backups/`.
    databaseId: Required. The id of the database to create and restore to.
      This database must not already exist. The `database_id` appended to
      `parent` forms the full database name of the form
      `projects//instances//databases/`.
    encryptionConfig: Optional. An encryption configuration describing the
      encryption type and key resources in Cloud KMS used to encrypt/decrypt
      the database to restore to. If this field is not specified, the restored
      database will use the same encryption configuration as the backup by
      default, namely encryption_type =
      `USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION`.
  """

  backup = _messages.StringField(1)
  databaseId = _messages.StringField(2)
  encryptionConfig = _messages.MessageField('RestoreDatabaseEncryptionConfig', 3)


class RestoreInfo(_messages.Message):
  r"""Information about the database restore.

  Enums:
    SourceTypeValueValuesEnum: The type of the restore source.

  Fields:
    backupInfo: Information about the backup used to restore the database. The
      backup may no longer exist.
    sourceType: The type of the restore source.
  """

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""The type of the restore source.

    Values:
      TYPE_UNSPECIFIED: No restore associated.
      BACKUP: A backup was used as the source of the restore.
    """
    TYPE_UNSPECIFIED = 0
    BACKUP = 1

  backupInfo = _messages.MessageField('BackupInfo', 1)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 2)


class ResultSet(_messages.Message):
  r"""Results from Read or ExecuteSql.

  Messages:
    RowsValueListEntry: Single entry in a RowsValue.

  Fields:
    metadata: Metadata about the result set, such as row type information.
    rows: Each element in `rows` is a row whose format is defined by
      metadata.row_type. The ith element in each row matches the ith field in
      metadata.row_type. Elements are encoded based on type as described here.
    stats: Query plan and execution statistics for the SQL statement that
      produced this result set. These can be requested by setting
      ExecuteSqlRequest.query_mode. DML statements always produce stats
      containing the number of rows modified, unless executed using the
      ExecuteSqlRequest.QueryMode.PLAN ExecuteSqlRequest.query_mode. Other
      fields may or may not be populated, based on the
      ExecuteSqlRequest.query_mode.
  """

  class RowsValueListEntry(_messages.Message):
    r"""Single entry in a RowsValue.

    Fields:
      entry: A extra_types.JsonValue attribute.
    """

    entry = _messages.MessageField('extra_types.JsonValue', 1, repeated=True)

  metadata = _messages.MessageField('ResultSetMetadata', 1)
  rows = _messages.MessageField('RowsValueListEntry', 2, repeated=True)
  stats = _messages.MessageField('ResultSetStats', 3)


class ResultSetMetadata(_messages.Message):
  r"""Metadata about a ResultSet or PartialResultSet.

  Fields:
    rowType: Indicates the field names and types for the rows in the result
      set. For example, a SQL query like `"SELECT UserId, UserName FROM
      Users"` could return a `row_type` value like: "fields": [ { "name":
      "UserId", "type": { "code": "INT64" } }, { "name": "UserName", "type": {
      "code": "STRING" } }, ]
    transaction: If the read or SQL query began a transaction as a side-
      effect, the information about the new transaction is yielded here.
    undeclaredParameters: A SQL query can be parameterized. In PLAN mode,
      these parameters can be undeclared. This indicates the field names and
      types for those undeclared parameters in the SQL query. For example, a
      SQL query like `"SELECT * FROM Users where UserId = @userId and UserName
      = @userName "` could return a `undeclared_parameters` value like:
      "fields": [ { "name": "UserId", "type": { "code": "INT64" } }, { "name":
      "UserName", "type": { "code": "STRING" } }, ]
  """

  rowType = _messages.MessageField('StructType', 1)
  transaction = _messages.MessageField('Transaction', 2)
  undeclaredParameters = _messages.MessageField('StructType', 3)


class ResultSetStats(_messages.Message):
  r"""Additional statistics about a ResultSet or PartialResultSet.

  Messages:
    QueryStatsValue: Aggregated statistics from the execution of the query.
      Only present when the query is profiled. For example, a query could
      return the statistics as follows: { "rows_returned": "3",
      "elapsed_time": "1.22 secs", "cpu_time": "1.19 secs" }

  Fields:
    queryPlan: QueryPlan for the query associated with this result.
    queryStats: Aggregated statistics from the execution of the query. Only
      present when the query is profiled. For example, a query could return
      the statistics as follows: { "rows_returned": "3", "elapsed_time": "1.22
      secs", "cpu_time": "1.19 secs" }
    rowCountExact: Standard DML returns an exact count of rows that were
      modified.
    rowCountLowerBound: Partitioned DML does not offer exactly-once semantics,
      so it returns a lower bound of the rows modified.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class QueryStatsValue(_messages.Message):
    r"""Aggregated statistics from the execution of the query. Only present
    when the query is profiled. For example, a query could return the
    statistics as follows: { "rows_returned": "3", "elapsed_time": "1.22
    secs", "cpu_time": "1.19 secs" }

    Messages:
      AdditionalProperty: An additional property for a QueryStatsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a QueryStatsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  queryPlan = _messages.MessageField('QueryPlan', 1)
  queryStats = _messages.MessageField('QueryStatsValue', 2)
  rowCountExact = _messages.IntegerField(3)
  rowCountLowerBound = _messages.IntegerField(4)


class RollbackRequest(_messages.Message):
  r"""The request for Rollback.

  Fields:
    transactionId: Required. The transaction to roll back.
  """

  transactionId = _messages.BytesField(1)


class Scan(_messages.Message):
  r"""Scan is a structure which describes Cloud Key Visualizer scan
  information.

  Messages:
    DetailsValue: Additional information provided by the implementer.

  Fields:
    details: Additional information provided by the implementer.
    endTime: The upper bound for when the scan is defined.
    name: The unique name of the scan, specific to the Database service
      implementing this interface.
    scanData: Output only. Cloud Key Visualizer scan data. Note, this field is
      not available to the ListScans method.
    startTime: A range of time (inclusive) for when the scan is defined. The
      lower bound for when the scan is defined.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValue(_messages.Message):
    r"""Additional information provided by the implementer.

    Messages:
      AdditionalProperty: An additional property for a DetailsValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  details = _messages.MessageField('DetailsValue', 1)
  endTime = _messages.StringField(2)
  name = _messages.StringField(3)
  scanData = _messages.MessageField('ScanData', 4)
  startTime = _messages.StringField(5)


class ScanData(_messages.Message):
  r"""ScanData contains Cloud Key Visualizer scan data used by the caller to
  construct a visualization.

  Fields:
    data: Cloud Key Visualizer scan data. The range of time this information
      covers is captured via the above time range fields. Note, this field is
      not available to the ListScans method.
    endTime: The upper bound for when the contained data is defined.
    startTime: A range of time (inclusive) for when the contained data is
      defined. The lower bound for when the contained data is defined.
  """

  data = _messages.MessageField('VisualizationData', 1)
  endTime = _messages.StringField(2)
  startTime = _messages.StringField(3)


class Session(_messages.Message):
  r"""A session in the Cloud Spanner API.

  Messages:
    LabelsValue: The labels for the session. * Label keys must be between 1
      and 63 characters long and must conform to the following regular
      expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. * Label values must be between
      0 and 63 characters long and must conform to the regular expression
      `([a-z]([-a-z0-9]*[a-z0-9])?)?`. * No more than 64 labels can be
      associated with a given session. See https://goo.gl/xmQnxf for more
      information on and examples of labels.

  Fields:
    approximateLastUseTime: Output only. The approximate timestamp when the
      session is last used. It is typically earlier than the actual last use
      time.
    createTime: Output only. The timestamp when the session is created.
    creatorRole: The database role which created this session.
    labels: The labels for the session. * Label keys must be between 1 and 63
      characters long and must conform to the following regular expression:
      `[a-z]([-a-z0-9]*[a-z0-9])?`. * Label values must be between 0 and 63
      characters long and must conform to the regular expression
      `([a-z]([-a-z0-9]*[a-z0-9])?)?`. * No more than 64 labels can be
      associated with a given session. See https://goo.gl/xmQnxf for more
      information on and examples of labels.
    name: Output only. The name of the session. This is always system-
      assigned.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels for the session. * Label keys must be between 1 and 63
    characters long and must conform to the following regular expression:
    `[a-z]([-a-z0-9]*[a-z0-9])?`. * Label values must be between 0 and 63
    characters long and must conform to the regular expression
    `([a-z]([-a-z0-9]*[a-z0-9])?)?`. * No more than 64 labels can be
    associated with a given session. See https://goo.gl/xmQnxf for more
    information on and examples of labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  approximateLastUseTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  creatorRole = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class ShortRepresentation(_messages.Message):
  r"""Condensed representation of a node and its subtree. Only present for
  `SCALAR` PlanNode(s).

  Messages:
    SubqueriesValue: A mapping of (subquery variable name) -> (subquery node
      id) for cases where the `description` string of this node references a
      `SCALAR` subquery contained in the expression subtree rooted at this
      node. The referenced `SCALAR` subquery may not necessarily be a direct
      child of this node.

  Fields:
    description: A string representation of the expression subtree rooted at
      this node.
    subqueries: A mapping of (subquery variable name) -> (subquery node id)
      for cases where the `description` string of this node references a
      `SCALAR` subquery contained in the expression subtree rooted at this
      node. The referenced `SCALAR` subquery may not necessarily be a direct
      child of this node.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubqueriesValue(_messages.Message):
    r"""A mapping of (subquery variable name) -> (subquery node id) for cases
    where the `description` string of this node references a `SCALAR` subquery
    contained in the expression subtree rooted at this node. The referenced
    `SCALAR` subquery may not necessarily be a direct child of this node.

    Messages:
      AdditionalProperty: An additional property for a SubqueriesValue object.

    Fields:
      additionalProperties: Additional properties of type SubqueriesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubqueriesValue object.

      Fields:
        key: Name of the additional property.
        value: A integer attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2, variant=_messages.Variant.INT32)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  subqueries = _messages.MessageField('SubqueriesValue', 2)


class SpannerProjectsInstanceConfigOperationsListRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigOperationsListRequest object.

  Fields:
    filter: An expression that filters the list of returned operations. A
      filter expression consists of a field name, a comparison operator, and a
      value for filtering. The value must be a string, a number, or a boolean.
      The comparison operator must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`,
      or `:`. Colon `:` is the contains operator. Filter rules are not case
      sensitive. The following fields in the Operation are eligible for
      filtering: * `name` - The name of the long-running operation * `done` -
      False if the operation is in progress, else true. * `metadata.@type` -
      the type of metadata. For example, the type string for
      CreateInstanceConfigMetadata is `type.googleapis.com/google.spanner.admi
      n.instance.v1.CreateInstanceConfigMetadata`. * `metadata.` - any field
      in metadata.value. `metadata.@type` must be specified first, if
      filtering on metadata fields. * `error` - Error associated with the
      long-running operation. * `response.@type` - the type of response. *
      `response.` - any field in response.value. You can combine multiple
      expressions by enclosing each expression in parentheses. By default,
      expressions are combined with AND logic. However, you can specify AND,
      OR, and NOT logic explicitly. Here are a few examples: * `done:true` -
      The operation is complete. * `(metadata.@type=` \ `type.googleapis.com/g
      oogle.spanner.admin.instance.v1.CreateInstanceConfigMetadata) AND` \
      `(metadata.instance_config.name:custom-config) AND` \
      `(metadata.progress.start_time < \"2021-03-28T14:50:00Z\") AND` \
      `(error:*)` - Return operations where: * The operation's metadata type
      is CreateInstanceConfigMetadata. * The instance config name contains
      "custom-config". * The operation started before 2021-03-28T14:50:00Z. *
      The operation resulted in an error.
    pageSize: Number of operations to be returned in the response. If 0 or
      less, defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListInstanceConfigOperationsResponse to the same
      `parent` and with the same `filter`.
    parent: Required. The project of the instance config operations. Values
      are of the form `projects/`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SpannerProjectsInstanceConfigsCreateRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigsCreateRequest object.

  Fields:
    createInstanceConfigRequest: A CreateInstanceConfigRequest resource to be
      passed as the request body.
    parent: Required. The name of the project in which to create the instance
      config. Values are of the form `projects/`.
  """

  createInstanceConfigRequest = _messages.MessageField('CreateInstanceConfigRequest', 1)
  parent = _messages.StringField(2, required=True)


class SpannerProjectsInstanceConfigsDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigsDeleteRequest object.

  Fields:
    etag: Used for optimistic concurrency control as a way to help prevent
      simultaneous deletes of an instance config from overwriting each other.
      If not empty, the API only deletes the instance config when the etag
      provided matches the current status of the requested instance config.
      Otherwise, deletes the instance config without checking the current
      status of the requested instance config.
    name: Required. The name of the instance configuration to be deleted.
      Values are of the form `projects//instanceConfigs/`
    validateOnly: An option to validate, but not actually execute, a request,
      and provide the same response.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class SpannerProjectsInstanceConfigsGetRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigsGetRequest object.

  Fields:
    name: Required. The name of the requested instance configuration. Values
      are of the form `projects//instanceConfigs/`.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstanceConfigsListRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigsListRequest object.

  Fields:
    pageSize: Number of instance configurations to be returned in the
      response. If 0 or less, defaults to the server's maximum allowed page
      size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListInstanceConfigsResponse.
    parent: Required. The name of the project for which a list of supported
      instance configurations is requested. Values are of the form
      `projects/`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SpannerProjectsInstanceConfigsOperationsCancelRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstanceConfigsOperationsDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstanceConfigsOperationsGetRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstanceConfigsOperationsListRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SpannerProjectsInstanceConfigsPatchRequest(_messages.Message):
  r"""A SpannerProjectsInstanceConfigsPatchRequest object.

  Fields:
    name: A unique identifier for the instance configuration. Values are of
      the form `projects//instanceConfigs/a-z*`.
    updateInstanceConfigRequest: A UpdateInstanceConfigRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateInstanceConfigRequest = _messages.MessageField('UpdateInstanceConfigRequest', 2)


class SpannerProjectsInstancesBackupOperationsListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupOperationsListRequest object.

  Fields:
    filter: An expression that filters the list of returned backup operations.
      A filter expression consists of a field name, a comparison operator, and
      a value for filtering. The value must be a string, a number, or a
      boolean. The comparison operator must be one of: `<`, `>`, `<=`, `>=`,
      `!=`, `=`, or `:`. Colon `:` is the contains operator. Filter rules are
      not case sensitive. The following fields in the operation are eligible
      for filtering: * `name` - The name of the long-running operation *
      `done` - False if the operation is in progress, else true. *
      `metadata.@type` - the type of metadata. For example, the type string
      for CreateBackupMetadata is `type.googleapis.com/google.spanner.admin.da
      tabase.v1.CreateBackupMetadata`. * `metadata.` - any field in
      metadata.value. `metadata.@type` must be specified first if filtering on
      metadata fields. * `error` - Error associated with the long-running
      operation. * `response.@type` - the type of response. * `response.` -
      any field in response.value. You can combine multiple expressions by
      enclosing each expression in parentheses. By default, expressions are
      combined with AND logic, but you can specify AND, OR, and NOT logic
      explicitly. Here are a few examples: * `done:true` - The operation is
      complete. * `(metadata.@type=type.googleapis.com/google.spanner.admin.da
      tabase.v1.CreateBackupMetadata) AND` \ `metadata.database:prod` -
      Returns operations where: * The operation's metadata type is
      CreateBackupMetadata. * The source database name of backup contains the
      string "prod". * `(metadata.@type=type.googleapis.com/google.spanner.adm
      in.database.v1.CreateBackupMetadata) AND` \ `(metadata.name:howl) AND` \
      `(metadata.progress.start_time < \"2018-03-28T14:50:00Z\") AND` \
      `(error:*)` - Returns operations where: * The operation's metadata type
      is CreateBackupMetadata. * The backup name contains the string "howl". *
      The operation started before 2018-03-28T14:50:00Z. * The operation
      resulted in an error. * `(metadata.@type=type.googleapis.com/google.span
      ner.admin.database.v1.CopyBackupMetadata) AND` \
      `(metadata.source_backup:test) AND` \ `(metadata.progress.start_time <
      \"2022-01-18T14:50:00Z\") AND` \ `(error:*)` - Returns operations where:
      * The operation's metadata type is CopyBackupMetadata. * The source
      backup name contains the string "test". * The operation started before
      2022-01-18T14:50:00Z. * The operation resulted in an error. * `((metadat
      a.@type=type.googleapis.com/google.spanner.admin.database.v1.CreateBacku
      pMetadata) AND` \ `(metadata.database:test_db)) OR` \ `((metadata.@type=
      type.googleapis.com/google.spanner.admin.database.v1.CopyBackupMetadata)
      AND` \ `(metadata.source_backup:test_bkp)) AND` \ `(error:*)` - Returns
      operations where: * The operation's metadata matches either of criteria:
      * The operation's metadata type is CreateBackupMetadata AND the source
      database name of the backup contains the string "test_db" * The
      operation's metadata type is CopyBackupMetadata AND the source backup
      name contains the string "test_bkp" * The operation resulted in an
      error.
    pageSize: Number of operations to be returned in the response. If 0 or
      less, defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListBackupOperationsResponse to the same `parent` and
      with the same `filter`.
    parent: Required. The instance of the backup operations. Values are of the
      form `projects//instances/`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SpannerProjectsInstancesBackupsCopyRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsCopyRequest object.

  Fields:
    copyBackupRequest: A CopyBackupRequest resource to be passed as the
      request body.
    parent: Required. The name of the destination instance that will contain
      the backup copy. Values are of the form: `projects//instances/`.
  """

  copyBackupRequest = _messages.MessageField('CopyBackupRequest', 1)
  parent = _messages.StringField(2, required=True)


class SpannerProjectsInstancesBackupsCreateRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsCreateRequest object.

  Enums:
    EncryptionConfigEncryptionTypeValueValuesEnum: Required. The encryption
      type of the backup.

  Fields:
    backup: A Backup resource to be passed as the request body.
    backupId: Required. The id of the backup to be created. The `backup_id`
      appended to `parent` forms the full backup name of the form
      `projects//instances//backups/`.
    encryptionConfig_encryptionType: Required. The encryption type of the
      backup.
    encryptionConfig_kmsKeyName: Optional. The Cloud KMS key that will be used
      to protect the backup. This field should be set only when
      encryption_type is `CUSTOMER_MANAGED_ENCRYPTION`. Values are of the form
      `projects//locations//keyRings//cryptoKeys/`.
    parent: Required. The name of the instance in which the backup will be
      created. This must be the same instance that contains the database the
      backup will be created from. The backup will be stored in the
      location(s) specified in the instance configuration of this instance.
      Values are of the form `projects//instances/`.
  """

  class EncryptionConfigEncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Required. The encryption type of the backup.

    Values:
      ENCRYPTION_TYPE_UNSPECIFIED: Unspecified. Do not use.
      USE_DATABASE_ENCRYPTION: Use the same encryption configuration as the
        database. This is the default option when encryption_config is empty.
        For example, if the database is using `Customer_Managed_Encryption`,
        the backup will be using the same Cloud KMS key as the database.
      GOOGLE_DEFAULT_ENCRYPTION: Use Google default encryption.
      CUSTOMER_MANAGED_ENCRYPTION: Use customer managed encryption. If
        specified, `kms_key_name` must contain a valid Cloud KMS key.
    """
    ENCRYPTION_TYPE_UNSPECIFIED = 0
    USE_DATABASE_ENCRYPTION = 1
    GOOGLE_DEFAULT_ENCRYPTION = 2
    CUSTOMER_MANAGED_ENCRYPTION = 3

  backup = _messages.MessageField('Backup', 1)
  backupId = _messages.StringField(2)
  encryptionConfig_encryptionType = _messages.EnumField('EncryptionConfigEncryptionTypeValueValuesEnum', 3)
  encryptionConfig_kmsKeyName = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SpannerProjectsInstancesBackupsDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsDeleteRequest object.

  Fields:
    name: Required. Name of the backup to delete. Values are of the form
      `projects//instances//backups/`.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesBackupsGetIamPolicyRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The Cloud Spanner resource for which the policy is
      being retrieved. The format is `projects//instances/` for instance
      resources and `projects//instances//databases/` for database resources.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class SpannerProjectsInstancesBackupsGetRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsGetRequest object.

  Fields:
    name: Required. Name of the backup. Values are of the form
      `projects//instances//backups/`.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesBackupsListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsListRequest object.

  Fields:
    filter: An expression that filters the list of returned backups. A filter
      expression consists of a field name, a comparison operator, and a value
      for filtering. The value must be a string, a number, or a boolean. The
      comparison operator must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or
      `:`. Colon `:` is the contains operator. Filter rules are not case
      sensitive. The following fields in the Backup are eligible for
      filtering: * `name` * `database` * `state` * `create_time` (and values
      are of the format YYYY-MM-DDTHH:MM:SSZ) * `expire_time` (and values are
      of the format YYYY-MM-DDTHH:MM:SSZ) * `version_time` (and values are of
      the format YYYY-MM-DDTHH:MM:SSZ) * `size_bytes` You can combine multiple
      expressions by enclosing each expression in parentheses. By default,
      expressions are combined with AND logic, but you can specify AND, OR,
      and NOT logic explicitly. Here are a few examples: * `name:Howl` - The
      backup's name contains the string "howl". * `database:prod` - The
      database's name contains the string "prod". * `state:CREATING` - The
      backup is pending creation. * `state:READY` - The backup is fully
      created and ready for use. * `(name:howl) AND (create_time <
      \"2018-03-28T14:50:00Z\")` - The backup name contains the string "howl"
      and `create_time` of the backup is before 2018-03-28T14:50:00Z. *
      `expire_time < \"2018-03-28T14:50:00Z\"` - The backup `expire_time` is
      before 2018-03-28T14:50:00Z. * `size_bytes > 10000000000` - The backup's
      size is greater than 10GB
    pageSize: Number of backups to be returned in the response. If 0 or less,
      defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListBackupsResponse to the same `parent` and with the
      same `filter`.
    parent: Required. The instance to list backups from. Values are of the
      form `projects//instances/`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SpannerProjectsInstancesBackupsOperationsCancelRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesBackupsOperationsDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesBackupsOperationsGetRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesBackupsOperationsListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SpannerProjectsInstancesBackupsPatchRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsPatchRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    name: Output only for the CreateBackup operation. Required for the
      UpdateBackup operation. A globally unique identifier for the backup
      which cannot be changed. Values are of the form
      `projects//instances//backups/a-z*[a-z0-9]` The final segment of the
      name must be between 2 and 60 characters in length. The backup is stored
      in the location(s) specified in the instance configuration of the
      instance containing the backup, identified by the prefix of the backup
      name of the form `projects//instances/`.
    updateMask: Required. A mask specifying which fields (e.g. `expire_time`)
      in the Backup resource should be updated. This mask is relative to the
      Backup resource, not to the request message. The field mask must always
      be specified; this prevents any future fields from being erased
      accidentally by clients that do not know about them.
  """

  backup = _messages.MessageField('Backup', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SpannerProjectsInstancesBackupsSetIamPolicyRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The Cloud Spanner resource for which the policy is
      being set. The format is `projects//instances/` for instance resources
      and `projects//instances//databases/` for databases resources.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class SpannerProjectsInstancesBackupsTestIamPermissionsRequest(_messages.Message):
  r"""A SpannerProjectsInstancesBackupsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The Cloud Spanner resource for which permissions are
      being tested. The format is `projects//instances/` for instance
      resources and `projects//instances//databases/` for database resources.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SpannerProjectsInstancesCreateRequest(_messages.Message):
  r"""A SpannerProjectsInstancesCreateRequest object.

  Fields:
    createInstanceRequest: A CreateInstanceRequest resource to be passed as
      the request body.
    parent: Required. The name of the project in which to create the instance.
      Values are of the form `projects/`.
  """

  createInstanceRequest = _messages.MessageField('CreateInstanceRequest', 1)
  parent = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabaseOperationsListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabaseOperationsListRequest object.

  Fields:
    filter: An expression that filters the list of returned operations. A
      filter expression consists of a field name, a comparison operator, and a
      value for filtering. The value must be a string, a number, or a boolean.
      The comparison operator must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`,
      or `:`. Colon `:` is the contains operator. Filter rules are not case
      sensitive. The following fields in the Operation are eligible for
      filtering: * `name` - The name of the long-running operation * `done` -
      False if the operation is in progress, else true. * `metadata.@type` -
      the type of metadata. For example, the type string for
      RestoreDatabaseMetadata is `type.googleapis.com/google.spanner.admin.dat
      abase.v1.RestoreDatabaseMetadata`. * `metadata.` - any field in
      metadata.value. `metadata.@type` must be specified first, if filtering
      on metadata fields. * `error` - Error associated with the long-running
      operation. * `response.@type` - the type of response. * `response.` -
      any field in response.value. You can combine multiple expressions by
      enclosing each expression in parentheses. By default, expressions are
      combined with AND logic. However, you can specify AND, OR, and NOT logic
      explicitly. Here are a few examples: * `done:true` - The operation is
      complete. * `(metadata.@type=type.googleapis.com/google.spanner.admin.da
      tabase.v1.RestoreDatabaseMetadata) AND` \ `(metadata.source_type:BACKUP)
      AND` \ `(metadata.backup_info.backup:backup_howl) AND` \
      `(metadata.name:restored_howl) AND` \ `(metadata.progress.start_time <
      \"2018-03-28T14:50:00Z\") AND` \ `(error:*)` - Return operations where:
      * The operation's metadata type is RestoreDatabaseMetadata. * The
      database is restored from a backup. * The backup name contains
      "backup_howl". * The restored database's name contains "restored_howl".
      * The operation started before 2018-03-28T14:50:00Z. * The operation
      resulted in an error.
    pageSize: Number of operations to be returned in the response. If 0 or
      less, defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListDatabaseOperationsResponse to the same `parent` and
      with the same `filter`.
    parent: Required. The instance of the database operations. Values are of
      the form `projects//instances/`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SpannerProjectsInstancesDatabasesCreateRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesCreateRequest object.

  Fields:
    createDatabaseRequest: A CreateDatabaseRequest resource to be passed as
      the request body.
    parent: Required. The name of the instance that will serve the new
      database. Values are of the form `projects//instances/`.
  """

  createDatabaseRequest = _messages.MessageField('CreateDatabaseRequest', 1)
  parent = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesDatabaseRolesListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesDatabaseRolesListRequest object.

  Fields:
    pageSize: Number of database roles to be returned in the response. If 0 or
      less, defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListDatabaseRolesResponse.
    parent: Required. The database whose roles should be listed. Values are of
      the form `projects//instances//databases/`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SpannerProjectsInstancesDatabasesDatabaseRolesTestIamPermissionsRequest(_messages.Message):
  r"""A
  SpannerProjectsInstancesDatabasesDatabaseRolesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The Cloud Spanner resource for which permissions are
      being tested. The format is `projects//instances/` for instance
      resources and `projects//instances//databases/` for database resources.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SpannerProjectsInstancesDatabasesDropDatabaseRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesDropDatabaseRequest object.

  Fields:
    database: Required. The database to be dropped.
  """

  database = _messages.StringField(1, required=True)


class SpannerProjectsInstancesDatabasesGetDdlRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesGetDdlRequest object.

  Fields:
    database: Required. The database whose schema we wish to get. Values are
      of the form `projects//instances//databases/`
  """

  database = _messages.StringField(1, required=True)


class SpannerProjectsInstancesDatabasesGetIamPolicyRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The Cloud Spanner resource for which the policy is
      being retrieved. The format is `projects//instances/` for instance
      resources and `projects//instances//databases/` for database resources.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesGetRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesGetRequest object.

  Fields:
    name: Required. The name of the requested database. Values are of the form
      `projects//instances//databases/`.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesDatabasesGetScansRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesGetScansRequest object.

  Enums:
    ViewValueValuesEnum: Specifies which parts of the Scan should be returned
      in the response. Note, if left unspecified, the FULL view is assumed.

  Fields:
    endTime: The upper bound for the time range to retrieve Scan data for.
    name: Required. The unique name of the scan containing the requested
      information, specific to the Database service implementing this
      interface.
    startTime: These fields restrict the Database-specific information
      returned in the `Scan.data` field. If a `View` is provided that does not
      include the `Scan.data` field, these are ignored. This range of time
      must be entirely contained within the defined time range of the targeted
      scan. The lower bound for the time range to retrieve Scan data for.
    view: Specifies which parts of the Scan should be returned in the
      response. Note, if left unspecified, the FULL view is assumed.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which parts of the Scan should be returned in the response.
    Note, if left unspecified, the FULL view is assumed.

    Values:
      VIEW_UNSPECIFIED: Not specified, equivalent to SUMMARY.
      SUMMARY: Server responses only include `name`, `details`, `start_time`
        and `end_time`. The default value. Note, the ListScans method may only
        use this view type, others view types are not supported.
      FULL: Full representation of the scan is returned in the server
        response, including `data`.
    """
    VIEW_UNSPECIFIED = 0
    SUMMARY = 1
    FULL = 2

  endTime = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  startTime = _messages.StringField(3)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class SpannerProjectsInstancesDatabasesListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesListRequest object.

  Fields:
    pageSize: Number of databases to be returned in the response. If 0 or
      less, defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListDatabasesResponse.
    parent: Required. The instance whose databases should be listed. Values
      are of the form `projects//instances/`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SpannerProjectsInstancesDatabasesOperationsCancelRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesDatabasesOperationsDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesDatabasesOperationsGetRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesDatabasesOperationsListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SpannerProjectsInstancesDatabasesPatchRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesPatchRequest object.

  Fields:
    database: A Database resource to be passed as the request body.
    name: Required. The name of the database. Values are of the form
      `projects//instances//databases/`, where `` is as specified in the
      `CREATE DATABASE` statement. This name can be passed to other API
      methods to identify the database.
    updateMask: Required. The list of fields to update. Currently, only
      `enable_drop_protection` field can be updated.
  """

  database = _messages.MessageField('Database', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SpannerProjectsInstancesDatabasesRestoreRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesRestoreRequest object.

  Fields:
    parent: Required. The name of the instance in which to create the restored
      database. This instance must be in the same project and have the same
      instance configuration as the instance containing the source backup.
      Values are of the form `projects//instances/`.
    restoreDatabaseRequest: A RestoreDatabaseRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  restoreDatabaseRequest = _messages.MessageField('RestoreDatabaseRequest', 2)


class SpannerProjectsInstancesDatabasesSessionsBatchCreateRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsBatchCreateRequest object.

  Fields:
    batchCreateSessionsRequest: A BatchCreateSessionsRequest resource to be
      passed as the request body.
    database: Required. The database in which the new sessions are created.
  """

  batchCreateSessionsRequest = _messages.MessageField('BatchCreateSessionsRequest', 1)
  database = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsBeginTransactionRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsBeginTransactionRequest
  object.

  Fields:
    beginTransactionRequest: A BeginTransactionRequest resource to be passed
      as the request body.
    session: Required. The session in which the transaction runs.
  """

  beginTransactionRequest = _messages.MessageField('BeginTransactionRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsCommitRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsCommitRequest object.

  Fields:
    commitRequest: A CommitRequest resource to be passed as the request body.
    session: Required. The session in which the transaction to be committed is
      running.
  """

  commitRequest = _messages.MessageField('CommitRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsCreateRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsCreateRequest object.

  Fields:
    createSessionRequest: A CreateSessionRequest resource to be passed as the
      request body.
    database: Required. The database in which the new session is created.
  """

  createSessionRequest = _messages.MessageField('CreateSessionRequest', 1)
  database = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsDeleteRequest object.

  Fields:
    name: Required. The name of the session to delete.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesDatabasesSessionsExecuteBatchDmlRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsExecuteBatchDmlRequest
  object.

  Fields:
    executeBatchDmlRequest: A ExecuteBatchDmlRequest resource to be passed as
      the request body.
    session: Required. The session in which the DML statements should be
      performed.
  """

  executeBatchDmlRequest = _messages.MessageField('ExecuteBatchDmlRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsExecuteSqlRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsExecuteSqlRequest object.

  Fields:
    executeSqlRequest: A ExecuteSqlRequest resource to be passed as the
      request body.
    session: Required. The session in which the SQL query should be performed.
  """

  executeSqlRequest = _messages.MessageField('ExecuteSqlRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsExecuteStreamingSqlRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsExecuteStreamingSqlRequest
  object.

  Fields:
    executeSqlRequest: A ExecuteSqlRequest resource to be passed as the
      request body.
    session: Required. The session in which the SQL query should be performed.
  """

  executeSqlRequest = _messages.MessageField('ExecuteSqlRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsGetRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsGetRequest object.

  Fields:
    name: Required. The name of the session to retrieve.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesDatabasesSessionsListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsListRequest object.

  Fields:
    database: Required. The database in which to list sessions.
    filter: An expression for filtering the results of the request. Filter
      rules are case insensitive. The fields eligible for filtering are: *
      `labels.key` where key is the name of a label Some examples of using
      filters are: * `labels.env:*` --> The session has the label "env". *
      `labels.env:dev` --> The session has the label "env" and the value of
      the label contains the string "dev".
    pageSize: Number of sessions to be returned in the response. If 0 or less,
      defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListSessionsResponse.
  """

  database = _messages.StringField(1, required=True)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SpannerProjectsInstancesDatabasesSessionsPartitionQueryRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsPartitionQueryRequest object.

  Fields:
    partitionQueryRequest: A PartitionQueryRequest resource to be passed as
      the request body.
    session: Required. The session used to create the partitions.
  """

  partitionQueryRequest = _messages.MessageField('PartitionQueryRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsPartitionReadRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsPartitionReadRequest object.

  Fields:
    partitionReadRequest: A PartitionReadRequest resource to be passed as the
      request body.
    session: Required. The session used to create the partitions.
  """

  partitionReadRequest = _messages.MessageField('PartitionReadRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsReadRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsReadRequest object.

  Fields:
    readRequest: A ReadRequest resource to be passed as the request body.
    session: Required. The session in which the read should be performed.
  """

  readRequest = _messages.MessageField('ReadRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsRollbackRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsRollbackRequest object.

  Fields:
    rollbackRequest: A RollbackRequest resource to be passed as the request
      body.
    session: Required. The session in which the transaction to roll back is
      running.
  """

  rollbackRequest = _messages.MessageField('RollbackRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSessionsStreamingReadRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSessionsStreamingReadRequest object.

  Fields:
    readRequest: A ReadRequest resource to be passed as the request body.
    session: Required. The session in which the read should be performed.
  """

  readRequest = _messages.MessageField('ReadRequest', 1)
  session = _messages.StringField(2, required=True)


class SpannerProjectsInstancesDatabasesSetIamPolicyRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The Cloud Spanner resource for which the policy is
      being set. The format is `projects//instances/` for instance resources
      and `projects//instances//databases/` for databases resources.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class SpannerProjectsInstancesDatabasesTestIamPermissionsRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The Cloud Spanner resource for which permissions are
      being tested. The format is `projects//instances/` for instance
      resources and `projects//instances//databases/` for database resources.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SpannerProjectsInstancesDatabasesUpdateDdlRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDatabasesUpdateDdlRequest object.

  Fields:
    database: Required. The database to update.
    updateDatabaseDdlRequest: A UpdateDatabaseDdlRequest resource to be passed
      as the request body.
  """

  database = _messages.StringField(1, required=True)
  updateDatabaseDdlRequest = _messages.MessageField('UpdateDatabaseDdlRequest', 2)


class SpannerProjectsInstancesDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstancesDeleteRequest object.

  Fields:
    name: Required. The name of the instance to be deleted. Values are of the
      form `projects//instances/`
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesGetIamPolicyRequest(_messages.Message):
  r"""A SpannerProjectsInstancesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The Cloud Spanner resource for which the policy is
      being retrieved. The format is `projects//instances/` for instance
      resources and `projects//instances//databases/` for database resources.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class SpannerProjectsInstancesGetRequest(_messages.Message):
  r"""A SpannerProjectsInstancesGetRequest object.

  Fields:
    fieldMask: If field_mask is present, specifies the subset of Instance
      fields that should be returned. If absent, all Instance fields are
      returned.
    name: Required. The name of the requested instance. Values are of the form
      `projects//instances/`.
  """

  fieldMask = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class SpannerProjectsInstancesInstancePartitionsCreateRequest(_messages.Message):
  r"""A SpannerProjectsInstancesInstancePartitionsCreateRequest object.

  Fields:
    createInstancePartitionRequest: A CreateInstancePartitionRequest resource
      to be passed as the request body.
    parent: Required. The name of the instance in which to create the instance
      partition. Values are of the form `projects//instances/`.
  """

  createInstancePartitionRequest = _messages.MessageField('CreateInstancePartitionRequest', 1)
  parent = _messages.StringField(2, required=True)


class SpannerProjectsInstancesInstancePartitionsDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstancesInstancePartitionsDeleteRequest object.

  Fields:
    etag: Optional. If not empty, the API only deletes the instance partition
      when the etag provided matches the current status of the requested
      instance partition. Otherwise, deletes the instance partition without
      checking the current status of the requested instance partition.
    name: Required. The name of the instance partition to be deleted. Values
      are of the form `projects/{project}/instances/{instance}/instancePartiti
      ons/{instance_partition}`
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class SpannerProjectsInstancesInstancePartitionsGetRequest(_messages.Message):
  r"""A SpannerProjectsInstancesInstancePartitionsGetRequest object.

  Fields:
    name: Required. The name of the requested instance partition. Values are
      of the form `projects/{project}/instances/{instance}/instancePartitions/
      {instance_partition}`.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesInstancePartitionsListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesInstancePartitionsListRequest object.

  Fields:
    pageSize: Number of instancePartitions to be returned in the response. If
      0 or less, defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListInstancePartitionsResponse.
    parent: Required. The instance whose instance partitions should be listed.
      Values are of the form `projects//instances/`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SpannerProjectsInstancesInstancePartitionsOperationsCancelRequest(_messages.Message):
  r"""A SpannerProjectsInstancesInstancePartitionsOperationsCancelRequest
  object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesInstancePartitionsOperationsDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstancesInstancePartitionsOperationsDeleteRequest
  object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesInstancePartitionsOperationsGetRequest(_messages.Message):
  r"""A SpannerProjectsInstancesInstancePartitionsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesInstancePartitionsOperationsListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesInstancePartitionsOperationsListRequest
  object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SpannerProjectsInstancesInstancePartitionsPatchRequest(_messages.Message):
  r"""A SpannerProjectsInstancesInstancePartitionsPatchRequest object.

  Fields:
    name: Required. A unique identifier for the instance partition. Values are
      of the form `projects//instances//instancePartitions/a-z*[a-z0-9]`. The
      final segment of the name must be between 2 and 64 characters in length.
      An instance partition's name cannot be changed after the instance
      partition is created.
    updateInstancePartitionRequest: A UpdateInstancePartitionRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateInstancePartitionRequest = _messages.MessageField('UpdateInstancePartitionRequest', 2)


class SpannerProjectsInstancesListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesListRequest object.

  Fields:
    filter: An expression for filtering the results of the request. Filter
      rules are case insensitive. The fields eligible for filtering are: *
      `name` * `display_name` * `labels.key` where key is the name of a label
      Some examples of using filters are: * `name:*` --> The instance has a
      name. * `name:Howl` --> The instance's name contains the string "howl".
      * `name:HOWL` --> Equivalent to above. * `NAME:howl` --> Equivalent to
      above. * `labels.env:*` --> The instance has the label "env". *
      `labels.env:dev` --> The instance has the label "env" and the value of
      the label contains the string "dev". * `name:howl labels.env:dev` -->
      The instance's name contains "howl" and it has the label "env" with its
      value containing "dev".
    instanceDeadline: Deadline used while retrieving metadata for instances.
      Instances whose metadata cannot be retrieved within this deadline will
      be added to unreachable in ListInstancesResponse.
    pageSize: Number of instances to be returned in the response. If 0 or
      less, defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListInstancesResponse.
    parent: Required. The name of the project for which a list of instances is
      requested. Values are of the form `projects/`.
  """

  filter = _messages.StringField(1)
  instanceDeadline = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SpannerProjectsInstancesOperationsCancelRequest(_messages.Message):
  r"""A SpannerProjectsInstancesOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesOperationsDeleteRequest(_messages.Message):
  r"""A SpannerProjectsInstancesOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesOperationsGetRequest(_messages.Message):
  r"""A SpannerProjectsInstancesOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SpannerProjectsInstancesOperationsListRequest(_messages.Message):
  r"""A SpannerProjectsInstancesOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SpannerProjectsInstancesPatchRequest(_messages.Message):
  r"""A SpannerProjectsInstancesPatchRequest object.

  Fields:
    name: Required. A unique identifier for the instance, which cannot be
      changed after the instance is created. Values are of the form
      `projects//instances/a-z*[a-z0-9]`. The final segment of the name must
      be between 2 and 64 characters in length.
    updateInstanceRequest: A UpdateInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  updateInstanceRequest = _messages.MessageField('UpdateInstanceRequest', 2)


class SpannerProjectsInstancesSetIamPolicyRequest(_messages.Message):
  r"""A SpannerProjectsInstancesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The Cloud Spanner resource for which the policy is
      being set. The format is `projects//instances/` for instance resources
      and `projects//instances//databases/` for databases resources.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class SpannerProjectsInstancesTestIamPermissionsRequest(_messages.Message):
  r"""A SpannerProjectsInstancesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The Cloud Spanner resource for which permissions are
      being tested. The format is `projects//instances/` for instance
      resources and `projects//instances//databases/` for database resources.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SpannerScansListRequest(_messages.Message):
  r"""A SpannerScansListRequest object.

  Enums:
    ViewValueValuesEnum: Specifies which parts of the Scan should be returned
      in the response. Note, only the SUMMARY view (the default) is currently
      supported for ListScans.

  Fields:
    filter: A filter expression to restrict the results based on information
      present in the available Scan collection. The filter applies to all
      fields within the Scan message except for `data`.
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The unique name of the parent resource, specific to the
      Database service implementing this interface.
    view: Specifies which parts of the Scan should be returned in the
      response. Note, only the SUMMARY view (the default) is currently
      supported for ListScans.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which parts of the Scan should be returned in the response.
    Note, only the SUMMARY view (the default) is currently supported for
    ListScans.

    Values:
      VIEW_UNSPECIFIED: Not specified, equivalent to SUMMARY.
      SUMMARY: Server responses only include `name`, `details`, `start_time`
        and `end_time`. The default value. Note, the ListScans method may only
        use this view type, others view types are not supported.
      FULL: Full representation of the scan is returned in the server
        response, including `data`.
    """
    VIEW_UNSPECIFIED = 0
    SUMMARY = 1
    FULL = 2

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Statement(_messages.Message):
  r"""A single DML statement.

  Messages:
    ParamTypesValue: It is not always possible for Cloud Spanner to infer the
      right SQL type from a JSON value. For example, values of type `BYTES`
      and values of type `STRING` both appear in params as JSON strings. In
      these cases, `param_types` can be used to specify the exact SQL type for
      some or all of the SQL statement parameters. See the definition of Type
      for more information about SQL types.
    ParamsValue: Parameter names and values that bind to placeholders in the
      DML string. A parameter placeholder consists of the `@` character
      followed by the parameter name (for example, `@firstName`). Parameter
      names can contain letters, numbers, and underscores. Parameters can
      appear anywhere that a literal value is expected. The same parameter
      name can be used more than once, for example: `"WHERE id > @msg_id AND
      id < @msg_id + 100"` It is an error to execute a SQL statement with
      unbound parameters.

  Fields:
    paramTypes: It is not always possible for Cloud Spanner to infer the right
      SQL type from a JSON value. For example, values of type `BYTES` and
      values of type `STRING` both appear in params as JSON strings. In these
      cases, `param_types` can be used to specify the exact SQL type for some
      or all of the SQL statement parameters. See the definition of Type for
      more information about SQL types.
    params: Parameter names and values that bind to placeholders in the DML
      string. A parameter placeholder consists of the `@` character followed
      by the parameter name (for example, `@firstName`). Parameter names can
      contain letters, numbers, and underscores. Parameters can appear
      anywhere that a literal value is expected. The same parameter name can
      be used more than once, for example: `"WHERE id > @msg_id AND id <
      @msg_id + 100"` It is an error to execute a SQL statement with unbound
      parameters.
    sql: Required. The DML string.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamTypesValue(_messages.Message):
    r"""It is not always possible for Cloud Spanner to infer the right SQL
    type from a JSON value. For example, values of type `BYTES` and values of
    type `STRING` both appear in params as JSON strings. In these cases,
    `param_types` can be used to specify the exact SQL type for some or all of
    the SQL statement parameters. See the definition of Type for more
    information about SQL types.

    Messages:
      AdditionalProperty: An additional property for a ParamTypesValue object.

    Fields:
      additionalProperties: Additional properties of type ParamTypesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamTypesValue object.

      Fields:
        key: Name of the additional property.
        value: A Type attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Type', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamsValue(_messages.Message):
    r"""Parameter names and values that bind to placeholders in the DML
    string. A parameter placeholder consists of the `@` character followed by
    the parameter name (for example, `@firstName`). Parameter names can
    contain letters, numbers, and underscores. Parameters can appear anywhere
    that a literal value is expected. The same parameter name can be used more
    than once, for example: `"WHERE id > @msg_id AND id < @msg_id + 100"` It
    is an error to execute a SQL statement with unbound parameters.

    Messages:
      AdditionalProperty: An additional property for a ParamsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  paramTypes = _messages.MessageField('ParamTypesValue', 1)
  params = _messages.MessageField('ParamsValue', 2)
  sql = _messages.StringField(3)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StructType(_messages.Message):
  r"""`StructType` defines the fields of a STRUCT type.

  Fields:
    fields: The list of fields that make up this struct. Order is significant,
      because values of this struct type are represented as lists, where the
      order of field values matches the order of fields in the StructType. In
      turn, the order of fields matches the order of columns in a read
      request, or the order of fields in the `SELECT` clause of a query.
  """

  fields = _messages.MessageField('Field', 1, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: REQUIRED: The set of permissions to check for 'resource'.
      Permissions with wildcards (such as '*', 'spanner.*',
      'spanner.instances.*') are not allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Transaction(_messages.Message):
  r"""A transaction.

  Fields:
    id: `id` may be used to identify the transaction in subsequent Read,
      ExecuteSql, Commit, or Rollback calls. Single-use read-only transactions
      do not have IDs, because single-use transactions do not support multiple
      requests.
    readTimestamp: For snapshot read-only transactions, the read timestamp
      chosen for the transaction. Not returned by default: see
      TransactionOptions.ReadOnly.return_read_timestamp. A timestamp in
      RFC3339 UTC \"Zulu\" format, accurate to nanoseconds. Example:
      `"2014-10-02T15:01:23.045123456Z"`.
  """

  id = _messages.BytesField(1)
  readTimestamp = _messages.StringField(2)


class TransactionOptions(_messages.Message):
  r"""Transactions: Each session can have at most one active transaction at a
  time (note that standalone reads and queries use a transaction internally
  and do count towards the one transaction limit). After the active
  transaction is completed, the session can immediately be re-used for the
  next transaction. It is not necessary to create a new session for each
  transaction. Transaction modes: Cloud Spanner supports three transaction
  modes: 1. Locking read-write. This type of transaction is the only way to
  write data into Cloud Spanner. These transactions rely on pessimistic
  locking and, if necessary, two-phase commit. Locking read-write transactions
  may abort, requiring the application to retry. 2. Snapshot read-only.
  Snapshot read-only transactions provide guaranteed consistency across
  several reads, but do not allow writes. Snapshot read-only transactions can
  be configured to read at timestamps in the past, or configured to perform a
  strong read (where Spanner will select a timestamp such that the read is
  guaranteed to see the effects of all transactions that have committed before
  the start of the read). Snapshot read-only transactions do not need to be
  committed. Queries on change streams must be performed with the snapshot
  read-only transaction mode, specifying a strong read. Please see
  TransactionOptions.ReadOnly.strong for more details. 3. Partitioned DML.
  This type of transaction is used to execute a single Partitioned DML
  statement. Partitioned DML partitions the key space and runs the DML
  statement over each partition in parallel using separate, internal
  transactions that commit independently. Partitioned DML transactions do not
  need to be committed. For transactions that only read, snapshot read-only
  transactions provide simpler semantics and are almost always faster. In
  particular, read-only transactions do not take locks, so they do not
  conflict with read-write transactions. As a consequence of not taking locks,
  they also do not abort, so retry loops are not needed. Transactions may only
  read-write data in a single database. They may, however, read-write data in
  different tables within that database. Locking read-write transactions:
  Locking transactions may be used to atomically read-modify-write data
  anywhere in a database. This type of transaction is externally consistent.
  Clients should attempt to minimize the amount of time a transaction is
  active. Faster transactions commit with higher probability and cause less
  contention. Cloud Spanner attempts to keep read locks active as long as the
  transaction continues to do reads, and the transaction has not been
  terminated by Commit or Rollback. Long periods of inactivity at the client
  may cause Cloud Spanner to release a transaction's locks and abort it.
  Conceptually, a read-write transaction consists of zero or more reads or SQL
  statements followed by Commit. At any time before Commit, the client can
  send a Rollback request to abort the transaction. Semantics: Cloud Spanner
  can commit the transaction if all read locks it acquired are still valid at
  commit time, and it is able to acquire write locks for all writes. Cloud
  Spanner can abort the transaction for any reason. If a commit attempt
  returns `ABORTED`, Cloud Spanner guarantees that the transaction has not
  modified any user data in Cloud Spanner. Unless the transaction commits,
  Cloud Spanner makes no guarantees about how long the transaction's locks
  were held for. It is an error to use Cloud Spanner locks for any sort of
  mutual exclusion other than between Cloud Spanner transactions themselves.
  Retrying aborted transactions: When a transaction aborts, the application
  can choose to retry the whole transaction again. To maximize the chances of
  successfully committing the retry, the client should execute the retry in
  the same session as the original attempt. The original session's lock
  priority increases with each consecutive abort, meaning that each attempt
  has a slightly better chance of success than the previous. Under some
  circumstances (for example, many transactions attempting to modify the same
  row(s)), a transaction can abort many times in a short period before
  successfully committing. Thus, it is not a good idea to cap the number of
  retries a transaction can attempt; instead, it is better to limit the total
  amount of time spent retrying. Idle transactions: A transaction is
  considered idle if it has no outstanding reads or SQL queries and has not
  started a read or SQL query within the last 10 seconds. Idle transactions
  can be aborted by Cloud Spanner so that they don't hold on to locks
  indefinitely. If an idle transaction is aborted, the commit will fail with
  error `ABORTED`. If this behavior is undesirable, periodically executing a
  simple SQL query in the transaction (for example, `SELECT 1`) prevents the
  transaction from becoming idle. Snapshot read-only transactions: Snapshot
  read-only transactions provides a simpler method than locking read-write
  transactions for doing several consistent reads. However, this type of
  transaction does not support writes. Snapshot transactions do not take
  locks. Instead, they work by choosing a Cloud Spanner timestamp, then
  executing all reads at that timestamp. Since they do not acquire locks, they
  do not block concurrent read-write transactions. Unlike locking read-write
  transactions, snapshot read-only transactions never abort. They can fail if
  the chosen read timestamp is garbage collected; however, the default garbage
  collection policy is generous enough that most applications do not need to
  worry about this in practice. Snapshot read-only transactions do not need to
  call Commit or Rollback (and in fact are not permitted to do so). To execute
  a snapshot transaction, the client specifies a timestamp bound, which tells
  Cloud Spanner how to choose a read timestamp. The types of timestamp bound
  are: - Strong (the default). - Bounded staleness. - Exact staleness. If the
  Cloud Spanner database to be read is geographically distributed, stale read-
  only transactions can execute more quickly than strong or read-write
  transactions, because they are able to execute far from the leader replica.
  Each type of timestamp bound is discussed in detail below. Strong: Strong
  reads are guaranteed to see the effects of all transactions that have
  committed before the start of the read. Furthermore, all rows yielded by a
  single read are consistent with each other -- if any part of the read
  observes a transaction, all parts of the read see the transaction. Strong
  reads are not repeatable: two consecutive strong read-only transactions
  might return inconsistent results if there are concurrent writes. If
  consistency across reads is required, the reads should be executed within a
  transaction or at an exact read timestamp. Queries on change streams (see
  below for more details) must also specify the strong read timestamp bound.
  See TransactionOptions.ReadOnly.strong. Exact staleness: These timestamp
  bounds execute reads at a user-specified timestamp. Reads at a timestamp are
  guaranteed to see a consistent prefix of the global transaction history:
  they observe modifications done by all transactions with a commit timestamp
  less than or equal to the read timestamp, and observe none of the
  modifications done by transactions with a larger commit timestamp. They will
  block until all conflicting transactions that may be assigned commit
  timestamps <= the read timestamp have finished. The timestamp can either be
  expressed as an absolute Cloud Spanner commit timestamp or a staleness
  relative to the current time. These modes do not require a "negotiation
  phase" to pick a timestamp. As a result, they execute slightly faster than
  the equivalent boundedly stale concurrency modes. On the other hand,
  boundedly stale reads usually return fresher results. See
  TransactionOptions.ReadOnly.read_timestamp and
  TransactionOptions.ReadOnly.exact_staleness. Bounded staleness: Bounded
  staleness modes allow Cloud Spanner to pick the read timestamp, subject to a
  user-provided staleness bound. Cloud Spanner chooses the newest timestamp
  within the staleness bound that allows execution of the reads at the closest
  available replica without blocking. All rows yielded are consistent with
  each other -- if any part of the read observes a transaction, all parts of
  the read see the transaction. Boundedly stale reads are not repeatable: two
  stale reads, even if they use the same staleness bound, can execute at
  different timestamps and thus return inconsistent results. Boundedly stale
  reads execute in two phases: the first phase negotiates a timestamp among
  all replicas needed to serve the read. In the second phase, reads are
  executed at the negotiated timestamp. As a result of the two phase
  execution, bounded staleness reads are usually a little slower than
  comparable exact staleness reads. However, they are typically able to return
  fresher results, and are more likely to execute at the closest replica.
  Because the timestamp negotiation requires up-front knowledge of which rows
  will be read, it can only be used with single-use read-only transactions.
  See TransactionOptions.ReadOnly.max_staleness and
  TransactionOptions.ReadOnly.min_read_timestamp. Old read timestamps and
  garbage collection: Cloud Spanner continuously garbage collects deleted and
  overwritten data in the background to reclaim storage space. This process is
  known as "version GC". By default, version GC reclaims versions after they
  are one hour old. Because of this, Cloud Spanner cannot perform reads at
  read timestamps more than one hour in the past. This restriction also
  applies to in-progress reads and/or SQL queries whose timestamp become too
  old while executing. Reads and SQL queries with too-old read timestamps fail
  with the error `FAILED_PRECONDITION`. You can configure and extend the
  `VERSION_RETENTION_PERIOD` of a database up to a period as long as one week,
  which allows Cloud Spanner to perform reads up to one week in the past.
  Querying change Streams: A Change Stream is a schema object that can be
  configured to watch data changes on the entire database, a set of tables, or
  a set of columns in a database. When a change stream is created, Spanner
  automatically defines a corresponding SQL Table-Valued Function (TVF) that
  can be used to query the change records in the associated change stream
  using the ExecuteStreamingSql API. The name of the TVF for a change stream
  is generated from the name of the change stream: READ_. All queries on
  change stream TVFs must be executed using the ExecuteStreamingSql API with a
  single-use read-only transaction with a strong read-only timestamp_bound.
  The change stream TVF allows users to specify the start_timestamp and
  end_timestamp for the time range of interest. All change records within the
  retention period is accessible using the strong read-only timestamp_bound.
  All other TransactionOptions are invalid for change stream queries. In
  addition, if TransactionOptions.read_only.return_read_timestamp is set to
  true, a special value of 2^63 - 2 will be returned in the Transaction
  message that describes the transaction, instead of a valid read timestamp.
  This special value should be discarded and not used for any subsequent
  queries. Please see https://cloud.google.com/spanner/docs/change-streams for
  more details on how to query the change stream TVFs. Partitioned DML
  transactions: Partitioned DML transactions are used to execute DML
  statements with a different execution strategy that provides different, and
  often better, scalability properties for large, table-wide operations than
  DML in a ReadWrite transaction. Smaller scoped statements, such as an OLTP
  workload, should prefer using ReadWrite transactions. Partitioned DML
  partitions the keyspace and runs the DML statement on each partition in
  separate, internal transactions. These transactions commit automatically
  when complete, and run independently from one another. To reduce lock
  contention, this execution strategy only acquires read locks on rows that
  match the WHERE clause of the statement. Additionally, the smaller per-
  partition transactions hold locks for less time. That said, Partitioned DML
  is not a drop-in replacement for standard DML used in ReadWrite
  transactions. - The DML statement must be fully-partitionable. Specifically,
  the statement must be expressible as the union of many statements which each
  access only a single row of the table. - The statement is not applied
  atomically to all rows of the table. Rather, the statement is applied
  atomically to partitions of the table, in independent transactions.
  Secondary index rows are updated atomically with the base table rows. -
  Partitioned DML does not guarantee exactly-once execution semantics against
  a partition. The statement will be applied at least once to each partition.
  It is strongly recommended that the DML statement should be idempotent to
  avoid unexpected results. For instance, it is potentially dangerous to run a
  statement such as `UPDATE table SET column = column + 1` as it could be run
  multiple times against some rows. - The partitions are committed
  automatically - there is no support for Commit or Rollback. If the call
  returns an error, or if the client issuing the ExecuteSql call dies, it is
  possible that some rows had the statement executed on them successfully. It
  is also possible that statement was never executed against other rows. -
  Partitioned DML transactions may only contain the execution of a single DML
  statement via ExecuteSql or ExecuteStreamingSql. - If any error is
  encountered during the execution of the partitioned DML operation (for
  instance, a UNIQUE INDEX violation, division by zero, or a value that cannot
  be stored due to schema constraints), then the operation is stopped at that
  point and an error is returned. It is possible that at this point, some
  partitions have been committed (or even committed multiple times), and other
  partitions have not been run at all. Given the above, Partitioned DML is
  good fit for large, database-wide, operations that are idempotent, such as
  deleting old rows from a very large table.

  Fields:
    partitionedDml: Partitioned DML transaction. Authorization to begin a
      Partitioned DML transaction requires
      `spanner.databases.beginPartitionedDmlTransaction` permission on the
      `session` resource.
    readOnly: Transaction will not write. Authorization to begin a read-only
      transaction requires `spanner.databases.beginReadOnlyTransaction`
      permission on the `session` resource.
    readWrite: Transaction may write. Authorization to begin a read-write
      transaction requires
      `spanner.databases.beginOrRollbackReadWriteTransaction` permission on
      the `session` resource.
  """

  partitionedDml = _messages.MessageField('PartitionedDml', 1)
  readOnly = _messages.MessageField('ReadOnly', 2)
  readWrite = _messages.MessageField('ReadWrite', 3)


class TransactionSelector(_messages.Message):
  r"""This message is used to select the transaction in which a Read or
  ExecuteSql call runs. See TransactionOptions for more information about
  transactions.

  Fields:
    begin: Begin a new transaction and execute this read or SQL query in it.
      The transaction ID of the new transaction is returned in
      ResultSetMetadata.transaction, which is a Transaction.
    id: Execute the read or SQL query in a previously-started transaction.
    singleUse: Execute the read or SQL query in a temporary transaction. This
      is the most efficient way to execute a transaction that consists of a
      single SQL query.
  """

  begin = _messages.MessageField('TransactionOptions', 1)
  id = _messages.BytesField(2)
  singleUse = _messages.MessageField('TransactionOptions', 3)


class Type(_messages.Message):
  r"""`Type` indicates the type of a Cloud Spanner value, as might be stored
  in a table cell or returned from an SQL query.

  Enums:
    CodeValueValuesEnum: Required. The TypeCode for this type.
    TypeAnnotationValueValuesEnum: The TypeAnnotationCode that disambiguates
      SQL type that Spanner will use to represent values of this type during
      query processing. This is necessary for some type codes because a single
      TypeCode can be mapped to different SQL types depending on the SQL
      dialect. type_annotation typically is not needed to process the content
      of a value (it doesn't affect serialization) and clients can ignore it
      on the read path.

  Fields:
    arrayElementType: If code == ARRAY, then `array_element_type` is the type
      of the array elements.
    code: Required. The TypeCode for this type.
    protoTypeFqn: If code == PROTO or code == ENUM, then `proto_type_fqn` is
      the fully qualified name of the proto type representing the proto/enum
      definition.
    structType: If code == STRUCT, then `struct_type` provides type
      information for the struct's fields.
    typeAnnotation: The TypeAnnotationCode that disambiguates SQL type that
      Spanner will use to represent values of this type during query
      processing. This is necessary for some type codes because a single
      TypeCode can be mapped to different SQL types depending on the SQL
      dialect. type_annotation typically is not needed to process the content
      of a value (it doesn't affect serialization) and clients can ignore it
      on the read path.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Required. The TypeCode for this type.

    Values:
      TYPE_CODE_UNSPECIFIED: Not specified.
      BOOL: Encoded as JSON `true` or `false`.
      INT64: Encoded as `string`, in decimal format.
      FLOAT64: Encoded as `number`, or the strings `"NaN"`, `"Infinity"`, or
        `"-Infinity"`.
      TIMESTAMP: Encoded as `string` in RFC 3339 timestamp format. The time
        zone must be present, and must be `"Z"`. If the schema has the column
        option `allow_commit_timestamp=true`, the placeholder string
        `"spanner.commit_timestamp()"` can be used to instruct the system to
        insert the commit timestamp associated with the transaction commit.
      DATE: Encoded as `string` in RFC 3339 date format.
      STRING: Encoded as `string`.
      BYTES: Encoded as a base64-encoded `string`, as described in RFC 4648,
        section 4.
      ARRAY: Encoded as `list`, where the list elements are represented
        according to array_element_type.
      STRUCT: Encoded as `list`, where list element `i` is represented
        according to
        [struct_type.fields[i]][google.spanner.v1.StructType.fields].
      NUMERIC: Encoded as `string`, in decimal format or scientific notation
        format. Decimal format: `[+-]Digits[.[Digits]]` or `+-.Digits`
        Scientific notation:
        `[+-]Digits[.[Digits]][ExponentIndicator[+-]Digits]` or
        `+-.Digits[ExponentIndicator[+-]Digits]` (ExponentIndicator is `"e"`
        or `"E"`)
      JSON: Encoded as a JSON-formatted `string` as described in RFC 7159. The
        following rules are applied when parsing JSON input: - Whitespace
        characters are not preserved. - If a JSON object has duplicate keys,
        only the first key is preserved. - Members of a JSON object are not
        guaranteed to have their order preserved. - JSON array elements will
        have their order preserved.
      PROTO: Encoded as a base64-encoded `string`, as described in RFC 4648,
        section 4.
      ENUM: Encoded as `string`, in decimal format.
    """
    TYPE_CODE_UNSPECIFIED = 0
    BOOL = 1
    INT64 = 2
    FLOAT64 = 3
    TIMESTAMP = 4
    DATE = 5
    STRING = 6
    BYTES = 7
    ARRAY = 8
    STRUCT = 9
    NUMERIC = 10
    JSON = 11
    PROTO = 12
    ENUM = 13

  class TypeAnnotationValueValuesEnum(_messages.Enum):
    r"""The TypeAnnotationCode that disambiguates SQL type that Spanner will
    use to represent values of this type during query processing. This is
    necessary for some type codes because a single TypeCode can be mapped to
    different SQL types depending on the SQL dialect. type_annotation
    typically is not needed to process the content of a value (it doesn't
    affect serialization) and clients can ignore it on the read path.

    Values:
      TYPE_ANNOTATION_CODE_UNSPECIFIED: Not specified.
      INT32: 32-bit signed integer type. This annotation can be used by a
        client interacting with PostgreSQL-enabled Spanner database to specify
        that a value should be treated using the semantics of the INTEGER
        type.
      PG_NUMERIC: PostgreSQL compatible NUMERIC type. This annotation needs to
        be applied to Type instances having NUMERIC type code to specify that
        values of this type should be treated as PostgreSQL NUMERIC values.
        Currently this annotation is always needed for NUMERIC when a client
        interacts with PostgreSQL-enabled Spanner databases.
      PG_JSONB: PostgreSQL compatible JSONB type. This annotation needs to be
        applied to Type instances having JSON type code to specify that values
        of this type should be treated as PostgreSQL JSONB values. Currently
        this annotation is always needed for JSON when a client interacts with
        PostgreSQL-enabled Spanner databases.
      PG_OID: PostgreSQL compatible OID type. This annotation can be used by a
        client interacting with PostgreSQL-enabled Spanner database to specify
        that a value should be treated using the semantics of the OID type.
    """
    TYPE_ANNOTATION_CODE_UNSPECIFIED = 0
    INT32 = 1
    PG_NUMERIC = 2
    PG_JSONB = 3
    PG_OID = 4

  arrayElementType = _messages.MessageField('Type', 1)
  code = _messages.EnumField('CodeValueValuesEnum', 2)
  protoTypeFqn = _messages.StringField(3)
  structType = _messages.MessageField('StructType', 4)
  typeAnnotation = _messages.EnumField('TypeAnnotationValueValuesEnum', 5)


class UpdateDatabaseDdlMetadata(_messages.Message):
  r"""Metadata type for the operation returned by UpdateDatabaseDdl.

  Fields:
    actions: The brief action info for the DDL statements. `actions[i]` is the
      brief info for `statements[i]`.
    commitTimestamps: Reports the commit timestamps of all statements that
      have succeeded so far, where `commit_timestamps[i]` is the commit
      timestamp for the statement `statements[i]`.
    database: The database being modified.
    progress: The progress of the UpdateDatabaseDdl operations. All DDL
      statements will have continuously updating progress, and `progress[i]`
      is the operation progress for `statements[i]`. Also, `progress[i]` will
      have start time and end time populated with commit timestamp of
      operation, as well as a progress of 100% once the operation has
      completed.
    statements: For an update this list contains all the statements. For an
      individual statement, this list contains only that statement.
    throttled: Output only. When true, indicates that the operation is
      throttled e.g. due to resource constraints. When resources become
      available the operation will resume and this field will be false again.
  """

  actions = _messages.MessageField('DdlStatementActionInfo', 1, repeated=True)
  commitTimestamps = _messages.StringField(2, repeated=True)
  database = _messages.StringField(3)
  progress = _messages.MessageField('OperationProgress', 4, repeated=True)
  statements = _messages.StringField(5, repeated=True)
  throttled = _messages.BooleanField(6)


class UpdateDatabaseDdlRequest(_messages.Message):
  r"""Enqueues the given DDL statements to be applied, in order but not
  necessarily all at once, to the database schema at some point (or points) in
  the future. The server checks that the statements are executable
  (syntactically valid, name tables that exist, etc.) before enqueueing them,
  but they may still fail upon later execution (e.g., if a statement from
  another batch of statements is applied first and it conflicts in some way,
  or if there is some data-related problem like a `NULL` value in a column to
  which `NOT NULL` would be added). If a statement fails, all subsequent
  statements in the batch are automatically cancelled. Each batch of
  statements is assigned a name which can be used with the Operations API to
  monitor progress. See the operation_id field for more details.

  Fields:
    operationId: If empty, the new update request is assigned an
      automatically-generated operation ID. Otherwise, `operation_id` is used
      to construct the name of the resulting Operation. Specifying an explicit
      operation ID simplifies determining whether the statements were executed
      in the event that the UpdateDatabaseDdl call is replayed, or the return
      value is otherwise lost: the database and `operation_id` fields can be
      combined to form the name of the resulting longrunning.Operation:
      `/operations/`. `operation_id` should be unique within the database, and
      must be a valid identifier: `a-z*`. Note that automatically-generated
      operation IDs always begin with an underscore. If the named operation
      already exists, UpdateDatabaseDdl returns `ALREADY_EXISTS`.
    protoDescriptors: Optional. Proto descriptors used by CREATE/ALTER PROTO
      BUNDLE statements. Contains a protobuf-serialized [google.protobuf.FileD
      escriptorSet](https://github.com/protocolbuffers/protobuf/blob/main/src/
      google/protobuf/descriptor.proto). To generate it,
      [install](https://grpc.io/docs/protoc-installation/) and run `protoc`
      with --include_imports and --descriptor_set_out. For example, to
      generate for moon/shot/app.proto, run " " " $protoc
      --proto_path=/app_path --proto_path=/lib_path \ --include_imports \
      --descriptor_set_out=descriptors.data \ moon/shot/app.proto " " " For
      more details, see protobuffer [self
      description](https://developers.google.com/protocol-
      buffers/docs/techniques#self-description).
    statements: Required. DDL statements to be applied to the database.
  """

  operationId = _messages.StringField(1)
  protoDescriptors = _messages.BytesField(2)
  statements = _messages.StringField(3, repeated=True)


class UpdateDatabaseMetadata(_messages.Message):
  r"""Metadata type for the operation returned by UpdateDatabase.

  Fields:
    cancelTime: The time at which this operation was cancelled. If set, this
      operation is in the process of undoing itself (which is best-effort).
    progress: The progress of the UpdateDatabase operation.
    request: The request for UpdateDatabase.
  """

  cancelTime = _messages.StringField(1)
  progress = _messages.MessageField('OperationProgress', 2)
  request = _messages.MessageField('UpdateDatabaseRequest', 3)


class UpdateDatabaseRequest(_messages.Message):
  r"""The request for UpdateDatabase.

  Fields:
    database: Required. The database to update. The `name` field of the
      database is of the form `projects//instances//databases/`.
    updateMask: Required. The list of fields to update. Currently, only
      `enable_drop_protection` field can be updated.
  """

  database = _messages.MessageField('Database', 1)
  updateMask = _messages.StringField(2)


class UpdateInstanceConfigMetadata(_messages.Message):
  r"""Metadata type for the operation returned by UpdateInstanceConfig.

  Fields:
    cancelTime: The time at which this operation was cancelled.
    instanceConfig: The desired instance config after updating.
    progress: The progress of the UpdateInstanceConfig operation.
  """

  cancelTime = _messages.StringField(1)
  instanceConfig = _messages.MessageField('InstanceConfig', 2)
  progress = _messages.MessageField('InstanceOperationProgress', 3)


class UpdateInstanceConfigRequest(_messages.Message):
  r"""The request for UpdateInstanceConfigRequest.

  Fields:
    instanceConfig: Required. The user instance config to update, which must
      always include the instance config name. Otherwise, only fields
      mentioned in update_mask need be included. To prevent conflicts of
      concurrent updates, etag can be used.
    updateMask: Required. A mask specifying which fields in InstanceConfig
      should be updated. The field mask must always be specified; this
      prevents any future fields in InstanceConfig from being erased
      accidentally by clients that do not know about them. Only display_name
      and labels can be updated.
    validateOnly: An option to validate, but not actually execute, a request,
      and provide the same response.
  """

  instanceConfig = _messages.MessageField('InstanceConfig', 1)
  updateMask = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class UpdateInstanceMetadata(_messages.Message):
  r"""Metadata type for the operation returned by UpdateInstance.

  Fields:
    cancelTime: The time at which this operation was cancelled. If set, this
      operation is in the process of undoing itself (which is guaranteed to
      succeed) and cannot be cancelled again.
    endTime: The time at which this operation failed or was completed
      successfully.
    instance: The desired end state of the update.
    startTime: The time at which UpdateInstance request was received.
  """

  cancelTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  instance = _messages.MessageField('Instance', 3)
  startTime = _messages.StringField(4)


class UpdateInstancePartitionMetadata(_messages.Message):
  r"""Metadata type for the operation returned by UpdateInstancePartition.

  Fields:
    cancelTime: The time at which this operation was cancelled. If set, this
      operation is in the process of undoing itself (which is guaranteed to
      succeed) and cannot be cancelled again.
    endTime: The time at which this operation failed or was completed
      successfully.
    instancePartition: The desired end state of the update.
    startTime: The time at which UpdateInstancePartition request was received.
  """

  cancelTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  instancePartition = _messages.MessageField('InstancePartition', 3)
  startTime = _messages.StringField(4)


class UpdateInstancePartitionRequest(_messages.Message):
  r"""The request for UpdateInstancePartition.

  Fields:
    fieldMask: Required. A mask specifying which fields in InstancePartition
      should be updated. The field mask must always be specified; this
      prevents any future fields in InstancePartition from being erased
      accidentally by clients that do not know about them.
    instancePartition: Required. The instance partition to update, which must
      always include the instance partition name. Otherwise, only fields
      mentioned in field_mask need be included.
  """

  fieldMask = _messages.StringField(1)
  instancePartition = _messages.MessageField('InstancePartition', 2)


class UpdateInstanceRequest(_messages.Message):
  r"""The request for UpdateInstance.

  Fields:
    fieldMask: Required. A mask specifying which fields in Instance should be
      updated. The field mask must always be specified; this prevents any
      future fields in Instance from being erased accidentally by clients that
      do not know about them.
    instance: Required. The instance to update, which must always include the
      instance name. Otherwise, only fields mentioned in field_mask need be
      included.
  """

  fieldMask = _messages.StringField(1)
  instance = _messages.MessageField('Instance', 2)


class VisualizationData(_messages.Message):
  r"""A VisualizationData object.

  Enums:
    KeyUnitValueValuesEnum: The unit for the key: e.g. 'key' or 'chunk'.

  Fields:
    dataSourceEndToken: The token signifying the end of a data_source.
    dataSourceSeparatorToken: The token delimiting a datasource name from the
      rest of a key in a data_source.
    diagnosticMessages: The list of messages (info, alerts, ...)
    endKeyStrings: We discretize the entire keyspace into buckets. Assuming
      each bucket has an inclusive keyrange and covers keys from k(i) ...
      k(n). In this case k(n) would be an end key for a given range.
      end_key_string is the collection of all such end keys
    hasPii: Whether this scan contains PII.
    indexedKeys: Keys of key ranges that contribute significantly to a given
      metric Can be thought of as heavy hitters.
    keySeparator: The token delimiting the key prefixes.
    keyUnit: The unit for the key: e.g. 'key' or 'chunk'.
    metrics: The list of data objects for each metric.
    prefixNodes: The list of extracted key prefix nodes used in the key prefix
      hierarchy.
  """

  class KeyUnitValueValuesEnum(_messages.Enum):
    r"""The unit for the key: e.g. 'key' or 'chunk'.

    Values:
      KEY_UNIT_UNSPECIFIED: Required default value
      KEY: Each entry corresponds to one key
      CHUNK: Each entry corresponds to a chunk of keys
    """
    KEY_UNIT_UNSPECIFIED = 0
    KEY = 1
    CHUNK = 2

  dataSourceEndToken = _messages.StringField(1)
  dataSourceSeparatorToken = _messages.StringField(2)
  diagnosticMessages = _messages.MessageField('DiagnosticMessage', 3, repeated=True)
  endKeyStrings = _messages.StringField(4, repeated=True)
  hasPii = _messages.BooleanField(5)
  indexedKeys = _messages.StringField(6, repeated=True)
  keySeparator = _messages.StringField(7)
  keyUnit = _messages.EnumField('KeyUnitValueValuesEnum', 8)
  metrics = _messages.MessageField('Metric', 9, repeated=True)
  prefixNodes = _messages.MessageField('PrefixNode', 10, repeated=True)


class Write(_messages.Message):
  r"""Arguments to insert, update, insert_or_update, and replace operations.

  Messages:
    ValuesValueListEntry: Single entry in a ValuesValue.

  Fields:
    columns: The names of the columns in table to be written. The list of
      columns must contain enough columns to allow Cloud Spanner to derive
      values for all primary key columns in the row(s) to be modified.
    table: Required. The table whose rows will be written.
    values: The values to be written. `values` can contain more than one list
      of values. If it does, then multiple rows are written, one for each
      entry in `values`. Each list in `values` must have exactly as many
      entries as there are entries in columns above. Sending multiple lists is
      equivalent to sending multiple `Mutation`s, each containing one `values`
      entry and repeating table and columns. Individual values in each list
      are encoded as described here.
  """

  class ValuesValueListEntry(_messages.Message):
    r"""Single entry in a ValuesValue.

    Fields:
      entry: A extra_types.JsonValue attribute.
    """

    entry = _messages.MessageField('extra_types.JsonValue', 1, repeated=True)

  columns = _messages.StringField(1, repeated=True)
  table = _messages.StringField(2)
  values = _messages.MessageField('ValuesValueListEntry', 3, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

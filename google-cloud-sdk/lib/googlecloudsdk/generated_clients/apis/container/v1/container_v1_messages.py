"""Generated message classes for container version v1.

Builds and manages container-based applications, powered by the open source
Kubernetes technology.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'container'


class AcceleratorConfig(_messages.Message):
  r"""AcceleratorConfig represents a Hardware Accelerator request.

  Fields:
    acceleratorCount: The number of the accelerator cards exposed to an
      instance.
    acceleratorType: The accelerator type resource name. List of supported
      accelerators [here](https://cloud.google.com/compute/docs/gpus)
    gpuDriverInstallationConfig: The configuration for auto installation of
      GPU driver.
    gpuPartitionSize: Size of partitions to create on the GPU. Valid values
      are described in the NVIDIA [mig user
      guide](https://docs.nvidia.com/datacenter/tesla/mig-user-
      guide/#partitioning).
    gpuSharingConfig: The configuration for GPU sharing options.
  """

  acceleratorCount = _messages.IntegerField(1)
  acceleratorType = _messages.StringField(2)
  gpuDriverInstallationConfig = _messages.MessageField('GPUDriverInstallationConfig', 3)
  gpuPartitionSize = _messages.StringField(4)
  gpuSharingConfig = _messages.MessageField('GPUSharingConfig', 5)


class AdditionalNodeNetworkConfig(_messages.Message):
  r"""AdditionalNodeNetworkConfig is the configuration for additional node
  networks within the NodeNetworkConfig message

  Fields:
    network: Name of the VPC where the additional interface belongs
    subnetwork: Name of the subnetwork where the additional interface belongs
  """

  network = _messages.StringField(1)
  subnetwork = _messages.StringField(2)


class AdditionalPodNetworkConfig(_messages.Message):
  r"""AdditionalPodNetworkConfig is the configuration for additional pod
  networks within the NodeNetworkConfig message

  Fields:
    maxPodsPerNode: The maximum number of pods per node which use this pod
      network
    secondaryPodRange: The name of the secondary range on the subnet which
      provides IP address for this pod range
    subnetwork: Name of the subnetwork where the additional pod network
      belongs
  """

  maxPodsPerNode = _messages.MessageField('MaxPodsConstraint', 1)
  secondaryPodRange = _messages.StringField(2)
  subnetwork = _messages.StringField(3)


class AdditionalPodRangesConfig(_messages.Message):
  r"""AdditionalPodRangesConfig is the configuration for additional pod
  secondary ranges supporting the ClusterUpdate message.

  Fields:
    podRangeInfo: Output only. [Output only] Information for additional pod
      range.
    podRangeNames: Name for pod secondary ipv4 range which has the actual
      range defined ahead.
  """

  podRangeInfo = _messages.MessageField('RangeInfo', 1, repeated=True)
  podRangeNames = _messages.StringField(2, repeated=True)


class AddonsConfig(_messages.Message):
  r"""Configuration for the addons that can be automatically spun up in the
  cluster, enabling additional functionality.

  Fields:
    cloudRunConfig: Configuration for the Cloud Run addon, which allows the
      user to use a managed Knative service.
    configConnectorConfig: Configuration for the ConfigConnector add-on, a
      Kubernetes extension to manage hosted GCP services through the
      Kubernetes API
    dnsCacheConfig: Configuration for NodeLocalDNS, a dns cache running on
      cluster nodes
    gcePersistentDiskCsiDriverConfig: Configuration for the Compute Engine
      Persistent Disk CSI driver.
    gcpFilestoreCsiDriverConfig: Configuration for the GCP Filestore CSI
      driver.
    gcsFuseCsiDriverConfig: Configuration for the Cloud Storage Fuse CSI
      driver.
    gkeBackupAgentConfig: Configuration for the Backup for GKE agent addon.
    horizontalPodAutoscaling: Configuration for the horizontal pod autoscaling
      feature, which increases or decreases the number of replica pods a
      replication controller has based on the resource usage of the existing
      pods.
    httpLoadBalancing: Configuration for the HTTP (L7) load balancing
      controller addon, which makes it easy to set up HTTP load balancers for
      services in a cluster.
    kubernetesDashboard: Configuration for the Kubernetes Dashboard. This
      addon is deprecated, and will be disabled in 1.15. It is recommended to
      use the Cloud Console to manage and monitor your Kubernetes clusters,
      workloads and applications. For more information, see:
      https://cloud.google.com/kubernetes-engine/docs/concepts/dashboards
    networkPolicyConfig: Configuration for NetworkPolicy. This only tracks
      whether the addon is enabled or not on the Master, it does not track
      whether network policy is enabled for the nodes.
  """

  cloudRunConfig = _messages.MessageField('CloudRunConfig', 1)
  configConnectorConfig = _messages.MessageField('ConfigConnectorConfig', 2)
  dnsCacheConfig = _messages.MessageField('DnsCacheConfig', 3)
  gcePersistentDiskCsiDriverConfig = _messages.MessageField('GcePersistentDiskCsiDriverConfig', 4)
  gcpFilestoreCsiDriverConfig = _messages.MessageField('GcpFilestoreCsiDriverConfig', 5)
  gcsFuseCsiDriverConfig = _messages.MessageField('GcsFuseCsiDriverConfig', 6)
  gkeBackupAgentConfig = _messages.MessageField('GkeBackupAgentConfig', 7)
  horizontalPodAutoscaling = _messages.MessageField('HorizontalPodAutoscaling', 8)
  httpLoadBalancing = _messages.MessageField('HttpLoadBalancing', 9)
  kubernetesDashboard = _messages.MessageField('KubernetesDashboard', 10)
  networkPolicyConfig = _messages.MessageField('NetworkPolicyConfig', 11)


class AdvancedDatapathObservabilityConfig(_messages.Message):
  r"""AdvancedDatapathObservabilityConfig specifies configuration of
  observability features of advanced datapath.

  Enums:
    RelayModeValueValuesEnum: Method used to make Relay available

  Fields:
    enableMetrics: Expose flow metrics on nodes
    relayMode: Method used to make Relay available
  """

  class RelayModeValueValuesEnum(_messages.Enum):
    r"""Method used to make Relay available

    Values:
      RELAY_MODE_UNSPECIFIED: Default value. This shouldn't be used.
      DISABLED: disabled
      INTERNAL_VPC_LB: exposed via internal load balancer
      EXTERNAL_LB: exposed via external load balancer
    """
    RELAY_MODE_UNSPECIFIED = 0
    DISABLED = 1
    INTERNAL_VPC_LB = 2
    EXTERNAL_LB = 3

  enableMetrics = _messages.BooleanField(1)
  relayMode = _messages.EnumField('RelayModeValueValuesEnum', 2)


class AdvancedMachineFeatures(_messages.Message):
  r"""Specifies options for controlling advanced machine features.

  Enums:
    PerformanceMonitoringUnitValueValuesEnum: Type of Performance Monitoring
      Unit (PMU) requested on node pool instances. If unset, PMU will not be
      available to the node.

  Fields:
    enableNestedVirtualization: Whether or not to enable nested virtualization
      (defaults to false).
    performanceMonitoringUnit: Type of Performance Monitoring Unit (PMU)
      requested on node pool instances. If unset, PMU will not be available to
      the node.
    threadsPerCore: The number of threads per physical core. To disable
      simultaneous multithreading (SMT) set this to 1. If unset, the maximum
      number of threads supported per core by the underlying processor is
      assumed.
  """

  class PerformanceMonitoringUnitValueValuesEnum(_messages.Enum):
    r"""Type of Performance Monitoring Unit (PMU) requested on node pool
    instances. If unset, PMU will not be available to the node.

    Values:
      PERFORMANCE_MONITORING_UNIT_UNSPECIFIED: PMU not enabled.
      ARCHITECTURAL: Architecturally defined non-LLC events.
      STANDARD: Most documented core/L2 events.
      ENHANCED: Most documented core/L2 and LLC events.
    """
    PERFORMANCE_MONITORING_UNIT_UNSPECIFIED = 0
    ARCHITECTURAL = 1
    STANDARD = 2
    ENHANCED = 3

  enableNestedVirtualization = _messages.BooleanField(1)
  performanceMonitoringUnit = _messages.EnumField('PerformanceMonitoringUnitValueValuesEnum', 2)
  threadsPerCore = _messages.IntegerField(3)


class AuthenticatorGroupsConfig(_messages.Message):
  r"""Configuration for returning group information from authenticators.

  Fields:
    enabled: Whether this cluster should return group membership lookups
      during authentication using a group of security groups.
    securityGroup: The name of the security group-of-groups to be used. Only
      relevant if enabled = true.
  """

  enabled = _messages.BooleanField(1)
  securityGroup = _messages.StringField(2)


class AutoUpgradeOptions(_messages.Message):
  r"""AutoUpgradeOptions defines the set of options for the user to control
  how the Auto Upgrades will proceed.

  Fields:
    autoUpgradeStartTime: [Output only] This field is set when upgrades are
      about to commence with the approximate start time for the upgrades, in
      [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
    description: [Output only] This field is set when upgrades are about to
      commence with the description of the upgrade.
  """

  autoUpgradeStartTime = _messages.StringField(1)
  description = _messages.StringField(2)


class Autopilot(_messages.Message):
  r"""Autopilot is the configuration for Autopilot settings on the cluster.

  Fields:
    enabled: Enable Autopilot
    workloadPolicyConfig: Workload policy configuration for Autopilot.
  """

  enabled = _messages.BooleanField(1)
  workloadPolicyConfig = _messages.MessageField('WorkloadPolicyConfig', 2)


class AutopilotCompatibilityIssue(_messages.Message):
  r"""AutopilotCompatibilityIssue contains information about a specific
  compatibility issue with Autopilot mode.

  Enums:
    IncompatibilityTypeValueValuesEnum: The incompatibility type of this
      issue.

  Fields:
    constraintType: The constraint type of the issue.
    description: The description of the issue.
    documentationUrl: A URL to a public documnetation, which addresses
      resolving this issue.
    incompatibilityType: The incompatibility type of this issue.
    lastObservation: The last time when this issue was observed.
    subjects: The name of the resources which are subject to this issue.
  """

  class IncompatibilityTypeValueValuesEnum(_messages.Enum):
    r"""The incompatibility type of this issue.

    Values:
      UNSPECIFIED: Default value, should not be used.
      INCOMPATIBILITY: Indicates that the issue is a known incompatibility
        between the cluster and Autopilot mode.
      ADDITIONAL_CONFIG_REQUIRED: Indicates the issue is an incompatibility if
        customers take no further action to resolve.
      PASSED_WITH_OPTIONAL_CONFIG: Indicates the issue is not an
        incompatibility, but depending on the workloads business logic, there
        is a potential that they won't work on Autopilot.
    """
    UNSPECIFIED = 0
    INCOMPATIBILITY = 1
    ADDITIONAL_CONFIG_REQUIRED = 2
    PASSED_WITH_OPTIONAL_CONFIG = 3

  constraintType = _messages.StringField(1)
  description = _messages.StringField(2)
  documentationUrl = _messages.StringField(3)
  incompatibilityType = _messages.EnumField('IncompatibilityTypeValueValuesEnum', 4)
  lastObservation = _messages.StringField(5)
  subjects = _messages.StringField(6, repeated=True)


class AutoprovisioningNodePoolDefaults(_messages.Message):
  r"""AutoprovisioningNodePoolDefaults contains defaults for a node pool
  created by NAP.

  Fields:
    bootDiskKmsKey: The Customer Managed Encryption Key used to encrypt the
      boot disk attached to each node in the node pool. This should be of the
      form projects/[KEY_PROJECT_ID]/locations/[LOCATION]/keyRings/[RING_NAME]
      /cryptoKeys/[KEY_NAME]. For more information about protecting resources
      with Cloud KMS Keys please see:
      https://cloud.google.com/compute/docs/disks/customer-managed-encryption
    diskSizeGb: Size of the disk attached to each node, specified in GB. The
      smallest allowed disk size is 10GB. If unspecified, the default disk
      size is 100GB.
    diskType: Type of the disk attached to each node (e.g. 'pd-standard', 'pd-
      ssd' or 'pd-balanced') If unspecified, the default disk type is 'pd-
      standard'
    imageType: The image type to use for NAP created node. Please see
      https://cloud.google.com/kubernetes-engine/docs/concepts/node-images for
      available image types.
    insecureKubeletReadonlyPortEnabled: Enable or disable Kubelet read only
      port.
    management: Specifies the node management options for NAP created node-
      pools.
    minCpuPlatform: Deprecated. Minimum CPU platform to be used for NAP
      created node pools. The instance may be scheduled on the specified or
      newer CPU platform. Applicable values are the friendly names of CPU
      platforms, such as minCpuPlatform: Intel Haswell or minCpuPlatform:
      Intel Sandy Bridge. For more information, read [how to specify min CPU
      platform](https://cloud.google.com/compute/docs/instances/specify-min-
      cpu-platform). This field is deprecated, min_cpu_platform should be
      specified using `cloud.google.com/requested-min-cpu-platform` label
      selector on the pod. To unset the min cpu platform field pass
      "automatic" as field value.
    oauthScopes: Scopes that are used by NAP when creating node pools.
    serviceAccount: The Google Cloud Platform Service Account to be used by
      the node VMs.
    shieldedInstanceConfig: Shielded Instance options.
    upgradeSettings: Specifies the upgrade settings for NAP created node pools
  """

  bootDiskKmsKey = _messages.StringField(1)
  diskSizeGb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  diskType = _messages.StringField(3)
  imageType = _messages.StringField(4)
  insecureKubeletReadonlyPortEnabled = _messages.BooleanField(5)
  management = _messages.MessageField('NodeManagement', 6)
  minCpuPlatform = _messages.StringField(7)
  oauthScopes = _messages.StringField(8, repeated=True)
  serviceAccount = _messages.StringField(9)
  shieldedInstanceConfig = _messages.MessageField('ShieldedInstanceConfig', 10)
  upgradeSettings = _messages.MessageField('UpgradeSettings', 11)


class BestEffortProvisioning(_messages.Message):
  r"""Best effort provisioning.

  Fields:
    enabled: When this is enabled, cluster/node pool creations will ignore
      non-fatal errors like stockout to best provision as many nodes as
      possible right now and eventually bring up all target number of nodes
    minProvisionNodes: Minimum number of nodes to be provisioned to be
      considered as succeeded, and the rest of nodes will be provisioned
      gradually and eventually when stockout issue has been resolved.
  """

  enabled = _messages.BooleanField(1)
  minProvisionNodes = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BigQueryDestination(_messages.Message):
  r"""Parameters for using BigQuery as the destination of resource usage
  export.

  Fields:
    datasetId: The ID of a BigQuery Dataset.
  """

  datasetId = _messages.StringField(1)


class BinaryAuthorization(_messages.Message):
  r"""Configuration for Binary Authorization.

  Enums:
    EvaluationModeValueValuesEnum: Mode of operation for binauthz policy
      evaluation. If unspecified, defaults to DISABLED.

  Fields:
    enabled: This field is deprecated. Leave this unset and instead configure
      BinaryAuthorization using evaluation_mode. If evaluation_mode is set to
      anything other than EVALUATION_MODE_UNSPECIFIED, this field is ignored.
    evaluationMode: Mode of operation for binauthz policy evaluation. If
      unspecified, defaults to DISABLED.
    policyBindings: Optional. Binauthz policies that apply to this cluster.
  """

  class EvaluationModeValueValuesEnum(_messages.Enum):
    r"""Mode of operation for binauthz policy evaluation. If unspecified,
    defaults to DISABLED.

    Values:
      EVALUATION_MODE_UNSPECIFIED: Default value
      DISABLED: Disable BinaryAuthorization
      PROJECT_SINGLETON_POLICY_ENFORCE: Enforce Kubernetes admission requests
        with BinaryAuthorization using the project's singleton policy. This is
        equivalent to setting the enabled boolean to true.
      POLICY_BINDINGS: Use Binary Authorization with the policies specified in
        policy_bindings.
      POLICY_BINDINGS_AND_PROJECT_SINGLETON_POLICY_ENFORCE: Use Binary
        Authorization with the policies specified in policy_bindings, and also
        with the project's singleton policy in enforcement mode.
    """
    EVALUATION_MODE_UNSPECIFIED = 0
    DISABLED = 1
    PROJECT_SINGLETON_POLICY_ENFORCE = 2
    POLICY_BINDINGS = 3
    POLICY_BINDINGS_AND_PROJECT_SINGLETON_POLICY_ENFORCE = 4

  enabled = _messages.BooleanField(1)
  evaluationMode = _messages.EnumField('EvaluationModeValueValuesEnum', 2)
  policyBindings = _messages.MessageField('PolicyBinding', 3, repeated=True)


class BlueGreenInfo(_messages.Message):
  r"""Information relevant to blue-green upgrade.

  Enums:
    PhaseValueValuesEnum: Current blue-green upgrade phase.

  Fields:
    blueInstanceGroupUrls: The resource URLs of the [managed instance groups]
      (/compute/docs/instance-groups/creating-groups-of-managed-instances)
      associated with blue pool.
    bluePoolDeletionStartTime: Time to start deleting blue pool to complete
      blue-green upgrade, in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt)
      text format.
    greenInstanceGroupUrls: The resource URLs of the [managed instance groups]
      (/compute/docs/instance-groups/creating-groups-of-managed-instances)
      associated with green pool.
    greenPoolVersion: Version of green pool.
    phase: Current blue-green upgrade phase.
  """

  class PhaseValueValuesEnum(_messages.Enum):
    r"""Current blue-green upgrade phase.

    Values:
      PHASE_UNSPECIFIED: Unspecified phase.
      UPDATE_STARTED: blue-green upgrade has been initiated.
      CREATING_GREEN_POOL: Start creating green pool nodes.
      CORDONING_BLUE_POOL: Start cordoning blue pool nodes.
      DRAINING_BLUE_POOL: Start draining blue pool nodes.
      NODE_POOL_SOAKING: Start soaking time after draining entire blue pool.
      DELETING_BLUE_POOL: Start deleting blue nodes.
      ROLLBACK_STARTED: Rollback has been initiated.
    """
    PHASE_UNSPECIFIED = 0
    UPDATE_STARTED = 1
    CREATING_GREEN_POOL = 2
    CORDONING_BLUE_POOL = 3
    DRAINING_BLUE_POOL = 4
    NODE_POOL_SOAKING = 5
    DELETING_BLUE_POOL = 6
    ROLLBACK_STARTED = 7

  blueInstanceGroupUrls = _messages.StringField(1, repeated=True)
  bluePoolDeletionStartTime = _messages.StringField(2)
  greenInstanceGroupUrls = _messages.StringField(3, repeated=True)
  greenPoolVersion = _messages.StringField(4)
  phase = _messages.EnumField('PhaseValueValuesEnum', 5)


class BlueGreenSettings(_messages.Message):
  r"""Settings for blue-green upgrade.

  Fields:
    nodePoolSoakDuration: Time needed after draining entire blue pool. After
      this period, blue pool will be cleaned up.
    standardRolloutPolicy: Standard policy for the blue-green upgrade.
  """

  nodePoolSoakDuration = _messages.StringField(1)
  standardRolloutPolicy = _messages.MessageField('StandardRolloutPolicy', 2)


class CancelOperationRequest(_messages.Message):
  r"""CancelOperationRequest cancels a single operation.

  Fields:
    name: The name (project, location, operation id) of the operation to
      cancel. Specified in the format `projects/*/locations/*/operations/*`.
    operationId: Deprecated. The server-assigned `name` of the operation. This
      field has been deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the operation resides. This field has been deprecated and replaced by
      the name field.
  """

  name = _messages.StringField(1)
  operationId = _messages.StringField(2)
  projectId = _messages.StringField(3)
  zone = _messages.StringField(4)


class CertificateAuthorityDomainConfig(_messages.Message):
  r"""CertificateAuthorityDomainConfig configures one or more fully qualified
  domain names (FQDN) to a specific certificate.

  Fields:
    fqdns: List of fully qualified domain names (FQDN). Specifying port is
      supported. Wilcards are NOT supported. Examples: - my.customdomain.com -
      10.0.1.2:5000
    gcpSecretManagerCertificateConfig: Google Secret Manager (GCP) certificate
      configuration.
  """

  fqdns = _messages.StringField(1, repeated=True)
  gcpSecretManagerCertificateConfig = _messages.MessageField('GCPSecretManagerCertificateConfig', 2)


class CheckAutopilotCompatibilityResponse(_messages.Message):
  r"""CheckAutopilotCompatibilityResponse has a list of compatibility issues.

  Fields:
    issues: The list of issues for the given operation.
    summary: The summary of the autopilot compatibility response.
  """

  issues = _messages.MessageField('AutopilotCompatibilityIssue', 1, repeated=True)
  summary = _messages.StringField(2)


class CidrBlock(_messages.Message):
  r"""CidrBlock contains an optional name and one CIDR block.

  Fields:
    cidrBlock: cidr_block must be specified in CIDR notation.
    displayName: display_name is an optional field for users to identify CIDR
      blocks.
  """

  cidrBlock = _messages.StringField(1)
  displayName = _messages.StringField(2)


class ClientCertificateConfig(_messages.Message):
  r"""Configuration for client certificates on the cluster.

  Fields:
    issueClientCertificate: Issue a client certificate.
  """

  issueClientCertificate = _messages.BooleanField(1)


class CloudRunConfig(_messages.Message):
  r"""Configuration options for the Cloud Run feature.

  Enums:
    LoadBalancerTypeValueValuesEnum: Which load balancer type is installed for
      Cloud Run.

  Fields:
    disabled: Whether Cloud Run addon is enabled for this cluster.
    loadBalancerType: Which load balancer type is installed for Cloud Run.
  """

  class LoadBalancerTypeValueValuesEnum(_messages.Enum):
    r"""Which load balancer type is installed for Cloud Run.

    Values:
      LOAD_BALANCER_TYPE_UNSPECIFIED: Load balancer type for Cloud Run is
        unspecified.
      LOAD_BALANCER_TYPE_EXTERNAL: Install external load balancer for Cloud
        Run.
      LOAD_BALANCER_TYPE_INTERNAL: Install internal load balancer for Cloud
        Run.
    """
    LOAD_BALANCER_TYPE_UNSPECIFIED = 0
    LOAD_BALANCER_TYPE_EXTERNAL = 1
    LOAD_BALANCER_TYPE_INTERNAL = 2

  disabled = _messages.BooleanField(1)
  loadBalancerType = _messages.EnumField('LoadBalancerTypeValueValuesEnum', 2)


class Cluster(_messages.Message):
  r"""A Google Kubernetes Engine cluster.

  Enums:
    StatusValueValuesEnum: [Output only] The current status of this cluster.

  Messages:
    ResourceLabelsValue: The resource labels for the cluster to use to
      annotate any related Google Compute Engine resources.

  Fields:
    addonsConfig: Configurations for the various addons available to run in
      the cluster.
    authenticatorGroupsConfig: Configuration controlling RBAC group membership
      information.
    autopilot: Autopilot configuration for the cluster.
    autoscaling: Cluster-level autoscaling configuration.
    binaryAuthorization: Configuration for Binary Authorization.
    clusterIpv4Cidr: The IP address range of the container pods in this
      cluster, in [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-
      Domain_Routing) notation (e.g. `*********/14`). Leave blank to have one
      automatically chosen or specify a `/14` block in `10.0.0.0/8`.
    concurrentOpsConfig: Allows enabling concurrent ops for supported
      operations on this cluster. This flag is for EAP users only. Concurrent
      ops will become the only option, and thus having an opt in/out is only
      applicable pre-GA. Once the feature is GA, "disabling" concurrent ops
      will have no effect and the flag will be removed in the future.
    conditions: Which conditions caused the current cluster state.
    confidentialNodes: Configuration of Confidential Nodes. All the nodes in
      the cluster will be Confidential VM once enabled.
    costManagementConfig: Configuration for the fine-grained cost management
      feature.
    createTime: [Output only] The time the cluster was created, in
      [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
    currentMasterVersion: [Output only] The current software version of the
      master endpoint.
    currentNodeCount: [Output only] The number of nodes currently in the
      cluster. Deprecated. Call Kubernetes API directly to retrieve node
      information.
    currentNodeVersion: [Output only] Deprecated, use
      [NodePools.version](https://cloud.google.com/kubernetes-
      engine/docs/reference/rest/v1/projects.locations.clusters.nodePools)
      instead. The current version of the node software components. If they
      are currently at multiple versions because they're in the process of
      being upgraded, this reflects the minimum version of all nodes.
    databaseEncryption: Configuration of etcd encryption.
    defaultMaxPodsConstraint: The default constraint on the maximum number of
      pods that can be run simultaneously on a node in the node pool of this
      cluster. Only honored if cluster created with IP Alias support.
    description: An optional description of this cluster.
    enableK8sBetaApis: Beta APIs Config
    enableKubernetesAlpha: Kubernetes alpha features are enabled on this
      cluster. This includes alpha API groups (e.g. v1alpha1) and features
      that may not be production ready in the kubernetes version of the master
      and nodes. The cluster has no SLA for uptime and master/node upgrades
      are disabled. Alpha enabled clusters are automatically deleted thirty
      days after creation.
    enableTpu: Enable the ability to use Cloud TPUs in this cluster.
    endpoint: [Output only] The IP address of this cluster's master endpoint.
      The endpoint can be accessed from the internet at
      `**********************************/`. See the `masterAuth` property of
      this resource for username and password information.
    enterpriseConfig: GKE Enterprise Configuration.
    etag: This checksum is computed by the server based on the value of
      cluster fields, and may be sent on update requests to ensure the client
      has an up-to-date value before proceeding.
    expireTime: [Output only] The time the cluster will be automatically
      deleted in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
    fleet: Fleet information for the cluster.
    id: Output only. Unique id for the cluster.
    identityServiceConfig: Configuration for Identity Service component.
    initialClusterVersion: The initial Kubernetes version for this cluster.
      Valid versions are those found in validMasterVersions returned by
      getServerConfig. The version can be upgraded over time; such upgrades
      are reflected in currentMasterVersion and currentNodeVersion. Users may
      specify either explicit versions offered by Kubernetes Engine or version
      aliases, which have the following behavior: - "latest": picks the
      highest valid Kubernetes version - "1.X": picks the highest valid
      patch+gke.N patch in the 1.X version - "1.X.Y": picks the highest valid
      gke.N patch in the 1.X.Y version - "1.X.Y-gke.N": picks an explicit
      Kubernetes version - "","-": picks the default Kubernetes version
    initialNodeCount: The number of nodes to create in this cluster. You must
      ensure that your Compute Engine [resource
      quota](https://cloud.google.com/compute/quotas) is sufficient for this
      number of instances. You must also have available firewall and routes
      quota. For requests, this field should only be used in lieu of a
      "node_pool" object, since this configuration (along with the
      "node_config") will be used to create a "NodePool" object with an auto-
      generated name. Do not use this and a node_pool at the same time. This
      field is deprecated, use node_pool.initial_node_count instead.
    instanceGroupUrls: Deprecated. Use node_pools.instance_group_urls.
    ipAllocationPolicy: Configuration for cluster IP allocation.
    labelFingerprint: The fingerprint of the set of labels for this cluster.
    legacyAbac: Configuration for the legacy ABAC authorization mode.
    location: [Output only] The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/regions-zones/regions-
      zones#available) or
      [region](https://cloud.google.com/compute/docs/regions-zones/regions-
      zones#available) in which the cluster resides.
    locations: The list of Google Compute Engine
      [zones](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster's nodes should be located. This field provides a default
      value if [NodePool.Locations](https://cloud.google.com/kubernetes-engine
      /docs/reference/rest/v1/projects.locations.clusters.nodePools#NodePool.F
      IELDS.locations) are not specified during node pool creation. Warning:
      changing cluster locations will update the
      [NodePool.Locations](https://cloud.google.com/kubernetes-engine/docs/ref
      erence/rest/v1/projects.locations.clusters.nodePools#NodePool.FIELDS.loc
      ations) of all node pools and will result in nodes being added and/or
      removed.
    loggingConfig: Logging configuration for the cluster.
    loggingService: The logging service the cluster should use to write logs.
      Currently available options: * `logging.googleapis.com/kubernetes` - The
      Cloud Logging service with a Kubernetes-native resource model *
      `logging.googleapis.com` - The legacy Cloud Logging service (no longer
      available as of GKE 1.15). * `none` - no logs will be exported from the
      cluster. If left as an empty string,`logging.googleapis.com/kubernetes`
      will be used for GKE 1.14+ or `logging.googleapis.com` for earlier
      versions.
    maintenancePolicy: Configure the maintenance policy for this cluster.
    managedConfig: ManagedConfig configuration for the cluster.
    masterAuth: The authentication information for accessing the master
      endpoint. If unspecified, the defaults are used: For clusters before
      v1.12, if master_auth is unspecified, `username` will be set to "admin",
      a random password will be generated, and a client certificate will be
      issued.
    masterAuthorizedNetworksConfig: The configuration options for master
      authorized networks feature.
    meshCertificates: Configuration for issuance of mTLS keys and certificates
      to Kubernetes pods.
    monitoringConfig: Monitoring configuration for the cluster.
    monitoringService: The monitoring service the cluster should use to write
      metrics. Currently available options: *
      "monitoring.googleapis.com/kubernetes" - The Cloud Monitoring service
      with a Kubernetes-native resource model * `monitoring.googleapis.com` -
      The legacy Cloud Monitoring service (no longer available as of GKE
      1.15). * `none` - No metrics will be exported from the cluster. If left
      as an empty string,`monitoring.googleapis.com/kubernetes` will be used
      for GKE 1.14+ or `monitoring.googleapis.com` for earlier versions.
    name: The name of this cluster. The name must be unique within this
      project and location (e.g. zone or region), and can be up to 40
      characters with the following restrictions: * Lowercase letters,
      numbers, and hyphens only. * Must start with a letter. * Must end with a
      number or a letter.
    network: The name of the Google Compute Engine
      [network](https://cloud.google.com/compute/docs/networks-and-
      firewalls#networks) to which the cluster is connected. If left
      unspecified, the `default` network will be used.
    networkConfig: Configuration for cluster networking.
    networkPolicy: Configuration options for the NetworkPolicy feature.
    nodeConfig: Parameters used in creating the cluster's nodes. For requests,
      this field should only be used in lieu of a "node_pool" object, since
      this configuration (along with the "initial_node_count") will be used to
      create a "NodePool" object with an auto-generated name. Do not use this
      and a node_pool at the same time. For responses, this field will be
      populated with the node configuration of the first node pool. (For
      configuration of each node pool, see `node_pool.config`) If unspecified,
      the defaults are used. This field is deprecated, use node_pool.config
      instead.
    nodeIpv4CidrSize: [Output only] The size of the address space on each node
      for hosting containers. This is provisioned from within the
      `container_ipv4_cidr` range. This field will only be set when cluster is
      in route-based network mode.
    nodePoolAutoConfig: Node pool configs that apply to all auto-provisioned
      node pools in autopilot clusters and node auto-provisioning enabled
      clusters.
    nodePoolDefaults: Default NodePool settings for the entire cluster. These
      settings are overridden if specified on the specific NodePool object.
    nodePools: The node pools associated with this cluster. This field should
      not be set if "node_config" or "initial_node_count" are specified.
    notificationConfig: Notification configuration of the cluster.
    privateClusterConfig: Configuration for private cluster.
    releaseChannel: Release channel configuration. If left unspecified on
      cluster creation and a version is specified, the cluster is enrolled in
      the most mature release channel where the version is available (first
      checking STABLE, then REGULAR, and finally RAPID). Otherwise, if no
      release channel configuration and no version is specified, the cluster
      is enrolled in the REGULAR channel with its default version.
    resourceLabels: The resource labels for the cluster to use to annotate any
      related Google Compute Engine resources.
    resourceUsageExportConfig: Configuration for exporting resource usages.
      Resource usage export is disabled when this config is unspecified.
    runtimeVulnerabilityInsightConfig: Enable/Disable RVI features for the
      cluster.
    securityPostureConfig: Enable/Disable Security Posture API features for
      the cluster.
    selfLink: [Output only] Server-defined URL for the resource.
    servicesIpv4Cidr: [Output only] The IP address range of the Kubernetes
      services in this cluster, in
      [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
      notation (e.g. `*******/29`). Service addresses are typically put in the
      last `/16` from the container CIDR.
    shieldedNodes: Shielded Nodes configuration.
    status: [Output only] The current status of this cluster.
    statusMessage: [Output only] Deprecated. Use conditions instead.
      Additional information about the current status of this cluster, if
      available.
    subnetwork: The name of the Google Compute Engine
      [subnetwork](https://cloud.google.com/compute/docs/subnetworks) to which
      the cluster is connected.
    tpuIpv4CidrBlock: [Output only] The IP address range of the Cloud TPUs in
      this cluster, in [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-
      Domain_Routing) notation (e.g. `*******/29`).
    verticalPodAutoscaling: Cluster-level Vertical Pod Autoscaling
      configuration.
    workloadIdentityConfig: Configuration for the use of Kubernetes Service
      Accounts in GCP IAM policies.
    zone: [Output only] The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field is deprecated, use location instead.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""[Output only] The current status of this cluster.

    Values:
      STATUS_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the cluster is being
        created.
      RUNNING: The RUNNING state indicates the cluster has been created and is
        fully usable.
      RECONCILING: The RECONCILING state indicates that some work is actively
        being done on the cluster, such as upgrading the master or node
        software. Details can be found in the `statusMessage` field.
      STOPPING: The STOPPING state indicates the cluster is being deleted.
      ERROR: The ERROR state indicates the cluster is unusable. It will be
        automatically deleted. Details can be found in the `statusMessage`
        field.
      DEGRADED: The DEGRADED state indicates the cluster requires user action
        to restore full functionality. Details can be found in the
        `statusMessage` field.
    """
    STATUS_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceLabelsValue(_messages.Message):
    r"""The resource labels for the cluster to use to annotate any related
    Google Compute Engine resources.

    Messages:
      AdditionalProperty: An additional property for a ResourceLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ResourceLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  addonsConfig = _messages.MessageField('AddonsConfig', 1)
  authenticatorGroupsConfig = _messages.MessageField('AuthenticatorGroupsConfig', 2)
  autopilot = _messages.MessageField('Autopilot', 3)
  autoscaling = _messages.MessageField('ClusterAutoscaling', 4)
  binaryAuthorization = _messages.MessageField('BinaryAuthorization', 5)
  clusterIpv4Cidr = _messages.StringField(6)
  concurrentOpsConfig = _messages.MessageField('ConcurrentOpsConfig', 7)
  conditions = _messages.MessageField('StatusCondition', 8, repeated=True)
  confidentialNodes = _messages.MessageField('ConfidentialNodes', 9)
  costManagementConfig = _messages.MessageField('CostManagementConfig', 10)
  createTime = _messages.StringField(11)
  currentMasterVersion = _messages.StringField(12)
  currentNodeCount = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  currentNodeVersion = _messages.StringField(14)
  databaseEncryption = _messages.MessageField('DatabaseEncryption', 15)
  defaultMaxPodsConstraint = _messages.MessageField('MaxPodsConstraint', 16)
  description = _messages.StringField(17)
  enableK8sBetaApis = _messages.MessageField('K8sBetaAPIConfig', 18)
  enableKubernetesAlpha = _messages.BooleanField(19)
  enableTpu = _messages.BooleanField(20)
  endpoint = _messages.StringField(21)
  enterpriseConfig = _messages.MessageField('EnterpriseConfig', 22)
  etag = _messages.StringField(23)
  expireTime = _messages.StringField(24)
  fleet = _messages.MessageField('Fleet', 25)
  id = _messages.StringField(26)
  identityServiceConfig = _messages.MessageField('IdentityServiceConfig', 27)
  initialClusterVersion = _messages.StringField(28)
  initialNodeCount = _messages.IntegerField(29, variant=_messages.Variant.INT32)
  instanceGroupUrls = _messages.StringField(30, repeated=True)
  ipAllocationPolicy = _messages.MessageField('IPAllocationPolicy', 31)
  labelFingerprint = _messages.StringField(32)
  legacyAbac = _messages.MessageField('LegacyAbac', 33)
  location = _messages.StringField(34)
  locations = _messages.StringField(35, repeated=True)
  loggingConfig = _messages.MessageField('LoggingConfig', 36)
  loggingService = _messages.StringField(37)
  maintenancePolicy = _messages.MessageField('MaintenancePolicy', 38)
  managedConfig = _messages.MessageField('ManagedConfig', 39)
  masterAuth = _messages.MessageField('MasterAuth', 40)
  masterAuthorizedNetworksConfig = _messages.MessageField('MasterAuthorizedNetworksConfig', 41)
  meshCertificates = _messages.MessageField('MeshCertificates', 42)
  monitoringConfig = _messages.MessageField('MonitoringConfig', 43)
  monitoringService = _messages.StringField(44)
  name = _messages.StringField(45)
  network = _messages.StringField(46)
  networkConfig = _messages.MessageField('NetworkConfig', 47)
  networkPolicy = _messages.MessageField('NetworkPolicy', 48)
  nodeConfig = _messages.MessageField('NodeConfig', 49)
  nodeIpv4CidrSize = _messages.IntegerField(50, variant=_messages.Variant.INT32)
  nodePoolAutoConfig = _messages.MessageField('NodePoolAutoConfig', 51)
  nodePoolDefaults = _messages.MessageField('NodePoolDefaults', 52)
  nodePools = _messages.MessageField('NodePool', 53, repeated=True)
  notificationConfig = _messages.MessageField('NotificationConfig', 54)
  privateClusterConfig = _messages.MessageField('PrivateClusterConfig', 55)
  releaseChannel = _messages.MessageField('ReleaseChannel', 56)
  resourceLabels = _messages.MessageField('ResourceLabelsValue', 57)
  resourceUsageExportConfig = _messages.MessageField('ResourceUsageExportConfig', 58)
  runtimeVulnerabilityInsightConfig = _messages.MessageField('RuntimeVulnerabilityInsightConfig', 59)
  securityPostureConfig = _messages.MessageField('SecurityPostureConfig', 60)
  selfLink = _messages.StringField(61)
  servicesIpv4Cidr = _messages.StringField(62)
  shieldedNodes = _messages.MessageField('ShieldedNodes', 63)
  status = _messages.EnumField('StatusValueValuesEnum', 64)
  statusMessage = _messages.StringField(65)
  subnetwork = _messages.StringField(66)
  tpuIpv4CidrBlock = _messages.StringField(67)
  verticalPodAutoscaling = _messages.MessageField('VerticalPodAutoscaling', 68)
  workloadIdentityConfig = _messages.MessageField('WorkloadIdentityConfig', 69)
  zone = _messages.StringField(70)


class ClusterAutoscaling(_messages.Message):
  r"""ClusterAutoscaling contains global, per-cluster information required by
  Cluster Autoscaler to automatically adjust the size of the cluster and
  create/delete node pools based on the current needs.

  Enums:
    AutoscalingProfileValueValuesEnum: Defines autoscaling behaviour.

  Fields:
    autoprovisioningLocations: The list of Google Compute Engine
      [zones](https://cloud.google.com/compute/docs/zones#available) in which
      the NodePool's nodes can be created by NAP.
    autoprovisioningNodePoolDefaults: AutoprovisioningNodePoolDefaults
      contains defaults for a node pool created by NAP.
    autoscalingProfile: Defines autoscaling behaviour.
    enableNodeAutoprovisioning: Enables automatic node pool creation and
      deletion.
    resourceLimits: Contains global constraints regarding minimum and maximum
      amount of resources in the cluster.
  """

  class AutoscalingProfileValueValuesEnum(_messages.Enum):
    r"""Defines autoscaling behaviour.

    Values:
      PROFILE_UNSPECIFIED: No change to autoscaling configuration.
      OPTIMIZE_UTILIZATION: Prioritize optimizing utilization of resources.
      BALANCED: Use default (balanced) autoscaling configuration.
    """
    PROFILE_UNSPECIFIED = 0
    OPTIMIZE_UTILIZATION = 1
    BALANCED = 2

  autoprovisioningLocations = _messages.StringField(1, repeated=True)
  autoprovisioningNodePoolDefaults = _messages.MessageField('AutoprovisioningNodePoolDefaults', 2)
  autoscalingProfile = _messages.EnumField('AutoscalingProfileValueValuesEnum', 3)
  enableNodeAutoprovisioning = _messages.BooleanField(4)
  resourceLimits = _messages.MessageField('ResourceLimit', 5, repeated=True)


class ClusterNetworkPerformanceConfig(_messages.Message):
  r"""Configuration of network bandwidth tiers

  Enums:
    TotalEgressBandwidthTierValueValuesEnum: Specifies the total network
      bandwidth tier for NodePools in the cluster.

  Fields:
    totalEgressBandwidthTier: Specifies the total network bandwidth tier for
      NodePools in the cluster.
  """

  class TotalEgressBandwidthTierValueValuesEnum(_messages.Enum):
    r"""Specifies the total network bandwidth tier for NodePools in the
    cluster.

    Values:
      TIER_UNSPECIFIED: Default value
      TIER_1: Higher bandwidth, actual values based on VM size.
    """
    TIER_UNSPECIFIED = 0
    TIER_1 = 1

  totalEgressBandwidthTier = _messages.EnumField('TotalEgressBandwidthTierValueValuesEnum', 1)


class ClusterUpdate(_messages.Message):
  r"""ClusterUpdate describes an update to the cluster. Exactly one update can
  be applied to a cluster with each request, so at most one field can be
  provided.

  Enums:
    DesiredDatapathProviderValueValuesEnum: The desired datapath provider for
      the cluster.
    DesiredInTransitEncryptionConfigValueValuesEnum: Specify the details of
      in-transit encryption.
    DesiredPrivateIpv6GoogleAccessValueValuesEnum: The desired state of IPv6
      connectivity to Google Services.
    DesiredStackTypeValueValuesEnum: The desired stack type of the cluster. If
      a stack type is provided and does not match the current stack type of
      the cluster, update will attempt to change the stack type to the new
      type.

  Fields:
    additionalPodRangesConfig: The additional pod ranges to be added to the
      cluster. These pod ranges can be used by node pools to allocate pod IPs.
    desiredAddonsConfig: Configurations for the various addons available to
      run in the cluster.
    desiredAuthenticatorGroupsConfig: The desired authenticator groups config
      for the cluster.
    desiredAutopilot: The desired Autopilot configuration for the cluster.
    desiredAutopilotInsecureKubeletReadonlyPortEnabled: Enable/disable kubelet
      readonly port for autopilot cluster
    desiredAutopilotWorkloadPolicyConfig: The desired workload policy
      configuration for the autopilot cluster.
    desiredBinaryAuthorization: The desired configuration options for the
      Binary Authorization feature.
    desiredClusterAutoscaling: Cluster-level autoscaling configuration.
    desiredConcurrentOpsConfig: Desired value for the cluster's
      concurrent_ops_config.
    desiredContainerdConfig: The desired containerd config for the cluster.
    desiredCostManagementConfig: The desired configuration for the fine-
      grained cost management feature.
    desiredDatabaseEncryption: Configuration of etcd encryption.
    desiredDatapathProvider: The desired datapath provider for the cluster.
    desiredDefaultSnatStatus: The desired status of whether to disable default
      sNAT for this cluster.
    desiredDnsConfig: DNSConfig contains clusterDNS config for this cluster.
    desiredEnableFqdnNetworkPolicy: Enable/Disable FQDN Network Policy for the
      cluster.
    desiredEnableMultiNetworking: Enable/Disable Multi-Networking for the
      cluster
    desiredEnablePrivateEndpoint: Enable/Disable private endpoint for the
      cluster's master.
    desiredFleet: The desired fleet configuration for the cluster.
    desiredGatewayApiConfig: The desired config of Gateway API on this
      cluster.
    desiredGcfsConfig: The desired GCFS config for the cluster
    desiredIdentityServiceConfig: The desired Identity Service component
      configuration.
    desiredImage: The desired name of the image to use for this node. This is
      used to create clusters using a custom image. NOTE: Set the
      "desired_node_pool" field as well.
    desiredImageProject: The project containing the desired image to use for
      this node. This is used to create clusters using a custom image. NOTE:
      Set the "desired_node_pool" field as well.
    desiredImageType: The desired image type for the node pool. NOTE: Set the
      "desired_node_pool" field as well.
    desiredInTransitEncryptionConfig: Specify the details of in-transit
      encryption.
    desiredIntraNodeVisibilityConfig: The desired config of Intra-node
      visibility.
    desiredK8sBetaApis: Desired Beta APIs to be enabled for cluster.
    desiredL4ilbSubsettingConfig: The desired L4 Internal Load Balancer
      Subsetting configuration.
    desiredLocations: The desired list of Google Compute Engine
      [zones](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster's nodes should be located. This list must always include the
      cluster's primary zone. Warning: changing cluster locations will update
      the locations of all node pools and will result in nodes being added
      and/or removed.
    desiredLoggingConfig: The desired logging configuration.
    desiredLoggingService: The logging service the cluster should use to write
      logs. Currently available options: * `logging.googleapis.com/kubernetes`
      - The Cloud Logging service with a Kubernetes-native resource model *
      `logging.googleapis.com` - The legacy Cloud Logging service (no longer
      available as of GKE 1.15). * `none` - no logs will be exported from the
      cluster. If left as an empty string,`logging.googleapis.com/kubernetes`
      will be used for GKE 1.14+ or `logging.googleapis.com` for earlier
      versions.
    desiredManagedConfig: The desired managed config for the cluster.
    desiredMasterAuthorizedNetworksConfig: The desired configuration options
      for master authorized networks feature.
    desiredMasterVersion: The Kubernetes version to change the master to.
      Users may specify either explicit versions offered by Kubernetes Engine
      or version aliases, which have the following behavior: - "latest": picks
      the highest valid Kubernetes version - "1.X": picks the highest valid
      patch+gke.N patch in the 1.X version - "1.X.Y": picks the highest valid
      gke.N patch in the 1.X.Y version - "1.X.Y-gke.N": picks an explicit
      Kubernetes version - "-": picks the default Kubernetes version
    desiredMeshCertificates: Configuration for issuance of mTLS keys and
      certificates to Kubernetes pods.
    desiredMonitoringConfig: The desired monitoring configuration.
    desiredMonitoringService: The monitoring service the cluster should use to
      write metrics. Currently available options: *
      "monitoring.googleapis.com/kubernetes" - The Cloud Monitoring service
      with a Kubernetes-native resource model * `monitoring.googleapis.com` -
      The legacy Cloud Monitoring service (no longer available as of GKE
      1.15). * `none` - No metrics will be exported from the cluster. If left
      as an empty string,`monitoring.googleapis.com/kubernetes` will be used
      for GKE 1.14+ or `monitoring.googleapis.com` for earlier versions.
    desiredNetworkPerformanceConfig: The desired network performance config.
    desiredNodePoolAutoConfigNetworkTags: The desired network tags that apply
      to all auto-provisioned node pools in autopilot clusters and node auto-
      provisioning enabled clusters.
    desiredNodePoolAutoConfigResourceManagerTags: The desired resource manager
      tags that apply to all auto-provisioned node pools in autopilot clusters
      and node auto-provisioning enabled clusters.
    desiredNodePoolAutoscaling: Autoscaler configuration for the node pool
      specified in desired_node_pool_id. If there is only one pool in the
      cluster and desired_node_pool_id is not provided then the change applies
      to that single node pool.
    desiredNodePoolId: The node pool to be upgraded. This field is mandatory
      if "desired_node_version", "desired_image_family" or
      "desired_node_pool_autoscaling" is specified and there is more than one
      node pool on the cluster.
    desiredNodePoolLoggingConfig: The desired node pool logging configuration
      defaults for the cluster.
    desiredNodeVersion: The Kubernetes version to change the nodes to
      (typically an upgrade). Users may specify either explicit versions
      offered by Kubernetes Engine or version aliases, which have the
      following behavior: - "latest": picks the highest valid Kubernetes
      version - "1.X": picks the highest valid patch+gke.N patch in the 1.X
      version - "1.X.Y": picks the highest valid gke.N patch in the 1.X.Y
      version - "1.X.Y-gke.N": picks an explicit Kubernetes version - "-":
      picks the Kubernetes master version
    desiredNotificationConfig: The desired notification configuration.
    desiredPrivateClusterConfig: The desired private cluster configuration.
    desiredPrivateIpv6GoogleAccess: The desired state of IPv6 connectivity to
      Google Services.
    desiredReleaseChannel: The desired release channel configuration.
    desiredResourceUsageExportConfig: The desired configuration for exporting
      resource usage.
    desiredRuntimeVulnerabilityInsightConfig: Enable/Disable RVI features for
      the cluster.
    desiredSecurityPostureConfig: Enable/Disable Security Posture API features
      for the cluster.
    desiredServiceExternalIpsConfig: ServiceExternalIPsConfig specifies the
      config for the use of Services with ExternalIPs field.
    desiredShieldedNodes: Configuration for Shielded Nodes.
    desiredStackType: The desired stack type of the cluster. If a stack type
      is provided and does not match the current stack type of the cluster,
      update will attempt to change the stack type to the new type.
    desiredVerticalPodAutoscaling: Cluster-level Vertical Pod Autoscaling
      configuration.
    desiredWorkloadIdentityConfig: Configuration for Workload Identity.
    enableK8sBetaApis: Kubernetes open source beta apis enabled on the
      cluster. Only beta apis
    etag: The current etag of the cluster. If an etag is provided and does not
      match the current etag of the cluster, update will be blocked and an
      ABORTED error will be returned.
    removedAdditionalPodRangesConfig: The additional pod ranges that are to be
      removed from the cluster. The pod ranges specified here must have been
      specified earlier in the 'additional_pod_ranges_config' argument.
  """

  class DesiredDatapathProviderValueValuesEnum(_messages.Enum):
    r"""The desired datapath provider for the cluster.

    Values:
      DATAPATH_PROVIDER_UNSPECIFIED: Default value.
      LEGACY_DATAPATH: Use the IPTables implementation based on kube-proxy.
      ADVANCED_DATAPATH: Use the eBPF based GKE Dataplane V2 with additional
        features. See the [GKE Dataplane V2
        documentation](https://cloud.google.com/kubernetes-engine/docs/how-
        to/dataplane-v2) for more.
      MIGRATE_TO_ADVANCED_DATAPATH: Cluster has some existing nodes but new
        nodes should use ADVANCED_DATAPATH.
      MIGRATE_TO_LEGACY_DATAPATH: Cluster has some existing nodes but new
        nodes should use LEGACY_DATAPATH.
    """
    DATAPATH_PROVIDER_UNSPECIFIED = 0
    LEGACY_DATAPATH = 1
    ADVANCED_DATAPATH = 2
    MIGRATE_TO_ADVANCED_DATAPATH = 3
    MIGRATE_TO_LEGACY_DATAPATH = 4

  class DesiredInTransitEncryptionConfigValueValuesEnum(_messages.Enum):
    r"""Specify the details of in-transit encryption.

    Values:
      IN_TRANSIT_ENCRYPTION_CONFIG_UNSPECIFIED: Unspecified, will be inferred
        as default - IN_TRANSIT_ENCRYPTION_UNSPECIFIED.
      IN_TRANSIT_ENCRYPTION_DISABLED: In-transit encryption is disabled.
      IN_TRANSIT_ENCRYPTION_INTER_NODE_TRANSPARENT: Data in-transit is
        encrypted with inter-node transparent encryption.
    """
    IN_TRANSIT_ENCRYPTION_CONFIG_UNSPECIFIED = 0
    IN_TRANSIT_ENCRYPTION_DISABLED = 1
    IN_TRANSIT_ENCRYPTION_INTER_NODE_TRANSPARENT = 2

  class DesiredPrivateIpv6GoogleAccessValueValuesEnum(_messages.Enum):
    r"""The desired state of IPv6 connectivity to Google Services.

    Values:
      PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED: Default value. Same as DISABLED
      PRIVATE_IPV6_GOOGLE_ACCESS_DISABLED: No private access to or from Google
        Services
      PRIVATE_IPV6_GOOGLE_ACCESS_TO_GOOGLE: Enables private IPv6 access to
        Google Services from GKE
      PRIVATE_IPV6_GOOGLE_ACCESS_BIDIRECTIONAL: Enables private IPv6 access to
        and from Google Services
    """
    PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED = 0
    PRIVATE_IPV6_GOOGLE_ACCESS_DISABLED = 1
    PRIVATE_IPV6_GOOGLE_ACCESS_TO_GOOGLE = 2
    PRIVATE_IPV6_GOOGLE_ACCESS_BIDIRECTIONAL = 3

  class DesiredStackTypeValueValuesEnum(_messages.Enum):
    r"""The desired stack type of the cluster. If a stack type is provided and
    does not match the current stack type of the cluster, update will attempt
    to change the stack type to the new type.

    Values:
      STACK_TYPE_UNSPECIFIED: Default value, will be defaulted as IPV4 only
      IPV4: Cluster is IPV4 only
      IPV4_IPV6: Cluster can use both IPv4 and IPv6
    """
    STACK_TYPE_UNSPECIFIED = 0
    IPV4 = 1
    IPV4_IPV6 = 2

  additionalPodRangesConfig = _messages.MessageField('AdditionalPodRangesConfig', 1)
  desiredAddonsConfig = _messages.MessageField('AddonsConfig', 2)
  desiredAuthenticatorGroupsConfig = _messages.MessageField('AuthenticatorGroupsConfig', 3)
  desiredAutopilot = _messages.MessageField('Autopilot', 4)
  desiredAutopilotInsecureKubeletReadonlyPortEnabled = _messages.BooleanField(5)
  desiredAutopilotWorkloadPolicyConfig = _messages.MessageField('WorkloadPolicyConfig', 6)
  desiredBinaryAuthorization = _messages.MessageField('BinaryAuthorization', 7)
  desiredClusterAutoscaling = _messages.MessageField('ClusterAutoscaling', 8)
  desiredConcurrentOpsConfig = _messages.MessageField('ConcurrentOpsConfig', 9)
  desiredContainerdConfig = _messages.MessageField('ContainerdConfig', 10)
  desiredCostManagementConfig = _messages.MessageField('CostManagementConfig', 11)
  desiredDatabaseEncryption = _messages.MessageField('DatabaseEncryption', 12)
  desiredDatapathProvider = _messages.EnumField('DesiredDatapathProviderValueValuesEnum', 13)
  desiredDefaultSnatStatus = _messages.MessageField('DefaultSnatStatus', 14)
  desiredDnsConfig = _messages.MessageField('DNSConfig', 15)
  desiredEnableFqdnNetworkPolicy = _messages.BooleanField(16)
  desiredEnableMultiNetworking = _messages.BooleanField(17)
  desiredEnablePrivateEndpoint = _messages.BooleanField(18)
  desiredFleet = _messages.MessageField('Fleet', 19)
  desiredGatewayApiConfig = _messages.MessageField('GatewayAPIConfig', 20)
  desiredGcfsConfig = _messages.MessageField('GcfsConfig', 21)
  desiredIdentityServiceConfig = _messages.MessageField('IdentityServiceConfig', 22)
  desiredImage = _messages.StringField(23)
  desiredImageProject = _messages.StringField(24)
  desiredImageType = _messages.StringField(25)
  desiredInTransitEncryptionConfig = _messages.EnumField('DesiredInTransitEncryptionConfigValueValuesEnum', 26)
  desiredIntraNodeVisibilityConfig = _messages.MessageField('IntraNodeVisibilityConfig', 27)
  desiredK8sBetaApis = _messages.MessageField('K8sBetaAPIConfig', 28)
  desiredL4ilbSubsettingConfig = _messages.MessageField('ILBSubsettingConfig', 29)
  desiredLocations = _messages.StringField(30, repeated=True)
  desiredLoggingConfig = _messages.MessageField('LoggingConfig', 31)
  desiredLoggingService = _messages.StringField(32)
  desiredManagedConfig = _messages.MessageField('ManagedConfig', 33)
  desiredMasterAuthorizedNetworksConfig = _messages.MessageField('MasterAuthorizedNetworksConfig', 34)
  desiredMasterVersion = _messages.StringField(35)
  desiredMeshCertificates = _messages.MessageField('MeshCertificates', 36)
  desiredMonitoringConfig = _messages.MessageField('MonitoringConfig', 37)
  desiredMonitoringService = _messages.StringField(38)
  desiredNetworkPerformanceConfig = _messages.MessageField('ClusterNetworkPerformanceConfig', 39)
  desiredNodePoolAutoConfigNetworkTags = _messages.MessageField('NetworkTags', 40)
  desiredNodePoolAutoConfigResourceManagerTags = _messages.MessageField('ResourceManagerTags', 41)
  desiredNodePoolAutoscaling = _messages.MessageField('NodePoolAutoscaling', 42)
  desiredNodePoolId = _messages.StringField(43)
  desiredNodePoolLoggingConfig = _messages.MessageField('NodePoolLoggingConfig', 44)
  desiredNodeVersion = _messages.StringField(45)
  desiredNotificationConfig = _messages.MessageField('NotificationConfig', 46)
  desiredPrivateClusterConfig = _messages.MessageField('PrivateClusterConfig', 47)
  desiredPrivateIpv6GoogleAccess = _messages.EnumField('DesiredPrivateIpv6GoogleAccessValueValuesEnum', 48)
  desiredReleaseChannel = _messages.MessageField('ReleaseChannel', 49)
  desiredResourceUsageExportConfig = _messages.MessageField('ResourceUsageExportConfig', 50)
  desiredRuntimeVulnerabilityInsightConfig = _messages.MessageField('RuntimeVulnerabilityInsightConfig', 51)
  desiredSecurityPostureConfig = _messages.MessageField('SecurityPostureConfig', 52)
  desiredServiceExternalIpsConfig = _messages.MessageField('ServiceExternalIPsConfig', 53)
  desiredShieldedNodes = _messages.MessageField('ShieldedNodes', 54)
  desiredStackType = _messages.EnumField('DesiredStackTypeValueValuesEnum', 55)
  desiredVerticalPodAutoscaling = _messages.MessageField('VerticalPodAutoscaling', 56)
  desiredWorkloadIdentityConfig = _messages.MessageField('WorkloadIdentityConfig', 57)
  enableK8sBetaApis = _messages.MessageField('K8sBetaAPIConfig', 58)
  etag = _messages.StringField(59)
  removedAdditionalPodRangesConfig = _messages.MessageField('AdditionalPodRangesConfig', 60)


class CompleteIPRotationRequest(_messages.Message):
  r"""CompleteIPRotationRequest moves the cluster master back into single-IP
  mode.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the name field.
    name: The name (project, location, cluster name) of the cluster to
      complete IP rotation. Specified in the format
      `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3)
  zone = _messages.StringField(4)


class CompleteNodePoolUpgradeRequest(_messages.Message):
  r"""CompleteNodePoolUpgradeRequest sets the name of target node pool to
  complete upgrade.
  """



class ConcurrentOpsConfig(_messages.Message):
  r"""Configures operations using concurrent ops.

  Fields:
    enableConcurrentCreateNodePool: Enables concurrent ops for supported
      CreateNodePool cases. Some fields may still use legacy ops.
    enableConcurrentDeleteNodePool: Enables concurrent ops for supported
      DeleteNodePool cases. Some fields may still use legacy ops.
    enableConcurrentResizeNodePool: Enables concurrent ops for ResizeNodePool
      operations.
    enableConcurrentRollbackNodePool: Enables concurrent ops for supported
      RollbackNodePool cases. Some fields may still use legacy ops.
    enableConcurrentUpdateNodePoolVersion: Enables concurrent ops for
      UpdateNodePool with only the version field. Some cluster features may
      still use legacy ops.
  """

  enableConcurrentCreateNodePool = _messages.BooleanField(1)
  enableConcurrentDeleteNodePool = _messages.BooleanField(2)
  enableConcurrentResizeNodePool = _messages.BooleanField(3)
  enableConcurrentRollbackNodePool = _messages.BooleanField(4)
  enableConcurrentUpdateNodePoolVersion = _messages.BooleanField(5)


class ConfidentialNodes(_messages.Message):
  r"""ConfidentialNodes is configuration for the confidential nodes feature,
  which makes nodes run on confidential VMs.

  Fields:
    enabled: Whether Confidential Nodes feature is enabled.
  """

  enabled = _messages.BooleanField(1)


class ConfigConnectorConfig(_messages.Message):
  r"""Configuration options for the Config Connector add-on.

  Fields:
    enabled: Whether Cloud Connector is enabled for this cluster.
  """

  enabled = _messages.BooleanField(1)


class ConsumptionMeteringConfig(_messages.Message):
  r"""Parameters for controlling consumption metering.

  Fields:
    enabled: Whether to enable consumption metering for this cluster. If
      enabled, a second BigQuery table will be created to hold resource
      consumption records.
  """

  enabled = _messages.BooleanField(1)


class ContainerProjectsAggregatedUsableSubnetworksListRequest(_messages.Message):
  r"""A ContainerProjectsAggregatedUsableSubnetworksListRequest object.

  Fields:
    filter: Filtering currently only supports equality on the networkProjectId
      and must be in the form: "networkProjectId=[PROJECTID]", where
      `networkProjectId` is the project which owns the listed subnetworks.
      This defaults to the parent project ID.
    pageSize: The max number of results per page that should be returned. If
      the number of available results is larger than `page_size`, a
      `next_page_token` is returned which can be used to get the next page of
      results in subsequent requests. Acceptable values are 0 to 500,
      inclusive. (Default: 500)
    pageToken: Specifies a page token to use. Set this to the nextPageToken
      returned by previous list requests to get the next page of results.
    parent: The parent project where subnetworks are usable. Specified in the
      format `projects/*`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ContainerProjectsLocationsClustersCheckAutopilotCompatibilityRequest(_messages.Message):
  r"""A ContainerProjectsLocationsClustersCheckAutopilotCompatibilityRequest
  object.

  Fields:
    name: The name (project, location, cluster) of the cluster to retrieve.
      Specified in the format `projects/*/locations/*/clusters/*`.
  """

  name = _messages.StringField(1, required=True)


class ContainerProjectsLocationsClustersDeleteRequest(_messages.Message):
  r"""A ContainerProjectsLocationsClustersDeleteRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster to delete. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster) of the cluster to delete.
      Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)
  zone = _messages.StringField(4)


class ContainerProjectsLocationsClustersGetJwksRequest(_messages.Message):
  r"""A ContainerProjectsLocationsClustersGetJwksRequest object.

  Fields:
    parent: The cluster (project, location, cluster name) to get keys for.
      Specified in the format `projects/*/locations/*/clusters/*`.
  """

  parent = _messages.StringField(1, required=True)


class ContainerProjectsLocationsClustersGetRequest(_messages.Message):
  r"""A ContainerProjectsLocationsClustersGetRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster to retrieve. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster) of the cluster to retrieve.
      Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)
  zone = _messages.StringField(4)


class ContainerProjectsLocationsClustersListRequest(_messages.Message):
  r"""A ContainerProjectsLocationsClustersListRequest object.

  Fields:
    parent: The parent (project and location) where the clusters will be
      listed. Specified in the format `projects/*/locations/*`. Location "-"
      matches all zones and all regions.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      parent field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides, or "-" for all zones. This field has been
      deprecated and replaced by the parent field.
  """

  parent = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2)
  zone = _messages.StringField(3)


class ContainerProjectsLocationsClustersNodePoolsCompleteUpgradeRequest(_messages.Message):
  r"""A ContainerProjectsLocationsClustersNodePoolsCompleteUpgradeRequest
  object.

  Fields:
    completeNodePoolUpgradeRequest: A CompleteNodePoolUpgradeRequest resource
      to be passed as the request body.
    name: The name (project, location, cluster, node pool id) of the node pool
      to complete upgrade. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
  """

  completeNodePoolUpgradeRequest = _messages.MessageField('CompleteNodePoolUpgradeRequest', 1)
  name = _messages.StringField(2, required=True)


class ContainerProjectsLocationsClustersNodePoolsDeleteRequest(_messages.Message):
  r"""A ContainerProjectsLocationsClustersNodePoolsDeleteRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the name field.
    name: The name (project, location, cluster, node pool id) of the node pool
      to delete. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
    nodePoolId: Deprecated. The name of the node pool to delete. This field
      has been deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  nodePoolId = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class ContainerProjectsLocationsClustersNodePoolsGetRequest(_messages.Message):
  r"""A ContainerProjectsLocationsClustersNodePoolsGetRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the name field.
    name: The name (project, location, cluster, node pool id) of the node pool
      to get. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
    nodePoolId: Deprecated. The name of the node pool. This field has been
      deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  nodePoolId = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class ContainerProjectsLocationsClustersNodePoolsListRequest(_messages.Message):
  r"""A ContainerProjectsLocationsClustersNodePoolsListRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the parent field.
    parent: The parent (project, location, cluster name) where the node pools
      will be listed. Specified in the format
      `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      parent field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      parent field.
  """

  clusterId = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3)
  zone = _messages.StringField(4)


class ContainerProjectsLocationsClustersWellKnownGetOpenidConfigurationRequest(_messages.Message):
  r"""A
  ContainerProjectsLocationsClustersWellKnownGetOpenidConfigurationRequest
  object.

  Fields:
    parent: The cluster (project, location, cluster name) to get the discovery
      document for. Specified in the format
      `projects/*/locations/*/clusters/*`.
  """

  parent = _messages.StringField(1, required=True)


class ContainerProjectsLocationsGetServerConfigRequest(_messages.Message):
  r"""A ContainerProjectsLocationsGetServerConfigRequest object.

  Fields:
    name: The name (project and location) of the server config to get,
      specified in the format `projects/*/locations/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) to return
      operations for. This field has been deprecated and replaced by the name
      field.
  """

  name = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2)
  zone = _messages.StringField(3)


class ContainerProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ContainerProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name (project, location, operation id) of the operation to get.
      Specified in the format `projects/*/locations/*/operations/*`.
    operationId: Deprecated. The server-assigned `name` of the operation. This
      field has been deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  name = _messages.StringField(1, required=True)
  operationId = _messages.StringField(2)
  projectId = _messages.StringField(3)
  zone = _messages.StringField(4)


class ContainerProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ContainerProjectsLocationsOperationsListRequest object.

  Fields:
    parent: The parent (project and location) where the operations will be
      listed. Specified in the format `projects/*/locations/*`. Location "-"
      matches all zones and all regions.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      parent field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) to return
      operations for, or `-` for all zones. This field has been deprecated and
      replaced by the parent field.
  """

  parent = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2)
  zone = _messages.StringField(3)


class ContainerProjectsZonesClustersDeleteRequest(_messages.Message):
  r"""A ContainerProjectsZonesClustersDeleteRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster to delete. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster) of the cluster to delete.
      Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1, required=True)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)
  zone = _messages.StringField(4, required=True)


class ContainerProjectsZonesClustersGetRequest(_messages.Message):
  r"""A ContainerProjectsZonesClustersGetRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster to retrieve. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster) of the cluster to retrieve.
      Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1, required=True)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)
  zone = _messages.StringField(4, required=True)


class ContainerProjectsZonesClustersListRequest(_messages.Message):
  r"""A ContainerProjectsZonesClustersListRequest object.

  Fields:
    parent: The parent (project and location) where the clusters will be
      listed. Specified in the format `projects/*/locations/*`. Location "-"
      matches all zones and all regions.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      parent field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides, or "-" for all zones. This field has been
      deprecated and replaced by the parent field.
  """

  parent = _messages.StringField(1)
  projectId = _messages.StringField(2, required=True)
  zone = _messages.StringField(3, required=True)


class ContainerProjectsZonesClustersNodePoolsDeleteRequest(_messages.Message):
  r"""A ContainerProjectsZonesClustersNodePoolsDeleteRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the name field.
    name: The name (project, location, cluster, node pool id) of the node pool
      to delete. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
    nodePoolId: Deprecated. The name of the node pool to delete. This field
      has been deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1, required=True)
  name = _messages.StringField(2)
  nodePoolId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)
  zone = _messages.StringField(5, required=True)


class ContainerProjectsZonesClustersNodePoolsGetRequest(_messages.Message):
  r"""A ContainerProjectsZonesClustersNodePoolsGetRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the name field.
    name: The name (project, location, cluster, node pool id) of the node pool
      to get. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
    nodePoolId: Deprecated. The name of the node pool. This field has been
      deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1, required=True)
  name = _messages.StringField(2)
  nodePoolId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)
  zone = _messages.StringField(5, required=True)


class ContainerProjectsZonesClustersNodePoolsListRequest(_messages.Message):
  r"""A ContainerProjectsZonesClustersNodePoolsListRequest object.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the parent field.
    parent: The parent (project, location, cluster name) where the node pools
      will be listed. Specified in the format
      `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      parent field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      parent field.
  """

  clusterId = _messages.StringField(1, required=True)
  parent = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)
  zone = _messages.StringField(4, required=True)


class ContainerProjectsZonesGetServerconfigRequest(_messages.Message):
  r"""A ContainerProjectsZonesGetServerconfigRequest object.

  Fields:
    name: The name (project and location) of the server config to get,
      specified in the format `projects/*/locations/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) to return
      operations for. This field has been deprecated and replaced by the name
      field.
  """

  name = _messages.StringField(1)
  projectId = _messages.StringField(2, required=True)
  zone = _messages.StringField(3, required=True)


class ContainerProjectsZonesOperationsGetRequest(_messages.Message):
  r"""A ContainerProjectsZonesOperationsGetRequest object.

  Fields:
    name: The name (project, location, operation id) of the operation to get.
      Specified in the format `projects/*/locations/*/operations/*`.
    operationId: Deprecated. The server-assigned `name` of the operation. This
      field has been deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  name = _messages.StringField(1)
  operationId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  zone = _messages.StringField(4, required=True)


class ContainerProjectsZonesOperationsListRequest(_messages.Message):
  r"""A ContainerProjectsZonesOperationsListRequest object.

  Fields:
    parent: The parent (project and location) where the operations will be
      listed. Specified in the format `projects/*/locations/*`. Location "-"
      matches all zones and all regions.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      parent field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) to return
      operations for, or `-` for all zones. This field has been deprecated and
      replaced by the parent field.
  """

  parent = _messages.StringField(1)
  projectId = _messages.StringField(2, required=True)
  zone = _messages.StringField(3, required=True)


class ContainerdConfig(_messages.Message):
  r"""ContainerdConfig contains configuration to customize containerd.

  Fields:
    privateRegistryAccessConfig: PrivateRegistryAccessConfig is used to
      configure access configuration for private container registries.
  """

  privateRegistryAccessConfig = _messages.MessageField('PrivateRegistryAccessConfig', 1)


class CostManagementConfig(_messages.Message):
  r"""Configuration for fine-grained cost management feature.

  Fields:
    enabled: Whether the feature is enabled or not.
  """

  enabled = _messages.BooleanField(1)


class CreateClusterRequest(_messages.Message):
  r"""CreateClusterRequest creates a cluster.

  Fields:
    cluster: Required. A [cluster
      resource](https://cloud.google.com/container-
      engine/reference/rest/v1/projects.locations.clusters)
    parent: The parent (project and location) where the cluster will be
      created. Specified in the format `projects/*/locations/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      parent field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      parent field.
  """

  cluster = _messages.MessageField('Cluster', 1)
  parent = _messages.StringField(2)
  projectId = _messages.StringField(3)
  zone = _messages.StringField(4)


class CreateNodePoolRequest(_messages.Message):
  r"""CreateNodePoolRequest creates a node pool for a cluster.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the parent field.
    nodePool: Required. The node pool to create.
    parent: The parent (project, location, cluster name) where the node pool
      will be created. Specified in the format
      `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      parent field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      parent field.
  """

  clusterId = _messages.StringField(1)
  nodePool = _messages.MessageField('NodePool', 2)
  parent = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class CustomImageConfig(_messages.Message):
  r"""CustomImageConfig contains the information r

  Fields:
    image: The name of the image to use for this node.
    imageFamily: The name of the image family to use for this node.
    imageProject: The project containing the image to use for this node.
  """

  image = _messages.StringField(1)
  imageFamily = _messages.StringField(2)
  imageProject = _messages.StringField(3)


class DNSConfig(_messages.Message):
  r"""DNSConfig contains the desired set of options for configuring
  clusterDNS.

  Enums:
    ClusterDnsValueValuesEnum: cluster_dns indicates which in-cluster DNS
      provider should be used.
    ClusterDnsScopeValueValuesEnum: cluster_dns_scope indicates the scope of
      access to cluster DNS records.

  Fields:
    additiveVpcScopeDnsDomain: Optional. The domain used in Additive VPC
      scope.
    clusterDns: cluster_dns indicates which in-cluster DNS provider should be
      used.
    clusterDnsDomain: cluster_dns_domain is the suffix used for all cluster
      service records.
    clusterDnsScope: cluster_dns_scope indicates the scope of access to
      cluster DNS records.
    enableAdditiveVpcScope: Optional. Indicates the enablement of Additive VPC
      scope. Which can be enabled alongside Cluster scope.
  """

  class ClusterDnsScopeValueValuesEnum(_messages.Enum):
    r"""cluster_dns_scope indicates the scope of access to cluster DNS
    records.

    Values:
      DNS_SCOPE_UNSPECIFIED: Default value, will be inferred as cluster scope.
      CLUSTER_SCOPE: DNS records are accessible from within the cluster.
      VPC_SCOPE: DNS records are accessible from within the VPC.
    """
    DNS_SCOPE_UNSPECIFIED = 0
    CLUSTER_SCOPE = 1
    VPC_SCOPE = 2

  class ClusterDnsValueValuesEnum(_messages.Enum):
    r"""cluster_dns indicates which in-cluster DNS provider should be used.

    Values:
      PROVIDER_UNSPECIFIED: Default value
      PLATFORM_DEFAULT: Use GKE default DNS provider(kube-dns) for DNS
        resolution.
      CLOUD_DNS: Use CloudDNS for DNS resolution.
      KUBE_DNS: Use KubeDNS for DNS resolution.
    """
    PROVIDER_UNSPECIFIED = 0
    PLATFORM_DEFAULT = 1
    CLOUD_DNS = 2
    KUBE_DNS = 3

  additiveVpcScopeDnsDomain = _messages.StringField(1)
  clusterDns = _messages.EnumField('ClusterDnsValueValuesEnum', 2)
  clusterDnsDomain = _messages.StringField(3)
  clusterDnsScope = _messages.EnumField('ClusterDnsScopeValueValuesEnum', 4)
  enableAdditiveVpcScope = _messages.BooleanField(5)


class DailyMaintenanceWindow(_messages.Message):
  r"""Time window specified for daily maintenance operations.

  Fields:
    duration: [Output only] Duration of the time window, automatically chosen
      to be smallest possible in the given scenario. Duration will be in
      [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) format "PTnHnMnS".
    startTime: Time within the maintenance window to start the maintenance
      operations. Time format should be in
      [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) format "HH:MM", where HH
      : [00-23] and MM : [00-59] GMT.
  """

  duration = _messages.StringField(1)
  startTime = _messages.StringField(2)


class DatabaseEncryption(_messages.Message):
  r"""Configuration of etcd encryption.

  Enums:
    StateValueValuesEnum: The desired state of etcd encryption.

  Fields:
    keyName: Name of CloudKMS key to use for the encryption of secrets in
      etcd. Ex. projects/my-project/locations/global/keyRings/my-
      ring/cryptoKeys/my-key
    state: The desired state of etcd encryption.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The desired state of etcd encryption.

    Values:
      UNKNOWN: Should never be set
      ENCRYPTED: Secrets in etcd are encrypted.
      DECRYPTED: Secrets in etcd are stored in plain text (at etcd level) -
        this is unrelated to Compute Engine level full disk encryption.
    """
    UNKNOWN = 0
    ENCRYPTED = 1
    DECRYPTED = 2

  keyName = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class DefaultSnatStatus(_messages.Message):
  r"""DefaultSnatStatus contains the desired state of whether default sNAT
  should be disabled on the cluster.

  Fields:
    disabled: Disables cluster default sNAT rules.
  """

  disabled = _messages.BooleanField(1)


class DnsCacheConfig(_messages.Message):
  r"""Configuration for NodeLocal DNSCache

  Fields:
    enabled: Whether NodeLocal DNSCache is enabled for this cluster.
  """

  enabled = _messages.BooleanField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnterpriseConfig(_messages.Message):
  r"""EnterpriseConfig is the cluster enterprise configuration.

  Enums:
    ClusterTierValueValuesEnum: Output only. [Output only] cluster_tier
      specifies the premium tier of the cluster.

  Fields:
    clusterTier: Output only. [Output only] cluster_tier specifies the premium
      tier of the cluster.
  """

  class ClusterTierValueValuesEnum(_messages.Enum):
    r"""Output only. [Output only] cluster_tier specifies the premium tier of
    the cluster.

    Values:
      CLUSTER_TIER_UNSPECIFIED: CLUSTER_TIER_UNSPECIFIED is when cluster_tier
        is not set.
      STANDARD: STANDARD indicates a standard GKE cluster.
      ENTERPRISE: ENTERPRISE indicates a GKE Enterprise cluster.
    """
    CLUSTER_TIER_UNSPECIFIED = 0
    STANDARD = 1
    ENTERPRISE = 2

  clusterTier = _messages.EnumField('ClusterTierValueValuesEnum', 1)


class EphemeralStorageLocalSsdConfig(_messages.Message):
  r"""EphemeralStorageLocalSsdConfig contains configuration for the node
  ephemeral storage using Local SSD.

  Fields:
    localSsdCount: Number of local SSDs to use to back ephemeral storage. Uses
      NVMe interfaces. Each local SSD is 375 GB in size. If zero, it means to
      disable using local SSDs as ephemeral storage. The limit for this value
      is dependent upon the maximum number of disks available on a machine per
      zone. See: https://cloud.google.com/compute/docs/disks/local-ssd for
      more information.
  """

  localSsdCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class FastSocket(_messages.Message):
  r"""Configuration of Fast Socket feature.

  Fields:
    enabled: Whether Fast Socket features are enabled in the node pool.
  """

  enabled = _messages.BooleanField(1)


class Filter(_messages.Message):
  r"""Allows filtering to one or more specific event types. If event types are
  present, those and only those event types will be transmitted to the
  cluster. Other types will be skipped. If no filter is specified, or no event
  types are present, all event types will be sent

  Enums:
    EventTypeValueListEntryValuesEnum:

  Fields:
    eventType: Event types to allowlist.
  """

  class EventTypeValueListEntryValuesEnum(_messages.Enum):
    r"""EventTypeValueListEntryValuesEnum enum type.

    Values:
      EVENT_TYPE_UNSPECIFIED: Not set, will be ignored.
      UPGRADE_AVAILABLE_EVENT: Corresponds with UpgradeAvailableEvent.
      UPGRADE_EVENT: Corresponds with UpgradeEvent.
      SECURITY_BULLETIN_EVENT: Corresponds with SecurityBulletinEvent.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    UPGRADE_AVAILABLE_EVENT = 1
    UPGRADE_EVENT = 2
    SECURITY_BULLETIN_EVENT = 3

  eventType = _messages.EnumField('EventTypeValueListEntryValuesEnum', 1, repeated=True)


class Fleet(_messages.Message):
  r"""Fleet is the fleet configuration for the cluster.

  Fields:
    membership: [Output only] The full resource name of the registered fleet
      membership of the cluster, in the format
      `//gkehub.googleapis.com/projects/*/locations/*/memberships/*`.
    preRegistered: [Output only] Whether the cluster has been registered
      through the fleet API.
    project: The Fleet host project(project ID or project number) where this
      cluster will be registered to. This field cannot be changed after the
      cluster has been registered.
  """

  membership = _messages.StringField(1)
  preRegistered = _messages.BooleanField(2)
  project = _messages.StringField(3)


class GCPSecretManagerCertificateConfig(_messages.Message):
  r"""GCPSecretManagerCertificateConfig configures a secret from [Google
  Secret Manager](https://cloud.google.com/secret-manager).

  Fields:
    secretUri: Secret URI, in the form
      "projects/$PROJECT_ID/secrets/$SECRET_NAME/versions/$VERSION". Version
      can be fixed (e.g. "2") or "latest"
  """

  secretUri = _messages.StringField(1)


class GPUDriverInstallationConfig(_messages.Message):
  r"""GPUDriverInstallationConfig specifies the version of GPU driver to be
  auto installed.

  Enums:
    GpuDriverVersionValueValuesEnum: Mode for how the GPU driver is installed.

  Fields:
    gpuDriverVersion: Mode for how the GPU driver is installed.
  """

  class GpuDriverVersionValueValuesEnum(_messages.Enum):
    r"""Mode for how the GPU driver is installed.

    Values:
      GPU_DRIVER_VERSION_UNSPECIFIED: Default value is to not install any GPU
        driver.
      INSTALLATION_DISABLED: Disable GPU driver auto installation and needs
        manual installation
      DEFAULT: "Default" GPU driver in COS and Ubuntu.
      LATEST: "Latest" GPU driver in COS.
    """
    GPU_DRIVER_VERSION_UNSPECIFIED = 0
    INSTALLATION_DISABLED = 1
    DEFAULT = 2
    LATEST = 3

  gpuDriverVersion = _messages.EnumField('GpuDriverVersionValueValuesEnum', 1)


class GPUSharingConfig(_messages.Message):
  r"""GPUSharingConfig represents the GPU sharing configuration for Hardware
  Accelerators.

  Enums:
    GpuSharingStrategyValueValuesEnum: The type of GPU sharing strategy to
      enable on the GPU node.

  Fields:
    gpuSharingStrategy: The type of GPU sharing strategy to enable on the GPU
      node.
    maxSharedClientsPerGpu: The max number of containers that can share a
      physical GPU.
  """

  class GpuSharingStrategyValueValuesEnum(_messages.Enum):
    r"""The type of GPU sharing strategy to enable on the GPU node.

    Values:
      GPU_SHARING_STRATEGY_UNSPECIFIED: Default value.
      TIME_SHARING: GPUs are time-shared between containers.
      MPS: GPUs are shared between containers with NVIDIA MPS.
    """
    GPU_SHARING_STRATEGY_UNSPECIFIED = 0
    TIME_SHARING = 1
    MPS = 2

  gpuSharingStrategy = _messages.EnumField('GpuSharingStrategyValueValuesEnum', 1)
  maxSharedClientsPerGpu = _messages.IntegerField(2)


class GatewayAPIConfig(_messages.Message):
  r"""GatewayAPIConfig contains the desired config of Gateway API on this
  cluster.

  Enums:
    ChannelValueValuesEnum: The Gateway API release channel to use for Gateway
      API.

  Fields:
    channel: The Gateway API release channel to use for Gateway API.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""The Gateway API release channel to use for Gateway API.

    Values:
      CHANNEL_UNSPECIFIED: Default value.
      CHANNEL_DISABLED: Gateway API support is disabled
      CHANNEL_EXPERIMENTAL: Gateway API support is enabled, experimental CRDs
        are installed
      CHANNEL_STANDARD: Gateway API support is enabled, standard CRDs are
        installed
    """
    CHANNEL_UNSPECIFIED = 0
    CHANNEL_DISABLED = 1
    CHANNEL_EXPERIMENTAL = 2
    CHANNEL_STANDARD = 3

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)


class GcePersistentDiskCsiDriverConfig(_messages.Message):
  r"""Configuration for the Compute Engine PD CSI driver.

  Fields:
    enabled: Whether the Compute Engine PD CSI driver is enabled for this
      cluster.
  """

  enabled = _messages.BooleanField(1)


class GcfsConfig(_messages.Message):
  r"""GcfsConfig contains configurations of Google Container File System
  (image streaming).

  Fields:
    enabled: Whether to use GCFS.
  """

  enabled = _messages.BooleanField(1)


class GcpFilestoreCsiDriverConfig(_messages.Message):
  r"""Configuration for the GCP Filestore CSI driver.

  Fields:
    enabled: Whether the GCP Filestore CSI driver is enabled for this cluster.
  """

  enabled = _messages.BooleanField(1)


class GcsFuseCsiDriverConfig(_messages.Message):
  r"""Configuration for the Cloud Storage Fuse CSI driver.

  Fields:
    enabled: Whether the Cloud Storage Fuse CSI driver is enabled for this
      cluster.
  """

  enabled = _messages.BooleanField(1)


class GetJSONWebKeysResponse(_messages.Message):
  r"""GetJSONWebKeysResponse is a valid JSON Web Key Set as specififed in rfc
  7517

  Fields:
    cacheHeader: OnePlatform automatically extracts this field and uses it to
      set the HTTP Cache-Control header.
    keys: The public component of the keys used by the cluster to sign token
      requests.
  """

  cacheHeader = _messages.MessageField('HttpCacheControlResponseHeader', 1)
  keys = _messages.MessageField('Jwk', 2, repeated=True)


class GetOpenIDConfigResponse(_messages.Message):
  r"""GetOpenIDConfigResponse is an OIDC discovery document for the cluster.
  See the OpenID Connect Discovery 1.0 specification for details.

  Fields:
    cacheHeader: OnePlatform automatically extracts this field and uses it to
      set the HTTP Cache-Control header.
    claims_supported: Supported claims.
    grant_types: Supported grant types.
    id_token_signing_alg_values_supported: supported ID Token signing
      Algorithms.
    issuer: OIDC Issuer.
    jwks_uri: JSON Web Key uri.
    response_types_supported: Supported response types.
    subject_types_supported: Supported subject types.
  """

  cacheHeader = _messages.MessageField('HttpCacheControlResponseHeader', 1)
  claims_supported = _messages.StringField(2, repeated=True)
  grant_types = _messages.StringField(3, repeated=True)
  id_token_signing_alg_values_supported = _messages.StringField(4, repeated=True)
  issuer = _messages.StringField(5)
  jwks_uri = _messages.StringField(6)
  response_types_supported = _messages.StringField(7, repeated=True)
  subject_types_supported = _messages.StringField(8, repeated=True)


class GkeBackupAgentConfig(_messages.Message):
  r"""Configuration for the Backup for GKE Agent.

  Fields:
    enabled: Whether the Backup for GKE agent is enabled for this cluster.
  """

  enabled = _messages.BooleanField(1)


class HorizontalPodAutoscaling(_messages.Message):
  r"""Configuration options for the horizontal pod autoscaling feature, which
  increases or decreases the number of replica pods a replication controller
  has based on the resource usage of the existing pods.

  Fields:
    disabled: Whether the Horizontal Pod Autoscaling feature is enabled in the
      cluster. When enabled, it ensures that metrics are collected into
      Stackdriver Monitoring.
  """

  disabled = _messages.BooleanField(1)


class HttpCacheControlResponseHeader(_messages.Message):
  r"""RFC-2616: cache control support

  Fields:
    age: 14.6 response cache age, in seconds since the response is generated
    directive: 14.9 request and response directives
    expires: 14.21 response cache expires, in RFC 1123 date format
  """

  age = _messages.IntegerField(1)
  directive = _messages.StringField(2)
  expires = _messages.StringField(3)


class HttpLoadBalancing(_messages.Message):
  r"""Configuration options for the HTTP (L7) load balancing controller addon,
  which makes it easy to set up HTTP load balancers for services in a cluster.

  Fields:
    disabled: Whether the HTTP Load Balancing controller is enabled in the
      cluster. When enabled, it runs a small pod in the cluster that manages
      the load balancers.
  """

  disabled = _messages.BooleanField(1)


class HugepagesConfig(_messages.Message):
  r"""Hugepages amount in both 2m and 1g size

  Fields:
    hugepageSize1g: Optional. Amount of 1G hugepages
    hugepageSize2m: Optional. Amount of 2M hugepages
  """

  hugepageSize1g = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  hugepageSize2m = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class ILBSubsettingConfig(_messages.Message):
  r"""ILBSubsettingConfig contains the desired config of L4 Internal
  LoadBalancer subsetting on this cluster.

  Fields:
    enabled: Enables l4 ILB subsetting for this cluster.
  """

  enabled = _messages.BooleanField(1)


class IPAllocationPolicy(_messages.Message):
  r"""Configuration for controlling how IPs are allocated in the cluster.

  Enums:
    Ipv6AccessTypeValueValuesEnum: The ipv6 access type (internal or external)
      when create_subnetwork is true
    StackTypeValueValuesEnum: The IP stack type of the cluster

  Fields:
    additionalPodRangesConfig: Output only. [Output only] The additional pod
      ranges that are added to the cluster. These pod ranges can be used by
      new node pools to allocate pod IPs automatically. Once the range is
      removed it will not show up in IPAllocationPolicy.
    clusterIpv4Cidr: This field is deprecated, use cluster_ipv4_cidr_block.
    clusterIpv4CidrBlock: The IP address range for the cluster pod IPs. If
      this field is set, then `cluster.cluster_ipv4_cidr` must be left blank.
      This field is only applicable when `use_ip_aliases` is true. Set to
      blank to have a range chosen with the default size. Set to /netmask
      (e.g. `/14`) to have a range chosen with a specific netmask. Set to a
      [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
      notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
      `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific
      range to use.
    clusterSecondaryRangeName: The name of the secondary range to be used for
      the cluster CIDR block. The secondary range will be used for pod IP
      addresses. This must be an existing secondary range associated with the
      cluster subnetwork. This field is only applicable with use_ip_aliases is
      true and create_subnetwork is false.
    createSubnetwork: Whether a new subnetwork will be created automatically
      for the cluster. This field is only applicable when `use_ip_aliases` is
      true.
    defaultPodIpv4RangeUtilization: Output only. [Output only] The utilization
      of the cluster default IPv4 range for the pod. The ratio is Usage/[Total
      number of IPs in the secondary range],
      Usage=numNodes*numZones*podIPsPerNode.
    ipv6AccessType: The ipv6 access type (internal or external) when
      create_subnetwork is true
    nodeIpv4Cidr: This field is deprecated, use node_ipv4_cidr_block.
    nodeIpv4CidrBlock: The IP address range of the instance IPs in this
      cluster. This is applicable only if `create_subnetwork` is true. Set to
      blank to have a range chosen with the default size. Set to /netmask
      (e.g. `/14`) to have a range chosen with a specific netmask. Set to a
      [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
      notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
      `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific
      range to use.
    podCidrOverprovisionConfig: [PRIVATE FIELD] Pod CIDR size overprovisioning
      config for the cluster. Pod CIDR size per node depends on
      max_pods_per_node. By default, the value of max_pods_per_node is doubled
      and then rounded off to next power of 2 to get the size of pod CIDR
      block per node. Example: max_pods_per_node of 30 would result in 64 IPs
      (/26). This config can disable the doubling of IPs (we still round off
      to next power of 2) Example: max_pods_per_node of 30 will result in 32
      IPs (/27) when overprovisioning is disabled.
    servicesIpv4Cidr: This field is deprecated, use services_ipv4_cidr_block.
    servicesIpv4CidrBlock: The IP address range of the services IPs in this
      cluster. If blank, a range will be automatically chosen with the default
      size. This field is only applicable when `use_ip_aliases` is true. Set
      to blank to have a range chosen with the default size. Set to /netmask
      (e.g. `/14`) to have a range chosen with a specific netmask. Set to a
      [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
      notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
      `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific
      range to use.
    servicesIpv6CidrBlock: Output only. [Output only] The services IPv6 CIDR
      block for the cluster.
    servicesSecondaryRangeName: The name of the secondary range to be used as
      for the services CIDR block. The secondary range will be used for
      service ClusterIPs. This must be an existing secondary range associated
      with the cluster subnetwork. This field is only applicable with
      use_ip_aliases is true and create_subnetwork is false.
    stackType: The IP stack type of the cluster
    subnetIpv6CidrBlock: Output only. [Output only] The subnet's IPv6 CIDR
      block used by nodes and pods.
    subnetworkName: A custom subnetwork name to be used if `create_subnetwork`
      is true. If this field is empty, then an automatic name will be chosen
      for the new subnetwork.
    tpuIpv4CidrBlock: The IP address range of the Cloud TPUs in this cluster.
      If unspecified, a range will be automatically chosen with the default
      size. This field is only applicable when `use_ip_aliases` is true. If
      unspecified, the range will use the default size. Set to /netmask (e.g.
      `/14`) to have a range chosen with a specific netmask. Set to a
      [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
      notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
      `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific
      range to use.
    useIpAliases: Whether alias IPs will be used for pod IPs in the cluster.
      This is used in conjunction with use_routes. It cannot be true if
      use_routes is true. If both use_ip_aliases and use_routes are false,
      then the server picks the default IP allocation mode
    useRoutes: Whether routes will be used for pod IPs in the cluster. This is
      used in conjunction with use_ip_aliases. It cannot be true if
      use_ip_aliases is true. If both use_ip_aliases and use_routes are false,
      then the server picks the default IP allocation mode
  """

  class Ipv6AccessTypeValueValuesEnum(_messages.Enum):
    r"""The ipv6 access type (internal or external) when create_subnetwork is
    true

    Values:
      IPV6_ACCESS_TYPE_UNSPECIFIED: Default value, will be defaulted as type
        external.
      INTERNAL: Access type internal (all v6 addresses are internal IPs)
      EXTERNAL: Access type external (all v6 addresses are external IPs)
    """
    IPV6_ACCESS_TYPE_UNSPECIFIED = 0
    INTERNAL = 1
    EXTERNAL = 2

  class StackTypeValueValuesEnum(_messages.Enum):
    r"""The IP stack type of the cluster

    Values:
      STACK_TYPE_UNSPECIFIED: Default value, will be defaulted as IPV4 only
      IPV4: Cluster is IPV4 only
      IPV4_IPV6: Cluster can use both IPv4 and IPv6
    """
    STACK_TYPE_UNSPECIFIED = 0
    IPV4 = 1
    IPV4_IPV6 = 2

  additionalPodRangesConfig = _messages.MessageField('AdditionalPodRangesConfig', 1)
  clusterIpv4Cidr = _messages.StringField(2)
  clusterIpv4CidrBlock = _messages.StringField(3)
  clusterSecondaryRangeName = _messages.StringField(4)
  createSubnetwork = _messages.BooleanField(5)
  defaultPodIpv4RangeUtilization = _messages.FloatField(6)
  ipv6AccessType = _messages.EnumField('Ipv6AccessTypeValueValuesEnum', 7)
  nodeIpv4Cidr = _messages.StringField(8)
  nodeIpv4CidrBlock = _messages.StringField(9)
  podCidrOverprovisionConfig = _messages.MessageField('PodCIDROverprovisionConfig', 10)
  servicesIpv4Cidr = _messages.StringField(11)
  servicesIpv4CidrBlock = _messages.StringField(12)
  servicesIpv6CidrBlock = _messages.StringField(13)
  servicesSecondaryRangeName = _messages.StringField(14)
  stackType = _messages.EnumField('StackTypeValueValuesEnum', 15)
  subnetIpv6CidrBlock = _messages.StringField(16)
  subnetworkName = _messages.StringField(17)
  tpuIpv4CidrBlock = _messages.StringField(18)
  useIpAliases = _messages.BooleanField(19)
  useRoutes = _messages.BooleanField(20)


class IdentityServiceConfig(_messages.Message):
  r"""IdentityServiceConfig is configuration for Identity Service which allows
  customers to use external identity providers with the K8S API

  Fields:
    enabled: Whether to enable the Identity Service component
  """

  enabled = _messages.BooleanField(1)


class IntraNodeVisibilityConfig(_messages.Message):
  r"""IntraNodeVisibilityConfig contains the desired config of the intra-node
  visibility on this cluster.

  Fields:
    enabled: Enables intra node visibility for this cluster.
  """

  enabled = _messages.BooleanField(1)


class Jwk(_messages.Message):
  r"""Jwk is a JSON Web Key as specified in RFC 7517

  Fields:
    alg: Algorithm.
    crv: Used for ECDSA keys.
    e: Used for RSA keys.
    kid: Key ID.
    kty: Key Type.
    n: Used for RSA keys.
    use: Permitted uses for the public keys.
    x: Used for ECDSA keys.
    y: Used for ECDSA keys.
  """

  alg = _messages.StringField(1)
  crv = _messages.StringField(2)
  e = _messages.StringField(3)
  kid = _messages.StringField(4)
  kty = _messages.StringField(5)
  n = _messages.StringField(6)
  use = _messages.StringField(7)
  x = _messages.StringField(8)
  y = _messages.StringField(9)


class K8sBetaAPIConfig(_messages.Message):
  r"""K8sBetaAPIConfig , configuration for beta APIs

  Fields:
    enabledApis: Enabled k8s beta APIs.
  """

  enabledApis = _messages.StringField(1, repeated=True)


class KubernetesDashboard(_messages.Message):
  r"""Configuration for the Kubernetes Dashboard.

  Fields:
    disabled: Whether the Kubernetes Dashboard is enabled for this cluster.
  """

  disabled = _messages.BooleanField(1)


class LegacyAbac(_messages.Message):
  r"""Configuration for the legacy Attribute Based Access Control
  authorization mode.

  Fields:
    enabled: Whether the ABAC authorizer is enabled for this cluster. When
      enabled, identities in the system, including service accounts, nodes,
      and controllers, will have statically granted permissions beyond those
      provided by the RBAC configuration or IAM.
  """

  enabled = _messages.BooleanField(1)


class LinuxNodeConfig(_messages.Message):
  r"""Parameters that can be configured on Linux nodes.

  Enums:
    CgroupModeValueValuesEnum: cgroup_mode specifies the cgroup mode to be
      used on the node.

  Messages:
    SysctlsValue: The Linux kernel parameters to be applied to the nodes and
      all pods running on the nodes. The following parameters are supported.
      net.core.busy_poll net.core.busy_read net.core.netdev_max_backlog
      net.core.rmem_max net.core.wmem_default net.core.wmem_max
      net.core.optmem_max net.core.somaxconn net.ipv4.tcp_rmem
      net.ipv4.tcp_wmem net.ipv4.tcp_tw_reuse

  Fields:
    cgroupMode: cgroup_mode specifies the cgroup mode to be used on the node.
    hugepages: Optional. Amounts for 2M and 1G hugepages
    sysctls: The Linux kernel parameters to be applied to the nodes and all
      pods running on the nodes. The following parameters are supported.
      net.core.busy_poll net.core.busy_read net.core.netdev_max_backlog
      net.core.rmem_max net.core.wmem_default net.core.wmem_max
      net.core.optmem_max net.core.somaxconn net.ipv4.tcp_rmem
      net.ipv4.tcp_wmem net.ipv4.tcp_tw_reuse
  """

  class CgroupModeValueValuesEnum(_messages.Enum):
    r"""cgroup_mode specifies the cgroup mode to be used on the node.

    Values:
      CGROUP_MODE_UNSPECIFIED: CGROUP_MODE_UNSPECIFIED is when unspecified
        cgroup configuration is used. The default for the GKE node OS image
        will be used.
      CGROUP_MODE_V1: CGROUP_MODE_V1 specifies to use cgroupv1 for the cgroup
        configuration on the node image.
      CGROUP_MODE_V2: CGROUP_MODE_V2 specifies to use cgroupv2 for the cgroup
        configuration on the node image.
    """
    CGROUP_MODE_UNSPECIFIED = 0
    CGROUP_MODE_V1 = 1
    CGROUP_MODE_V2 = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SysctlsValue(_messages.Message):
    r"""The Linux kernel parameters to be applied to the nodes and all pods
    running on the nodes. The following parameters are supported.
    net.core.busy_poll net.core.busy_read net.core.netdev_max_backlog
    net.core.rmem_max net.core.wmem_default net.core.wmem_max
    net.core.optmem_max net.core.somaxconn net.ipv4.tcp_rmem net.ipv4.tcp_wmem
    net.ipv4.tcp_tw_reuse

    Messages:
      AdditionalProperty: An additional property for a SysctlsValue object.

    Fields:
      additionalProperties: Additional properties of type SysctlsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SysctlsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cgroupMode = _messages.EnumField('CgroupModeValueValuesEnum', 1)
  hugepages = _messages.MessageField('HugepagesConfig', 2)
  sysctls = _messages.MessageField('SysctlsValue', 3)


class ListClustersResponse(_messages.Message):
  r"""ListClustersResponse is the result of ListClustersRequest.

  Fields:
    clusters: A list of clusters in the project in the specified zone, or
      across all ones.
    missingZones: If any zones are listed here, the list of clusters returned
      may be missing those zones.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  missingZones = _messages.StringField(2, repeated=True)


class ListNodePoolsResponse(_messages.Message):
  r"""ListNodePoolsResponse is the result of ListNodePoolsRequest.

  Fields:
    nodePools: A list of node pools for a cluster.
  """

  nodePools = _messages.MessageField('NodePool', 1, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""ListOperationsResponse is the result of ListOperationsRequest.

  Fields:
    missingZones: If any zones are listed here, the list of operations
      returned may be missing the operations from those zones.
    operations: A list of operations in the project in the specified zone.
  """

  missingZones = _messages.StringField(1, repeated=True)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListUsableSubnetworksResponse(_messages.Message):
  r"""ListUsableSubnetworksResponse is the response of
  ListUsableSubnetworksRequest.

  Fields:
    nextPageToken: This token allows you to get the next page of results for
      list requests. If the number of results is larger than `page_size`, use
      the `next_page_token` as a value for the query parameter `page_token` in
      the next request. The value will become empty when there are no more
      pages.
    subnetworks: A list of usable subnetworks in the specified network
      project.
  """

  nextPageToken = _messages.StringField(1)
  subnetworks = _messages.MessageField('UsableSubnetwork', 2, repeated=True)


class LocalNvmeSsdBlockConfig(_messages.Message):
  r"""LocalNvmeSsdBlockConfig contains configuration for using raw-block local
  NVMe SSD.

  Fields:
    localSsdCount: The number of raw-block local NVMe SSD disks to be attached
      to the node. Each local SSD is 375 GB in size. If zero, it means no raw-
      block local NVMe SSD disks to be attached to the node. The limit for
      this value is dependent upon the maximum number of disks available on a
      machine per zone. See:
      https://cloud.google.com/compute/docs/disks/local-ssd for more
      information.
  """

  localSsdCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class LoggingComponentConfig(_messages.Message):
  r"""LoggingComponentConfig is cluster logging component configuration.

  Enums:
    EnableComponentsValueListEntryValuesEnum:

  Fields:
    enableComponents: Select components to collect logs. An empty set would
      disable all logging.
  """

  class EnableComponentsValueListEntryValuesEnum(_messages.Enum):
    r"""EnableComponentsValueListEntryValuesEnum enum type.

    Values:
      COMPONENT_UNSPECIFIED: Default value. This shouldn't be used.
      SYSTEM_COMPONENTS: system components
      WORKLOADS: workloads
      APISERVER: kube-apiserver
      SCHEDULER: kube-scheduler
      CONTROLLER_MANAGER: kube-controller-manager
      ADDON_MANAGER: kube-addon-manager
    """
    COMPONENT_UNSPECIFIED = 0
    SYSTEM_COMPONENTS = 1
    WORKLOADS = 2
    APISERVER = 3
    SCHEDULER = 4
    CONTROLLER_MANAGER = 5
    ADDON_MANAGER = 6

  enableComponents = _messages.EnumField('EnableComponentsValueListEntryValuesEnum', 1, repeated=True)


class LoggingConfig(_messages.Message):
  r"""LoggingConfig is cluster logging configuration.

  Fields:
    componentConfig: Logging components configuration
  """

  componentConfig = _messages.MessageField('LoggingComponentConfig', 1)


class LoggingVariantConfig(_messages.Message):
  r"""LoggingVariantConfig specifies the behaviour of the logging component.

  Enums:
    VariantValueValuesEnum: Logging variant deployed on nodes.

  Fields:
    variant: Logging variant deployed on nodes.
  """

  class VariantValueValuesEnum(_messages.Enum):
    r"""Logging variant deployed on nodes.

    Values:
      VARIANT_UNSPECIFIED: Default value. This shouldn't be used.
      DEFAULT: default logging variant.
      MAX_THROUGHPUT: maximum logging throughput variant.
    """
    VARIANT_UNSPECIFIED = 0
    DEFAULT = 1
    MAX_THROUGHPUT = 2

  variant = _messages.EnumField('VariantValueValuesEnum', 1)


class MaintenanceExclusionOptions(_messages.Message):
  r"""Represents the Maintenance exclusion option.

  Enums:
    ScopeValueValuesEnum: Scope specifies the upgrade scope which upgrades are
      blocked by the exclusion.

  Fields:
    scope: Scope specifies the upgrade scope which upgrades are blocked by the
      exclusion.
  """

  class ScopeValueValuesEnum(_messages.Enum):
    r"""Scope specifies the upgrade scope which upgrades are blocked by the
    exclusion.

    Values:
      NO_UPGRADES: NO_UPGRADES excludes all upgrades, including patch upgrades
        and minor upgrades across control planes and nodes. This is the
        default exclusion behavior.
      NO_MINOR_UPGRADES: NO_MINOR_UPGRADES excludes all minor upgrades for the
        cluster, only patches are allowed.
      NO_MINOR_OR_NODE_UPGRADES: NO_MINOR_OR_NODE_UPGRADES excludes all minor
        upgrades for the cluster, and also exclude all node pool upgrades.
        Only control plane patches are allowed.
    """
    NO_UPGRADES = 0
    NO_MINOR_UPGRADES = 1
    NO_MINOR_OR_NODE_UPGRADES = 2

  scope = _messages.EnumField('ScopeValueValuesEnum', 1)


class MaintenancePolicy(_messages.Message):
  r"""MaintenancePolicy defines the maintenance policy to be used for the
  cluster.

  Fields:
    resourceVersion: A hash identifying the version of this policy, so that
      updates to fields of the policy won't accidentally undo intermediate
      changes (and so that users of the API unaware of some fields won't
      accidentally remove other fields). Make a `get()` request to the cluster
      to get the current resource version and include it with requests to set
      the policy.
    window: Specifies the maintenance window in which maintenance may be
      performed.
  """

  resourceVersion = _messages.StringField(1)
  window = _messages.MessageField('MaintenanceWindow', 2)


class MaintenanceWindow(_messages.Message):
  r"""MaintenanceWindow defines the maintenance window to be used for the
  cluster.

  Messages:
    MaintenanceExclusionsValue: Exceptions to maintenance window. Non-
      emergency maintenance should not occur in these windows.

  Fields:
    dailyMaintenanceWindow: DailyMaintenanceWindow specifies a daily
      maintenance operation window.
    maintenanceExclusions: Exceptions to maintenance window. Non-emergency
      maintenance should not occur in these windows.
    recurringWindow: RecurringWindow specifies some number of recurring time
      periods for maintenance to occur. The time windows may be overlapping.
      If no maintenance windows are set, maintenance can occur at any time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenanceExclusionsValue(_messages.Message):
    r"""Exceptions to maintenance window. Non-emergency maintenance should not
    occur in these windows.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenanceExclusionsValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenanceExclusionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenanceExclusionsValue object.

      Fields:
        key: Name of the additional property.
        value: A TimeWindow attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TimeWindow', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  dailyMaintenanceWindow = _messages.MessageField('DailyMaintenanceWindow', 1)
  maintenanceExclusions = _messages.MessageField('MaintenanceExclusionsValue', 2)
  recurringWindow = _messages.MessageField('RecurringTimeWindow', 3)


class ManagedConfig(_messages.Message):
  r"""ManagedConfig is used for enforcing set of cluster configurations that
  are conforming to strandards.

  Enums:
    TypeValueValuesEnum: The type of standard configurations to enforce for
      cluster.

  Fields:
    type: The type of standard configurations to enforce for cluster.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of standard configurations to enforce for cluster.

    Values:
      TYPE_UNSPECIFIED: Default value.
      DISABLED: ManagedConfig is disabled.
      AUTOFLEET: Use ManagedConfig that is conforming to Autofleet
        requirements.
    """
    TYPE_UNSPECIFIED = 0
    DISABLED = 1
    AUTOFLEET = 2

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class ManagedPrometheusConfig(_messages.Message):
  r"""ManagedPrometheusConfig defines the configuration for Google Cloud
  Managed Service for Prometheus.

  Fields:
    enabled: Enable Managed Collection.
  """

  enabled = _messages.BooleanField(1)


class MasterAuth(_messages.Message):
  r"""The authentication information for accessing the master endpoint.
  Authentication can be done using HTTP basic auth or using client
  certificates.

  Fields:
    clientCertificate: [Output only] Base64-encoded public certificate used by
      clients to authenticate to the cluster endpoint.
    clientCertificateConfig: Configuration for client certificate
      authentication on the cluster. For clusters before v1.12, if no
      configuration is specified, a client certificate is issued.
    clientKey: [Output only] Base64-encoded private key used by clients to
      authenticate to the cluster endpoint.
    clusterCaCertificate: [Output only] Base64-encoded public certificate that
      is the root of trust for the cluster.
    password: The password to use for HTTP basic authentication to the master
      endpoint. Because the master endpoint is open to the Internet, you
      should create a strong password. If a password is provided for cluster
      creation, username must be non-empty. Warning: basic authentication is
      deprecated, and will be removed in GKE control plane versions 1.19 and
      newer. For a list of recommended authentication methods, see:
      https://cloud.google.com/kubernetes-engine/docs/how-to/api-server-
      authentication
    username: The username to use for HTTP basic authentication to the master
      endpoint. For clusters v1.6.0 and later, basic authentication can be
      disabled by leaving username unspecified (or setting it to the empty
      string). Warning: basic authentication is deprecated, and will be
      removed in GKE control plane versions 1.19 and newer. For a list of
      recommended authentication methods, see:
      https://cloud.google.com/kubernetes-engine/docs/how-to/api-server-
      authentication
  """

  clientCertificate = _messages.StringField(1)
  clientCertificateConfig = _messages.MessageField('ClientCertificateConfig', 2)
  clientKey = _messages.StringField(3)
  clusterCaCertificate = _messages.StringField(4)
  password = _messages.StringField(5)
  username = _messages.StringField(6)


class MasterAuthorizedNetworksConfig(_messages.Message):
  r"""Configuration options for the master authorized networks feature.
  Enabled master authorized networks will disallow all external traffic to
  access Kubernetes master through HTTPS except traffic from the given CIDR
  blocks, Google Compute Engine Public IPs and Google Prod IPs.

  Fields:
    cidrBlocks: cidr_blocks define up to 50 external networks that could
      access Kubernetes master through HTTPS.
    enabled: Whether or not master authorized networks is enabled.
    gcpPublicCidrsAccessEnabled: Whether master is accessbile via Google
      Compute Engine Public IP addresses.
  """

  cidrBlocks = _messages.MessageField('CidrBlock', 1, repeated=True)
  enabled = _messages.BooleanField(2)
  gcpPublicCidrsAccessEnabled = _messages.BooleanField(3)


class MaxPodsConstraint(_messages.Message):
  r"""Constraints applied to pods.

  Fields:
    maxPodsPerNode: Constraint enforced on the max num of pods per node.
  """

  maxPodsPerNode = _messages.IntegerField(1)


class MeshCertificates(_messages.Message):
  r"""Configuration for issuance of mTLS keys and certificates to Kubernetes
  pods.

  Fields:
    enableCertificates: enable_certificates controls issuance of workload mTLS
      certificates. If set, the GKE Workload Identity Certificates controller
      and node agent will be deployed in the cluster, which can then be
      configured by creating a WorkloadCertificateConfig Custom Resource.
      Requires Workload Identity (workload_pool must be non-empty).
  """

  enableCertificates = _messages.BooleanField(1)


class Metric(_messages.Message):
  r"""Progress metric is (string, int|float|string) pair.

  Fields:
    doubleValue: For metrics with floating point value.
    intValue: For metrics with integer value.
    name: Required. Metric name, e.g., "nodes total", "percent done".
    stringValue: For metrics with custom values (ratios, visual progress,
      etc.).
  """

  doubleValue = _messages.FloatField(1)
  intValue = _messages.IntegerField(2)
  name = _messages.StringField(3)
  stringValue = _messages.StringField(4)


class MonitoringComponentConfig(_messages.Message):
  r"""MonitoringComponentConfig is cluster monitoring component configuration.

  Enums:
    EnableComponentsValueListEntryValuesEnum:

  Fields:
    enableComponents: Select components to collect metrics. An empty set would
      disable all monitoring.
  """

  class EnableComponentsValueListEntryValuesEnum(_messages.Enum):
    r"""EnableComponentsValueListEntryValuesEnum enum type.

    Values:
      COMPONENT_UNSPECIFIED: Default value. This shouldn't be used.
      SYSTEM_COMPONENTS: system components
      WORKLOADS: workloads
      APISERVER: kube-apiserver
      SCHEDULER: kube-scheduler
      CONTROLLER_MANAGER: kube-controller-manager
      STORAGE: Storage
      HPA: Horizontal Pod Autoscaling
      POD: Pod
      DAEMONSET: DaemonSet
      DEPLOYMENT: Deployment
      STATEFULSET: Statefulset
    """
    COMPONENT_UNSPECIFIED = 0
    SYSTEM_COMPONENTS = 1
    WORKLOADS = 2
    APISERVER = 3
    SCHEDULER = 4
    CONTROLLER_MANAGER = 5
    STORAGE = 6
    HPA = 7
    POD = 8
    DAEMONSET = 9
    DEPLOYMENT = 10
    STATEFULSET = 11

  enableComponents = _messages.EnumField('EnableComponentsValueListEntryValuesEnum', 1, repeated=True)


class MonitoringConfig(_messages.Message):
  r"""MonitoringConfig is cluster monitoring configuration.

  Fields:
    advancedDatapathObservabilityConfig: Configuration of Advanced Datapath
      Observability features.
    componentConfig: Monitoring components configuration
    managedPrometheusConfig: Enable Google Cloud Managed Service for
      Prometheus in the cluster.
  """

  advancedDatapathObservabilityConfig = _messages.MessageField('AdvancedDatapathObservabilityConfig', 1)
  componentConfig = _messages.MessageField('MonitoringComponentConfig', 2)
  managedPrometheusConfig = _messages.MessageField('ManagedPrometheusConfig', 3)


class NetworkConfig(_messages.Message):
  r"""NetworkConfig reports the relative names of network & subnetwork.

  Enums:
    DatapathProviderValueValuesEnum: The desired datapath provider for this
      cluster. By default, uses the IPTables-based kube-proxy implementation.
    InTransitEncryptionConfigValueValuesEnum: Specify the details of in-
      transit encryption.
    PrivateIpv6GoogleAccessValueValuesEnum: The desired state of IPv6
      connectivity to Google Services. By default, no private IPv6 access to
      or from Google Services (all access will be via IPv4)

  Fields:
    datapathProvider: The desired datapath provider for this cluster. By
      default, uses the IPTables-based kube-proxy implementation.
    defaultSnatStatus: Whether the cluster disables default in-node sNAT
      rules. In-node sNAT rules will be disabled when default_snat_status is
      disabled. When disabled is set to false, default IP masquerade rules
      will be applied to the nodes to prevent sNAT on cluster internal
      traffic.
    dnsConfig: DNSConfig contains clusterDNS config for this cluster.
    enableFqdnNetworkPolicy: Whether FQDN Network Policy is enabled on this
      cluster.
    enableIntraNodeVisibility: Whether Intra-node visibility is enabled for
      this cluster. This makes same node pod to pod traffic visible for VPC
      network.
    enableL4ilbSubsetting: Whether L4ILB Subsetting is enabled for this
      cluster.
    enableMultiNetworking: Whether multi-networking is enabled for this
      cluster.
    gatewayApiConfig: GatewayAPIConfig contains the desired config of Gateway
      API on this cluster.
    inTransitEncryptionConfig: Specify the details of in-transit encryption.
    network: Output only. The relative name of the Google Compute Engine
      network(https://cloud.google.com/compute/docs/networks-and-
      firewalls#networks) to which the cluster is connected. Example:
      projects/my-project/global/networks/my-network
    networkPerformanceConfig: Network bandwidth tier configuration.
    privateIpv6GoogleAccess: The desired state of IPv6 connectivity to Google
      Services. By default, no private IPv6 access to or from Google Services
      (all access will be via IPv4)
    serviceExternalIpsConfig: ServiceExternalIPsConfig specifies if services
      with externalIPs field are blocked or not.
    subnetwork: Output only. The relative name of the Google Compute Engine
      [subnetwork](https://cloud.google.com/compute/docs/vpc) to which the
      cluster is connected. Example: projects/my-project/regions/us-
      central1/subnetworks/my-subnet
  """

  class DatapathProviderValueValuesEnum(_messages.Enum):
    r"""The desired datapath provider for this cluster. By default, uses the
    IPTables-based kube-proxy implementation.

    Values:
      DATAPATH_PROVIDER_UNSPECIFIED: Default value.
      LEGACY_DATAPATH: Use the IPTables implementation based on kube-proxy.
      ADVANCED_DATAPATH: Use the eBPF based GKE Dataplane V2 with additional
        features. See the [GKE Dataplane V2
        documentation](https://cloud.google.com/kubernetes-engine/docs/how-
        to/dataplane-v2) for more.
      MIGRATE_TO_ADVANCED_DATAPATH: Cluster has some existing nodes but new
        nodes should use ADVANCED_DATAPATH.
      MIGRATE_TO_LEGACY_DATAPATH: Cluster has some existing nodes but new
        nodes should use LEGACY_DATAPATH.
    """
    DATAPATH_PROVIDER_UNSPECIFIED = 0
    LEGACY_DATAPATH = 1
    ADVANCED_DATAPATH = 2
    MIGRATE_TO_ADVANCED_DATAPATH = 3
    MIGRATE_TO_LEGACY_DATAPATH = 4

  class InTransitEncryptionConfigValueValuesEnum(_messages.Enum):
    r"""Specify the details of in-transit encryption.

    Values:
      IN_TRANSIT_ENCRYPTION_CONFIG_UNSPECIFIED: Unspecified, will be inferred
        as default - IN_TRANSIT_ENCRYPTION_UNSPECIFIED.
      IN_TRANSIT_ENCRYPTION_DISABLED: In-transit encryption is disabled.
      IN_TRANSIT_ENCRYPTION_INTER_NODE_TRANSPARENT: Data in-transit is
        encrypted with inter-node transparent encryption.
    """
    IN_TRANSIT_ENCRYPTION_CONFIG_UNSPECIFIED = 0
    IN_TRANSIT_ENCRYPTION_DISABLED = 1
    IN_TRANSIT_ENCRYPTION_INTER_NODE_TRANSPARENT = 2

  class PrivateIpv6GoogleAccessValueValuesEnum(_messages.Enum):
    r"""The desired state of IPv6 connectivity to Google Services. By default,
    no private IPv6 access to or from Google Services (all access will be via
    IPv4)

    Values:
      PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED: Default value. Same as DISABLED
      PRIVATE_IPV6_GOOGLE_ACCESS_DISABLED: No private access to or from Google
        Services
      PRIVATE_IPV6_GOOGLE_ACCESS_TO_GOOGLE: Enables private IPv6 access to
        Google Services from GKE
      PRIVATE_IPV6_GOOGLE_ACCESS_BIDIRECTIONAL: Enables private IPv6 access to
        and from Google Services
    """
    PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED = 0
    PRIVATE_IPV6_GOOGLE_ACCESS_DISABLED = 1
    PRIVATE_IPV6_GOOGLE_ACCESS_TO_GOOGLE = 2
    PRIVATE_IPV6_GOOGLE_ACCESS_BIDIRECTIONAL = 3

  datapathProvider = _messages.EnumField('DatapathProviderValueValuesEnum', 1)
  defaultSnatStatus = _messages.MessageField('DefaultSnatStatus', 2)
  dnsConfig = _messages.MessageField('DNSConfig', 3)
  enableFqdnNetworkPolicy = _messages.BooleanField(4)
  enableIntraNodeVisibility = _messages.BooleanField(5)
  enableL4ilbSubsetting = _messages.BooleanField(6)
  enableMultiNetworking = _messages.BooleanField(7)
  gatewayApiConfig = _messages.MessageField('GatewayAPIConfig', 8)
  inTransitEncryptionConfig = _messages.EnumField('InTransitEncryptionConfigValueValuesEnum', 9)
  network = _messages.StringField(10)
  networkPerformanceConfig = _messages.MessageField('ClusterNetworkPerformanceConfig', 11)
  privateIpv6GoogleAccess = _messages.EnumField('PrivateIpv6GoogleAccessValueValuesEnum', 12)
  serviceExternalIpsConfig = _messages.MessageField('ServiceExternalIPsConfig', 13)
  subnetwork = _messages.StringField(14)


class NetworkPerformanceConfig(_messages.Message):
  r"""Configuration of all network bandwidth tiers

  Enums:
    TotalEgressBandwidthTierValueValuesEnum: Specifies the total network
      bandwidth tier for the NodePool.

  Fields:
    totalEgressBandwidthTier: Specifies the total network bandwidth tier for
      the NodePool.
  """

  class TotalEgressBandwidthTierValueValuesEnum(_messages.Enum):
    r"""Specifies the total network bandwidth tier for the NodePool.

    Values:
      TIER_UNSPECIFIED: Default value
      TIER_1: Higher bandwidth, actual values based on VM size.
    """
    TIER_UNSPECIFIED = 0
    TIER_1 = 1

  totalEgressBandwidthTier = _messages.EnumField('TotalEgressBandwidthTierValueValuesEnum', 1)


class NetworkPolicy(_messages.Message):
  r"""Configuration options for the NetworkPolicy feature.
  https://kubernetes.io/docs/concepts/services-networking/networkpolicies/

  Enums:
    ProviderValueValuesEnum: The selected network policy provider.

  Fields:
    enabled: Whether network policy is enabled on the cluster.
    provider: The selected network policy provider.
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""The selected network policy provider.

    Values:
      PROVIDER_UNSPECIFIED: Not set
      CALICO: Tigera (Calico Felix).
    """
    PROVIDER_UNSPECIFIED = 0
    CALICO = 1

  enabled = _messages.BooleanField(1)
  provider = _messages.EnumField('ProviderValueValuesEnum', 2)


class NetworkPolicyConfig(_messages.Message):
  r"""Configuration for NetworkPolicy. This only tracks whether the addon is
  enabled or not on the Master, it does not track whether network policy is
  enabled for the nodes.

  Fields:
    disabled: Whether NetworkPolicy is enabled for this cluster.
  """

  disabled = _messages.BooleanField(1)


class NetworkTags(_messages.Message):
  r"""Collection of Compute Engine network tags that can be applied to a
  node's underlying VM instance.

  Fields:
    tags: List of network tags.
  """

  tags = _messages.StringField(1, repeated=True)


class NodeAffinity(_messages.Message):
  r"""Specifies the NodeAffinity key, values, and affinity operator according
  to [shared sole tenant node group
  affinities](https://cloud.google.com/compute/docs/nodes/sole-tenant-
  nodes#node_affinity_and_anti-affinity).

  Enums:
    OperatorValueValuesEnum: Operator for NodeAffinity.

  Fields:
    key: Key for NodeAffinity.
    operator: Operator for NodeAffinity.
    values: Values for NodeAffinity.
  """

  class OperatorValueValuesEnum(_messages.Enum):
    r"""Operator for NodeAffinity.

    Values:
      OPERATOR_UNSPECIFIED: Invalid or unspecified affinity operator.
      IN: Affinity operator.
      NOT_IN: Anti-affinity operator.
    """
    OPERATOR_UNSPECIFIED = 0
    IN = 1
    NOT_IN = 2

  key = _messages.StringField(1)
  operator = _messages.EnumField('OperatorValueValuesEnum', 2)
  values = _messages.StringField(3, repeated=True)


class NodeConfig(_messages.Message):
  r"""Parameters that describe the nodes in a cluster. GKE Autopilot clusters
  do not recognize parameters in `NodeConfig`. Use
  AutoprovisioningNodePoolDefaults instead.

  Messages:
    LabelsValue: The map of Kubernetes labels (key/value pairs) to be applied
      to each node. These will added in addition to any default label(s) that
      Kubernetes may apply to the node. In case of conflict in label keys, the
      applied set may differ depending on the Kubernetes version -- it's best
      to assume the behavior is undefined and conflicts should be avoided. For
      more information, including usage and the valid values, see:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/labels/
    MetadataValue: The metadata key/value pairs assigned to instances in the
      cluster. Keys must conform to the regexp `[a-zA-Z0-9-_]+` and be less
      than 128 bytes in length. These are reflected as part of a URL in the
      metadata server. Additionally, to avoid ambiguity, keys must not
      conflict with any other metadata keys for the project or be one of the
      reserved keys: - "cluster-location" - "cluster-name" - "cluster-uid" -
      "configure-sh" - "containerd-configure-sh" - "enable-os-login" - "gci-
      ensure-gke-docker" - "gci-metrics-enabled" - "gci-update-strategy" -
      "instance-template" - "kube-env" - "startup-script" - "user-data" -
      "disable-address-manager" - "windows-startup-script-ps1" - "common-psm1"
      - "k8s-node-setup-psm1" - "install-ssh-psm1" - "user-profile-psm1"
      Values are free-form strings, and only have meaning as interpreted by
      the image running in the instance. The only restriction placed on them
      is that each value's size must be less than or equal to 32 KB. The total
      size of all keys and values must be less than 512 KB.
    ResourceLabelsValue: The resource labels for the node pool to use to
      annotate any related Google Compute Engine resources.

  Fields:
    accelerators: A list of hardware accelerators to be attached to each node.
      See https://cloud.google.com/compute/docs/gpus for more information
      about support for GPUs.
    advancedMachineFeatures: Advanced features for the Compute Engine VM.
    bootDiskKmsKey:  The Customer Managed Encryption Key used to encrypt the
      boot disk attached to each node in the node pool. This should be of the
      form projects/[KEY_PROJECT_ID]/locations/[LOCATION]/keyRings/[RING_NAME]
      /cryptoKeys/[KEY_NAME]. For more information about protecting resources
      with Cloud KMS Keys please see:
      https://cloud.google.com/compute/docs/disks/customer-managed-encryption
    confidentialNodes: Confidential nodes config. All the nodes in the node
      pool will be Confidential VM once enabled.
    containerdConfig: Parameters for containerd customization.
    diskSizeGb: Size of the disk attached to each node, specified in GB. The
      smallest allowed disk size is 10GB. If unspecified, the default disk
      size is 100GB.
    diskType: Type of the disk attached to each node (e.g. 'pd-standard', 'pd-
      ssd' or 'pd-balanced') If unspecified, the default disk type is 'pd-
      standard'
    ephemeralStorageLocalSsdConfig: Parameters for the node ephemeral storage
      using Local SSDs. If unspecified, ephemeral storage is backed by the
      boot disk.
    fastSocket: Enable or disable NCCL fast socket for the node pool.
    gcfsConfig: Google Container File System (image streaming) configs.
    gvnic: Enable or disable gvnic in the node pool.
    imageType: The image type to use for this node. Note that for a given
      image type, the latest version of it will be used. Please see
      https://cloud.google.com/kubernetes-engine/docs/concepts/node-images for
      available image types.
    kubeletConfig: Node kubelet configs.
    labels: The map of Kubernetes labels (key/value pairs) to be applied to
      each node. These will added in addition to any default label(s) that
      Kubernetes may apply to the node. In case of conflict in label keys, the
      applied set may differ depending on the Kubernetes version -- it's best
      to assume the behavior is undefined and conflicts should be avoided. For
      more information, including usage and the valid values, see:
      https://kubernetes.io/docs/concepts/overview/working-with-
      objects/labels/
    linuxNodeConfig: Parameters that can be configured on Linux nodes.
    localNvmeSsdBlockConfig: Parameters for using raw-block Local NVMe SSDs.
    localSsdCount: The number of local SSD disks to be attached to the node.
      The limit for this value is dependent upon the maximum number of disks
      available on a machine per zone. See:
      https://cloud.google.com/compute/docs/disks/local-ssd for more
      information.
    loggingConfig: Logging configuration.
    machineType: The name of a Google Compute Engine [machine
      type](https://cloud.google.com/compute/docs/machine-types) If
      unspecified, the default machine type is `e2-medium`.
    metadata: The metadata key/value pairs assigned to instances in the
      cluster. Keys must conform to the regexp `[a-zA-Z0-9-_]+` and be less
      than 128 bytes in length. These are reflected as part of a URL in the
      metadata server. Additionally, to avoid ambiguity, keys must not
      conflict with any other metadata keys for the project or be one of the
      reserved keys: - "cluster-location" - "cluster-name" - "cluster-uid" -
      "configure-sh" - "containerd-configure-sh" - "enable-os-login" - "gci-
      ensure-gke-docker" - "gci-metrics-enabled" - "gci-update-strategy" -
      "instance-template" - "kube-env" - "startup-script" - "user-data" -
      "disable-address-manager" - "windows-startup-script-ps1" - "common-psm1"
      - "k8s-node-setup-psm1" - "install-ssh-psm1" - "user-profile-psm1"
      Values are free-form strings, and only have meaning as interpreted by
      the image running in the instance. The only restriction placed on them
      is that each value's size must be less than or equal to 32 KB. The total
      size of all keys and values must be less than 512 KB.
    minCpuPlatform: Minimum CPU platform to be used by this instance. The
      instance may be scheduled on the specified or newer CPU platform.
      Applicable values are the friendly names of CPU platforms, such as
      `minCpuPlatform: "Intel Haswell"` or `minCpuPlatform: "Intel Sandy
      Bridge"`. For more information, read [how to specify min CPU
      platform](https://cloud.google.com/compute/docs/instances/specify-min-
      cpu-platform)
    nodeGroup: Setting this field will assign instances of this pool to run on
      the specified node group. This is useful for running workloads on [sole
      tenant nodes](https://cloud.google.com/compute/docs/nodes/sole-tenant-
      nodes).
    nodeImageConfig: The node image configuration to use for this node pool.
      Note that this is only applicable for node pools using
      image_type=CUSTOM.
    oauthScopes: The set of Google API scopes to be made available on all of
      the node VMs under the "default" service account. The following scopes
      are recommended, but not required, and by default are not included: *
      `https://www.googleapis.com/auth/compute` is required for mounting
      persistent storage on your nodes. *
      `https://www.googleapis.com/auth/devstorage.read_only` is required for
      communicating with **gcr.io** (the [Google Container
      Registry](https://cloud.google.com/container-registry/)). If
      unspecified, no scopes are added, unless Cloud Logging or Cloud
      Monitoring are enabled, in which case their required scopes will be
      added.
    preemptible: Whether the nodes are created as preemptible VM instances.
      See: https://cloud.google.com/compute/docs/instances/preemptible for
      more information about preemptible VM instances.
    reservationAffinity: The optional reservation affinity. Setting this field
      will apply the specified [Zonal Compute
      Reservation](https://cloud.google.com/compute/docs/instances/reserving-
      zonal-resources) to this node pool.
    resourceLabels: The resource labels for the node pool to use to annotate
      any related Google Compute Engine resources.
    resourceManagerTags: A map of resource manager tag keys and values to be
      attached to the nodes.
    sandboxConfig: Sandbox configuration for this node.
    secondaryBootDisks: List of secondary boot disks attached to the nodes.
    serviceAccount: The Google Cloud Platform Service Account to be used by
      the node VMs. Specify the email address of the Service Account;
      otherwise, if no Service Account is specified, the "default" service
      account is used.
    shieldedInstanceConfig: Shielded Instance options.
    soleTenantConfig: Parameters for node pools to be backed by shared sole
      tenant node groups.
    spot: Spot flag for enabling Spot VM, which is a rebrand of the existing
      preemptible flag.
    tags: The list of instance tags applied to all nodes. Tags are used to
      identify valid sources or targets for network firewalls and are
      specified by the client during cluster or node pool creation. Each tag
      within the list must comply with RFC1035.
    taints: List of kubernetes taints to be applied to each node. For more
      information, including usage and the valid values, see:
      https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
    windowsNodeConfig: Parameters that can be configured on Windows nodes.
    workloadMetadataConfig: The workload metadata configuration for this node.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The map of Kubernetes labels (key/value pairs) to be applied to each
    node. These will added in addition to any default label(s) that Kubernetes
    may apply to the node. In case of conflict in label keys, the applied set
    may differ depending on the Kubernetes version -- it's best to assume the
    behavior is undefined and conflicts should be avoided. For more
    information, including usage and the valid values, see:
    https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""The metadata key/value pairs assigned to instances in the cluster.
    Keys must conform to the regexp `[a-zA-Z0-9-_]+` and be less than 128
    bytes in length. These are reflected as part of a URL in the metadata
    server. Additionally, to avoid ambiguity, keys must not conflict with any
    other metadata keys for the project or be one of the reserved keys: -
    "cluster-location" - "cluster-name" - "cluster-uid" - "configure-sh" -
    "containerd-configure-sh" - "enable-os-login" - "gci-ensure-gke-docker" -
    "gci-metrics-enabled" - "gci-update-strategy" - "instance-template" -
    "kube-env" - "startup-script" - "user-data" - "disable-address-manager" -
    "windows-startup-script-ps1" - "common-psm1" - "k8s-node-setup-psm1" -
    "install-ssh-psm1" - "user-profile-psm1" Values are free-form strings, and
    only have meaning as interpreted by the image running in the instance. The
    only restriction placed on them is that each value's size must be less
    than or equal to 32 KB. The total size of all keys and values must be less
    than 512 KB.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceLabelsValue(_messages.Message):
    r"""The resource labels for the node pool to use to annotate any related
    Google Compute Engine resources.

    Messages:
      AdditionalProperty: An additional property for a ResourceLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ResourceLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accelerators = _messages.MessageField('AcceleratorConfig', 1, repeated=True)
  advancedMachineFeatures = _messages.MessageField('AdvancedMachineFeatures', 2)
  bootDiskKmsKey = _messages.StringField(3)
  confidentialNodes = _messages.MessageField('ConfidentialNodes', 4)
  containerdConfig = _messages.MessageField('ContainerdConfig', 5)
  diskSizeGb = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  diskType = _messages.StringField(7)
  ephemeralStorageLocalSsdConfig = _messages.MessageField('EphemeralStorageLocalSsdConfig', 8)
  fastSocket = _messages.MessageField('FastSocket', 9)
  gcfsConfig = _messages.MessageField('GcfsConfig', 10)
  gvnic = _messages.MessageField('VirtualNIC', 11)
  imageType = _messages.StringField(12)
  kubeletConfig = _messages.MessageField('NodeKubeletConfig', 13)
  labels = _messages.MessageField('LabelsValue', 14)
  linuxNodeConfig = _messages.MessageField('LinuxNodeConfig', 15)
  localNvmeSsdBlockConfig = _messages.MessageField('LocalNvmeSsdBlockConfig', 16)
  localSsdCount = _messages.IntegerField(17, variant=_messages.Variant.INT32)
  loggingConfig = _messages.MessageField('NodePoolLoggingConfig', 18)
  machineType = _messages.StringField(19)
  metadata = _messages.MessageField('MetadataValue', 20)
  minCpuPlatform = _messages.StringField(21)
  nodeGroup = _messages.StringField(22)
  nodeImageConfig = _messages.MessageField('CustomImageConfig', 23)
  oauthScopes = _messages.StringField(24, repeated=True)
  preemptible = _messages.BooleanField(25)
  reservationAffinity = _messages.MessageField('ReservationAffinity', 26)
  resourceLabels = _messages.MessageField('ResourceLabelsValue', 27)
  resourceManagerTags = _messages.MessageField('ResourceManagerTags', 28)
  sandboxConfig = _messages.MessageField('SandboxConfig', 29)
  secondaryBootDisks = _messages.MessageField('SecondaryBootDisk', 30, repeated=True)
  serviceAccount = _messages.StringField(31)
  shieldedInstanceConfig = _messages.MessageField('ShieldedInstanceConfig', 32)
  soleTenantConfig = _messages.MessageField('SoleTenantConfig', 33)
  spot = _messages.BooleanField(34)
  tags = _messages.StringField(35, repeated=True)
  taints = _messages.MessageField('NodeTaint', 36, repeated=True)
  windowsNodeConfig = _messages.MessageField('WindowsNodeConfig', 37)
  workloadMetadataConfig = _messages.MessageField('WorkloadMetadataConfig', 38)


class NodeConfigDefaults(_messages.Message):
  r"""Subset of NodeConfig message that has defaults.

  Fields:
    containerdConfig: Parameters for containerd customization.
    gcfsConfig: GCFS (Google Container File System, also known as Riptide)
      options.
    loggingConfig: Logging configuration for node pools.
  """

  containerdConfig = _messages.MessageField('ContainerdConfig', 1)
  gcfsConfig = _messages.MessageField('GcfsConfig', 2)
  loggingConfig = _messages.MessageField('NodePoolLoggingConfig', 3)


class NodeKubeletConfig(_messages.Message):
  r"""Node kubelet configs.

  Fields:
    cpuCfsQuota: Enable CPU CFS quota enforcement for containers that specify
      CPU limits. This option is enabled by default which makes kubelet use
      CFS quota (https://www.kernel.org/doc/Documentation/scheduler/sched-
      bwc.txt) to enforce container CPU limits. Otherwise, CPU limits will not
      be enforced at all. Disable this option to mitigate CPU throttling
      problems while still having your pods to be in Guaranteed QoS class by
      specifying the CPU limits. The default value is 'true' if unspecified.
    cpuCfsQuotaPeriod: Set the CPU CFS quota period value 'cpu.cfs_period_us'.
      The string must be a sequence of decimal numbers, each with optional
      fraction and a unit suffix, such as "300ms". Valid time units are "ns",
      "us" (or "\xb5s"), "ms", "s", "m", "h". The value must be a positive
      duration.
    cpuManagerPolicy: Control the CPU management policy on the node. See
      https://kubernetes.io/docs/tasks/administer-cluster/cpu-management-
      policies/ The following values are allowed. * "none": the default, which
      represents the existing scheduling behavior. * "static": allows pods
      with certain resource characteristics to be granted increased CPU
      affinity and exclusivity on the node. The default value is 'none' if
      unspecified.
    insecureKubeletReadonlyPortEnabled: Enable or disable Kubelet read only
      port.
    podPidsLimit: Set the Pod PID limits. See
      https://kubernetes.io/docs/concepts/policy/pid-limiting/#pod-pid-limits
      Controls the maximum number of processes allowed to run in a pod. The
      value must be greater than or equal to 1024 and less than 4194304.
  """

  cpuCfsQuota = _messages.BooleanField(1)
  cpuCfsQuotaPeriod = _messages.StringField(2)
  cpuManagerPolicy = _messages.StringField(3)
  insecureKubeletReadonlyPortEnabled = _messages.BooleanField(4)
  podPidsLimit = _messages.IntegerField(5)


class NodeLabels(_messages.Message):
  r"""Collection of node-level [Kubernetes
  labels](https://kubernetes.io/docs/concepts/overview/working-with-
  objects/labels).

  Messages:
    LabelsValue: Map of node label keys and node label values.

  Fields:
    labels: Map of node label keys and node label values.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Map of node label keys and node label values.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class NodeManagement(_messages.Message):
  r"""NodeManagement defines the set of node management services turned on for
  the node pool.

  Fields:
    autoRepair: A flag that specifies whether the node auto-repair is enabled
      for the node pool. If enabled, the nodes in this node pool will be
      monitored and, if they fail health checks too many times, an automatic
      repair action will be triggered.
    autoUpgrade: A flag that specifies whether node auto-upgrade is enabled
      for the node pool. If enabled, node auto-upgrade helps keep the nodes in
      your node pool up to date with the latest release version of Kubernetes.
    upgradeOptions: Specifies the Auto Upgrade knobs for the node pool.
  """

  autoRepair = _messages.BooleanField(1)
  autoUpgrade = _messages.BooleanField(2)
  upgradeOptions = _messages.MessageField('AutoUpgradeOptions', 3)


class NodeNetworkConfig(_messages.Message):
  r"""Parameters for node pool-level network config.

  Fields:
    additionalNodeNetworkConfigs: We specify the additional node networks for
      this node pool using this list. Each node network corresponds to an
      additional interface
    additionalPodNetworkConfigs: We specify the additional pod networks for
      this node pool using this list. Each pod network corresponds to an
      additional alias IP range for the node
    createPodRange: Input only. Whether to create a new range for pod IPs in
      this node pool. Defaults are provided for `pod_range` and
      `pod_ipv4_cidr_block` if they are not specified. If neither
      `create_pod_range` or `pod_range` are specified, the cluster-level
      default (`ip_allocation_policy.cluster_ipv4_cidr_block`) is used. Only
      applicable if `ip_allocation_policy.use_ip_aliases` is true. This field
      cannot be changed after the node pool has been created.
    enablePrivateNodes: Whether nodes have internal IP addresses only. If
      enable_private_nodes is not specified, then the value is derived from
      cluster.privateClusterConfig.enablePrivateNodes
    networkPerformanceConfig: Network bandwidth tier configuration.
    podCidrOverprovisionConfig: [PRIVATE FIELD] Pod CIDR size overprovisioning
      config for the nodepool. Pod CIDR size per node depends on
      max_pods_per_node. By default, the value of max_pods_per_node is rounded
      off to next power of 2 and we then double that to get the size of pod
      CIDR block per node. Example: max_pods_per_node of 30 would result in 64
      IPs (/26). This config can disable the doubling of IPs (we still round
      off to next power of 2) Example: max_pods_per_node of 30 will result in
      32 IPs (/27) when overprovisioning is disabled.
    podIpv4CidrBlock: The IP address range for pod IPs in this node pool. Only
      applicable if `create_pod_range` is true. Set to blank to have a range
      chosen with the default size. Set to /netmask (e.g. `/14`) to have a
      range chosen with a specific netmask. Set to a
      [CIDR](https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
      notation (e.g. `*********/14`) to pick a specific range to use. Only
      applicable if `ip_allocation_policy.use_ip_aliases` is true. This field
      cannot be changed after the node pool has been created.
    podIpv4RangeUtilization: Output only. [Output only] The utilization of the
      IPv4 range for the pod. The ratio is Usage/[Total number of IPs in the
      secondary range], Usage=numNodes*numZones*podIPsPerNode.
    podRange: The ID of the secondary range for pod IPs. If `create_pod_range`
      is true, this ID is used for the new range. If `create_pod_range` is
      false, uses an existing secondary range with this ID. Only applicable if
      `ip_allocation_policy.use_ip_aliases` is true. This field cannot be
      changed after the node pool has been created.
  """

  additionalNodeNetworkConfigs = _messages.MessageField('AdditionalNodeNetworkConfig', 1, repeated=True)
  additionalPodNetworkConfigs = _messages.MessageField('AdditionalPodNetworkConfig', 2, repeated=True)
  createPodRange = _messages.BooleanField(3)
  enablePrivateNodes = _messages.BooleanField(4)
  networkPerformanceConfig = _messages.MessageField('NetworkPerformanceConfig', 5)
  podCidrOverprovisionConfig = _messages.MessageField('PodCIDROverprovisionConfig', 6)
  podIpv4CidrBlock = _messages.StringField(7)
  podIpv4RangeUtilization = _messages.FloatField(8)
  podRange = _messages.StringField(9)


class NodePool(_messages.Message):
  r"""NodePool contains the name and configuration for a cluster's node pool.
  Node pools are a set of nodes (i.e. VM's), with a common configuration and
  specification, under the control of the cluster master. They may have a set
  of Kubernetes labels applied to them, which may be used to reference them
  during pod scheduling. They may also be resized up or down, to accommodate
  the workload.

  Enums:
    StatusValueValuesEnum: [Output only] The status of the nodes in this pool
      instance.

  Fields:
    autoscaling: Autoscaler configuration for this NodePool. Autoscaler is
      enabled only if a valid configuration is present.
    bestEffortProvisioning: Enable best effort provisioning for nodes
    conditions: Which conditions caused the current node pool state.
    config: The node configuration of the pool.
    etag: This checksum is computed by the server based on the value of node
      pool fields, and may be sent on update requests to ensure the client has
      an up-to-date value before proceeding.
    initialNodeCount: The initial node count for the pool. You must ensure
      that your Compute Engine [resource
      quota](https://cloud.google.com/compute/quotas) is sufficient for this
      number of instances. You must also have available firewall and routes
      quota.
    instanceGroupUrls: [Output only] The resource URLs of the [managed
      instance groups](https://cloud.google.com/compute/docs/instance-
      groups/creating-groups-of-managed-instances) associated with this node
      pool. During the node pool blue-green upgrade operation, the URLs
      contain both blue and green resources.
    locations: The list of Google Compute Engine
      [zones](https://cloud.google.com/compute/docs/zones#available) in which
      the NodePool's nodes should be located. If this value is unspecified
      during node pool creation, the
      [Cluster.Locations](https://cloud.google.com/kubernetes-engine/docs/refe
      rence/rest/v1/projects.locations.clusters#Cluster.FIELDS.locations)
      value will be used, instead. Warning: changing node pool locations will
      result in nodes being added and/or removed.
    management: NodeManagement configuration for this NodePool.
    maxPodsConstraint: The constraint on the maximum number of pods that can
      be run simultaneously on a node in the node pool.
    name: The name of the node pool.
    networkConfig: Networking configuration for this NodePool. If specified,
      it overrides the cluster-level defaults.
    placementPolicy: Specifies the node placement policy.
    podIpv4CidrSize: [Output only] The pod CIDR block size per node in this
      node pool.
    queuedProvisioning: Specifies the configuration of queued provisioning.
    selfLink: [Output only] Server-defined URL for the resource.
    status: [Output only] The status of the nodes in this pool instance.
    statusMessage: [Output only] Deprecated. Use conditions instead.
      Additional information about the current status of this node pool
      instance, if available.
    updateInfo: Output only. [Output only] Update info contains relevant
      information during a node pool update.
    upgradeSettings: Upgrade settings control disruption and speed of the
      upgrade.
    version: The version of Kubernetes running on this NodePool's nodes. If
      unspecified, it defaults as described
      [here](https://cloud.google.com/kubernetes-
      engine/versioning#specifying_node_version).
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""[Output only] The status of the nodes in this pool instance.

    Values:
      STATUS_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the node pool is being
        created.
      RUNNING: The RUNNING state indicates the node pool has been created and
        is fully usable.
      RUNNING_WITH_ERROR: The RUNNING_WITH_ERROR state indicates the node pool
        has been created and is partially usable. Some error state has
        occurred and some functionality may be impaired. Customer may need to
        reissue a request or trigger a new update.
      RECONCILING: The RECONCILING state indicates that some work is actively
        being done on the node pool, such as upgrading node software. Details
        can be found in the `statusMessage` field.
      STOPPING: The STOPPING state indicates the node pool is being deleted.
      ERROR: The ERROR state indicates the node pool may be unusable. Details
        can be found in the `statusMessage` field.
    """
    STATUS_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RUNNING_WITH_ERROR = 3
    RECONCILING = 4
    STOPPING = 5
    ERROR = 6

  autoscaling = _messages.MessageField('NodePoolAutoscaling', 1)
  bestEffortProvisioning = _messages.MessageField('BestEffortProvisioning', 2)
  conditions = _messages.MessageField('StatusCondition', 3, repeated=True)
  config = _messages.MessageField('NodeConfig', 4)
  etag = _messages.StringField(5)
  initialNodeCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  instanceGroupUrls = _messages.StringField(7, repeated=True)
  locations = _messages.StringField(8, repeated=True)
  management = _messages.MessageField('NodeManagement', 9)
  maxPodsConstraint = _messages.MessageField('MaxPodsConstraint', 10)
  name = _messages.StringField(11)
  networkConfig = _messages.MessageField('NodeNetworkConfig', 12)
  placementPolicy = _messages.MessageField('PlacementPolicy', 13)
  podIpv4CidrSize = _messages.IntegerField(14, variant=_messages.Variant.INT32)
  queuedProvisioning = _messages.MessageField('QueuedProvisioning', 15)
  selfLink = _messages.StringField(16)
  status = _messages.EnumField('StatusValueValuesEnum', 17)
  statusMessage = _messages.StringField(18)
  updateInfo = _messages.MessageField('UpdateInfo', 19)
  upgradeSettings = _messages.MessageField('UpgradeSettings', 20)
  version = _messages.StringField(21)


class NodePoolAutoConfig(_messages.Message):
  r"""Node pool configs that apply to all auto-provisioned node pools in
  autopilot clusters and node auto-provisioning enabled clusters.

  Fields:
    networkTags: The list of instance tags applied to all nodes. Tags are used
      to identify valid sources or targets for network firewalls and are
      specified by the client during cluster creation. Each tag within the
      list must comply with RFC1035.
    resourceManagerTags: Resource manager tag keys and values to be attached
      to the nodes for managing Compute Engine firewalls using Network
      Firewall Policies.
  """

  networkTags = _messages.MessageField('NetworkTags', 1)
  resourceManagerTags = _messages.MessageField('ResourceManagerTags', 2)


class NodePoolAutoscaling(_messages.Message):
  r"""NodePoolAutoscaling contains information required by cluster autoscaler
  to adjust the size of the node pool to the current cluster usage.

  Enums:
    LocationPolicyValueValuesEnum: Location policy used when scaling up a
      nodepool.

  Fields:
    autoprovisioned: Can this node pool be deleted automatically.
    enabled: Is autoscaling enabled for this node pool.
    locationPolicy: Location policy used when scaling up a nodepool.
    maxNodeCount: Maximum number of nodes for one location in the NodePool.
      Must be >= min_node_count. There has to be enough quota to scale up the
      cluster.
    minNodeCount: Minimum number of nodes for one location in the NodePool.
      Must be >= 1 and <= max_node_count.
    totalMaxNodeCount: Maximum number of nodes in the node pool. Must be
      greater than total_min_node_count. There has to be enough quota to scale
      up the cluster. The total_*_node_count fields are mutually exclusive
      with the *_node_count fields.
    totalMinNodeCount: Minimum number of nodes in the node pool. Must be
      greater than 1 less than total_max_node_count. The total_*_node_count
      fields are mutually exclusive with the *_node_count fields.
  """

  class LocationPolicyValueValuesEnum(_messages.Enum):
    r"""Location policy used when scaling up a nodepool.

    Values:
      LOCATION_POLICY_UNSPECIFIED: Not set.
      BALANCED: BALANCED is a best effort policy that aims to balance the
        sizes of different zones.
      ANY: ANY policy picks zones that have the highest capacity available.
    """
    LOCATION_POLICY_UNSPECIFIED = 0
    BALANCED = 1
    ANY = 2

  autoprovisioned = _messages.BooleanField(1)
  enabled = _messages.BooleanField(2)
  locationPolicy = _messages.EnumField('LocationPolicyValueValuesEnum', 3)
  maxNodeCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  minNodeCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  totalMaxNodeCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  totalMinNodeCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class NodePoolDefaults(_messages.Message):
  r"""Subset of Nodepool message that has defaults.

  Fields:
    nodeConfigDefaults: Subset of NodeConfig message that has defaults.
  """

  nodeConfigDefaults = _messages.MessageField('NodeConfigDefaults', 1)


class NodePoolLoggingConfig(_messages.Message):
  r"""NodePoolLoggingConfig specifies logging configuration for nodepools.

  Fields:
    variantConfig: Logging variant configuration.
  """

  variantConfig = _messages.MessageField('LoggingVariantConfig', 1)


class NodeTaint(_messages.Message):
  r"""Kubernetes taint is composed of three fields: key, value, and effect.
  Effect can only be one of three types: NoSchedule, PreferNoSchedule or
  NoExecute. See
  [here](https://kubernetes.io/docs/concepts/configuration/taint-and-
  toleration) for more information, including usage and the valid values.

  Enums:
    EffectValueValuesEnum: Effect for taint.

  Fields:
    effect: Effect for taint.
    key: Key for taint.
    value: Value for taint.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""Effect for taint.

    Values:
      EFFECT_UNSPECIFIED: Not set
      NO_SCHEDULE: NoSchedule
      PREFER_NO_SCHEDULE: PreferNoSchedule
      NO_EXECUTE: NoExecute
    """
    EFFECT_UNSPECIFIED = 0
    NO_SCHEDULE = 1
    PREFER_NO_SCHEDULE = 2
    NO_EXECUTE = 3

  effect = _messages.EnumField('EffectValueValuesEnum', 1)
  key = _messages.StringField(2)
  value = _messages.StringField(3)


class NodeTaints(_messages.Message):
  r"""Collection of Kubernetes [node
  taints](https://kubernetes.io/docs/concepts/configuration/taint-and-
  toleration).

  Fields:
    taints: List of node taints.
  """

  taints = _messages.MessageField('NodeTaint', 1, repeated=True)


class NotificationConfig(_messages.Message):
  r"""NotificationConfig is the configuration of notifications.

  Fields:
    pubsub: Notification config for Pub/Sub.
  """

  pubsub = _messages.MessageField('PubSub', 1)


class Operation(_messages.Message):
  r"""This operation resource represents operations that may have happened or
  are happening on the cluster. All fields are output only.

  Enums:
    OperationTypeValueValuesEnum: The operation type.
    StatusValueValuesEnum: The current status of the operation.

  Fields:
    clusterConditions: Which conditions caused the current cluster state.
      Deprecated. Use field error instead.
    detail: Detailed operation progress, if available.
    endTime: [Output only] The time the operation completed, in
      [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
    error: The error result of the operation in case of failure.
    location: [Output only] The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/regions-zones/regions-
      zones#available) or
      [region](https://cloud.google.com/compute/docs/regions-zones/regions-
      zones#available) in which the cluster resides.
    name: The server-assigned ID for the operation.
    nodepoolConditions: Which conditions caused the current node pool state.
      Deprecated. Use field error instead.
    operationType: The operation type.
    progress: Output only. [Output only] Progress information for an
      operation.
    selfLink: Server-defined URI for the operation. Example:
      `https://container.googleapis.com/v1alpha1/projects/123/locations/us-
      central1/operations/operation-123`.
    startTime: [Output only] The time the operation started, in
      [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
    status: The current status of the operation.
    statusMessage: Output only. If an error has occurred, a textual
      description of the error. Deprecated. Use the field error instead.
    targetLink: Server-defined URI for the target of the operation. The format
      of this is a URI to the resource being modified (such as a cluster, node
      pool, or node). For node pool repairs, there may be multiple nodes being
      repaired, but only one will be the target. Examples: - ##
      `https://container.googleapis.com/v1/projects/123/locations/us-
      central1/clusters/my-cluster` ##
      `https://container.googleapis.com/v1/projects/123/zones/us-
      central1-c/clusters/my-cluster/nodePools/my-np`
      `https://container.googleapis.com/v1/projects/123/zones/us-
      central1-c/clusters/my-cluster/nodePools/my-np/node/my-node`
    zone: The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the operation is taking place. This field is deprecated, use location
      instead.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""The operation type.

    Values:
      TYPE_UNSPECIFIED: Not set.
      CREATE_CLUSTER: The cluster is being created. The cluster should be
        assumed to be unusable until the operation finishes. In the event of
        the operation failing, the cluster will enter the ERROR state and
        eventually be deleted.
      DELETE_CLUSTER: The cluster is being deleted. The cluster should be
        assumed to be unusable as soon as this operation starts. In the event
        of the operation failing, the cluster will enter the ERROR state and
        the deletion will be automatically retried until completed.
      UPGRADE_MASTER: The cluster version is being updated. Note that this
        includes "upgrades" to the same version, which are simply a
        recreation. This also includes [auto-
        upgrades](https://cloud.google.com/kubernetes-
        engine/docs/concepts/cluster-upgrades#upgrading_automatically). For
        more details, see [documentation on cluster
        upgrades](https://cloud.google.com/kubernetes-
        engine/docs/concepts/cluster-upgrades#cluster_upgrades).
      UPGRADE_NODES: A node pool is being updated. Despite calling this an
        "upgrade", this includes most forms of updates to node pools. This
        also includes [auto-upgrades](https://cloud.google.com/kubernetes-
        engine/docs/how-to/node-auto-upgrades). This operation sets the
        progress field and may be canceled. The upgrade strategy depends on
        [node pool configuration](https://cloud.google.com/kubernetes-
        engine/docs/concepts/node-pool-upgrade-strategies). The nodes are
        generally still usable during this operation.
      REPAIR_CLUSTER: A problem has been detected with the control plane and
        is being repaired. This operation type is initiated by GKE. For more
        details, see [documentation on
        repairs](https://cloud.google.com/kubernetes-
        engine/docs/concepts/maintenance-windows-and-exclusions#repairs).
      UPDATE_CLUSTER: The cluster is being updated. This is a broad category
        of operations and includes operations that only change metadata as
        well as those that must recreate the entire cluster. If the control
        plane must be recreated, this will cause temporary downtime for zonal
        clusters. Some features require recreating the nodes as well. Those
        will be recreated as separate operations and the update may not be
        completely functional until the node pools recreations finish. Node
        recreations will generally follow [maintenance
        policies](https://cloud.google.com/kubernetes-
        engine/docs/concepts/maintenance-windows-and-exclusions). Some GKE-
        initiated operations use this type. This includes certain types of
        auto-upgrades and incident mitigations.
      CREATE_NODE_POOL: A node pool is being created. The node pool should be
        assumed to be unusable until this operation finishes. In the event of
        an error, the node pool may be partially created. If enabled, [node
        autoprovisioning](https://cloud.google.com/kubernetes-engine/docs/how-
        to/node-auto-provisioning) may have automatically initiated such
        operations.
      DELETE_NODE_POOL: The node pool is being deleted. The node pool should
        be assumed to be unusable as soon as this operation starts.
      SET_NODE_POOL_MANAGEMENT: The node pool's manamagent field is being
        updated. These operations only update metadata and may be concurrent
        with most other operations.
      AUTO_REPAIR_NODES: A problem has been detected with nodes and [they are
        being repaired](https://cloud.google.com/kubernetes-engine/docs/how-
        to/node-auto-repair). This operation type is initiated by GKE,
        typically automatically. This operation may be concurrent with other
        operations and there may be multiple repairs occurring on the same
        node pool.
      AUTO_UPGRADE_NODES: Unused. Automatic node upgrade uses UPGRADE_NODES.
      SET_LABELS: Unused. Updating labels uses UPDATE_CLUSTER.
      SET_MASTER_AUTH: Unused. Updating master auth uses UPDATE_CLUSTER.
      SET_NODE_POOL_SIZE: The node pool is being resized. With the exception
        of resizing to or from size zero, the node pool is generally usable
        during this operation.
      SET_NETWORK_POLICY: Unused. Updating network policy uses UPDATE_CLUSTER.
      SET_MAINTENANCE_POLICY: Unused. Updating maintenance policy uses
        UPDATE_CLUSTER.
      RESIZE_CLUSTER: The control plane is being resized. This operation type
        is initiated by GKE. These operations are often performed preemptively
        to ensure that the control plane has sufficient resources and is not
        typically an indication of issues. For more details, see
        [documentation on resizes](https://cloud.google.com/kubernetes-
        engine/docs/concepts/maintenance-windows-and-exclusions#repairs).
    """
    TYPE_UNSPECIFIED = 0
    CREATE_CLUSTER = 1
    DELETE_CLUSTER = 2
    UPGRADE_MASTER = 3
    UPGRADE_NODES = 4
    REPAIR_CLUSTER = 5
    UPDATE_CLUSTER = 6
    CREATE_NODE_POOL = 7
    DELETE_NODE_POOL = 8
    SET_NODE_POOL_MANAGEMENT = 9
    AUTO_REPAIR_NODES = 10
    AUTO_UPGRADE_NODES = 11
    SET_LABELS = 12
    SET_MASTER_AUTH = 13
    SET_NODE_POOL_SIZE = 14
    SET_NETWORK_POLICY = 15
    SET_MAINTENANCE_POLICY = 16
    RESIZE_CLUSTER = 17

  class StatusValueValuesEnum(_messages.Enum):
    r"""The current status of the operation.

    Values:
      STATUS_UNSPECIFIED: Not set.
      PENDING: The operation has been created.
      RUNNING: The operation is currently running.
      DONE: The operation is done, either cancelled or completed.
      ABORTING: The operation is aborting.
    """
    STATUS_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    DONE = 3
    ABORTING = 4

  clusterConditions = _messages.MessageField('StatusCondition', 1, repeated=True)
  detail = _messages.StringField(2)
  endTime = _messages.StringField(3)
  error = _messages.MessageField('Status', 4)
  location = _messages.StringField(5)
  name = _messages.StringField(6)
  nodepoolConditions = _messages.MessageField('StatusCondition', 7, repeated=True)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 8)
  progress = _messages.MessageField('OperationProgress', 9)
  selfLink = _messages.StringField(10)
  startTime = _messages.StringField(11)
  status = _messages.EnumField('StatusValueValuesEnum', 12)
  statusMessage = _messages.StringField(13)
  targetLink = _messages.StringField(14)
  zone = _messages.StringField(15)


class OperationProgress(_messages.Message):
  r"""Information about operation (or operation stage) progress.

  Enums:
    StatusValueValuesEnum: Status of an operation stage. Unset for single-
      stage operations.

  Fields:
    metrics: Progress metric bundle, for example: metrics: [{name: "nodes
      done", int_value: 15}, {name: "nodes total", int_value: 32}] or metrics:
      [{name: "progress", double_value: 0.56}, {name: "progress scale",
      double_value: 1.0}]
    name: A non-parameterized string describing an operation stage. Unset for
      single-stage operations.
    stages: Substages of an operation or a stage.
    status: Status of an operation stage. Unset for single-stage operations.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Status of an operation stage. Unset for single-stage operations.

    Values:
      STATUS_UNSPECIFIED: Not set.
      PENDING: The operation has been created.
      RUNNING: The operation is currently running.
      DONE: The operation is done, either cancelled or completed.
      ABORTING: The operation is aborting.
    """
    STATUS_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    DONE = 3
    ABORTING = 4

  metrics = _messages.MessageField('Metric', 1, repeated=True)
  name = _messages.StringField(2)
  stages = _messages.MessageField('OperationProgress', 3, repeated=True)
  status = _messages.EnumField('StatusValueValuesEnum', 4)


class PlacementPolicy(_messages.Message):
  r"""PlacementPolicy defines the placement policy used by the node pool.

  Enums:
    TypeValueValuesEnum: The type of placement.

  Fields:
    policyName: If set, refers to the name of a custom resource policy
      supplied by the user. The resource policy must be in the same project
      and region as the node pool. If not found, InvalidArgument error is
      returned.
    tpuTopology: Optional. TPU placement topology for pod slice node pool.
      https://cloud.google.com/tpu/docs/types-topologies#tpu_topologies
    type: The type of placement.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of placement.

    Values:
      TYPE_UNSPECIFIED: TYPE_UNSPECIFIED specifies no requirements on nodes
        placement.
      COMPACT: COMPACT specifies node placement in the same availability
        domain to ensure low communication latency.
    """
    TYPE_UNSPECIFIED = 0
    COMPACT = 1

  policyName = _messages.StringField(1)
  tpuTopology = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class PodCIDROverprovisionConfig(_messages.Message):
  r"""[PRIVATE FIELD] Config for pod CIDR size overprovisioning.

  Fields:
    disable: Whether Pod CIDR overprovisioning is disabled. Note: Pod CIDR
      overprovisioning is enabled by default.
  """

  disable = _messages.BooleanField(1)


class PolicyBinding(_messages.Message):
  r"""Binauthz policy that applies to this cluster.

  Fields:
    name: The relative resource name of the binauthz platform policy to audit.
      GKE platform policies have the following format:
      `projects/{project_number}/platforms/gke/policies/{policy_id}`.
  """

  name = _messages.StringField(1)


class PrivateClusterConfig(_messages.Message):
  r"""Configuration options for private clusters.

  Fields:
    enablePrivateEndpoint: Whether the master's internal IP address is used as
      the cluster endpoint.
    enablePrivateNodes: Whether nodes have internal IP addresses only. If
      enabled, all nodes are given only RFC 1918 private addresses and
      communicate with the master via private networking.
    masterGlobalAccessConfig: Controls master global access settings.
    masterIpv4CidrBlock: The IP range in CIDR notation to use for the hosted
      master network. This range will be used for assigning internal IP
      addresses to the master or set of masters, as well as the ILB VIP. This
      range must not overlap with any other ranges in use within the cluster's
      network.
    peeringName: Output only. The peering name in the customer VPC used by
      this cluster.
    privateEndpoint: Output only. The internal IP address of this cluster's
      master endpoint.
    privateEndpointSubnetwork: Subnet to provision the master's private
      endpoint during cluster creation. Specified in
      projects/*/regions/*/subnetworks/* format.
    publicEndpoint: Output only. The external IP address of this cluster's
      master endpoint.
  """

  enablePrivateEndpoint = _messages.BooleanField(1)
  enablePrivateNodes = _messages.BooleanField(2)
  masterGlobalAccessConfig = _messages.MessageField('PrivateClusterMasterGlobalAccessConfig', 3)
  masterIpv4CidrBlock = _messages.StringField(4)
  peeringName = _messages.StringField(5)
  privateEndpoint = _messages.StringField(6)
  privateEndpointSubnetwork = _messages.StringField(7)
  publicEndpoint = _messages.StringField(8)


class PrivateClusterMasterGlobalAccessConfig(_messages.Message):
  r"""Configuration for controlling master global access settings.

  Fields:
    enabled: Whenever master is accessible globally or not.
  """

  enabled = _messages.BooleanField(1)


class PrivateRegistryAccessConfig(_messages.Message):
  r"""PrivateRegistryAccessConfig contains access configuration for private
  container registries.

  Fields:
    certificateAuthorityDomainConfig: Private registry access configuration.
    enabled: Private registry access is enabled.
  """

  certificateAuthorityDomainConfig = _messages.MessageField('CertificateAuthorityDomainConfig', 1, repeated=True)
  enabled = _messages.BooleanField(2)


class PubSub(_messages.Message):
  r"""Pub/Sub specific notification config.

  Fields:
    enabled: Enable notifications for Pub/Sub.
    filter: Allows filtering to one or more specific event types. If no filter
      is specified, or if a filter is specified with no event types, all event
      types will be sent
    topic: The desired Pub/Sub topic to which notifications will be sent by
      GKE. Format is `projects/{project}/topics/{topic}`.
  """

  enabled = _messages.BooleanField(1)
  filter = _messages.MessageField('Filter', 2)
  topic = _messages.StringField(3)


class QueuedProvisioning(_messages.Message):
  r"""QueuedProvisioning defines the queued provisioning used by the node
  pool.

  Fields:
    enabled: Denotes that this nodepool is QRM specific, meaning nodes can be
      only obtained through queuing via the Cluster Autoscaler
      ProvisioningRequest API.
  """

  enabled = _messages.BooleanField(1)


class RangeInfo(_messages.Message):
  r"""RangeInfo contains the range name and the range utilization by this
  cluster.

  Fields:
    rangeName: Output only. [Output only] Name of a range.
    utilization: Output only. [Output only] The utilization of the range.
  """

  rangeName = _messages.StringField(1)
  utilization = _messages.FloatField(2)


class RecurringTimeWindow(_messages.Message):
  r"""Represents an arbitrary window of time that recurs.

  Fields:
    recurrence: An RRULE (https://tools.ietf.org/html/rfc5545#section-3.8.5.3)
      for how this window reccurs. They go on for the span of time between the
      start and end time. For example, to have something repeat every weekday,
      you'd use: `FREQ=WEEKLY;BYDAY=MO,TU,WE,TH,FR` To repeat some window
      daily (equivalent to the DailyMaintenanceWindow): `FREQ=DAILY` For the
      first weekend of every month: `FREQ=MONTHLY;BYSETPOS=1;BYDAY=SA,SU` This
      specifies how frequently the window starts. Eg, if you wanted to have a
      9-5 UTC-4 window every weekday, you'd use something like: ``` start time
      = 2019-01-01T09:00:00-0400 end time = 2019-01-01T17:00:00-0400
      recurrence = FREQ=WEEKLY;BYDAY=MO,TU,WE,TH,FR ``` Windows can span
      multiple days. Eg, to make the window encompass every weekend from
      midnight Saturday till the last minute of Sunday UTC: ``` start time =
      2019-01-05T00:00:00Z end time = 2019-01-07T23:59:00Z recurrence =
      FREQ=WEEKLY;BYDAY=SA ``` Note the start and end time's specific dates
      are largely arbitrary except to specify duration of the window and when
      it first starts. The FREQ values of HOURLY, MINUTELY, and SECONDLY are
      not supported.
    window: The window of the first recurrence.
  """

  recurrence = _messages.StringField(1)
  window = _messages.MessageField('TimeWindow', 2)


class ReleaseChannel(_messages.Message):
  r"""ReleaseChannel indicates which release channel a cluster is subscribed
  to. Release channels are arranged in order of risk. When a cluster is
  subscribed to a release channel, Google maintains both the master version
  and the node version. Node auto-upgrade defaults to true and cannot be
  disabled.

  Enums:
    ChannelValueValuesEnum: channel specifies which release channel the
      cluster is subscribed to.

  Fields:
    channel: channel specifies which release channel the cluster is subscribed
      to.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""channel specifies which release channel the cluster is subscribed to.

    Values:
      UNSPECIFIED: No channel specified.
      RAPID: RAPID channel is offered on an early access basis for customers
        who want to test new releases. WARNING: Versions available in the
        RAPID Channel may be subject to unresolved issues with no known
        workaround and are not subject to any SLAs.
      REGULAR: Clusters subscribed to REGULAR receive versions that are
        considered GA quality. REGULAR is intended for production users who
        want to take advantage of new features.
      STABLE: Clusters subscribed to STABLE receive versions that are known to
        be stable and reliable in production.
    """
    UNSPECIFIED = 0
    RAPID = 1
    REGULAR = 2
    STABLE = 3

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)


class ReleaseChannelConfig(_messages.Message):
  r"""ReleaseChannelConfig exposes configuration for a release channel.

  Enums:
    ChannelValueValuesEnum: The release channel this configuration applies to.

  Fields:
    channel: The release channel this configuration applies to.
    defaultVersion: The default version for newly created clusters on the
      channel.
    validVersions: List of valid versions for the channel.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""The release channel this configuration applies to.

    Values:
      UNSPECIFIED: No channel specified.
      RAPID: RAPID channel is offered on an early access basis for customers
        who want to test new releases. WARNING: Versions available in the
        RAPID Channel may be subject to unresolved issues with no known
        workaround and are not subject to any SLAs.
      REGULAR: Clusters subscribed to REGULAR receive versions that are
        considered GA quality. REGULAR is intended for production users who
        want to take advantage of new features.
      STABLE: Clusters subscribed to STABLE receive versions that are known to
        be stable and reliable in production.
    """
    UNSPECIFIED = 0
    RAPID = 1
    REGULAR = 2
    STABLE = 3

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)
  defaultVersion = _messages.StringField(2)
  validVersions = _messages.StringField(3, repeated=True)


class ReservationAffinity(_messages.Message):
  r"""[ReservationAffinity](https://cloud.google.com/compute/docs/instances/re
  serving-zonal-resources) is the configuration of desired reservation which
  instances could take capacity from.

  Enums:
    ConsumeReservationTypeValueValuesEnum: Corresponds to the type of
      reservation consumption.

  Fields:
    consumeReservationType: Corresponds to the type of reservation
      consumption.
    key: Corresponds to the label key of a reservation resource. To target a
      SPECIFIC_RESERVATION by name, specify
      "compute.googleapis.com/reservation-name" as the key and specify the
      name of your reservation as its value.
    values: Corresponds to the label value(s) of reservation resource(s).
  """

  class ConsumeReservationTypeValueValuesEnum(_messages.Enum):
    r"""Corresponds to the type of reservation consumption.

    Values:
      UNSPECIFIED: Default value. This should not be used.
      NO_RESERVATION: Do not consume from any reserved capacity.
      ANY_RESERVATION: Consume any reservation available.
      SPECIFIC_RESERVATION: Must consume from a specific reservation. Must
        specify key value fields for specifying the reservations.
    """
    UNSPECIFIED = 0
    NO_RESERVATION = 1
    ANY_RESERVATION = 2
    SPECIFIC_RESERVATION = 3

  consumeReservationType = _messages.EnumField('ConsumeReservationTypeValueValuesEnum', 1)
  key = _messages.StringField(2)
  values = _messages.StringField(3, repeated=True)


class ResourceLabels(_messages.Message):
  r"""Collection of [GCP labels](https://cloud.google.com/resource-
  manager/docs/creating-managing-labels).

  Messages:
    LabelsValue: Map of node label keys and node label values.

  Fields:
    labels: Map of node label keys and node label values.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Map of node label keys and node label values.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class ResourceLimit(_messages.Message):
  r"""Contains information about amount of some resource in the cluster. For
  memory, value should be in GB.

  Fields:
    maximum: Maximum amount of the resource in the cluster.
    minimum: Minimum amount of the resource in the cluster.
    resourceType: Resource name "cpu", "memory" or gpu-specific string.
  """

  maximum = _messages.IntegerField(1)
  minimum = _messages.IntegerField(2)
  resourceType = _messages.StringField(3)


class ResourceManagerTags(_messages.Message):
  r"""A map of resource manager tag keys and values to be attached to the
  nodes for managing Compute Engine firewalls using Network Firewall Policies.
  Tags must be according to specifications in
  https://cloud.google.com/vpc/docs/tags-firewalls-overview#specifications. A
  maximum of 5 tag key-value pairs can be specified. Existing tags will be
  replaced with new values.

  Messages:
    TagsValue: TagKeyValue must be in one of the following formats
      ([KEY]=[VALUE]) 1. `tagKeys/{tag_key_id}=tagValues/{tag_value_id}` 2.
      `{org_id}/{tag_key_name}={tag_value_name}` 3.
      `{project_id}/{tag_key_name}={tag_value_name}`

  Fields:
    tags: TagKeyValue must be in one of the following formats ([KEY]=[VALUE])
      1. `tagKeys/{tag_key_id}=tagValues/{tag_value_id}` 2.
      `{org_id}/{tag_key_name}={tag_value_name}` 3.
      `{project_id}/{tag_key_name}={tag_value_name}`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""TagKeyValue must be in one of the following formats ([KEY]=[VALUE]) 1.
    `tagKeys/{tag_key_id}=tagValues/{tag_value_id}` 2.
    `{org_id}/{tag_key_name}={tag_value_name}` 3.
    `{project_id}/{tag_key_name}={tag_value_name}`

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  tags = _messages.MessageField('TagsValue', 1)


class ResourceUsageExportConfig(_messages.Message):
  r"""Configuration for exporting cluster resource usages.

  Fields:
    bigqueryDestination: Configuration to use BigQuery as usage export
      destination.
    consumptionMeteringConfig: Configuration to enable resource consumption
      metering.
    enableNetworkEgressMetering: Whether to enable network egress metering for
      this cluster. If enabled, a daemonset will be created in the cluster to
      meter network egress traffic.
  """

  bigqueryDestination = _messages.MessageField('BigQueryDestination', 1)
  consumptionMeteringConfig = _messages.MessageField('ConsumptionMeteringConfig', 2)
  enableNetworkEgressMetering = _messages.BooleanField(3)


class RollbackNodePoolUpgradeRequest(_messages.Message):
  r"""RollbackNodePoolUpgradeRequest rollbacks the previously Aborted or
  Failed NodePool upgrade. This will be an no-op if the last upgrade
  successfully completed.

  Fields:
    clusterId: Deprecated. The name of the cluster to rollback. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster, node pool id) of the node poll
      to rollback upgrade. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
    nodePoolId: Deprecated. The name of the node pool to rollback. This field
      has been deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    respectPdb: Option for rollback to ignore the PodDisruptionBudget. Default
      value is false.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2)
  nodePoolId = _messages.StringField(3)
  projectId = _messages.StringField(4)
  respectPdb = _messages.BooleanField(5)
  zone = _messages.StringField(6)


class RollingSettings(_messages.Message):
  r"""Settings for rolling update.

  Fields:
    maxSurgePercentage: Percentage of the maximum number of nodes that can be
      created beyond the current size of the node pool during the upgrade
      process. The range of this field should be [0, 100].
    maxUnavailablePercentage: Percentage of the maximum number of nodes that
      can be unavailable during during the upgrade process.
  """

  maxSurgePercentage = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  maxUnavailablePercentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class RuntimeVulnerabilityInsightConfig(_messages.Message):
  r"""RuntimeVulnerabilityInsightConfig defines the flags needed to
  enable/disable RVI features for the cluster.

  Enums:
    ModeValueValuesEnum: Sets which mode to use for Runtime Vulnerability
      Insight features.

  Fields:
    mode: Sets which mode to use for Runtime Vulnerability Insight features.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Sets which mode to use for Runtime Vulnerability Insight features.

    Values:
      MODE_UNSPECIFIED: Default value not specified.
      DISABLED: Disables RuntimeVulnerabilityInsight on the cluster.
      PREMIUM_VULNERABILITY_SCAN: Applies premium vulnerability insights on
        the cluster.
    """
    MODE_UNSPECIFIED = 0
    DISABLED = 1
    PREMIUM_VULNERABILITY_SCAN = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)


class SandboxConfig(_messages.Message):
  r"""SandboxConfig contains configurations of the sandbox to use for the
  node.

  Enums:
    TypeValueValuesEnum: Type of the sandbox to use for the node.

  Fields:
    type: Type of the sandbox to use for the node.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of the sandbox to use for the node.

    Values:
      UNSPECIFIED: Default value. This should not be used.
      GVISOR: Run sandbox using gvisor.
    """
    UNSPECIFIED = 0
    GVISOR = 1

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class SecondaryBootDisk(_messages.Message):
  r"""SecondaryBootDisk represents a persistent disk attached to a node with
  special configurations based on its mode.

  Enums:
    ModeValueValuesEnum: Disk mode (container image cache, etc.)

  Fields:
    diskImage: Fully-qualified resource ID for an existing disk image.
    mode: Disk mode (container image cache, etc.)
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Disk mode (container image cache, etc.)

    Values:
      MODE_UNSPECIFIED: MODE_UNSPECIFIED is when mode is not set.
      CONTAINER_IMAGE_CACHE: CONTAINER_IMAGE_CACHE is for using the secondary
        boot disk as a container image cache.
    """
    MODE_UNSPECIFIED = 0
    CONTAINER_IMAGE_CACHE = 1

  diskImage = _messages.StringField(1)
  mode = _messages.EnumField('ModeValueValuesEnum', 2)


class SecurityBulletinEvent(_messages.Message):
  r"""SecurityBulletinEvent is a notification sent to customers when a
  security bulletin has been posted that they are vulnerable to.

  Fields:
    affectedSupportedMinors: The GKE minor versions affected by this
      vulnerability.
    briefDescription: A brief description of the bulletin. See the bulletin
      pointed to by the bulletin_uri field for an expanded description.
    bulletinId: The ID of the bulletin corresponding to the vulnerability.
    bulletinUri: The URI link to the bulletin on the website for more
      information.
    cveIds: The CVEs associated with this bulletin.
    manualStepsRequired: If this field is specified, it means there are manual
      steps that the user must take to make their clusters safe.
    patchedVersions: The GKE versions where this vulnerability is patched.
    resourceTypeAffected: The resource type (node/control plane) that has the
      vulnerability. Multiple notifications (1 notification per resource type)
      will be sent for a vulnerability that affects > 1 resource type.
    severity: The severity of this bulletin as it relates to GKE.
    suggestedUpgradeTarget: This represents a version selected from the
      patched_versions field that the cluster receiving this notification
      should most likely want to upgrade to based on its current version. Note
      that if this notification is being received by a given cluster, it means
      that this version is currently available as an upgrade target in that
      cluster's location.
  """

  affectedSupportedMinors = _messages.StringField(1, repeated=True)
  briefDescription = _messages.StringField(2)
  bulletinId = _messages.StringField(3)
  bulletinUri = _messages.StringField(4)
  cveIds = _messages.StringField(5, repeated=True)
  manualStepsRequired = _messages.BooleanField(6)
  patchedVersions = _messages.StringField(7, repeated=True)
  resourceTypeAffected = _messages.StringField(8)
  severity = _messages.StringField(9)
  suggestedUpgradeTarget = _messages.StringField(10)


class SecurityPostureConfig(_messages.Message):
  r"""SecurityPostureConfig defines the flags needed to enable/disable
  features for the Security Posture API.

  Enums:
    ModeValueValuesEnum: Sets which mode to use for Security Posture features.
    VulnerabilityModeValueValuesEnum: Sets which mode to use for vulnerability
      scanning.

  Fields:
    mode: Sets which mode to use for Security Posture features.
    vulnerabilityMode: Sets which mode to use for vulnerability scanning.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Sets which mode to use for Security Posture features.

    Values:
      MODE_UNSPECIFIED: Default value not specified.
      DISABLED: Disables Security Posture features on the cluster.
      BASIC: Applies Security Posture features on the cluster.
      ENTERPRISE: Applies the Security Posture off cluster Enterprise level
        features.
    """
    MODE_UNSPECIFIED = 0
    DISABLED = 1
    BASIC = 2
    ENTERPRISE = 3

  class VulnerabilityModeValueValuesEnum(_messages.Enum):
    r"""Sets which mode to use for vulnerability scanning.

    Values:
      VULNERABILITY_MODE_UNSPECIFIED: Default value not specified.
      VULNERABILITY_DISABLED: Disables vulnerability scanning on the cluster.
      VULNERABILITY_BASIC: Applies basic vulnerability scanning on the
        cluster.
      VULNERABILITY_ENTERPRISE: Applies the Security Posture's vulnerability
        on cluster Enterprise level features.
    """
    VULNERABILITY_MODE_UNSPECIFIED = 0
    VULNERABILITY_DISABLED = 1
    VULNERABILITY_BASIC = 2
    VULNERABILITY_ENTERPRISE = 3

  mode = _messages.EnumField('ModeValueValuesEnum', 1)
  vulnerabilityMode = _messages.EnumField('VulnerabilityModeValueValuesEnum', 2)


class ServerConfig(_messages.Message):
  r"""Kubernetes Engine service configuration.

  Fields:
    channels: List of release channel configurations.
    defaultClusterVersion: Version of Kubernetes the service deploys by
      default.
    defaultImageType: Default image type.
    validImageTypes: List of valid image types.
    validMasterVersions: List of valid master versions, in descending order.
    validNodeVersions: List of valid node upgrade target versions, in
      descending order.
  """

  channels = _messages.MessageField('ReleaseChannelConfig', 1, repeated=True)
  defaultClusterVersion = _messages.StringField(2)
  defaultImageType = _messages.StringField(3)
  validImageTypes = _messages.StringField(4, repeated=True)
  validMasterVersions = _messages.StringField(5, repeated=True)
  validNodeVersions = _messages.StringField(6, repeated=True)


class ServiceExternalIPsConfig(_messages.Message):
  r"""Config to block services with externalIPs field.

  Fields:
    enabled: Whether Services with ExternalIPs field are allowed or not.
  """

  enabled = _messages.BooleanField(1)


class SetAddonsConfigRequest(_messages.Message):
  r"""SetAddonsConfigRequest sets the addons associated with the cluster.

  Fields:
    addonsConfig: Required. The desired configurations for the various addons
      available to run in the cluster.
    clusterId: Deprecated. The name of the cluster to upgrade. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster) of the cluster to set addons.
      Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  addonsConfig = _messages.MessageField('AddonsConfig', 1)
  clusterId = _messages.StringField(2)
  name = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class SetLabelsRequest(_messages.Message):
  r"""SetLabelsRequest sets the Google Cloud Platform labels on a Google
  Container Engine cluster, which will in turn set them for Google Compute
  Engine resources used by that cluster

  Messages:
    ResourceLabelsValue: Required. The labels to set for that cluster.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the name field.
    labelFingerprint: Required. The fingerprint of the previous set of labels
      for this resource, used to detect conflicts. The fingerprint is
      initially generated by Kubernetes Engine and changes after every request
      to modify or update labels. You must always provide an up-to-date
      fingerprint hash when updating or changing labels. Make a `get()`
      request to the resource to get the latest fingerprint.
    name: The name (project, location, cluster name) of the cluster to set
      labels. Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    resourceLabels: Required. The labels to set for that cluster.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceLabelsValue(_messages.Message):
    r"""Required. The labels to set for that cluster.

    Messages:
      AdditionalProperty: An additional property for a ResourceLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ResourceLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  clusterId = _messages.StringField(1)
  labelFingerprint = _messages.StringField(2)
  name = _messages.StringField(3)
  projectId = _messages.StringField(4)
  resourceLabels = _messages.MessageField('ResourceLabelsValue', 5)
  zone = _messages.StringField(6)


class SetLegacyAbacRequest(_messages.Message):
  r"""SetLegacyAbacRequest enables or disables the ABAC authorization
  mechanism for a cluster.

  Fields:
    clusterId: Deprecated. The name of the cluster to update. This field has
      been deprecated and replaced by the name field.
    enabled: Required. Whether ABAC authorization will be enabled in the
      cluster.
    name: The name (project, location, cluster name) of the cluster to set
      legacy abac. Specified in the format
      `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  enabled = _messages.BooleanField(2)
  name = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class SetLocationsRequest(_messages.Message):
  r"""SetLocationsRequest sets the locations of the cluster.

  Fields:
    clusterId: Deprecated. The name of the cluster to upgrade. This field has
      been deprecated and replaced by the name field.
    locations: Required. The desired list of Google Compute Engine
      [zones](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster's nodes should be located. Changing the locations a cluster
      is in will result in nodes being either created or removed from the
      cluster, depending on whether locations are being added or removed. This
      list must always include the cluster's primary zone.
    name: The name (project, location, cluster) of the cluster to set
      locations. Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  locations = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class SetLoggingServiceRequest(_messages.Message):
  r"""SetLoggingServiceRequest sets the logging service of a cluster.

  Fields:
    clusterId: Deprecated. The name of the cluster to upgrade. This field has
      been deprecated and replaced by the name field.
    loggingService: Required. The logging service the cluster should use to
      write logs. Currently available options: *
      `logging.googleapis.com/kubernetes` - The Cloud Logging service with a
      Kubernetes-native resource model * `logging.googleapis.com` - The legacy
      Cloud Logging service (no longer available as of GKE 1.15). * `none` -
      no logs will be exported from the cluster. If left as an empty
      string,`logging.googleapis.com/kubernetes` will be used for GKE 1.14+ or
      `logging.googleapis.com` for earlier versions.
    name: The name (project, location, cluster) of the cluster to set logging.
      Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  loggingService = _messages.StringField(2)
  name = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class SetMaintenancePolicyRequest(_messages.Message):
  r"""SetMaintenancePolicyRequest sets the maintenance policy for a cluster.

  Fields:
    clusterId: Required. The name of the cluster to update.
    maintenancePolicy: Required. The maintenance policy to be set for the
      cluster. An empty field clears the existing maintenance policy.
    name: The name (project, location, cluster name) of the cluster to set
      maintenance policy. Specified in the format
      `projects/*/locations/*/clusters/*`.
    projectId: Required. The Google Developers Console [project ID or project
      number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects).
    zone: Required. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides.
  """

  clusterId = _messages.StringField(1)
  maintenancePolicy = _messages.MessageField('MaintenancePolicy', 2)
  name = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class SetMasterAuthRequest(_messages.Message):
  r"""SetMasterAuthRequest updates the admin password of a cluster.

  Enums:
    ActionValueValuesEnum: Required. The exact form of action to be taken on
      the master auth.

  Fields:
    action: Required. The exact form of action to be taken on the master auth.
    clusterId: Deprecated. The name of the cluster to upgrade. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster) of the cluster to set auth.
      Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    update: Required. A description of the update.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. The exact form of action to be taken on the master auth.

    Values:
      UNKNOWN: Operation is unknown and will error out.
      SET_PASSWORD: Set the password to a user generated value.
      GENERATE_PASSWORD: Generate a new password and set it to that.
      SET_USERNAME: Set the username. If an empty username is provided, basic
        authentication is disabled for the cluster. If a non-empty username is
        provided, basic authentication is enabled, with either a provided
        password or a generated one.
    """
    UNKNOWN = 0
    SET_PASSWORD = 1
    GENERATE_PASSWORD = 2
    SET_USERNAME = 3

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  clusterId = _messages.StringField(2)
  name = _messages.StringField(3)
  projectId = _messages.StringField(4)
  update = _messages.MessageField('MasterAuth', 5)
  zone = _messages.StringField(6)


class SetMonitoringServiceRequest(_messages.Message):
  r"""SetMonitoringServiceRequest sets the monitoring service of a cluster.

  Fields:
    clusterId: Deprecated. The name of the cluster to upgrade. This field has
      been deprecated and replaced by the name field.
    monitoringService: Required. The monitoring service the cluster should use
      to write metrics. Currently available options: *
      "monitoring.googleapis.com/kubernetes" - The Cloud Monitoring service
      with a Kubernetes-native resource model * `monitoring.googleapis.com` -
      The legacy Cloud Monitoring service (no longer available as of GKE
      1.15). * `none` - No metrics will be exported from the cluster. If left
      as an empty string,`monitoring.googleapis.com/kubernetes` will be used
      for GKE 1.14+ or `monitoring.googleapis.com` for earlier versions.
    name: The name (project, location, cluster) of the cluster to set
      monitoring. Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  monitoringService = _messages.StringField(2)
  name = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class SetNetworkPolicyRequest(_messages.Message):
  r"""SetNetworkPolicyRequest enables/disables network policy for a cluster.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the name field.
    name: The name (project, location, cluster name) of the cluster to set
      networking policy. Specified in the format
      `projects/*/locations/*/clusters/*`.
    networkPolicy: Required. Configuration options for the NetworkPolicy
      feature.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2)
  networkPolicy = _messages.MessageField('NetworkPolicy', 3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class SetNodePoolAutoscalingRequest(_messages.Message):
  r"""SetNodePoolAutoscalingRequest sets the autoscaler settings of a node
  pool.

  Fields:
    autoscaling: Required. Autoscaling configuration for the node pool.
    clusterId: Deprecated. The name of the cluster to upgrade. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster, node pool) of the node pool to
      set autoscaler settings. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
    nodePoolId: Deprecated. The name of the node pool to upgrade. This field
      has been deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  autoscaling = _messages.MessageField('NodePoolAutoscaling', 1)
  clusterId = _messages.StringField(2)
  name = _messages.StringField(3)
  nodePoolId = _messages.StringField(4)
  projectId = _messages.StringField(5)
  zone = _messages.StringField(6)


class SetNodePoolManagementRequest(_messages.Message):
  r"""SetNodePoolManagementRequest sets the node management properties of a
  node pool.

  Fields:
    clusterId: Deprecated. The name of the cluster to update. This field has
      been deprecated and replaced by the name field.
    management: Required. NodeManagement configuration for the node pool.
    name: The name (project, location, cluster, node pool id) of the node pool
      to set management properties. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
    nodePoolId: Deprecated. The name of the node pool to update. This field
      has been deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  management = _messages.MessageField('NodeManagement', 2)
  name = _messages.StringField(3)
  nodePoolId = _messages.StringField(4)
  projectId = _messages.StringField(5)
  zone = _messages.StringField(6)


class SetNodePoolSizeRequest(_messages.Message):
  r"""SetNodePoolSizeRequest sets the size of a node pool.

  Fields:
    clusterId: Deprecated. The name of the cluster to update. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster, node pool id) of the node pool
      to set size. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
    nodeCount: Required. The desired node count for the pool.
    nodePoolId: Deprecated. The name of the node pool to update. This field
      has been deprecated and replaced by the name field.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2)
  nodeCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  nodePoolId = _messages.StringField(4)
  projectId = _messages.StringField(5)
  zone = _messages.StringField(6)


class ShieldedInstanceConfig(_messages.Message):
  r"""A set of Shielded Instance options.

  Fields:
    enableIntegrityMonitoring: Defines whether the instance has integrity
      monitoring enabled. Enables monitoring and attestation of the boot
      integrity of the instance. The attestation is performed against the
      integrity policy baseline. This baseline is initially derived from the
      implicitly trusted boot image when the instance is created.
    enableSecureBoot: Defines whether the instance has Secure Boot enabled.
      Secure Boot helps ensure that the system only runs authentic software by
      verifying the digital signature of all boot components, and halting the
      boot process if signature verification fails.
  """

  enableIntegrityMonitoring = _messages.BooleanField(1)
  enableSecureBoot = _messages.BooleanField(2)


class ShieldedNodes(_messages.Message):
  r"""Configuration of Shielded Nodes feature.

  Fields:
    enabled: Whether Shielded Nodes features are enabled on all nodes in this
      cluster.
  """

  enabled = _messages.BooleanField(1)


class SoleTenantConfig(_messages.Message):
  r"""SoleTenantConfig contains the NodeAffinities to specify what shared sole
  tenant node groups should back the node pool.

  Fields:
    nodeAffinities: NodeAffinities used to match to a shared sole tenant node
      group.
  """

  nodeAffinities = _messages.MessageField('NodeAffinity', 1, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StandardRolloutPolicy(_messages.Message):
  r"""Standard rollout policy is the default policy for blue-green.

  Fields:
    batchNodeCount: Number of blue nodes to drain in a batch.
    batchPercentage: Percentage of the blue pool nodes to drain in a batch.
      The range of this field should be (0.0, 1.0].
    batchSoakDuration: Soak time after each batch gets drained. Default to
      zero.
  """

  batchNodeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  batchPercentage = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  batchSoakDuration = _messages.StringField(3)


class StartIPRotationRequest(_messages.Message):
  r"""StartIPRotationRequest creates a new IP for the cluster and then
  performs a node upgrade on each node pool to point to the new IP.

  Fields:
    clusterId: Deprecated. The name of the cluster. This field has been
      deprecated and replaced by the name field.
    name: The name (project, location, cluster name) of the cluster to start
      IP rotation. Specified in the format
      `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    rotateCredentials: Whether to rotate credentials during IP rotation.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3)
  rotateCredentials = _messages.BooleanField(4)
  zone = _messages.StringField(5)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StatusCondition(_messages.Message):
  r"""StatusCondition describes why a cluster or a node pool has a certain
  status (e.g., ERROR or DEGRADED).

  Enums:
    CanonicalCodeValueValuesEnum: Canonical code of the condition.
    CodeValueValuesEnum: Machine-friendly representation of the condition
      Deprecated. Use canonical_code instead.

  Fields:
    canonicalCode: Canonical code of the condition.
    code: Machine-friendly representation of the condition Deprecated. Use
      canonical_code instead.
    message: Human-friendly representation of the condition
  """

  class CanonicalCodeValueValuesEnum(_messages.Enum):
    r"""Canonical code of the condition.

    Values:
      OK: Not an error; returned on success. HTTP Mapping: 200 OK
      CANCELLED: The operation was cancelled, typically by the caller. HTTP
        Mapping: 499 Client Closed Request
      UNKNOWN: Unknown error. For example, this error may be returned when a
        `Status` value received from another address space belongs to an error
        space that is not known in this address space. Also errors raised by
        APIs that do not return enough error information may be converted to
        this error. HTTP Mapping: 500 Internal Server Error
      INVALID_ARGUMENT: The client specified an invalid argument. Note that
        this differs from `FAILED_PRECONDITION`. `INVALID_ARGUMENT` indicates
        arguments that are problematic regardless of the state of the system
        (e.g., a malformed file name). HTTP Mapping: 400 Bad Request
      DEADLINE_EXCEEDED: The deadline expired before the operation could
        complete. For operations that change the state of the system, this
        error may be returned even if the operation has completed
        successfully. For example, a successful response from a server could
        have been delayed long enough for the deadline to expire. HTTP
        Mapping: 504 Gateway Timeout
      NOT_FOUND: Some requested entity (e.g., file or directory) was not
        found. Note to server developers: if a request is denied for an entire
        class of users, such as gradual feature rollout or undocumented
        allowlist, `NOT_FOUND` may be used. If a request is denied for some
        users within a class of users, such as user-based access control,
        `PERMISSION_DENIED` must be used. HTTP Mapping: 404 Not Found
      ALREADY_EXISTS: The entity that a client attempted to create (e.g., file
        or directory) already exists. HTTP Mapping: 409 Conflict
      PERMISSION_DENIED: The caller does not have permission to execute the
        specified operation. `PERMISSION_DENIED` must not be used for
        rejections caused by exhausting some resource (use
        `RESOURCE_EXHAUSTED` instead for those errors). `PERMISSION_DENIED`
        must not be used if the caller can not be identified (use
        `UNAUTHENTICATED` instead for those errors). This error code does not
        imply the request is valid or the requested entity exists or satisfies
        other pre-conditions. HTTP Mapping: 403 Forbidden
      UNAUTHENTICATED: The request does not have valid authentication
        credentials for the operation. HTTP Mapping: 401 Unauthorized
      RESOURCE_EXHAUSTED: Some resource has been exhausted, perhaps a per-user
        quota, or perhaps the entire file system is out of space. HTTP
        Mapping: 429 Too Many Requests
      FAILED_PRECONDITION: The operation was rejected because the system is
        not in a state required for the operation's execution. For example,
        the directory to be deleted is non-empty, an rmdir operation is
        applied to a non-directory, etc. Service implementors can use the
        following guidelines to decide between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`: (a) Use `UNAVAILABLE` if the client can
        retry just the failing call. (b) Use `ABORTED` if the client should
        retry at a higher level. For example, when a client-specified test-
        and-set fails, indicating the client should restart a read-modify-
        write sequence. (c) Use `FAILED_PRECONDITION` if the client should not
        retry until the system state has been explicitly fixed. For example,
        if an "rmdir" fails because the directory is non-empty,
        `FAILED_PRECONDITION` should be returned since the client should not
        retry unless the files are deleted from the directory. HTTP Mapping:
        400 Bad Request
      ABORTED: The operation was aborted, typically due to a concurrency issue
        such as a sequencer check failure or transaction abort. See the
        guidelines above for deciding between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 409 Conflict
      OUT_OF_RANGE: The operation was attempted past the valid range. E.g.,
        seeking or reading past end-of-file. Unlike `INVALID_ARGUMENT`, this
        error indicates a problem that may be fixed if the system state
        changes. For example, a 32-bit file system will generate
        `INVALID_ARGUMENT` if asked to read at an offset that is not in the
        range [0,2^32-1], but it will generate `OUT_OF_RANGE` if asked to read
        from an offset past the current file size. There is a fair bit of
        overlap between `FAILED_PRECONDITION` and `OUT_OF_RANGE`. We recommend
        using `OUT_OF_RANGE` (the more specific error) when it applies so that
        callers who are iterating through a space can easily look for an
        `OUT_OF_RANGE` error to detect when they are done. HTTP Mapping: 400
        Bad Request
      UNIMPLEMENTED: The operation is not implemented or is not
        supported/enabled in this service. HTTP Mapping: 501 Not Implemented
      INTERNAL: Internal errors. This means that some invariants expected by
        the underlying system have been broken. This error code is reserved
        for serious errors. HTTP Mapping: 500 Internal Server Error
      UNAVAILABLE: The service is currently unavailable. This is most likely a
        transient condition, which can be corrected by retrying with a
        backoff. Note that it is not always safe to retry non-idempotent
        operations. See the guidelines above for deciding between
        `FAILED_PRECONDITION`, `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 503
        Service Unavailable
      DATA_LOSS: Unrecoverable data loss or corruption. HTTP Mapping: 500
        Internal Server Error
    """
    OK = 0
    CANCELLED = 1
    UNKNOWN = 2
    INVALID_ARGUMENT = 3
    DEADLINE_EXCEEDED = 4
    NOT_FOUND = 5
    ALREADY_EXISTS = 6
    PERMISSION_DENIED = 7
    UNAUTHENTICATED = 8
    RESOURCE_EXHAUSTED = 9
    FAILED_PRECONDITION = 10
    ABORTED = 11
    OUT_OF_RANGE = 12
    UNIMPLEMENTED = 13
    INTERNAL = 14
    UNAVAILABLE = 15
    DATA_LOSS = 16

  class CodeValueValuesEnum(_messages.Enum):
    r"""Machine-friendly representation of the condition Deprecated. Use
    canonical_code instead.

    Values:
      UNKNOWN: UNKNOWN indicates a generic condition.
      GCE_STOCKOUT: GCE_STOCKOUT indicates that Google Compute Engine
        resources are temporarily unavailable.
      GKE_SERVICE_ACCOUNT_DELETED: GKE_SERVICE_ACCOUNT_DELETED indicates that
        the user deleted their robot service account.
      GCE_QUOTA_EXCEEDED: Google Compute Engine quota was exceeded.
      SET_BY_OPERATOR: Cluster state was manually changed by an SRE due to a
        system logic error.
      CLOUD_KMS_KEY_ERROR: Unable to perform an encrypt operation against the
        CloudKMS key used for etcd level encryption.
      CA_EXPIRING: Cluster CA is expiring soon.
    """
    UNKNOWN = 0
    GCE_STOCKOUT = 1
    GKE_SERVICE_ACCOUNT_DELETED = 2
    GCE_QUOTA_EXCEEDED = 3
    SET_BY_OPERATOR = 4
    CLOUD_KMS_KEY_ERROR = 5
    CA_EXPIRING = 6

  canonicalCode = _messages.EnumField('CanonicalCodeValueValuesEnum', 1)
  code = _messages.EnumField('CodeValueValuesEnum', 2)
  message = _messages.StringField(3)


class TimeWindow(_messages.Message):
  r"""Represents an arbitrary window of time.

  Fields:
    endTime: The time that the window ends. The end time should take place
      after the start time.
    maintenanceExclusionOptions: MaintenanceExclusionOptions provides
      maintenance exclusion related options.
    startTime: The time that the window first starts.
  """

  endTime = _messages.StringField(1)
  maintenanceExclusionOptions = _messages.MessageField('MaintenanceExclusionOptions', 2)
  startTime = _messages.StringField(3)


class UpdateClusterRequest(_messages.Message):
  r"""UpdateClusterRequest updates the settings of a cluster.

  Fields:
    clusterId: Deprecated. The name of the cluster to upgrade. This field has
      been deprecated and replaced by the name field.
    name: The name (project, location, cluster) of the cluster to update.
      Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    update: Required. A description of the update.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  name = _messages.StringField(2)
  projectId = _messages.StringField(3)
  update = _messages.MessageField('ClusterUpdate', 4)
  zone = _messages.StringField(5)


class UpdateInfo(_messages.Message):
  r"""UpdateInfo contains resource (instance groups, etc), status and other
  intermediate information relevant to a node pool upgrade.

  Fields:
    blueGreenInfo: Information of a blue-green upgrade.
  """

  blueGreenInfo = _messages.MessageField('BlueGreenInfo', 1)


class UpdateMasterRequest(_messages.Message):
  r"""UpdateMasterRequest updates the master of the cluster.

  Fields:
    clusterId: Deprecated. The name of the cluster to upgrade. This field has
      been deprecated and replaced by the name field.
    masterVersion: Required. The Kubernetes version to change the master to.
      Users may specify either explicit versions offered by Kubernetes Engine
      or version aliases, which have the following behavior: - "latest": picks
      the highest valid Kubernetes version - "1.X": picks the highest valid
      patch+gke.N patch in the 1.X version - "1.X.Y": picks the highest valid
      gke.N patch in the 1.X.Y version - "1.X.Y-gke.N": picks an explicit
      Kubernetes version - "-": picks the default Kubernetes version
    name: The name (project, location, cluster) of the cluster to update.
      Specified in the format `projects/*/locations/*/clusters/*`.
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  masterVersion = _messages.StringField(2)
  name = _messages.StringField(3)
  projectId = _messages.StringField(4)
  zone = _messages.StringField(5)


class UpdateNodePoolRequest(_messages.Message):
  r"""UpdateNodePoolRequests update a node pool's image and/or version.

  Fields:
    clusterId: Deprecated. The name of the cluster to upgrade. This field has
      been deprecated and replaced by the name field.
    confidentialNodes: Confidential nodes config. All the nodes in the node
      pool will be Confidential VM once enabled.
    containerdConfig: The desired containerd config for nodes in the node
      pool. Initiates an upgrade operation that recreates the nodes with the
      new config.
    diskSizeGb: Optional. The desired disk size for nodes in the node pool
      specified in GB. The smallest allowed disk size is 10GB. Initiates an
      upgrade operation that migrates the nodes in the node pool to the
      specified disk size.
    diskType: Optional. The desired disk type (e.g. 'pd-standard', 'pd-ssd' or
      'pd-balanced') for nodes in the node pool. Initiates an upgrade
      operation that migrates the nodes in the node pool to the specified disk
      type.
    etag: The current etag of the node pool. If an etag is provided and does
      not match the current etag of the node pool, update will be blocked and
      an ABORTED error will be returned.
    fastSocket: Enable or disable NCCL fast socket for the node pool.
    gcfsConfig: GCFS config.
    gvnic: Enable or disable gvnic on the node pool.
    image: The desired name of the image name to use for this node. This is
      used to create clusters using a custom image.
    imageProject: The project containing the desired image to use for this
      node pool. This is used to create clusters using a custom image.
    imageType: Required. The desired image type for the node pool. Please see
      https://cloud.google.com/kubernetes-engine/docs/concepts/node-images for
      available image types.
    kubeletConfig: Node kubelet configs.
    labels: The desired node labels to be applied to all nodes in the node
      pool. If this field is not present, the labels will not be changed.
      Otherwise, the existing node labels will be *replaced* with the provided
      labels.
    linuxNodeConfig: Parameters that can be configured on Linux nodes.
    locations: The desired list of Google Compute Engine
      [zones](https://cloud.google.com/compute/docs/zones#available) in which
      the node pool's nodes should be located. Changing the locations for a
      node pool will result in nodes being either created or removed from the
      node pool, depending on whether locations are being added or removed.
    loggingConfig: Logging configuration.
    machineType: Optional. The desired [Google Compute Engine machine
      type](https://cloud.google.com/compute/docs/machine-types) for nodes in
      the node pool. Initiates an upgrade operation that migrates the nodes in
      the node pool to the specified machine type.
    name: The name (project, location, cluster, node pool) of the node pool to
      update. Specified in the format
      `projects/*/locations/*/clusters/*/nodePools/*`.
    nodeNetworkConfig: Node network config.
    nodePoolId: Deprecated. The name of the node pool to upgrade. This field
      has been deprecated and replaced by the name field.
    nodeVersion: Required. The Kubernetes version to change the nodes to
      (typically an upgrade). Users may specify either explicit versions
      offered by Kubernetes Engine or version aliases, which have the
      following behavior: - "latest": picks the highest valid Kubernetes
      version - "1.X": picks the highest valid patch+gke.N patch in the 1.X
      version - "1.X.Y": picks the highest valid gke.N patch in the 1.X.Y
      version - "1.X.Y-gke.N": picks an explicit Kubernetes version - "-":
      picks the Kubernetes master version
    projectId: Deprecated. The Google Developers Console [project ID or
      project number](https://cloud.google.com/resource-manager/docs/creating-
      managing-projects). This field has been deprecated and replaced by the
      name field.
    resourceLabels: The resource labels for the node pool to use to annotate
      any related Google Compute Engine resources.
    resourceManagerTags: Desired resource manager tag keys and values to be
      attached to the nodes for managing Compute Engine firewalls using
      Network Firewall Policies. Existing tags will be replaced with new
      values.
    tags: The desired network tags to be applied to all nodes in the node
      pool. If this field is not present, the tags will not be changed.
      Otherwise, the existing network tags will be *replaced* with the
      provided tags.
    taints: The desired node taints to be applied to all nodes in the node
      pool. If this field is not present, the taints will not be changed.
      Otherwise, the existing node taints will be *replaced* with the provided
      taints.
    upgradeSettings: Upgrade settings control disruption and speed of the
      upgrade.
    windowsNodeConfig: Parameters that can be configured on Windows nodes.
    workloadMetadataConfig: The desired workload metadata config for the node
      pool.
    zone: Deprecated. The name of the Google Compute Engine
      [zone](https://cloud.google.com/compute/docs/zones#available) in which
      the cluster resides. This field has been deprecated and replaced by the
      name field.
  """

  clusterId = _messages.StringField(1)
  confidentialNodes = _messages.MessageField('ConfidentialNodes', 2)
  containerdConfig = _messages.MessageField('ContainerdConfig', 3)
  diskSizeGb = _messages.IntegerField(4)
  diskType = _messages.StringField(5)
  etag = _messages.StringField(6)
  fastSocket = _messages.MessageField('FastSocket', 7)
  gcfsConfig = _messages.MessageField('GcfsConfig', 8)
  gvnic = _messages.MessageField('VirtualNIC', 9)
  image = _messages.StringField(10)
  imageProject = _messages.StringField(11)
  imageType = _messages.StringField(12)
  kubeletConfig = _messages.MessageField('NodeKubeletConfig', 13)
  labels = _messages.MessageField('NodeLabels', 14)
  linuxNodeConfig = _messages.MessageField('LinuxNodeConfig', 15)
  locations = _messages.StringField(16, repeated=True)
  loggingConfig = _messages.MessageField('NodePoolLoggingConfig', 17)
  machineType = _messages.StringField(18)
  name = _messages.StringField(19)
  nodeNetworkConfig = _messages.MessageField('NodeNetworkConfig', 20)
  nodePoolId = _messages.StringField(21)
  nodeVersion = _messages.StringField(22)
  projectId = _messages.StringField(23)
  resourceLabels = _messages.MessageField('ResourceLabels', 24)
  resourceManagerTags = _messages.MessageField('ResourceManagerTags', 25)
  tags = _messages.MessageField('NetworkTags', 26)
  taints = _messages.MessageField('NodeTaints', 27)
  upgradeSettings = _messages.MessageField('UpgradeSettings', 28)
  windowsNodeConfig = _messages.MessageField('WindowsNodeConfig', 29)
  workloadMetadataConfig = _messages.MessageField('WorkloadMetadataConfig', 30)
  zone = _messages.StringField(31)


class UpgradeAvailableEvent(_messages.Message):
  r"""UpgradeAvailableEvent is a notification sent to customers when a new
  available version is released.

  Enums:
    ResourceTypeValueValuesEnum: The resource type of the release version.

  Fields:
    releaseChannel: The release channel of the version. If empty, it means a
      non-channel release.
    resource: Optional relative path to the resource. For example, the
      relative path of the node pool.
    resourceType: The resource type of the release version.
    version: The release version available for upgrade.
  """

  class ResourceTypeValueValuesEnum(_messages.Enum):
    r"""The resource type of the release version.

    Values:
      UPGRADE_RESOURCE_TYPE_UNSPECIFIED: Default value. This shouldn't be
        used.
      MASTER: Master / control plane
      NODE_POOL: Node pool
    """
    UPGRADE_RESOURCE_TYPE_UNSPECIFIED = 0
    MASTER = 1
    NODE_POOL = 2

  releaseChannel = _messages.MessageField('ReleaseChannel', 1)
  resource = _messages.StringField(2)
  resourceType = _messages.EnumField('ResourceTypeValueValuesEnum', 3)
  version = _messages.StringField(4)


class UpgradeEvent(_messages.Message):
  r"""UpgradeEvent is a notification sent to customers by the cluster server
  when a resource is upgrading.

  Enums:
    ResourceTypeValueValuesEnum: The resource type that is upgrading.

  Fields:
    currentVersion: The current version before the upgrade.
    operation: The operation associated with this upgrade.
    operationStartTime: The time when the operation was started.
    resource: Optional relative path to the resource. For example in node pool
      upgrades, the relative path of the node pool.
    resourceType: The resource type that is upgrading.
    targetVersion: The target version for the upgrade.
  """

  class ResourceTypeValueValuesEnum(_messages.Enum):
    r"""The resource type that is upgrading.

    Values:
      UPGRADE_RESOURCE_TYPE_UNSPECIFIED: Default value. This shouldn't be
        used.
      MASTER: Master / control plane
      NODE_POOL: Node pool
    """
    UPGRADE_RESOURCE_TYPE_UNSPECIFIED = 0
    MASTER = 1
    NODE_POOL = 2

  currentVersion = _messages.StringField(1)
  operation = _messages.StringField(2)
  operationStartTime = _messages.StringField(3)
  resource = _messages.StringField(4)
  resourceType = _messages.EnumField('ResourceTypeValueValuesEnum', 5)
  targetVersion = _messages.StringField(6)


class UpgradeSettings(_messages.Message):
  r"""These upgrade settings control the level of parallelism and the level of
  disruption caused by an upgrade. maxUnavailable controls the number of nodes
  that can be simultaneously unavailable. maxSurge controls the number of
  additional nodes that can be added to the node pool temporarily for the time
  of the upgrade to increase the number of available nodes. (maxUnavailable +
  maxSurge) determines the level of parallelism (how many nodes are being
  upgraded at the same time). Note: upgrades inevitably introduce some
  disruption since workloads need to be moved from old nodes to new, upgraded
  ones. Even if maxUnavailable=0, this holds true. (Disruption stays within
  the limits of PodDisruptionBudget, if it is configured.) Consider a
  hypothetical node pool with 5 nodes having maxSurge=2, maxUnavailable=1.
  This means the upgrade process upgrades 3 nodes simultaneously. It creates 2
  additional (upgraded) nodes, then it brings down 3 old (not yet upgraded)
  nodes at the same time. This ensures that there are always at least 4 nodes
  available. These upgrade settings configure the upgrade strategy for the
  node pool. Use strategy to switch between the strategies applied to the node
  pool. If the strategy is ROLLING, use max_surge and max_unavailable to
  control the level of parallelism and the level of disruption caused by
  upgrade. 1. maxSurge controls the number of additional nodes that can be
  added to the node pool temporarily for the time of the upgrade to increase
  the number of available nodes. 2. maxUnavailable controls the number of
  nodes that can be simultaneously unavailable. 3. (maxUnavailable + maxSurge)
  determines the level of parallelism (how many nodes are being upgraded at
  the same time). If the strategy is BLUE_GREEN, use blue_green_settings to
  configure the blue-green upgrade related settings. 1.
  standard_rollout_policy is the default policy. The policy is used to control
  the way blue pool gets drained. The draining is executed in the batch mode.
  The batch size could be specified as either percentage of the node pool size
  or the number of nodes. batch_soak_duration is the soak time after each
  batch gets drained. 2. node_pool_soak_duration is the soak time after all
  blue nodes are drained. After this period, the blue pool nodes will be
  deleted.

  Enums:
    StrategyValueValuesEnum: Update strategy of the node pool.

  Fields:
    blueGreenSettings: Settings for blue-green upgrade strategy.
    maxSurge: The maximum number of nodes that can be created beyond the
      current size of the node pool during the upgrade process.
    maxUnavailable: The maximum number of nodes that can be simultaneously
      unavailable during the upgrade process. A node is considered available
      if its status is Ready.
    rollingSettings: Settings for rolling update strategy.
    strategy: Update strategy of the node pool.
  """

  class StrategyValueValuesEnum(_messages.Enum):
    r"""Update strategy of the node pool.

    Values:
      NODE_POOL_UPDATE_STRATEGY_UNSPECIFIED: Default value if unset. GKE
        internally defaults the update strategy to SURGE for unspecified
        strategies.
      ROLLING: ROLLING is the synonymous with SURGE. Deprecate this value and
        use SURGE instead.
      BLUE_GREEN: blue-green upgrade.
      SURGE: SURGE is the traditional way of upgrade a node pool. max_surge
        and max_unavailable determines the level of upgrade parallelism.
    """
    NODE_POOL_UPDATE_STRATEGY_UNSPECIFIED = 0
    ROLLING = 1
    BLUE_GREEN = 2
    SURGE = 3

  blueGreenSettings = _messages.MessageField('BlueGreenSettings', 1)
  maxSurge = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  maxUnavailable = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  rollingSettings = _messages.MessageField('RollingSettings', 4)
  strategy = _messages.EnumField('StrategyValueValuesEnum', 5)


class UsableSubnetwork(_messages.Message):
  r"""UsableSubnetwork resource returns the subnetwork name, its associated
  network and the primary CIDR range.

  Fields:
    ipCidrRange: The range of internal addresses that are owned by this
      subnetwork.
    network: Network Name. Example: projects/my-project/global/networks/my-
      network
    secondaryIpRanges: Secondary IP ranges.
    statusMessage: A human readable status message representing the reasons
      for cases where the caller cannot use the secondary ranges under the
      subnet. For example if the secondary_ip_ranges is empty due to a
      permission issue, an insufficient permission message will be given by
      status_message.
    subnetwork: Subnetwork Name. Example: projects/my-project/regions/us-
      central1/subnetworks/my-subnet
  """

  ipCidrRange = _messages.StringField(1)
  network = _messages.StringField(2)
  secondaryIpRanges = _messages.MessageField('UsableSubnetworkSecondaryRange', 3, repeated=True)
  statusMessage = _messages.StringField(4)
  subnetwork = _messages.StringField(5)


class UsableSubnetworkSecondaryRange(_messages.Message):
  r"""Secondary IP range of a usable subnetwork.

  Enums:
    StatusValueValuesEnum: This field is to determine the status of the
      secondary range programmably.

  Fields:
    ipCidrRange: The range of IP addresses belonging to this subnetwork
      secondary range.
    rangeName: The name associated with this subnetwork secondary range, used
      when adding an alias IP range to a VM instance.
    status: This field is to determine the status of the secondary range
      programmably.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""This field is to determine the status of the secondary range
    programmably.

    Values:
      UNKNOWN: UNKNOWN is the zero value of the Status enum. It's not a valid
        status.
      UNUSED: UNUSED denotes that this range is unclaimed by any cluster.
      IN_USE_SERVICE: IN_USE_SERVICE denotes that this range is claimed by
        cluster(s) for services. User-managed services range can be shared
        between clusters within the same subnetwork.
      IN_USE_SHAREABLE_POD: IN_USE_SHAREABLE_POD denotes this range was
        created by the network admin and is currently claimed by a cluster for
        pods. It can only be used by other clusters as a pod range.
      IN_USE_MANAGED_POD: IN_USE_MANAGED_POD denotes this range was created by
        GKE and is claimed for pods. It cannot be used for other clusters.
    """
    UNKNOWN = 0
    UNUSED = 1
    IN_USE_SERVICE = 2
    IN_USE_SHAREABLE_POD = 3
    IN_USE_MANAGED_POD = 4

  ipCidrRange = _messages.StringField(1)
  rangeName = _messages.StringField(2)
  status = _messages.EnumField('StatusValueValuesEnum', 3)


class VerticalPodAutoscaling(_messages.Message):
  r"""VerticalPodAutoscaling contains global, per-cluster information required
  by Vertical Pod Autoscaler to automatically adjust the resources of pods
  controlled by it.

  Fields:
    enabled: Enables vertical pod autoscaling.
  """

  enabled = _messages.BooleanField(1)


class VirtualNIC(_messages.Message):
  r"""Configuration of gVNIC feature.

  Fields:
    enabled: Whether gVNIC features are enabled in the node pool.
  """

  enabled = _messages.BooleanField(1)


class WindowsNodeConfig(_messages.Message):
  r"""Parameters that can be configured on Windows nodes. Windows Node Config
  that define the parameters that will be used to configure the Windows node
  pool settings

  Enums:
    OsVersionValueValuesEnum: OSVersion specifies the Windows node config to
      be used on the node

  Fields:
    osVersion: OSVersion specifies the Windows node config to be used on the
      node
  """

  class OsVersionValueValuesEnum(_messages.Enum):
    r"""OSVersion specifies the Windows node config to be used on the node

    Values:
      OS_VERSION_UNSPECIFIED: When OSVersion is not specified
      OS_VERSION_LTSC2019: LTSC2019 specifies to use LTSC2019 as the Windows
        Servercore Base Image
      OS_VERSION_LTSC2022: LTSC2022 specifies to use LTSC2022 as the Windows
        Servercore Base Image
    """
    OS_VERSION_UNSPECIFIED = 0
    OS_VERSION_LTSC2019 = 1
    OS_VERSION_LTSC2022 = 2

  osVersion = _messages.EnumField('OsVersionValueValuesEnum', 1)


class WorkloadIdentityConfig(_messages.Message):
  r"""Configuration for the use of Kubernetes Service Accounts in GCP IAM
  policies.

  Fields:
    workloadPool: The workload pool to attach all Kubernetes service accounts
      to.
  """

  workloadPool = _messages.StringField(1)


class WorkloadMetadataConfig(_messages.Message):
  r"""WorkloadMetadataConfig defines the metadata configuration to expose to
  workloads on the node pool.

  Enums:
    ModeValueValuesEnum: Mode is the configuration for how to expose metadata
      to workloads running on the node pool.

  Fields:
    mode: Mode is the configuration for how to expose metadata to workloads
      running on the node pool.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Mode is the configuration for how to expose metadata to workloads
    running on the node pool.

    Values:
      MODE_UNSPECIFIED: Not set.
      GCE_METADATA: Expose all Compute Engine metadata to pods.
      GKE_METADATA: Run the GKE Metadata Server on this node. The GKE Metadata
        Server exposes a metadata API to workloads that is compatible with the
        V1 Compute Metadata APIs exposed by the Compute Engine and App Engine
        Metadata Servers. This feature can only be enabled if Workload
        Identity is enabled at the cluster level.
    """
    MODE_UNSPECIFIED = 0
    GCE_METADATA = 1
    GKE_METADATA = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)


class WorkloadPolicyConfig(_messages.Message):
  r"""WorkloadPolicyConfig is the configuration of workload policy for
  autopilot clusters.

  Fields:
    allowNetAdmin: If true, workloads can use NET_ADMIN capability.
  """

  allowNetAdmin = _messages.BooleanField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

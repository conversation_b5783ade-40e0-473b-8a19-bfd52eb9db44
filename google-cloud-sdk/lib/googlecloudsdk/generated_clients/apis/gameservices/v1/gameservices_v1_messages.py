"""Generated message classes for gameservices version v1.

Deploy and manage infrastructure for global multiplayer gaming experiences.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'gameservices'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It <NAME_EMAIL> from DATA_READ logging, and
  <EMAIL> from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    exemptedMembers: A string attribute.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  exemptedMembers = _messages.StringField(2, repeated=True)
  service = _messages.StringField(3)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class AuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class Binding(_messages.Message):
  r"""Associates `members` with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the members in this binding.
      To learn which resources support conditions in their IAM policies, see
      the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the identities requesting access for a Cloud Platform
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. * `user:{emailid}`: An email address that
      represents a specific Google account. For example, `<EMAIL>` .
      * `serviceAccount:{emailid}`: An email address that represents a service
      account. For example, `<EMAIL>`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `domain:{domain}`: The G Suite
      domain (primary) that represents all the users of that domain. For
      example, `google.com` or `example.com`.
    role: Role that is assigned to `members`. For example, `roles/viewer`,
      `roles/editor`, or `roles/owner`.
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('Expr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline.
    logName: The log_name to populate in the Cloud Audit Record.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  authorizationLoggingOptions = _messages.MessageField('AuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)


class Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self' (i.e., allow connections from clients that are in the
        same security realm, which is currently but not guaranteed to be
        campus-sized) - 'self:metro' (i.e., clients that are in the same
        metro) - 'self:cloud-region' (i.e., allow connections from clients
        that are in the same cloud region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - a realm (e.g., 'campus-
        abc') - a realm group (e.g., 'realms-for-borg-cell-xx', see: go/realm-
        groups) A match is determined by a realm group membership check
        performed by a RealmAclRep object (go/realm-acl-howto). It is not
        permitted to grant access based on the *absence* of a realm, so realm
        conditions can only be used in a "positive" context (e.g., ALLOW/IN or
        DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: EXPERIMENTAL -- DO NOT USE. The conditions can only be
        used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class CounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('CustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class CustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class DataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  logMode = _messages.EnumField('LogModeValueValuesEnum', 1)


class DeployedClusterState(_messages.Message):
  r"""The game server cluster changes made by the game server deployment.

  Fields:
    cluster: The name of the cluster.
    fleetDetails: The details about the Agones fleets and autoscalers created
      in the game server cluster.
  """

  cluster = _messages.StringField(1)
  fleetDetails = _messages.MessageField('DeployedFleetDetails', 2, repeated=True)


class DeployedFleet(_messages.Message):
  r"""Agones fleet specification and details.

  Fields:
    fleet: The name of the Agones fleet.
    fleetSpec: The fleet spec retrieved from the Agones fleet.
    specSource: The source spec that is used to create the Agones fleet. The
      GameServerConfig resource may no longer exist in the system.
    status: The current status of the Agones fleet. Includes count of game
      servers in various states.
  """

  fleet = _messages.StringField(1)
  fleetSpec = _messages.StringField(2)
  specSource = _messages.MessageField('SpecSource', 3)
  status = _messages.MessageField('DeployedFleetStatus', 4)


class DeployedFleetAutoscaler(_messages.Message):
  r"""Details about the Agones autoscaler.

  Fields:
    autoscaler: The name of the Agones autoscaler.
    fleetAutoscalerSpec: The autoscaler spec retrieved from Agones.
    specSource: The source spec that is used to create the autoscaler. The
      GameServerConfig resource may no longer exist in the system.
  """

  autoscaler = _messages.StringField(1)
  fleetAutoscalerSpec = _messages.StringField(2)
  specSource = _messages.MessageField('SpecSource', 3)


class DeployedFleetDetails(_messages.Message):
  r"""Details of the deployed Agones fleet.

  Fields:
    deployedAutoscaler: Information about the Agones autoscaler for that
      fleet.
    deployedFleet: Information about the Agones fleet.
  """

  deployedAutoscaler = _messages.MessageField('DeployedFleetAutoscaler', 1)
  deployedFleet = _messages.MessageField('DeployedFleet', 2)


class DeployedFleetStatus(_messages.Message):
  r"""DeployedFleetStatus has details about the Agones fleets such as how many
  are running, how many allocated, and so on.

  Fields:
    allocatedReplicas: The number of GameServer replicas in the ALLOCATED
      state in this fleet.
    readyReplicas: The number of GameServer replicas in the READY state in
      this fleet.
    replicas: The total number of current GameServer replicas in this fleet.
    reservedReplicas: The number of GameServer replicas in the RESERVED state
      in this fleet. Reserved instances won't be deleted on scale down, but
      won't cause an autoscaler to scale up.
  """

  allocatedReplicas = _messages.IntegerField(1)
  readyReplicas = _messages.IntegerField(2)
  replicas = _messages.IntegerField(3)
  reservedReplicas = _messages.IntegerField(4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } The JSON
  representation for `Empty` is empty JSON object `{}`.
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FetchDeploymentStateRequest(_messages.Message):
  r"""Request message for GameServerDeploymentsService.FetchDeploymentState.
  """



class FetchDeploymentStateResponse(_messages.Message):
  r"""Response message for GameServerDeploymentsService.FetchDeploymentState.

  Fields:
    clusterState: The state of the game server deployment in each game server
      cluster.
    unavailable: List of locations that could not be reached.
  """

  clusterState = _messages.MessageField('DeployedClusterState', 1, repeated=True)
  unavailable = _messages.StringField(2, repeated=True)


class FleetConfig(_messages.Message):
  r"""Fleet configs for Agones.

  Fields:
    fleetSpec: Agones fleet spec. Example spec:
      `https://agones.dev/site/docs/reference/fleet/`.
    name: The name of the FleetConfig.
  """

  fleetSpec = _messages.StringField(1)
  name = _messages.StringField(2)


class GameServerCluster(_messages.Message):
  r"""A game server cluster resource.

  Messages:
    LabelsValue: The labels associated with this game server cluster. Each
      label is a key-value pair.

  Fields:
    clusterState: Output only. The state of the Kubernetes cluster, this will
      be available if 'view' is set to `FULL` in the relevant List/Get/Preview
      request.
    connectionInfo: The game server cluster connection information. This
      information is used to manage game server clusters.
    createTime: Output only. The creation time.
    description: Human readable description of the cluster.
    etag: ETag of the resource.
    labels: The labels associated with this game server cluster. Each label is
      a key-value pair.
    name: Required. The resource name of the game server cluster, in the
      following form: `projects/{project}/locations/{location}/realms/{realm}/
      gameServerClusters/{cluster}`. For example, `projects/my-
      project/locations/{location}/realms/zanzibar/gameServerClusters/my-
      onprem-cluster`.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this game server cluster. Each label is a
    key-value pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  clusterState = _messages.MessageField('KubernetesClusterState', 1)
  connectionInfo = _messages.MessageField('GameServerClusterConnectionInfo', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  etag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GameServerClusterConnectionInfo(_messages.Message):
  r"""The game server cluster connection information.

  Fields:
    gkeClusterReference: Reference to the GKE cluster where the game servers
      are installed.
    namespace: Namespace designated on the game server cluster where the
      Agones game server instances will be created. Existence of the namespace
      will be validated during creation.
  """

  gkeClusterReference = _messages.MessageField('GkeClusterReference', 1)
  namespace = _messages.StringField(2)


class GameServerConfig(_messages.Message):
  r"""A game server config resource.

  Messages:
    LabelsValue: The labels associated with this game server config. Each
      label is a key-value pair.

  Fields:
    createTime: Output only. The creation time.
    description: The description of the game server config.
    fleetConfigs: FleetConfig contains a list of Agones fleet specs. Only one
      FleetConfig is allowed.
    labels: The labels associated with this game server config. Each label is
      a key-value pair.
    name: The resource name of the game server config, in the following form:
      `projects/{project}/locations/{location}/gameServerDeployments/{deployme
      nt}/configs/{config}`. For example, `projects/my-
      project/locations/global/gameServerDeployments/my-game/configs/my-
      config`.
    scalingConfigs: The autoscaling settings.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this game server config. Each label is a
    key-value pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  fleetConfigs = _messages.MessageField('FleetConfig', 3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  scalingConfigs = _messages.MessageField('ScalingConfig', 6, repeated=True)
  updateTime = _messages.StringField(7)


class GameServerConfigOverride(_messages.Message):
  r"""A game server config override.

  Fields:
    configVersion: The game server config for this override.
    realmsSelector: Selector for choosing applicable realms.
  """

  configVersion = _messages.StringField(1)
  realmsSelector = _messages.MessageField('RealmSelector', 2)


class GameServerDeployment(_messages.Message):
  r"""A game server deployment resource.

  Messages:
    LabelsValue: The labels associated with this game server deployment. Each
      label is a key-value pair.

  Fields:
    createTime: Output only. The creation time.
    description: Human readable description of the game server delpoyment.
    etag: ETag of the resource.
    labels: The labels associated with this game server deployment. Each label
      is a key-value pair.
    name: The resource name of the game server deployment, in the following
      form: `projects/{project}/locations/{location}/gameServerDeployments/{de
      ployment}`. For example, `projects/my-
      project/locations/global/gameServerDeployments/my-deployment`.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this game server deployment. Each label is
    a key-value pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class GameServerDeploymentRollout(_messages.Message):
  r"""The game server deployment rollout which represents the desired rollout
  state.

  Fields:
    createTime: Output only. The creation time.
    defaultGameServerConfig: The default game server config is applied to all
      realms unless overridden in the rollout. For example, `projects/my-
      project/locations/global/gameServerDeployments/my-game/configs/my-
      config`.
    etag: ETag of the resource.
    gameServerConfigOverrides: Contains the game server config rollout
      overrides. Overrides are processed in the order they are listed. Once a
      match is found for a realm, the rest of the list is not processed.
    name: The resource name of the game server deployment rollout, in the
      following form: `projects/{project}/locations/{location}/gameServerDeplo
      yments/{deployment}/rollout`. For example, `projects/my-
      project/locations/global/gameServerDeployments/my-deployment/rollout`.
    updateTime: Output only. The last-modified time.
  """

  createTime = _messages.StringField(1)
  defaultGameServerConfig = _messages.StringField(2)
  etag = _messages.StringField(3)
  gameServerConfigOverrides = _messages.MessageField('GameServerConfigOverride', 4, repeated=True)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class GameservicesProjectsLocationsGameServerDeploymentsConfigsCreateRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsConfigsCreateRequest
  object.

  Fields:
    configId: Required. The ID of the game server config resource to be
      created.
    gameServerConfig: A GameServerConfig resource to be passed as the request
      body.
    parent: Required. The parent resource name, in the following form: `projec
      ts/{project}/locations/{location}/gameServerDeployments/{deployment}/`.
  """

  configId = _messages.StringField(1)
  gameServerConfig = _messages.MessageField('GameServerConfig', 2)
  parent = _messages.StringField(3, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsConfigsDeleteRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsConfigsDeleteRequest
  object.

  Fields:
    name: Required. The name of the game server config to delete, in the
      following form: `projects/{project}/locations/{location}/gameServerDeplo
      yments/{deployment}/configs/{config}`.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsConfigsGetRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsConfigsGetRequest
  object.

  Fields:
    name: Required. The name of the game server config to retrieve, in the
      following form: `projects/{project}/locations/{location}/gameServerDeplo
      yments/{deployment}/configs/{config}`.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsConfigsListRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsConfigsListRequest
  object.

  Fields:
    filter: Optional. The filter to apply to list results.
    orderBy: Optional. Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      server will pick an appropriate default. Server may return fewer items
      than requested. A caller should only rely on response's next_page_token
      to determine if there are more GameServerConfigs left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      list request, if any.
    parent: Required. The parent resource name, in the following form: `projec
      ts/{project}/locations/{location}/gameServerDeployments/{deployment}/con
      figs/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsCreateRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsCreateRequest
  object.

  Fields:
    deploymentId: Required. The ID of the game server delpoyment resource to
      be created.
    gameServerDeployment: A GameServerDeployment resource to be passed as the
      request body.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  deploymentId = _messages.StringField(1)
  gameServerDeployment = _messages.MessageField('GameServerDeployment', 2)
  parent = _messages.StringField(3, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsDeleteRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsDeleteRequest
  object.

  Fields:
    name: Required. The name of the game server delpoyment to delete, in the
      following form: `projects/{project}/locations/{location}/gameServerDeplo
      yments/{deployment}`.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsFetchDeploymentStateRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsFetchDeploymentState
  Request object.

  Fields:
    fetchDeploymentStateRequest: A FetchDeploymentStateRequest resource to be
      passed as the request body.
    name: Required. The name of the game server delpoyment, in the following
      form: `projects/{project}/locations/{location}/gameServerDeployments/{de
      ployment}`.
  """

  fetchDeploymentStateRequest = _messages.MessageField('FetchDeploymentStateRequest', 1)
  name = _messages.StringField(2, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsGetIamPolicyRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The policy format version to be
      returned. Valid values are 0, 1, and 3. Requests specifying an invalid
      value will be rejected. Requests for policies with any conditional
      bindings must specify version 3. Policies without any conditional
      bindings may specify any valid value or leave the field unset. To learn
      which resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See the operation documentation for the appropriate value for this
      field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsGetRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsGetRequest object.

  Fields:
    name: Required. The name of the game server delpoyment to retrieve, in the
      following form: `projects/{project}/locations/{location}/gameServerDeplo
      yments/{deployment}`.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsGetRolloutRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsGetRolloutRequest
  object.

  Fields:
    name: Required. The name of the game server delpoyment to retrieve, in the
      following form: `projects/{project}/locations/{location}/gameServerDeplo
      yments/{deployment}/rollout`.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsListRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsListRequest object.

  Fields:
    filter: Optional. The filter to apply to list results.
    orderBy: Optional. Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      the server will pick an appropriate default. The server may return fewer
      items than requested. A caller should only rely on response's
      next_page_token to determine if there are more GameServerDeployments
      left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GameservicesProjectsLocationsGameServerDeploymentsPatchRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsPatchRequest object.

  Fields:
    gameServerDeployment: A GameServerDeployment resource to be passed as the
      request body.
    name: The resource name of the game server deployment, in the following
      form: `projects/{project}/locations/{location}/gameServerDeployments/{de
      ployment}`. For example, `projects/my-
      project/locations/global/gameServerDeployments/my-deployment`.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. For the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  gameServerDeployment = _messages.MessageField('GameServerDeployment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class GameservicesProjectsLocationsGameServerDeploymentsPreviewRolloutRequest(_messages.Message):
  r"""A
  GameservicesProjectsLocationsGameServerDeploymentsPreviewRolloutRequest
  object.

  Fields:
    gameServerDeploymentRollout: A GameServerDeploymentRollout resource to be
      passed as the request body.
    name: The resource name of the game server deployment rollout, in the
      following form: `projects/{project}/locations/{location}/gameServerDeplo
      yments/{deployment}/rollout`. For example, `projects/my-
      project/locations/global/gameServerDeployments/my-deployment/rollout`.
    previewTime: Optional. The target timestamp to compute the preview.
      Defaults to the immediately after the proposed rollout completes.
    updateMask: Optional. Mask of fields to update. At least one path must be
      supplied in this field. For the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  gameServerDeploymentRollout = _messages.MessageField('GameServerDeploymentRollout', 1)
  name = _messages.StringField(2, required=True)
  previewTime = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class GameservicesProjectsLocationsGameServerDeploymentsSetIamPolicyRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See the operation documentation for the appropriate value for this
      field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GameservicesProjectsLocationsGameServerDeploymentsTestIamPermissionsRequest(_messages.Message):
  r"""A
  GameservicesProjectsLocationsGameServerDeploymentsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See the operation documentation for the appropriate value for
      this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GameservicesProjectsLocationsGameServerDeploymentsUpdateRolloutRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGameServerDeploymentsUpdateRolloutRequest
  object.

  Fields:
    gameServerDeploymentRollout: A GameServerDeploymentRollout resource to be
      passed as the request body.
    name: The resource name of the game server deployment rollout, in the
      following form: `projects/{project}/locations/{location}/gameServerDeplo
      yments/{deployment}/rollout`. For example, `projects/my-
      project/locations/global/gameServerDeployments/my-deployment/rollout`.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. For the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  gameServerDeploymentRollout = _messages.MessageField('GameServerDeploymentRollout', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class GameservicesProjectsLocationsGetRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsListRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like "displayName=tokyo", and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  includeUnrevealedLocations = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class GameservicesProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class GameservicesProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GameservicesProjectsLocationsRealmsCreateRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsCreateRequest object.

  Fields:
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
    realm: A Realm resource to be passed as the request body.
    realmId: Required. The ID of the realm resource to be created.
  """

  parent = _messages.StringField(1, required=True)
  realm = _messages.MessageField('Realm', 2)
  realmId = _messages.StringField(3)


class GameservicesProjectsLocationsRealmsDeleteRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsDeleteRequest object.

  Fields:
    name: Required. The name of the realm to delete, in the following form:
      `projects/{project}/locations/{location}/realms/{realm}`.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsRealmsGameServerClustersCreateRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsGameServerClustersCreateRequest
  object.

  Fields:
    gameServerCluster: A GameServerCluster resource to be passed as the
      request body.
    gameServerClusterId: Required. The ID of the game server cluster resource
      to be created.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}/realms/{realm-id}`.
  """

  gameServerCluster = _messages.MessageField('GameServerCluster', 1)
  gameServerClusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GameservicesProjectsLocationsRealmsGameServerClustersDeleteRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsGameServerClustersDeleteRequest
  object.

  Fields:
    name: Required. The name of the game server cluster to delete, in the
      following form:
      `projects/{project}/locations/{location}/gameServerClusters/{cluster}`.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsRealmsGameServerClustersGetRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsGameServerClustersGetRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. View for the returned GameServerCluster
      objects. When `FULL` is specified, the `cluster_state` field is also
      returned in the GameServerCluster object, which includes the state of
      the referenced Kubernetes cluster such as versions and provider info.
      The default/unset value is GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED, same as
      BASIC, which does not return the `cluster_state` field.

  Fields:
    name: Required. The name of the game server cluster to retrieve, in the
      following form: `projects/{project}/locations/{location}/realms/{realm-
      id}/gameServerClusters/{cluster}`.
    view: Optional. View for the returned GameServerCluster objects. When
      `FULL` is specified, the `cluster_state` field is also returned in the
      GameServerCluster object, which includes the state of the referenced
      Kubernetes cluster such as versions and provider info. The default/unset
      value is GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED, same as BASIC, which does
      not return the `cluster_state` field.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. View for the returned GameServerCluster objects. When `FULL`
    is specified, the `cluster_state` field is also returned in the
    GameServerCluster object, which includes the state of the referenced
    Kubernetes cluster such as versions and provider info. The default/unset
    value is GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED, same as BASIC, which does
    not return the `cluster_state` field.

    Values:
      GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED: The default / unset value. The API
        will default to the BASIC view.
      BASIC: Include basic information of a GameServerCluster resource and
        omit `cluster_state`. This is the default value (for
        ListGameServerClusters, GetGameServerCluster and
        PreviewCreateGameServerCluster).
      FULL: Include everything.
    """
    GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class GameservicesProjectsLocationsRealmsGameServerClustersListRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsGameServerClustersListRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. View for the returned GameServerCluster
      objects. When `FULL` is specified, the `cluster_state` field is also
      returned in the GameServerCluster object, which includes the state of
      the referenced Kubernetes cluster such as versions and provider info.
      The default/unset value is GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED, same as
      BASIC, which does not return the `cluster_state` field.

  Fields:
    filter: Optional. The filter to apply to list results.
    orderBy: Optional. Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      the server will pick an appropriate default. The server may return fewer
      items than requested. A caller should only rely on response's
      next_page_token to determine if there are more GameServerClusters left
      to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The parent resource name, in the following form:
      "projects/{project}/locations/{location}/realms/{realm}".
    view: Optional. View for the returned GameServerCluster objects. When
      `FULL` is specified, the `cluster_state` field is also returned in the
      GameServerCluster object, which includes the state of the referenced
      Kubernetes cluster such as versions and provider info. The default/unset
      value is GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED, same as BASIC, which does
      not return the `cluster_state` field.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. View for the returned GameServerCluster objects. When `FULL`
    is specified, the `cluster_state` field is also returned in the
    GameServerCluster object, which includes the state of the referenced
    Kubernetes cluster such as versions and provider info. The default/unset
    value is GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED, same as BASIC, which does
    not return the `cluster_state` field.

    Values:
      GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED: The default / unset value. The API
        will default to the BASIC view.
      BASIC: Include basic information of a GameServerCluster resource and
        omit `cluster_state`. This is the default value (for
        ListGameServerClusters, GetGameServerCluster and
        PreviewCreateGameServerCluster).
      FULL: Include everything.
    """
    GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class GameservicesProjectsLocationsRealmsGameServerClustersPatchRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsGameServerClustersPatchRequest
  object.

  Fields:
    gameServerCluster: A GameServerCluster resource to be passed as the
      request body.
    name: Required. The resource name of the game server cluster, in the
      following form: `projects/{project}/locations/{location}/realms/{realm}/
      gameServerClusters/{cluster}`. For example, `projects/my-
      project/locations/{location}/realms/zanzibar/gameServerClusters/my-
      onprem-cluster`.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. For the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  gameServerCluster = _messages.MessageField('GameServerCluster', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class GameservicesProjectsLocationsRealmsGameServerClustersPreviewCreateRequest(_messages.Message):
  r"""A
  GameservicesProjectsLocationsRealmsGameServerClustersPreviewCreateRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. This field is deprecated, preview will
      always return KubernetesClusterState.

  Fields:
    gameServerCluster: A GameServerCluster resource to be passed as the
      request body.
    gameServerClusterId: Required. The ID of the game server cluster resource
      to be created.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}/realms/{realm}`.
    previewTime: Optional. The target timestamp to compute the preview.
    view: Optional. This field is deprecated, preview will always return
      KubernetesClusterState.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. This field is deprecated, preview will always return
    KubernetesClusterState.

    Values:
      GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED: The default / unset value. The API
        will default to the BASIC view.
      BASIC: Include basic information of a GameServerCluster resource and
        omit `cluster_state`. This is the default value (for
        ListGameServerClusters, GetGameServerCluster and
        PreviewCreateGameServerCluster).
      FULL: Include everything.
    """
    GAME_SERVER_CLUSTER_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  gameServerCluster = _messages.MessageField('GameServerCluster', 1)
  gameServerClusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  previewTime = _messages.StringField(4)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class GameservicesProjectsLocationsRealmsGameServerClustersPreviewDeleteRequest(_messages.Message):
  r"""A
  GameservicesProjectsLocationsRealmsGameServerClustersPreviewDeleteRequest
  object.

  Fields:
    name: Required. The name of the game server cluster to delete, in the
      following form:
      `projects/{project}/locations/{location}/gameServerClusters/{cluster}`.
    previewTime: Optional. The target timestamp to compute the preview.
  """

  name = _messages.StringField(1, required=True)
  previewTime = _messages.StringField(2)


class GameservicesProjectsLocationsRealmsGameServerClustersPreviewUpdateRequest(_messages.Message):
  r"""A
  GameservicesProjectsLocationsRealmsGameServerClustersPreviewUpdateRequest
  object.

  Fields:
    gameServerCluster: A GameServerCluster resource to be passed as the
      request body.
    name: Required. The resource name of the game server cluster, in the
      following form: `projects/{project}/locations/{location}/realms/{realm}/
      gameServerClusters/{cluster}`. For example, `projects/my-
      project/locations/{location}/realms/zanzibar/gameServerClusters/my-
      onprem-cluster`.
    previewTime: Optional. The target timestamp to compute the preview.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. For the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  gameServerCluster = _messages.MessageField('GameServerCluster', 1)
  name = _messages.StringField(2, required=True)
  previewTime = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class GameservicesProjectsLocationsRealmsGetRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsGetRequest object.

  Fields:
    name: Required. The name of the realm to retrieve, in the following form:
      `projects/{project}/locations/{location}/realms/{realm}`.
  """

  name = _messages.StringField(1, required=True)


class GameservicesProjectsLocationsRealmsListRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsListRequest object.

  Fields:
    filter: Optional. The filter to apply to list results.
    orderBy: Optional. Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: Optional. The maximum number of items to return. If unspecified,
      server will pick an appropriate default. Server may return fewer items
      than requested. A caller should only rely on response's next_page_token
      to determine if there are more realms left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GameservicesProjectsLocationsRealmsPatchRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsPatchRequest object.

  Fields:
    name: The resource name of the realm, in the following form:
      `projects/{project}/locations/{location}/realms/{realm}`. For example,
      `projects/my-project/locations/{location}/realms/my-realm`.
    realm: A Realm resource to be passed as the request body.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  realm = _messages.MessageField('Realm', 2)
  updateMask = _messages.StringField(3)


class GameservicesProjectsLocationsRealmsPreviewUpdateRequest(_messages.Message):
  r"""A GameservicesProjectsLocationsRealmsPreviewUpdateRequest object.

  Fields:
    name: The resource name of the realm, in the following form:
      `projects/{project}/locations/{location}/realms/{realm}`. For example,
      `projects/my-project/locations/{location}/realms/my-realm`.
    previewTime: Optional. The target timestamp to compute the preview.
    realm: A Realm resource to be passed as the request body.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  previewTime = _messages.StringField(2)
  realm = _messages.MessageField('Realm', 3)
  updateMask = _messages.StringField(4)


class GkeClusterReference(_messages.Message):
  r"""A reference to a GKE cluster.

  Fields:
    cluster: The full or partial name of a GKE cluster, using one of the
      following forms: *
      `projects/{project}/locations/{location}/clusters/{cluster}` *
      `locations/{location}/clusters/{cluster}` * `{cluster}` If project and
      location are not specified, the project and location of the
      GameServerCluster resource are used to generate the full name of the GKE
      cluster.
  """

  cluster = _messages.StringField(1)


class KubernetesClusterState(_messages.Message):
  r"""The state of the Kubernetes cluster.

  Enums:
    InstallationStateValueValuesEnum: Output only. The state for the installed
      versions of Agones/Kubernetes.

  Fields:
    agonesVersionInstalled: Output only. The version of Agones currently
      installed in the registered Kubernetes cluster.
    agonesVersionTargeted: Output only. The version of Agones that is targeted
      to be installed in the cluster.
    installationState: Output only. The state for the installed versions of
      Agones/Kubernetes.
    kubernetesVersionInstalled: Output only. The version of Kubernetes that is
      currently used in the registered Kubernetes cluster (as detected by the
      Cloud Game Servers service).
    provider: Output only. The cloud provider type reported by the first
      node's providerID in the list of nodes on the Kubernetes endpoint. On
      Kubernetes platforms that support zero-node clusters (like GKE-on-GCP),
      the provider type will be empty.
    versionInstalledErrorMessage: Output only. The detailed error message for
      the installed versions of Agones/Kubernetes.
  """

  class InstallationStateValueValuesEnum(_messages.Enum):
    r"""Output only. The state for the installed versions of
    Agones/Kubernetes.

    Values:
      INSTALLATION_STATE_UNSPECIFIED: The default value. This value is used if
        the state is omitted.
      AGONES_KUBERNETES_VERSION_SUPPORTED: The combination of Agones and
        Kubernetes versions is supported by Google Cloud Game Servers.
      AGONES_VERSION_UNSUPPORTED: The installed version of Agones is not
        supported by Google Cloud Game Servers.
      AGONES_KUBERNETES_VERSION_UNSUPPORTED: The installed version of Agones
        is supported by Google Cloud Game Servers, but the installed version
        of Kubernetes is not recommended or supported by the version of
        Agones.
      AGONES_VERSION_UNRECOGNIZED: The installed version of Agones is not
        recognized because the Agones controller's image name does not have a
        version string reported as {major}.{minor}(.{patch}).
      KUBERNETES_VERSION_UNRECOGNIZED: The server version of Kubernetes
        cluster is not recognized because the API server didn't return
        parsable version info on path/version.
      VERSION_VERIFICATION_FAILED: Failed to read or verify the version of
        Agones or Kubernetes. See version_installed_error_message for details.
      AGONES_NOT_INSTALLED: Agones is not installed.
    """
    INSTALLATION_STATE_UNSPECIFIED = 0
    AGONES_KUBERNETES_VERSION_SUPPORTED = 1
    AGONES_VERSION_UNSUPPORTED = 2
    AGONES_KUBERNETES_VERSION_UNSUPPORTED = 3
    AGONES_VERSION_UNRECOGNIZED = 4
    KUBERNETES_VERSION_UNRECOGNIZED = 5
    VERSION_VERIFICATION_FAILED = 6
    AGONES_NOT_INSTALLED = 7

  agonesVersionInstalled = _messages.StringField(1)
  agonesVersionTargeted = _messages.StringField(2)
  installationState = _messages.EnumField('InstallationStateValueValuesEnum', 3)
  kubernetesVersionInstalled = _messages.StringField(4)
  provider = _messages.StringField(5)
  versionInstalledErrorMessage = _messages.StringField(6)


class LabelSelector(_messages.Message):
  r"""The label selector, used to group labels on the resources.

  Messages:
    LabelsValue: Resource labels for this selector.

  Fields:
    labels: Resource labels for this selector.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels for this selector.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class ListGameServerClustersResponse(_messages.Message):
  r"""Response message for GameServerClustersService.ListGameServerClusters.

  Fields:
    gameServerClusters: The list of game server clusters.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: List of locations that could not be reached.
  """

  gameServerClusters = _messages.MessageField('GameServerCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGameServerConfigsResponse(_messages.Message):
  r"""Response message for GameServerConfigsService.ListGameServerConfigs.

  Fields:
    gameServerConfigs: The list of game server configs.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: List of locations that could not be reached.
  """

  gameServerConfigs = _messages.MessageField('GameServerConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGameServerDeploymentsResponse(_messages.Message):
  r"""Response message for
  GameServerDeploymentsService.ListGameServerDeployments.

  Fields:
    gameServerDeployments: The list of game server deployments.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: List of locations that could not be reached.
  """

  gameServerDeployments = _messages.MessageField('GameServerDeployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListRealmsResponse(_messages.Message):
  r"""Response message for RealmsService.ListRealms.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    realms: The list of realms.
    unreachable: List of locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  realms = _messages.MessageField('Realm', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents Google Cloud Platform location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('CloudAuditOptions', 1)
  counter = _messages.MessageField('CounterOptions', 2)
  dataAccess = _messages.MessageField('DataAccessOptions', 3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal response of the operation in case of success. If
      the original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal response of the operation in case of success. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal response of the operation in case of success. If the
    original method returns no data on success, such as `Delete`, the response
    is `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Messages:
    OperationStatusValue: Output only. Operation status for Game Services API
      operations. Operation status is in the form of key-value pairs where
      keys are resource IDs and the values show the status of the operation.
      In case of failures, the value includes an error code and error message.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    operationStatus: Output only. Operation status for Game Services API
      operations. Operation status is in the form of key-value pairs where
      keys are resource IDs and the values show the status of the operation.
      In case of failures, the value includes an error code and error message.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    unreachable: Output only. List of Locations that could not be reached.
    verb: Output only. Name of the verb executed by the operation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class OperationStatusValue(_messages.Message):
    r"""Output only. Operation status for Game Services API operations.
    Operation status is in the form of key-value pairs where keys are resource
    IDs and the values show the status of the operation. In case of failures,
    the value includes an error code and error message.

    Messages:
      AdditionalProperty: An additional property for a OperationStatusValue
        object.

    Fields:
      additionalProperties: Additional properties of type OperationStatusValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a OperationStatusValue object.

      Fields:
        key: Name of the additional property.
        value: A OperationStatus attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('OperationStatus', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  operationStatus = _messages.MessageField('OperationStatusValue', 4)
  requestedCancellation = _messages.BooleanField(5)
  statusMessage = _messages.StringField(6)
  target = _messages.StringField(7)
  unreachable = _messages.StringField(8, repeated=True)
  verb = _messages.StringField(9)


class OperationStatus(_messages.Message):
  r"""A OperationStatus object.

  Enums:
    ErrorCodeValueValuesEnum: The error code in case of failures.

  Fields:
    done: Output only. Whether the operation is done or still in progress.
    errorCode: The error code in case of failures.
    errorMessage: The human-readable error message.
  """

  class ErrorCodeValueValuesEnum(_messages.Enum):
    r"""The error code in case of failures.

    Values:
      ERROR_CODE_UNSPECIFIED: <no description>
      INTERNAL_ERROR: <no description>
      PERMISSION_DENIED: <no description>
      CLUSTER_CONNECTION: <no description>
    """
    ERROR_CODE_UNSPECIFIED = 0
    INTERNAL_ERROR = 1
    PERMISSION_DENIED = 2
    CLUSTER_CONNECTION = 3

  done = _messages.BooleanField(1)
  errorCode = _messages.EnumField('ErrorCodeValueValuesEnum', 2)
  errorMessage = _messages.StringField(3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members` to a single `role`.
  Members can be user accounts, service accounts, Google groups, and domains
  (such as G Suite). A `role` is a named list of permissions; each `role` can
  be an IAM predefined role or a user-created custom role. For some types of
  Google Cloud resources, a `binding` can also specify a `condition`, which is
  a logical expression that allows access to a resource only if the expression
  evaluates to `true`. A condition can add constraints based on attributes of
  the request, the resource, or both. To learn which resources support
  conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } **YAML example:** bindings: - members: -
  user:<EMAIL> - group:<EMAIL> - domain:google.com -
  serviceAccount:<EMAIL> role:
  roles/resourcemanager.organizationAdmin - members: - user:<EMAIL>
  role: roles/resourcemanager.organizationViewer condition: title: expirable
  access description: Does not grant access after Sep 2020 expression:
  request.time < timestamp('2020-10-01T00:00:00.000Z') - etag: BwWWja0YfJA= -
  version: 3 For a description of IAM and its features, see the [IAM
  documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members` to a `role`. Optionally, may
      specify a `condition` that determines how and when the `bindings` are
      applied. Each of the `bindings` must contain at least one member.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    iamOwned: A boolean attribute.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  iamOwned = _messages.BooleanField(4)
  rules = _messages.MessageField('Rule', 5, repeated=True)
  version = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class PreviewCreateGameServerClusterResponse(_messages.Message):
  r"""Response message for
  GameServerClustersService.PreviewCreateGameServerCluster.

  Fields:
    clusterState: Output only. The state of the Kubernetes cluster in preview,
      this will be available if 'view' is set to `FULL` in the relevant
      List/Get/Preview request.
    etag: The ETag of the game server cluster.
    targetState: The target state.
  """

  clusterState = _messages.MessageField('KubernetesClusterState', 1)
  etag = _messages.StringField(2)
  targetState = _messages.MessageField('TargetState', 3)


class PreviewDeleteGameServerClusterResponse(_messages.Message):
  r"""Response message for
  GameServerClustersService.PreviewDeleteGameServerCluster.

  Fields:
    etag: The ETag of the game server cluster.
    targetState: The target state.
  """

  etag = _messages.StringField(1)
  targetState = _messages.MessageField('TargetState', 2)


class PreviewGameServerDeploymentRolloutResponse(_messages.Message):
  r"""Response message for PreviewGameServerDeploymentRollout. This has
  details about the Agones fleet and autoscaler to be actuated.

  Fields:
    etag: ETag of the game server deployment.
    targetState: The target state.
    unavailable: Locations that could not be reached on this request.
  """

  etag = _messages.StringField(1)
  targetState = _messages.MessageField('TargetState', 2)
  unavailable = _messages.StringField(3, repeated=True)


class PreviewRealmUpdateResponse(_messages.Message):
  r"""Response message for RealmsService.PreviewRealmUpdate.

  Fields:
    etag: ETag of the realm.
    targetState: The target state.
  """

  etag = _messages.StringField(1)
  targetState = _messages.MessageField('TargetState', 2)


class PreviewUpdateGameServerClusterResponse(_messages.Message):
  r"""Response message for
  GameServerClustersService.PreviewUpdateGameServerCluster

  Fields:
    etag: The ETag of the game server cluster.
    targetState: The target state.
  """

  etag = _messages.StringField(1)
  targetState = _messages.MessageField('TargetState', 2)


class Realm(_messages.Message):
  r"""A realm resource.

  Messages:
    LabelsValue: The labels associated with this realm. Each label is a key-
      value pair.

  Fields:
    createTime: Output only. The creation time.
    description: Human readable description of the realm.
    etag: ETag of the resource.
    labels: The labels associated with this realm. Each label is a key-value
      pair.
    name: The resource name of the realm, in the following form:
      `projects/{project}/locations/{location}/realms/{realm}`. For example,
      `projects/my-project/locations/{location}/realms/my-realm`.
    timeZone: Required. Time zone where all policies targeting this realm are
      evaluated. The value of this field must be from the IANA time zone
      database: https://www.iana.org/time-zones.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this realm. Each label is a key-value pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  timeZone = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class RealmSelector(_messages.Message):
  r"""The realm selector, used to match realm resources.

  Fields:
    realms: List of realms to match.
  """

  realms = _messages.StringField(1, repeated=True)


class Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of tech.iam.IAM.CheckPolicy for
      any entries that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form '..' (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class ScalingConfig(_messages.Message):
  r"""Autoscaling config for an Agones fleet.

  Fields:
    fleetAutoscalerSpec: Required. Agones fleet autoscaler spec. Example spec:
      https://agones.dev/site/docs/reference/fleetautoscaler/
    name: Required. The name of the Scaling Config
    schedules: The schedules to which this Scaling Config applies.
    selectors: Labels used to identify the game server clusters to which this
      Agones scaling config applies. A game server cluster is subject to this
      Agones scaling config if its labels match any of the selector entries.
  """

  fleetAutoscalerSpec = _messages.StringField(1)
  name = _messages.StringField(2)
  schedules = _messages.MessageField('Schedule', 3, repeated=True)
  selectors = _messages.MessageField('LabelSelector', 4, repeated=True)


class Schedule(_messages.Message):
  r"""The schedule of a recurring or one time event. The event's time span is
  specified by start_time and end_time. If the scheduled event's timespan is
  larger than the cron_spec + cron_job_duration, the event will be recurring.
  If only cron_spec + cron_job_duration are specified, the event is effective
  starting at the local time specified by cron_spec, and is recurring.
  start_time|-------[cron job]-------[cron job]-------[cron job]---|end_time
  cron job: cron spec start time + duration

  Fields:
    cronJobDuration: The duration for the cron job event. The duration of the
      event is effective after the cron job's start time.
    cronSpec: The cron definition of the scheduled event. See
      https://en.wikipedia.org/wiki/Cron. Cron spec specifies the local time
      as defined by the realm.
    endTime: The end time of the event.
    startTime: The start time of the event.
  """

  cronJobDuration = _messages.StringField(1)
  cronSpec = _messages.StringField(2)
  endTime = _messages.StringField(3)
  startTime = _messages.StringField(4)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Cloud Platform services (such as Projects)
      might reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SpecSource(_messages.Message):
  r"""Encapsulates Agones fleet spec and Agones autoscaler spec sources.

  Fields:
    gameServerConfigName: The game server config resource. Uses the form: `pro
      jects/{project}/locations/{location}/gameServerDeployments/{deployment_i
      d}/configs/{config_id}`.
    name: The name of the Agones leet config or Agones scaling config used to
      derive the Agones fleet or Agones autoscaler spec.
  """

  gameServerConfigName = _messages.StringField(1)
  name = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TargetDetails(_messages.Message):
  r"""Details about the Agones resources.

  Fields:
    fleetDetails: Agones fleet details for game server clusters and game
      server deployments.
    gameServerClusterName: The game server cluster name. Uses the form: `proje
      cts/{project}/locations/{location}/realms/{realm}/gameServerClusters/{cl
      uster}`.
    gameServerDeploymentName: The game server deployment name. Uses the form:
      `projects/{project}/locations/{location}/gameServerDeployments/{deployme
      nt_id}`.
  """

  fleetDetails = _messages.MessageField('TargetFleetDetails', 1, repeated=True)
  gameServerClusterName = _messages.StringField(2)
  gameServerDeploymentName = _messages.StringField(3)


class TargetFleet(_messages.Message):
  r"""Target Agones fleet specification.

  Fields:
    name: The name of the Agones fleet.
    specSource: Encapsulates the source of the Agones fleet spec. The Agones
      fleet spec source.
  """

  name = _messages.StringField(1)
  specSource = _messages.MessageField('SpecSource', 2)


class TargetFleetAutoscaler(_messages.Message):
  r"""Target Agones autoscaler policy reference.

  Fields:
    name: The name of the Agones autoscaler.
    specSource: Encapsulates the source of the Agones fleet spec. Details
      about the Agones autoscaler spec.
  """

  name = _messages.StringField(1)
  specSource = _messages.MessageField('SpecSource', 2)


class TargetFleetDetails(_messages.Message):
  r"""Details of the target Agones fleet.

  Fields:
    autoscaler: Reference to target Agones fleet autoscaling policy.
    fleet: Reference to target Agones fleet.
  """

  autoscaler = _messages.MessageField('TargetFleetAutoscaler', 1)
  fleet = _messages.MessageField('TargetFleet', 2)


class TargetState(_messages.Message):
  r"""Encapsulates the Target state.

  Fields:
    details: Details about Agones fleets.
  """

  details = _messages.MessageField('TargetDetails', 1, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as '*' or 'storage.*') are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

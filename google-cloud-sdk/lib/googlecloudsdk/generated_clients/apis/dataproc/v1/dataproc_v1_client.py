"""Generated client library for dataproc version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.dataproc.v1 import dataproc_v1_messages as messages


class DataprocV1(base_api.BaseApiClient):
  """Generated client library for service dataproc version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://dataproc.googleapis.com/'
  MTLS_BASE_URL = 'https://dataproc.mtls.googleapis.com/'

  _PACKAGE = 'dataproc'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'DataprocV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new dataproc handle."""
    url = url or self.BASE_URL
    super(DataprocV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_autoscalingPolicies = self.ProjectsLocationsAutoscalingPoliciesService(self)
    self.projects_locations_batches = self.ProjectsLocationsBatchesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_sessionTemplates = self.ProjectsLocationsSessionTemplatesService(self)
    self.projects_locations_sessions = self.ProjectsLocationsSessionsService(self)
    self.projects_locations_workflowTemplates = self.ProjectsLocationsWorkflowTemplatesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_regions_autoscalingPolicies = self.ProjectsRegionsAutoscalingPoliciesService(self)
    self.projects_regions_clusters_nodeGroups = self.ProjectsRegionsClustersNodeGroupsService(self)
    self.projects_regions_clusters = self.ProjectsRegionsClustersService(self)
    self.projects_regions_jobs = self.ProjectsRegionsJobsService(self)
    self.projects_regions_operations = self.ProjectsRegionsOperationsService(self)
    self.projects_regions_workflowTemplates = self.ProjectsRegionsWorkflowTemplatesService(self)
    self.projects_regions = self.ProjectsRegionsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsAutoscalingPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_autoscalingPolicies resource."""

    _NAME = 'projects_locations_autoscalingPolicies'

    def __init__(self, client):
      super(DataprocV1.ProjectsLocationsAutoscalingPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates new autoscaling policy.

      Args:
        request: (DataprocProjectsLocationsAutoscalingPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutoscalingPolicy) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies',
        http_method='POST',
        method_id='dataproc.projects.locations.autoscalingPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/autoscalingPolicies',
        request_field='autoscalingPolicy',
        request_type_name='DataprocProjectsLocationsAutoscalingPoliciesCreateRequest',
        response_type_name='AutoscalingPolicy',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an autoscaling policy. It is an error to delete an autoscaling policy that is in use by one or more clusters.

      Args:
        request: (DataprocProjectsLocationsAutoscalingPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}',
        http_method='DELETE',
        method_id='dataproc.projects.locations.autoscalingPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsAutoscalingPoliciesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves autoscaling policy.

      Args:
        request: (DataprocProjectsLocationsAutoscalingPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutoscalingPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}',
        http_method='GET',
        method_id='dataproc.projects.locations.autoscalingPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsAutoscalingPoliciesGetRequest',
        response_type_name='AutoscalingPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (DataprocProjectsLocationsAutoscalingPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}:getIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.locations.autoscalingPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='DataprocProjectsLocationsAutoscalingPoliciesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists autoscaling policies in the project.

      Args:
        request: (DataprocProjectsLocationsAutoscalingPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutoscalingPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies',
        http_method='GET',
        method_id='dataproc.projects.locations.autoscalingPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/autoscalingPolicies',
        request_field='',
        request_type_name='DataprocProjectsLocationsAutoscalingPoliciesListRequest',
        response_type_name='ListAutoscalingPoliciesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (DataprocProjectsLocationsAutoscalingPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.locations.autoscalingPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='DataprocProjectsLocationsAutoscalingPoliciesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (DataprocProjectsLocationsAutoscalingPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='dataproc.projects.locations.autoscalingPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='DataprocProjectsLocationsAutoscalingPoliciesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates (replaces) autoscaling policy.Disabled check for update_mask, because all updates will be full replacements.

      Args:
        request: (AutoscalingPolicy) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutoscalingPolicy) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}',
        http_method='PUT',
        method_id='dataproc.projects.locations.autoscalingPolicies.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='AutoscalingPolicy',
        response_type_name='AutoscalingPolicy',
        supports_download=False,
    )

  class ProjectsLocationsBatchesService(base_api.BaseApiService):
    """Service class for the projects_locations_batches resource."""

    _NAME = 'projects_locations_batches'

    def __init__(self, client):
      super(DataprocV1.ProjectsLocationsBatchesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a batch workload that executes asynchronously.

      Args:
        request: (DataprocProjectsLocationsBatchesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batches',
        http_method='POST',
        method_id='dataproc.projects.locations.batches.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['batchId', 'requestId'],
        relative_path='v1/{+parent}/batches',
        request_field='batch',
        request_type_name='DataprocProjectsLocationsBatchesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the batch workload resource. If the batch is not in a CANCELLED, SUCCEEDED or FAILED State, the delete operation fails and the response returns FAILED_PRECONDITION.

      Args:
        request: (DataprocProjectsLocationsBatchesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}',
        http_method='DELETE',
        method_id='dataproc.projects.locations.batches.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsBatchesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the batch workload resource representation.

      Args:
        request: (DataprocProjectsLocationsBatchesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Batch) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}',
        http_method='GET',
        method_id='dataproc.projects.locations.batches.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsBatchesGetRequest',
        response_type_name='Batch',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists batch workloads.

      Args:
        request: (DataprocProjectsLocationsBatchesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBatchesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batches',
        http_method='GET',
        method_id='dataproc.projects.locations.batches.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/batches',
        request_field='',
        request_type_name='DataprocProjectsLocationsBatchesListRequest',
        response_type_name='ListBatchesResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(DataprocV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.

      Args:
        request: (DataprocProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='dataproc.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='',
        request_type_name='DataprocProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED.

      Args:
        request: (DataprocProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='dataproc.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (DataprocProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='dataproc.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.

      Args:
        request: (DataprocProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='dataproc.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSessionTemplatesService(base_api.BaseApiService):
    """Service class for the projects_locations_sessionTemplates resource."""

    _NAME = 'projects_locations_sessionTemplates'

    def __init__(self, client):
      super(DataprocV1.ProjectsLocationsSessionTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an session template, synchronously.

      Args:
        request: (DataprocProjectsLocationsSessionTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SessionTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates',
        http_method='POST',
        method_id='dataproc.projects.locations.sessionTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/sessionTemplates',
        request_field='sessionTemplate',
        request_type_name='DataprocProjectsLocationsSessionTemplatesCreateRequest',
        response_type_name='SessionTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a session template.

      Args:
        request: (DataprocProjectsLocationsSessionTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates/{sessionTemplatesId}',
        http_method='DELETE',
        method_id='dataproc.projects.locations.sessionTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsSessionTemplatesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the resource representation for a session template.

      Args:
        request: (DataprocProjectsLocationsSessionTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SessionTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates/{sessionTemplatesId}',
        http_method='GET',
        method_id='dataproc.projects.locations.sessionTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsSessionTemplatesGetRequest',
        response_type_name='SessionTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists session templates.

      Args:
        request: (DataprocProjectsLocationsSessionTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSessionTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates',
        http_method='GET',
        method_id='dataproc.projects.locations.sessionTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/sessionTemplates',
        request_field='',
        request_type_name='DataprocProjectsLocationsSessionTemplatesListRequest',
        response_type_name='ListSessionTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the session template, synchronously.Disable check for update_mask, because all updates will be full replacements.

      Args:
        request: (SessionTemplate) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SessionTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates/{sessionTemplatesId}',
        http_method='PATCH',
        method_id='dataproc.projects.locations.sessionTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='SessionTemplate',
        response_type_name='SessionTemplate',
        supports_download=False,
    )

  class ProjectsLocationsSessionsService(base_api.BaseApiService):
    """Service class for the projects_locations_sessions resource."""

    _NAME = 'projects_locations_sessions'

    def __init__(self, client):
      super(DataprocV1.ProjectsLocationsSessionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an interactive session asynchronously.

      Args:
        request: (DataprocProjectsLocationsSessionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessions',
        http_method='POST',
        method_id='dataproc.projects.locations.sessions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'sessionId'],
        relative_path='v1/{+parent}/sessions',
        request_field='session',
        request_type_name='DataprocProjectsLocationsSessionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the interactive session resource. If the session is not in terminal state, it will be terminated and deleted afterwards.

      Args:
        request: (DataprocProjectsLocationsSessionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}',
        http_method='DELETE',
        method_id='dataproc.projects.locations.sessions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsSessionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the resource representation for an interactive session.

      Args:
        request: (DataprocProjectsLocationsSessionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Session) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}',
        http_method='GET',
        method_id='dataproc.projects.locations.sessions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsSessionsGetRequest',
        response_type_name='Session',
        supports_download=False,
    )

    def InjectCredentials(self, request, global_params=None):
      r"""Inject Credentials in the interactive session.

      Args:
        request: (DataprocProjectsLocationsSessionsInjectCredentialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('InjectCredentials')
      return self._RunMethod(
          config, request, global_params=global_params)

    InjectCredentials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}:injectCredentials',
        http_method='POST',
        method_id='dataproc.projects.locations.sessions.injectCredentials',
        ordered_params=['session'],
        path_params=['session'],
        query_params=[],
        relative_path='v1/{+session}:injectCredentials',
        request_field='injectSessionCredentialsRequest',
        request_type_name='DataprocProjectsLocationsSessionsInjectCredentialsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists interactive sessions.

      Args:
        request: (DataprocProjectsLocationsSessionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSessionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessions',
        http_method='GET',
        method_id='dataproc.projects.locations.sessions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/sessions',
        request_field='',
        request_type_name='DataprocProjectsLocationsSessionsListRequest',
        response_type_name='ListSessionsResponse',
        supports_download=False,
    )

    def Terminate(self, request, global_params=None):
      r"""Terminates the interactive session.

      Args:
        request: (DataprocProjectsLocationsSessionsTerminateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Terminate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Terminate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}:terminate',
        http_method='POST',
        method_id='dataproc.projects.locations.sessions.terminate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:terminate',
        request_field='terminateSessionRequest',
        request_type_name='DataprocProjectsLocationsSessionsTerminateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsWorkflowTemplatesService(base_api.BaseApiService):
    """Service class for the projects_locations_workflowTemplates resource."""

    _NAME = 'projects_locations_workflowTemplates'

    def __init__(self, client):
      super(DataprocV1.ProjectsLocationsWorkflowTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates new workflow template.

      Args:
        request: (DataprocProjectsLocationsWorkflowTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkflowTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates',
        http_method='POST',
        method_id='dataproc.projects.locations.workflowTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/workflowTemplates',
        request_field='workflowTemplate',
        request_type_name='DataprocProjectsLocationsWorkflowTemplatesCreateRequest',
        response_type_name='WorkflowTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a workflow template. It does not cancel in-progress workflows.

      Args:
        request: (DataprocProjectsLocationsWorkflowTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}',
        http_method='DELETE',
        method_id='dataproc.projects.locations.workflowTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['version'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsWorkflowTemplatesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the latest workflow template.Can retrieve previously instantiated template by specifying optional version parameter.

      Args:
        request: (DataprocProjectsLocationsWorkflowTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkflowTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}',
        http_method='GET',
        method_id='dataproc.projects.locations.workflowTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['version'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsLocationsWorkflowTemplatesGetRequest',
        response_type_name='WorkflowTemplate',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (DataprocProjectsLocationsWorkflowTemplatesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}:getIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.locations.workflowTemplates.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='DataprocProjectsLocationsWorkflowTemplatesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Instantiate(self, request, global_params=None):
      r"""Instantiates a template and begins execution.The returned Operation can be used to track execution of workflow by polling operations.get. The Operation will complete when entire workflow is finished.The running workflow can be aborted via operations.cancel. This will cause any inflight jobs to be cancelled and workflow-owned clusters to be deleted.The Operation.metadata will be WorkflowMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#workflowmetadata). Also see Using WorkflowMetadata (https://cloud.google.com/dataproc/docs/concepts/workflows/debugging#using_workflowmetadata).On successful completion, Operation.response will be Empty.

      Args:
        request: (DataprocProjectsLocationsWorkflowTemplatesInstantiateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Instantiate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Instantiate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}:instantiate',
        http_method='POST',
        method_id='dataproc.projects.locations.workflowTemplates.instantiate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:instantiate',
        request_field='instantiateWorkflowTemplateRequest',
        request_type_name='DataprocProjectsLocationsWorkflowTemplatesInstantiateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def InstantiateInline(self, request, global_params=None):
      r"""Instantiates a template and begins execution.This method is equivalent to executing the sequence CreateWorkflowTemplate, InstantiateWorkflowTemplate, DeleteWorkflowTemplate.The returned Operation can be used to track execution of workflow by polling operations.get. The Operation will complete when entire workflow is finished.The running workflow can be aborted via operations.cancel. This will cause any inflight jobs to be cancelled and workflow-owned clusters to be deleted.The Operation.metadata will be WorkflowMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#workflowmetadata). Also see Using WorkflowMetadata (https://cloud.google.com/dataproc/docs/concepts/workflows/debugging#using_workflowmetadata).On successful completion, Operation.response will be Empty.

      Args:
        request: (DataprocProjectsLocationsWorkflowTemplatesInstantiateInlineRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('InstantiateInline')
      return self._RunMethod(
          config, request, global_params=global_params)

    InstantiateInline.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates:instantiateInline',
        http_method='POST',
        method_id='dataproc.projects.locations.workflowTemplates.instantiateInline',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId'],
        relative_path='v1/{+parent}/workflowTemplates:instantiateInline',
        request_field='workflowTemplate',
        request_type_name='DataprocProjectsLocationsWorkflowTemplatesInstantiateInlineRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists workflows that match the specified filter in the request.

      Args:
        request: (DataprocProjectsLocationsWorkflowTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkflowTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates',
        http_method='GET',
        method_id='dataproc.projects.locations.workflowTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/workflowTemplates',
        request_field='',
        request_type_name='DataprocProjectsLocationsWorkflowTemplatesListRequest',
        response_type_name='ListWorkflowTemplatesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (DataprocProjectsLocationsWorkflowTemplatesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}:setIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.locations.workflowTemplates.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='DataprocProjectsLocationsWorkflowTemplatesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (DataprocProjectsLocationsWorkflowTemplatesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}:testIamPermissions',
        http_method='POST',
        method_id='dataproc.projects.locations.workflowTemplates.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='DataprocProjectsLocationsWorkflowTemplatesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates (replaces) workflow template. The updated template must contain version that matches the current server version.

      Args:
        request: (WorkflowTemplate) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkflowTemplate) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}',
        http_method='PUT',
        method_id='dataproc.projects.locations.workflowTemplates.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='WorkflowTemplate',
        response_type_name='WorkflowTemplate',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(DataprocV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsRegionsAutoscalingPoliciesService(base_api.BaseApiService):
    """Service class for the projects_regions_autoscalingPolicies resource."""

    _NAME = 'projects_regions_autoscalingPolicies'

    def __init__(self, client):
      super(DataprocV1.ProjectsRegionsAutoscalingPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates new autoscaling policy.

      Args:
        request: (DataprocProjectsRegionsAutoscalingPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutoscalingPolicy) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies',
        http_method='POST',
        method_id='dataproc.projects.regions.autoscalingPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/autoscalingPolicies',
        request_field='autoscalingPolicy',
        request_type_name='DataprocProjectsRegionsAutoscalingPoliciesCreateRequest',
        response_type_name='AutoscalingPolicy',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an autoscaling policy. It is an error to delete an autoscaling policy that is in use by one or more clusters.

      Args:
        request: (DataprocProjectsRegionsAutoscalingPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}',
        http_method='DELETE',
        method_id='dataproc.projects.regions.autoscalingPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsRegionsAutoscalingPoliciesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves autoscaling policy.

      Args:
        request: (DataprocProjectsRegionsAutoscalingPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutoscalingPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}',
        http_method='GET',
        method_id='dataproc.projects.regions.autoscalingPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsRegionsAutoscalingPoliciesGetRequest',
        response_type_name='AutoscalingPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (DataprocProjectsRegionsAutoscalingPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}:getIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.autoscalingPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsAutoscalingPoliciesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists autoscaling policies in the project.

      Args:
        request: (DataprocProjectsRegionsAutoscalingPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAutoscalingPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies',
        http_method='GET',
        method_id='dataproc.projects.regions.autoscalingPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/autoscalingPolicies',
        request_field='',
        request_type_name='DataprocProjectsRegionsAutoscalingPoliciesListRequest',
        response_type_name='ListAutoscalingPoliciesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (DataprocProjectsRegionsAutoscalingPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.autoscalingPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsAutoscalingPoliciesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (DataprocProjectsRegionsAutoscalingPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='dataproc.projects.regions.autoscalingPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='DataprocProjectsRegionsAutoscalingPoliciesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates (replaces) autoscaling policy.Disabled check for update_mask, because all updates will be full replacements.

      Args:
        request: (AutoscalingPolicy) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutoscalingPolicy) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}',
        http_method='PUT',
        method_id='dataproc.projects.regions.autoscalingPolicies.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='AutoscalingPolicy',
        response_type_name='AutoscalingPolicy',
        supports_download=False,
    )

  class ProjectsRegionsClustersNodeGroupsService(base_api.BaseApiService):
    """Service class for the projects_regions_clusters_nodeGroups resource."""

    _NAME = 'projects_regions_clusters_nodeGroups'

    def __init__(self, client):
      super(DataprocV1.ProjectsRegionsClustersNodeGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a node group in a cluster. The returned Operation.metadata is NodeGroupOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#nodegroupoperationmetadata).

      Args:
        request: (DataprocProjectsRegionsClustersNodeGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}/nodeGroups',
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.nodeGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['nodeGroupId', 'requestId'],
        relative_path='v1/{+parent}/nodeGroups',
        request_field='nodeGroup',
        request_type_name='DataprocProjectsRegionsClustersNodeGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a node group in a cluster. The returned Operation.metadata is NodeGroupOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#nodegroupoperationmetadata).

      Args:
        request: (DataprocProjectsRegionsClustersNodeGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}/nodeGroups/{nodeGroupsId}',
        http_method='DELETE',
        method_id='dataproc.projects.regions.clusters.nodeGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsRegionsClustersNodeGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the resource representation for a node group in a cluster.

      Args:
        request: (DataprocProjectsRegionsClustersNodeGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NodeGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}/nodeGroups/{nodeGroupsId}',
        http_method='GET',
        method_id='dataproc.projects.regions.clusters.nodeGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsRegionsClustersNodeGroupsGetRequest',
        response_type_name='NodeGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all node groups in a cluster.

      Args:
        request: (DataprocProjectsRegionsClustersNodeGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNodeGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}/nodeGroups',
        http_method='GET',
        method_id='dataproc.projects.regions.clusters.nodeGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/nodeGroups',
        request_field='',
        request_type_name='DataprocProjectsRegionsClustersNodeGroupsListRequest',
        response_type_name='ListNodeGroupsResponse',
        supports_download=False,
    )

    def Resize(self, request, global_params=None):
      r"""Resizes a node group in a cluster. The returned Operation.metadata is NodeGroupOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#nodegroupoperationmetadata).

      Args:
        request: (DataprocProjectsRegionsClustersNodeGroupsResizeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Resize')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resize.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}/nodeGroups/{nodeGroupsId}:resize',
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.nodeGroups.resize',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:resize',
        request_field='resizeNodeGroupRequest',
        request_type_name='DataprocProjectsRegionsClustersNodeGroupsResizeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsRegionsClustersService(base_api.BaseApiService):
    """Service class for the projects_regions_clusters resource."""

    _NAME = 'projects_regions_clusters'

    def __init__(self, client):
      super(DataprocV1.ProjectsRegionsClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a cluster in a project. The returned Operation.metadata will be ClusterOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#clusteroperationmetadata).

      Args:
        request: (DataprocProjectsRegionsClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.create',
        ordered_params=['projectId', 'region'],
        path_params=['projectId', 'region'],
        query_params=['actionOnFailedPrimaryWorkers', 'requestId'],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters',
        request_field='cluster',
        request_type_name='DataprocProjectsRegionsClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a cluster in a project. The returned Operation.metadata will be ClusterOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#clusteroperationmetadata).

      Args:
        request: (DataprocProjectsRegionsClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='dataproc.projects.regions.clusters.delete',
        ordered_params=['projectId', 'region', 'clusterName'],
        path_params=['clusterName', 'projectId', 'region'],
        query_params=['clusterUuid', 'gracefulTerminationTimeout', 'requestId'],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters/{clusterName}',
        request_field='',
        request_type_name='DataprocProjectsRegionsClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Diagnose(self, request, global_params=None):
      r"""Gets cluster diagnostic information. The returned Operation.metadata will be ClusterOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#clusteroperationmetadata). After the operation completes, Operation.response contains DiagnoseClusterResults (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#diagnoseclusterresults).

      Args:
        request: (DataprocProjectsRegionsClustersDiagnoseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Diagnose')
      return self._RunMethod(
          config, request, global_params=global_params)

    Diagnose.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.diagnose',
        ordered_params=['projectId', 'region', 'clusterName'],
        path_params=['clusterName', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:diagnose',
        request_field='diagnoseClusterRequest',
        request_type_name='DataprocProjectsRegionsClustersDiagnoseRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the resource representation for a cluster in a project.

      Args:
        request: (DataprocProjectsRegionsClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Cluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dataproc.projects.regions.clusters.get',
        ordered_params=['projectId', 'region', 'clusterName'],
        path_params=['clusterName', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters/{clusterName}',
        request_field='',
        request_type_name='DataprocProjectsRegionsClustersGetRequest',
        response_type_name='Cluster',
        supports_download=False,
    )

    def GetClusterAsTemplate(self, request, global_params=None):
      r"""Exports a template for a cluster in a project that can be used in future CreateCluster requests.

      Args:
        request: (DataprocProjectsRegionsClustersGetClusterAsTemplateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Cluster) The response message.
      """
      config = self.GetMethodConfig('GetClusterAsTemplate')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetClusterAsTemplate.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dataproc.projects.regions.clusters.getClusterAsTemplate',
        ordered_params=['projectId', 'region', 'clusterName'],
        path_params=['clusterName', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:getClusterAsTemplate',
        request_field='',
        request_type_name='DataprocProjectsRegionsClustersGetClusterAsTemplateRequest',
        response_type_name='Cluster',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (DataprocProjectsRegionsClustersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}:getIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsClustersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def InjectCredentials(self, request, global_params=None):
      r"""Inject encrypted credentials into all of the VMs in a cluster.The target cluster must be a personal auth cluster assigned to the user who is issuing the RPC.

      Args:
        request: (DataprocProjectsRegionsClustersInjectCredentialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('InjectCredentials')
      return self._RunMethod(
          config, request, global_params=global_params)

    InjectCredentials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}:injectCredentials',
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.injectCredentials',
        ordered_params=['project', 'region', 'cluster'],
        path_params=['cluster', 'project', 'region'],
        query_params=[],
        relative_path='v1/{+project}/{+region}/{+cluster}:injectCredentials',
        request_field='injectCredentialsRequest',
        request_type_name='DataprocProjectsRegionsClustersInjectCredentialsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all regions/{region}/clusters in a project alphabetically.

      Args:
        request: (DataprocProjectsRegionsClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dataproc.projects.regions.clusters.list',
        ordered_params=['projectId', 'region'],
        path_params=['projectId', 'region'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters',
        request_field='',
        request_type_name='DataprocProjectsRegionsClustersListRequest',
        response_type_name='ListClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a cluster in a project. The returned Operation.metadata will be ClusterOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#clusteroperationmetadata). The cluster must be in a RUNNING state or an error is returned.

      Args:
        request: (DataprocProjectsRegionsClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='dataproc.projects.regions.clusters.patch',
        ordered_params=['projectId', 'region', 'clusterName'],
        path_params=['clusterName', 'projectId', 'region'],
        query_params=['gracefulDecommissionTimeout', 'requestId', 'updateMask'],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters/{clusterName}',
        request_field='cluster',
        request_type_name='DataprocProjectsRegionsClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Repair(self, request, global_params=None):
      r"""Repairs a cluster.

      Args:
        request: (DataprocProjectsRegionsClustersRepairRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Repair')
      return self._RunMethod(
          config, request, global_params=global_params)

    Repair.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.repair',
        ordered_params=['projectId', 'region', 'clusterName'],
        path_params=['clusterName', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:repair',
        request_field='repairClusterRequest',
        request_type_name='DataprocProjectsRegionsClustersRepairRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (DataprocProjectsRegionsClustersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}:setIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsClustersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Start(self, request, global_params=None):
      r"""Starts a cluster in a project.

      Args:
        request: (DataprocProjectsRegionsClustersStartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Start')
      return self._RunMethod(
          config, request, global_params=global_params)

    Start.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.start',
        ordered_params=['projectId', 'region', 'clusterName'],
        path_params=['clusterName', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:start',
        request_field='startClusterRequest',
        request_type_name='DataprocProjectsRegionsClustersStartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops a cluster in a project.

      Args:
        request: (DataprocProjectsRegionsClustersStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.stop',
        ordered_params=['projectId', 'region', 'clusterName'],
        path_params=['clusterName', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:stop',
        request_field='stopClusterRequest',
        request_type_name='DataprocProjectsRegionsClustersStopRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (DataprocProjectsRegionsClustersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}:testIamPermissions',
        http_method='POST',
        method_id='dataproc.projects.regions.clusters.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='DataprocProjectsRegionsClustersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsRegionsJobsService(base_api.BaseApiService):
    """Service class for the projects_regions_jobs resource."""

    _NAME = 'projects_regions_jobs'

    def __init__(self, client):
      super(DataprocV1.ProjectsRegionsJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts a job cancellation request. To access the job resource after cancellation, call regions/{region}/jobs.list (https://cloud.google.com/dataproc/docs/reference/rest/v1/projects.regions.jobs/list) or regions/{region}/jobs.get (https://cloud.google.com/dataproc/docs/reference/rest/v1/projects.regions.jobs/get).

      Args:
        request: (DataprocProjectsRegionsJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dataproc.projects.regions.jobs.cancel',
        ordered_params=['projectId', 'region', 'jobId'],
        path_params=['jobId', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/jobs/{jobId}:cancel',
        request_field='cancelJobRequest',
        request_type_name='DataprocProjectsRegionsJobsCancelRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the job from the project. If the job is active, the delete fails, and the response returns FAILED_PRECONDITION.

      Args:
        request: (DataprocProjectsRegionsJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='dataproc.projects.regions.jobs.delete',
        ordered_params=['projectId', 'region', 'jobId'],
        path_params=['jobId', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/jobs/{jobId}',
        request_field='',
        request_type_name='DataprocProjectsRegionsJobsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the resource representation for a job in a project.

      Args:
        request: (DataprocProjectsRegionsJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dataproc.projects.regions.jobs.get',
        ordered_params=['projectId', 'region', 'jobId'],
        path_params=['jobId', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/jobs/{jobId}',
        request_field='',
        request_type_name='DataprocProjectsRegionsJobsGetRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (DataprocProjectsRegionsJobsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/jobs/{jobsId}:getIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.jobs.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsJobsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def GetJobAsTemplate(self, request, global_params=None):
      r"""Exports the resource representation for a job in a project as a template that can be used as a SubmitJobRequest.

      Args:
        request: (DataprocProjectsRegionsJobsGetJobAsTemplateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('GetJobAsTemplate')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetJobAsTemplate.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dataproc.projects.regions.jobs.getJobAsTemplate',
        ordered_params=['projectId', 'region', 'jobId'],
        path_params=['jobId', 'projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/jobs/{jobId}:getJobAsTemplate',
        request_field='',
        request_type_name='DataprocProjectsRegionsJobsGetJobAsTemplateRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists regions/{region}/jobs in a project.

      Args:
        request: (DataprocProjectsRegionsJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dataproc.projects.regions.jobs.list',
        ordered_params=['projectId', 'region'],
        path_params=['projectId', 'region'],
        query_params=['clusterName', 'filter', 'jobStateMatcher', 'pageSize', 'pageToken'],
        relative_path='v1/projects/{projectId}/regions/{region}/jobs',
        request_field='',
        request_type_name='DataprocProjectsRegionsJobsListRequest',
        response_type_name='ListJobsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a job in a project.

      Args:
        request: (DataprocProjectsRegionsJobsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='dataproc.projects.regions.jobs.patch',
        ordered_params=['projectId', 'region', 'jobId'],
        path_params=['jobId', 'projectId', 'region'],
        query_params=['updateMask'],
        relative_path='v1/projects/{projectId}/regions/{region}/jobs/{jobId}',
        request_field='job',
        request_type_name='DataprocProjectsRegionsJobsPatchRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (DataprocProjectsRegionsJobsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/jobs/{jobsId}:setIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.jobs.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsJobsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Submit(self, request, global_params=None):
      r"""Submits a job to a cluster.

      Args:
        request: (DataprocProjectsRegionsJobsSubmitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Submit')
      return self._RunMethod(
          config, request, global_params=global_params)

    Submit.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dataproc.projects.regions.jobs.submit',
        ordered_params=['projectId', 'region'],
        path_params=['projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/jobs:submit',
        request_field='submitJobRequest',
        request_type_name='DataprocProjectsRegionsJobsSubmitRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def SubmitAsOperation(self, request, global_params=None):
      r"""Submits job to a cluster.

      Args:
        request: (DataprocProjectsRegionsJobsSubmitAsOperationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('SubmitAsOperation')
      return self._RunMethod(
          config, request, global_params=global_params)

    SubmitAsOperation.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dataproc.projects.regions.jobs.submitAsOperation',
        ordered_params=['projectId', 'region'],
        path_params=['projectId', 'region'],
        query_params=[],
        relative_path='v1/projects/{projectId}/regions/{region}/jobs:submitAsOperation',
        request_field='submitJobRequest',
        request_type_name='DataprocProjectsRegionsJobsSubmitAsOperationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (DataprocProjectsRegionsJobsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/jobs/{jobsId}:testIamPermissions',
        http_method='POST',
        method_id='dataproc.projects.regions.jobs.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='DataprocProjectsRegionsJobsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsRegionsOperationsService(base_api.BaseApiService):
    """Service class for the projects_regions_operations resource."""

    _NAME = 'projects_regions_operations'

    def __init__(self, client):
      super(DataprocV1.ProjectsRegionsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.

      Args:
        request: (DataprocProjectsRegionsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='dataproc.projects.regions.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='',
        request_type_name='DataprocProjectsRegionsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED.

      Args:
        request: (DataprocProjectsRegionsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='dataproc.projects.regions.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsRegionsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (DataprocProjectsRegionsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}',
        http_method='GET',
        method_id='dataproc.projects.regions.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsRegionsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (DataprocProjectsRegionsOperationsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}:getIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.operations.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsOperationsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.

      Args:
        request: (DataprocProjectsRegionsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/operations',
        http_method='GET',
        method_id='dataproc.projects.regions.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsRegionsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (DataprocProjectsRegionsOperationsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}:setIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.operations.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsOperationsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (DataprocProjectsRegionsOperationsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}:testIamPermissions',
        http_method='POST',
        method_id='dataproc.projects.regions.operations.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='DataprocProjectsRegionsOperationsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsRegionsWorkflowTemplatesService(base_api.BaseApiService):
    """Service class for the projects_regions_workflowTemplates resource."""

    _NAME = 'projects_regions_workflowTemplates'

    def __init__(self, client):
      super(DataprocV1.ProjectsRegionsWorkflowTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates new workflow template.

      Args:
        request: (DataprocProjectsRegionsWorkflowTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkflowTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates',
        http_method='POST',
        method_id='dataproc.projects.regions.workflowTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/workflowTemplates',
        request_field='workflowTemplate',
        request_type_name='DataprocProjectsRegionsWorkflowTemplatesCreateRequest',
        response_type_name='WorkflowTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a workflow template. It does not cancel in-progress workflows.

      Args:
        request: (DataprocProjectsRegionsWorkflowTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}',
        http_method='DELETE',
        method_id='dataproc.projects.regions.workflowTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['version'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsRegionsWorkflowTemplatesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the latest workflow template.Can retrieve previously instantiated template by specifying optional version parameter.

      Args:
        request: (DataprocProjectsRegionsWorkflowTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkflowTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}',
        http_method='GET',
        method_id='dataproc.projects.regions.workflowTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['version'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocProjectsRegionsWorkflowTemplatesGetRequest',
        response_type_name='WorkflowTemplate',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (DataprocProjectsRegionsWorkflowTemplatesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}:getIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.workflowTemplates.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsWorkflowTemplatesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Instantiate(self, request, global_params=None):
      r"""Instantiates a template and begins execution.The returned Operation can be used to track execution of workflow by polling operations.get. The Operation will complete when entire workflow is finished.The running workflow can be aborted via operations.cancel. This will cause any inflight jobs to be cancelled and workflow-owned clusters to be deleted.The Operation.metadata will be WorkflowMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#workflowmetadata). Also see Using WorkflowMetadata (https://cloud.google.com/dataproc/docs/concepts/workflows/debugging#using_workflowmetadata).On successful completion, Operation.response will be Empty.

      Args:
        request: (DataprocProjectsRegionsWorkflowTemplatesInstantiateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Instantiate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Instantiate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}:instantiate',
        http_method='POST',
        method_id='dataproc.projects.regions.workflowTemplates.instantiate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:instantiate',
        request_field='instantiateWorkflowTemplateRequest',
        request_type_name='DataprocProjectsRegionsWorkflowTemplatesInstantiateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def InstantiateInline(self, request, global_params=None):
      r"""Instantiates a template and begins execution.This method is equivalent to executing the sequence CreateWorkflowTemplate, InstantiateWorkflowTemplate, DeleteWorkflowTemplate.The returned Operation can be used to track execution of workflow by polling operations.get. The Operation will complete when entire workflow is finished.The running workflow can be aborted via operations.cancel. This will cause any inflight jobs to be cancelled and workflow-owned clusters to be deleted.The Operation.metadata will be WorkflowMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#workflowmetadata). Also see Using WorkflowMetadata (https://cloud.google.com/dataproc/docs/concepts/workflows/debugging#using_workflowmetadata).On successful completion, Operation.response will be Empty.

      Args:
        request: (DataprocProjectsRegionsWorkflowTemplatesInstantiateInlineRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('InstantiateInline')
      return self._RunMethod(
          config, request, global_params=global_params)

    InstantiateInline.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates:instantiateInline',
        http_method='POST',
        method_id='dataproc.projects.regions.workflowTemplates.instantiateInline',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId'],
        relative_path='v1/{+parent}/workflowTemplates:instantiateInline',
        request_field='workflowTemplate',
        request_type_name='DataprocProjectsRegionsWorkflowTemplatesInstantiateInlineRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists workflows that match the specified filter in the request.

      Args:
        request: (DataprocProjectsRegionsWorkflowTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkflowTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates',
        http_method='GET',
        method_id='dataproc.projects.regions.workflowTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/workflowTemplates',
        request_field='',
        request_type_name='DataprocProjectsRegionsWorkflowTemplatesListRequest',
        response_type_name='ListWorkflowTemplatesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.

      Args:
        request: (DataprocProjectsRegionsWorkflowTemplatesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}:setIamPolicy',
        http_method='POST',
        method_id='dataproc.projects.regions.workflowTemplates.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='DataprocProjectsRegionsWorkflowTemplatesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (DataprocProjectsRegionsWorkflowTemplatesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}:testIamPermissions',
        http_method='POST',
        method_id='dataproc.projects.regions.workflowTemplates.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='DataprocProjectsRegionsWorkflowTemplatesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates (replaces) workflow template. The updated template must contain version that matches the current server version.

      Args:
        request: (WorkflowTemplate) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkflowTemplate) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}',
        http_method='PUT',
        method_id='dataproc.projects.regions.workflowTemplates.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='WorkflowTemplate',
        response_type_name='WorkflowTemplate',
        supports_download=False,
    )

  class ProjectsRegionsService(base_api.BaseApiService):
    """Service class for the projects_regions resource."""

    _NAME = 'projects_regions'

    def __init__(self, client):
      super(DataprocV1.ProjectsRegionsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(DataprocV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

"""Generated message classes for assuredworkloads version v1beta1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'assuredworkloads'


class AssuredworkloadsOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AssuredworkloadsOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class AssuredworkloadsOrganizationsLocationsWorkloadsCreateRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsWorkloadsCreateRequest object.

  Fields:
    externalId: Optional. A identifier associated with the workload and
      underlying projects which allows for the break down of billing costs for
      a workload. The value provided for the identifier will add a label to
      the workload and contained projects with the identifier as the value.
    googleCloudAssuredworkloadsV1beta1Workload: A
      GoogleCloudAssuredworkloadsV1beta1Workload resource to be passed as the
      request body.
    parent: Required. The resource name of the new Workload's parent. Must be
      of the form `organizations/{org_id}/locations/{location_id}`.
  """

  externalId = _messages.StringField(1)
  googleCloudAssuredworkloadsV1beta1Workload = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1Workload', 2)
  parent = _messages.StringField(3, required=True)


class AssuredworkloadsOrganizationsLocationsWorkloadsDeleteRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsWorkloadsDeleteRequest object.

  Fields:
    etag: Optional. The etag of the workload. If this is provided, it must
      match the server's etag.
    name: Required. The `name` field is used to identify the workload. Format:
      organizations/{org_id}/locations/{location_id}/workloads/{workload_id}
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class AssuredworkloadsOrganizationsLocationsWorkloadsEnableResourceMonitoringRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsWorkloadsEnableResourceMonitorin
  gRequest object.

  Fields:
    name: Required. The `name` field is used to identify the workload. Format:
      organizations/{org_id}/locations/{location_id}/workloads/{workload_id}
  """

  name = _messages.StringField(1, required=True)


class AssuredworkloadsOrganizationsLocationsWorkloadsGetRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsWorkloadsGetRequest object.

  Fields:
    name: Required. The resource name of the Workload to fetch. This is the
      workloads's relative path in the API, formatted as "organizations/{organ
      ization_id}/locations/{location_id}/workloads/{workload_id}". For
      example, "organizations/123/locations/us-east1/workloads/assured-
      workload-1".
  """

  name = _messages.StringField(1, required=True)


class AssuredworkloadsOrganizationsLocationsWorkloadsListRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsWorkloadsListRequest object.

  Fields:
    filter: A custom filter for filtering by properties of a workload. At this
      time, only filtering by labels is supported.
    pageSize: Page size.
    pageToken: Page token returned from previous request. Page token contains
      context from previous request. Page token needs to be passed in the
      second and following requests.
    parent: Required. Parent Resource to list workloads from. Must be of the
      form `organizations/{org_id}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class AssuredworkloadsOrganizationsLocationsWorkloadsPatchRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsWorkloadsPatchRequest object.

  Fields:
    googleCloudAssuredworkloadsV1beta1Workload: A
      GoogleCloudAssuredworkloadsV1beta1Workload resource to be passed as the
      request body.
    name: Optional. The resource name of the workload. Format:
      organizations/{organization}/locations/{location}/workloads/{workload}
      Read-only.
    updateMask: Required. The list of fields to be updated.
  """

  googleCloudAssuredworkloadsV1beta1Workload = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1Workload', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class AssuredworkloadsOrganizationsLocationsWorkloadsRestrictAllowedResourcesRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsWorkloadsRestrictAllowedResource
  sRequest object.

  Fields:
    googleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesRequest: A
      GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesRequest
      resource to be passed as the request body.
    name: Required. The resource name of the Workload. This is the workloads's
      relative path in the API, formatted as "organizations/{organization_id}/
      locations/{location_id}/workloads/{workload_id}". For example,
      "organizations/123/locations/us-east1/workloads/assured-workload-1".
  """

  googleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesRequest = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesRequest', 1)
  name = _messages.StringField(2, required=True)


class AssuredworkloadsOrganizationsLocationsWorkloadsViolationsAcknowledgeRequest(_messages.Message):
  r"""A
  AssuredworkloadsOrganizationsLocationsWorkloadsViolationsAcknowledgeRequest
  object.

  Fields:
    googleCloudAssuredworkloadsV1beta1AcknowledgeViolationRequest: A
      GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationRequest resource
      to be passed as the request body.
    name: Required. The resource name of the Violation to acknowledge. Format:
      organizations/{organization}/locations/{location}/workloads/{workload}/v
      iolations/{violation}
  """

  googleCloudAssuredworkloadsV1beta1AcknowledgeViolationRequest = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationRequest', 1)
  name = _messages.StringField(2, required=True)


class AssuredworkloadsOrganizationsLocationsWorkloadsViolationsGetRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsWorkloadsViolationsGetRequest
  object.

  Fields:
    name: Required. The resource name of the Violation to fetch (ie.
      Violation.name). Format: organizations/{organization}/locations/{locatio
      n}/workloads/{workload}/violations/{violation}
  """

  name = _messages.StringField(1, required=True)


class AssuredworkloadsOrganizationsLocationsWorkloadsViolationsListRequest(_messages.Message):
  r"""A AssuredworkloadsOrganizationsLocationsWorkloadsViolationsListRequest
  object.

  Fields:
    filter: Optional. A custom filter for filtering by the Violations
      properties.
    interval_endTime: The end of the time window.
    interval_startTime: The start of the time window.
    pageSize: Optional. Page size.
    pageToken: Optional. Page token returned from previous request.
    parent: Required. The Workload name. Format
      `organizations/{org_id}/locations/{location}/workloads/{workload}`.
  """

  filter = _messages.StringField(1)
  interval_endTime = _messages.StringField(2)
  interval_startTime = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)


class AssuredworkloadsProjectsOrganizationsLocationsWorkloadsAnalyzeWorkloadMoveRequest(_messages.Message):
  r"""A AssuredworkloadsProjectsOrganizationsLocationsWorkloadsAnalyzeWorkload
  MoveRequest object.

  Fields:
    analyzeChildAssets: Optional. Indicates if all child assets of the source
      resource should also be analyzed in addition to the source.
    pageSize: Optional. Page size. If a value is not specified, the default
      value of 10 is used.
    pageToken: Optional. The page token from the previous response. It needs
      to be passed in the second and following requests.
    project: The source type is a project. Specify the project's relative
      resource name, formatted as either a project number or a project ID:
      "projects/{PROJECT_NUMBER}" or "projects/{PROJECT_ID}" For example:
      "projects/951040570662" when specifying a project number, or
      "projects/my-project-123" when specifying a project ID.
    source: The source type is a project-based workload. Specify the
      workloads's relative resource name, formatted as: "organizations/{ORGANI
      ZATION_ID}/locations/{LOCATION_ID}/workloads/{WORKLOAD_ID}" For example:
      "organizations/123/locations/us-east1/workloads/assured-workload-1" This
      option is now deprecated.
    target: Required. The resource ID of the folder-based destination
      workload. This workload is where the source resource will hypothetically
      be moved to. Specify the workload's relative resource name, formatted
      as: "organizations/{ORGANIZATION_ID}/locations/{LOCATION_ID}/workloads/{
      WORKLOAD_ID}" For example: "organizations/123/locations/us-
      east1/workloads/assured-workload-2"
  """

  analyzeChildAssets = _messages.BooleanField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  project = _messages.StringField(4, required=True)
  source = _messages.StringField(5)
  target = _messages.StringField(6, required=True)


class GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationRequest(_messages.Message):
  r"""Request for acknowledging the violation Next Id: 5

  Enums:
    AcknowledgeTypeValueValuesEnum: Optional. Acknowledge type of specified
      violation.

  Fields:
    acknowledgeType: Optional. Acknowledge type of specified violation.
    comment: Required. Business justification explaining the need for
      violation acknowledgement
    nonCompliantOrgPolicy: Optional. This field is deprecated and will be
      removed in future version of the API. Name of the OrgPolicy which was
      modified with non-compliant change and resulted in this violation.
      Format: projects/{project_number}/policies/{constraint_name}
      folders/{folder_id}/policies/{constraint_name}
      organizations/{organization_id}/policies/{constraint_name}
  """

  class AcknowledgeTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Acknowledge type of specified violation.

    Values:
      ACKNOWLEDGE_TYPE_UNSPECIFIED: Acknowledge type unspecified.
      SINGLE_VIOLATION: Acknowledge only the specific violation.
      EXISTING_CHILD_RESOURCE_VIOLATIONS: Acknowledge specified orgPolicy
        violation and also associated resource violations.
    """
    ACKNOWLEDGE_TYPE_UNSPECIFIED = 0
    SINGLE_VIOLATION = 1
    EXISTING_CHILD_RESOURCE_VIOLATIONS = 2

  acknowledgeType = _messages.EnumField('AcknowledgeTypeValueValuesEnum', 1)
  comment = _messages.StringField(2)
  nonCompliantOrgPolicy = _messages.StringField(3)


class GoogleCloudAssuredworkloadsV1beta1AcknowledgeViolationResponse(_messages.Message):
  r"""Response for violation acknowledgement"""


class GoogleCloudAssuredworkloadsV1beta1AnalyzeWorkloadMoveResponse(_messages.Message):
  r"""Response containing the analysis results for the hypothetical resource
  move.

  Fields:
    assetMoveAnalyses: List of analysis results for each asset in scope.
    blockers: A list of blockers that should be addressed before moving the
      source project or project-based workload to the destination folder-based
      workload. This field is now deprecated.
    nextPageToken: The next page token. Is empty if the last page is reached.
  """

  assetMoveAnalyses = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1AssetMoveAnalysis', 1, repeated=True)
  blockers = _messages.StringField(2, repeated=True)
  nextPageToken = _messages.StringField(3)


class GoogleCloudAssuredworkloadsV1beta1AssetMoveAnalysis(_messages.Message):
  r"""Represents move analysis results for an asset.

  Fields:
    analysisGroups: List of eligible analyses performed for the asset.
    asset: The full resource name of the asset being analyzed. Example: //comp
      ute.googleapis.com/projects/my_project_123/zones/zone1/instances/instanc
      e1
    assetType: Type of the asset being analyzed. Possible values will be among
      the ones listed [here](https://cloud.google.com/asset-
      inventory/docs/supported-asset-types#searchable_asset_types).
  """

  analysisGroups = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1MoveAnalysisGroup', 1, repeated=True)
  asset = _messages.StringField(2)
  assetType = _messages.StringField(3)


class GoogleCloudAssuredworkloadsV1beta1CreateWorkloadOperationMetadata(_messages.Message):
  r"""Operation metadata to give request details of CreateWorkload.

  Enums:
    ComplianceRegimeValueValuesEnum: Optional. Compliance controls that should
      be applied to the resources managed by the workload.

  Fields:
    complianceRegime: Optional. Compliance controls that should be applied to
      the resources managed by the workload.
    createTime: Optional. Time when the operation was created.
    displayName: Optional. The display name of the workload.
    parent: Optional. The parent of the workload.
    resourceSettings: Optional. Resource properties in the input that are used
      for creating/customizing workload resources.
  """

  class ComplianceRegimeValueValuesEnum(_messages.Enum):
    r"""Optional. Compliance controls that should be applied to the resources
    managed by the workload.

    Values:
      COMPLIANCE_REGIME_UNSPECIFIED: Unknown compliance regime.
      IL4: Information protection as per DoD IL4 requirements.
      CJIS: Criminal Justice Information Services (CJIS) Security policies.
      FEDRAMP_HIGH: FedRAMP High data protection controls
      FEDRAMP_MODERATE: FedRAMP Moderate data protection controls
      US_REGIONAL_ACCESS: Assured Workloads For US Regions data protection
        controls
      HIPAA: Health Insurance Portability and Accountability Act controls
      HITRUST: Health Information Trust Alliance controls
      EU_REGIONS_AND_SUPPORT: Assured Workloads For EU Regions and Support
        controls
      CA_REGIONS_AND_SUPPORT: Assured Workloads For Canada Regions and Support
        controls
      ITAR: International Traffic in Arms Regulations
      AU_REGIONS_AND_US_SUPPORT: Assured Workloads for Australia Regions and
        Support controls Available for public preview consumption. Don't
        create production workloads.
      ASSURED_WORKLOADS_FOR_PARTNERS: Assured Workloads for Partners;
      ISR_REGIONS: Assured Workloads for Israel
      ISR_REGIONS_AND_SUPPORT: Assured Workloads for Israel Regions
      CA_PROTECTED_B: Assured Workloads for Canada Protected B regime
      IL5: Information protection as per DoD IL5 requirements.
    """
    COMPLIANCE_REGIME_UNSPECIFIED = 0
    IL4 = 1
    CJIS = 2
    FEDRAMP_HIGH = 3
    FEDRAMP_MODERATE = 4
    US_REGIONAL_ACCESS = 5
    HIPAA = 6
    HITRUST = 7
    EU_REGIONS_AND_SUPPORT = 8
    CA_REGIONS_AND_SUPPORT = 9
    ITAR = 10
    AU_REGIONS_AND_US_SUPPORT = 11
    ASSURED_WORKLOADS_FOR_PARTNERS = 12
    ISR_REGIONS = 13
    ISR_REGIONS_AND_SUPPORT = 14
    CA_PROTECTED_B = 15
    IL5 = 16

  complianceRegime = _messages.EnumField('ComplianceRegimeValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  parent = _messages.StringField(4)
  resourceSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadResourceSettings', 5, repeated=True)


class GoogleCloudAssuredworkloadsV1beta1EnableResourceMonitoringResponse(_messages.Message):
  r"""Response for EnableResourceMonitoring endpoint."""


class GoogleCloudAssuredworkloadsV1beta1ListViolationsResponse(_messages.Message):
  r"""Response of ListViolations endpoint.

  Fields:
    nextPageToken: The next page token. Returns empty if reached the last
      page.
    violations: List of Violations under a Workload.
  """

  nextPageToken = _messages.StringField(1)
  violations = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1Violation', 2, repeated=True)


class GoogleCloudAssuredworkloadsV1beta1ListWorkloadsResponse(_messages.Message):
  r"""Response of ListWorkloads endpoint.

  Fields:
    nextPageToken: The next page token. Return empty if reached the last page.
    workloads: List of Workloads under a given parent.
  """

  nextPageToken = _messages.StringField(1)
  workloads = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1Workload', 2, repeated=True)


class GoogleCloudAssuredworkloadsV1beta1MoveAnalysisGroup(_messages.Message):
  r"""Represents a logical group of checks performed for an asset. If
  successful, the group contains the analysis result, otherwise it contains an
  error with the failure reason.

  Fields:
    analysisResult: Result of a successful analysis.
    displayName: Name of the analysis group.
    error: Error details for a failed analysis.
  """

  analysisResult = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1MoveAnalysisResult', 1)
  displayName = _messages.StringField(2)
  error = _messages.MessageField('GoogleRpcStatus', 3)


class GoogleCloudAssuredworkloadsV1beta1MoveAnalysisResult(_messages.Message):
  r"""Represents the successful move analysis results for a group.

  Fields:
    blockers: List of blockers. If not resolved, these will result in
      compliance violations in the target.
    warnings: List of warnings. These are risks that may or may not result in
      compliance violations.
  """

  blockers = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1MoveImpact', 1, repeated=True)
  warnings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1MoveImpact', 2, repeated=True)


class GoogleCloudAssuredworkloadsV1beta1MoveImpact(_messages.Message):
  r"""Represents the impact of moving the asset to the target.

  Fields:
    detail: Explanation of the impact.
  """

  detail = _messages.StringField(1)


class GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesRequest(_messages.Message):
  r"""Request for restricting list of available resources in Workload
  environment.

  Enums:
    RestrictionTypeValueValuesEnum: Required. The type of restriction for
      using gcp products in the Workload environment.

  Fields:
    restrictionType: Required. The type of restriction for using gcp products
      in the Workload environment.
  """

  class RestrictionTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of restriction for using gcp products in the
    Workload environment.

    Values:
      RESTRICTION_TYPE_UNSPECIFIED: Unknown restriction type.
      ALLOW_ALL_GCP_RESOURCES: Allow the use all of all gcp products,
        irrespective of the compliance posture. This effectively removes
        gcp.restrictServiceUsage OrgPolicy on the AssuredWorkloads Folder.
      ALLOW_COMPLIANT_RESOURCES: Based on Workload's compliance regime,
        allowed list changes. See - https://cloud.google.com/assured-
        workloads/docs/supported-products for the list of supported resources.
      APPEND_COMPLIANT_RESOURCES: Similar to ALLOW_COMPLIANT_RESOURCES but
        adds the list of compliant resources to the existing list of compliant
        resources. Effective org-policy of the Folder is considered to ensure
        there is no disruption to the existing customer workflows.
    """
    RESTRICTION_TYPE_UNSPECIFIED = 0
    ALLOW_ALL_GCP_RESOURCES = 1
    ALLOW_COMPLIANT_RESOURCES = 2
    APPEND_COMPLIANT_RESOURCES = 3

  restrictionType = _messages.EnumField('RestrictionTypeValueValuesEnum', 1)


class GoogleCloudAssuredworkloadsV1beta1RestrictAllowedResourcesResponse(_messages.Message):
  r"""Response for restricting the list of allowed resources."""


class GoogleCloudAssuredworkloadsV1beta1Violation(_messages.Message):
  r"""Workload monitoring Violation. Next Id: 27

  Enums:
    StateValueValuesEnum: Output only. State of the violation
    ViolationTypeValueValuesEnum: Output only. Type of the violation

  Fields:
    acknowledged: A boolean that indicates if the violation is acknowledged
    acknowledgementTime: Optional. Timestamp when this violation was
      acknowledged first. Check exception_contexts to find the last time the
      violation was acknowledged when there are more than one violations. This
      field will be absent when acknowledged field is marked as false.
    associatedOrgPolicyViolationId: Optional. Output only. Violation Id of the
      org-policy violation due to which the resource violation is caused.
      Empty for org-policy violations.
    auditLogLink: Output only. Immutable. Audit Log Link for violated resource
      Format: https://console.cloud.google.com/logs/query;query={logName}{prot
      oPayload.resourceName}{timeRange}{folder}
    beginTime: Output only. Time of the event which triggered the Violation.
    category: Output only. Category under which this violation is mapped. e.g.
      Location, Service Usage, Access, Encryption, etc.
    description: Output only. Description for the Violation. e.g. OrgPolicy
      gcp.resourceLocations has non compliant value.
    exceptionAuditLogLink: Output only. Immutable. Audit Log link to find
      business justification provided for violation exception. Format: https:/
      /console.cloud.google.com/logs/query;query={logName}{protoPayload.resour
      ceName}{protoPayload.methodName}{timeRange}{organization}
    exceptionContexts: Output only. List of all the exception detail added for
      the violation.
    name: Output only. Immutable. Name of the Violation. Format: organizations
      /{organization}/locations/{location}/workloads/{workload_id}/violations/
      {violations_id}
    nonCompliantOrgPolicy: Output only. Immutable. Name of the OrgPolicy which
      was modified with non-compliant change and resulted this violation.
      Format: projects/{project_number}/policies/{constraint_name}
      folders/{folder_id}/policies/{constraint_name}
      organizations/{organization_id}/policies/{constraint_name}
    orgPolicyConstraint: Output only. Immutable. The org-policy-constraint
      that was incorrectly changed, which resulted in this violation.
    parentProjectNumber: Optional. Output only. Parent project number where
      resource is present. Empty for org-policy violations.
    remediation: Output only. Compliance violation remediation
    resolveTime: Output only. Time of the event which fixed the Violation. If
      the violation is ACTIVE this will be empty.
    resourceName: Optional. Output only. Name of the resource like
      //storage.googleapis.com/myprojectxyz-testbucket. Empty for org-policy
      violations.
    resourceType: Optional. Output only. Type of the resource like
      compute.googleapis.com/Disk, etc. Empty for org-policy violations.
    state: Output only. State of the violation
    updateTime: Output only. The last time when the Violation record was
      updated.
    violationType: Output only. Type of the violation
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the violation

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      RESOLVED: Violation is resolved.
      UNRESOLVED: Violation is Unresolved
      EXCEPTION: Violation is Exception
    """
    STATE_UNSPECIFIED = 0
    RESOLVED = 1
    UNRESOLVED = 2
    EXCEPTION = 3

  class ViolationTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the violation

    Values:
      VIOLATION_TYPE_UNSPECIFIED: Unspecified type.
      ORG_POLICY: Org Policy Violation.
      RESOURCE: Resource Violation.
    """
    VIOLATION_TYPE_UNSPECIFIED = 0
    ORG_POLICY = 1
    RESOURCE = 2

  acknowledged = _messages.BooleanField(1)
  acknowledgementTime = _messages.StringField(2)
  associatedOrgPolicyViolationId = _messages.StringField(3)
  auditLogLink = _messages.StringField(4)
  beginTime = _messages.StringField(5)
  category = _messages.StringField(6)
  description = _messages.StringField(7)
  exceptionAuditLogLink = _messages.StringField(8)
  exceptionContexts = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1ViolationExceptionContext', 9, repeated=True)
  name = _messages.StringField(10)
  nonCompliantOrgPolicy = _messages.StringField(11)
  orgPolicyConstraint = _messages.StringField(12)
  parentProjectNumber = _messages.StringField(13)
  remediation = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1ViolationRemediation', 14)
  resolveTime = _messages.StringField(15)
  resourceName = _messages.StringField(16)
  resourceType = _messages.StringField(17)
  state = _messages.EnumField('StateValueValuesEnum', 18)
  updateTime = _messages.StringField(19)
  violationType = _messages.EnumField('ViolationTypeValueValuesEnum', 20)


class GoogleCloudAssuredworkloadsV1beta1ViolationExceptionContext(_messages.Message):
  r"""Violation exception detail. Next Id: 6

  Fields:
    acknowledgementTime: Timestamp when the violation was acknowledged.
    comment: Business justification provided towards the acknowledgement of
      the violation.
    userName: Name of the user (or service account) who acknowledged the
      violation.
  """

  acknowledgementTime = _messages.StringField(1)
  comment = _messages.StringField(2)
  userName = _messages.StringField(3)


class GoogleCloudAssuredworkloadsV1beta1ViolationRemediation(_messages.Message):
  r"""Represents remediation guidance to resolve compliance violation for
  AssuredWorkload

  Enums:
    RemediationTypeValueValuesEnum: Output only. Reemediation type based on
      the type of org policy values violated

  Fields:
    compliantValues: Values that can resolve the violation For example: for
      list org policy violations, this will either be the list of allowed or
      denied values
    instructions: Required. Remediation instructions to resolve violations
    remediationType: Output only. Reemediation type based on the type of org
      policy values violated
  """

  class RemediationTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Reemediation type based on the type of org policy values
    violated

    Values:
      REMEDIATION_TYPE_UNSPECIFIED: Unspecified remediation type
      REMEDIATION_BOOLEAN_ORG_POLICY_VIOLATION: Remediation type for boolean
        org policy
      REMEDIATION_LIST_ALLOWED_VALUES_ORG_POLICY_VIOLATION: Remediation type
        for list org policy which have allowed values in the monitoring rule
      REMEDIATION_LIST_DENIED_VALUES_ORG_POLICY_VIOLATION: Remediation type
        for list org policy which have denied values in the monitoring rule
      REMEDIATION_RESTRICT_CMEK_CRYPTO_KEY_PROJECTS_ORG_POLICY_VIOLATION:
        Remediation type for gcp.restrictCmekCryptoKeyProjects
      REMEDIATION_RESOURCE_VIOLATION: Remediation type for resource violation.
    """
    REMEDIATION_TYPE_UNSPECIFIED = 0
    REMEDIATION_BOOLEAN_ORG_POLICY_VIOLATION = 1
    REMEDIATION_LIST_ALLOWED_VALUES_ORG_POLICY_VIOLATION = 2
    REMEDIATION_LIST_DENIED_VALUES_ORG_POLICY_VIOLATION = 3
    REMEDIATION_RESTRICT_CMEK_CRYPTO_KEY_PROJECTS_ORG_POLICY_VIOLATION = 4
    REMEDIATION_RESOURCE_VIOLATION = 5

  compliantValues = _messages.StringField(1, repeated=True)
  instructions = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructions', 2)
  remediationType = _messages.EnumField('RemediationTypeValueValuesEnum', 3)


class GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructions(_messages.Message):
  r"""Instructions to remediate violation

  Fields:
    consoleInstructions: Remediation instructions to resolve violation via
      cloud console
    gcloudInstructions: Remediation instructions to resolve violation via
      gcloud cli
  """

  consoleInstructions = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsConsole', 1)
  gcloudInstructions = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsGcloud', 2)


class GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsConsole(_messages.Message):
  r"""Remediation instructions to resolve violation via cloud console

  Fields:
    additionalLinks: Additional urls for more information about steps
    consoleUris: Link to console page where violations can be resolved
    steps: Steps to resolve violation via cloud console
  """

  additionalLinks = _messages.StringField(1, repeated=True)
  consoleUris = _messages.StringField(2, repeated=True)
  steps = _messages.StringField(3, repeated=True)


class GoogleCloudAssuredworkloadsV1beta1ViolationRemediationInstructionsGcloud(_messages.Message):
  r"""Remediation instructions to resolve violation via gcloud cli

  Fields:
    additionalLinks: Additional urls for more information about steps
    gcloudCommands: Gcloud command to resolve violation
    steps: Steps to resolve violation via gcloud cli
  """

  additionalLinks = _messages.StringField(1, repeated=True)
  gcloudCommands = _messages.StringField(2, repeated=True)
  steps = _messages.StringField(3, repeated=True)


class GoogleCloudAssuredworkloadsV1beta1Workload(_messages.Message):
  r"""A Workload object for managing highly regulated workloads of cloud
  customers.

  Enums:
    ComplianceRegimeValueValuesEnum: Required. Immutable. Compliance Regime
      associated with this workload.
    KajEnrollmentStateValueValuesEnum: Output only. Represents the KAJ
      enrollment state of the given workload.
    PartnerValueValuesEnum: Optional. Partner regime associated with this
      workload.

  Messages:
    LabelsValue: Optional. Labels applied to the workload.

  Fields:
    billingAccount: Optional. The billing account used for the resources which
      are direct children of workload. This billing account is initially
      associated with the resources created as part of Workload creation.
      After the initial creation of these resources, the customer can change
      the assigned billing account. The resource name has the form
      `billingAccounts/{billing_account_id}`. For example,
      `billingAccounts/012345-567890-ABCDEF`.
    cjisSettings: Input only. Immutable. Settings specific to resources needed
      for CJIS.
    complianceRegime: Required. Immutable. Compliance Regime associated with
      this workload.
    complianceStatus: Output only. Count of active Violations in the Workload.
    compliantButDisallowedServices: Output only. Urls for services which are
      compliant for this Assured Workload, but which are currently disallowed
      by the ResourceUsageRestriction org policy. Invoke
      RestrictAllowedResources endpoint to allow your project developers to
      use these services in their environment."
    createTime: Output only. Immutable. The Workload creation timestamp.
    displayName: Required. The user-assigned display name of the Workload.
      When present it must be between 4 to 30 characters. Allowed characters
      are: lowercase and uppercase letters, numbers, hyphen, and spaces.
      Example: My Workload
    ekmProvisioningResponse: Optional. Represents the Ekm Provisioning State
      of the given workload.
    enableSovereignControls: Optional. Indicates the sovereignty status of the
      given workload. Currently meant to be used by Europe/Canada customers.
    etag: Optional. ETag of the workload, it is calculated on the basis of the
      Workload contents. It will be used in Update & Delete operations.
    fedrampHighSettings: Input only. Immutable. Settings specific to resources
      needed for FedRAMP High.
    fedrampModerateSettings: Input only. Immutable. Settings specific to
      resources needed for FedRAMP Moderate.
    il4Settings: Input only. Immutable. Settings specific to resources needed
      for IL4.
    kajEnrollmentState: Output only. Represents the KAJ enrollment state of
      the given workload.
    kmsSettings: Input only. Settings used to create a CMEK crypto key. When
      set, a project with a KMS CMEK key is provisioned. This field is
      deprecated as of Feb 28, 2022. In order to create a Keyring, callers
      should specify, ENCRYPTION_KEYS_PROJECT or KEYRING in
      ResourceSettings.resource_type field.
    labels: Optional. Labels applied to the workload.
    name: Optional. The resource name of the workload. Format:
      organizations/{organization}/locations/{location}/workloads/{workload}
      Read-only.
    partner: Optional. Partner regime associated with this workload.
    partnerPermissions: Optional. Permissions granted to the AW Partner SA
      account for the customer workload
    provisionedResourcesParent: Input only. The parent resource for the
      resources managed by this Assured Workload. May be either empty or a
      folder resource which is a child of the Workload parent. If not
      specified all resources are created under the parent organization.
      Format: folders/{folder_id}
    resourceMonitoringEnabled: Output only. Indicates whether resource
      monitoring is enabled for workload or not. It is true when Resource feed
      is subscribed to AWM topic and AWM Service Agent Role is binded to AW
      Service Account for resource Assured workload.
    resourceSettings: Input only. Resource properties that are used to
      customize workload resources. These properties (such as custom project
      id) will be used to create workload resources if possible. This field is
      optional.
    resources: Output only. The resources associated with this workload. These
      resources will be created when creating the workload. If any of the
      projects already exist, the workload creation will fail. Always read
      only.
    saaEnrollmentResponse: Output only. Represents the SAA enrollment response
      of the given workload. SAA enrollment response is queried during
      GetWorkload call. In failure cases, user friendly error message is shown
      in SAA details page.
    violationNotificationsEnabled: Optional. Indicates whether the e-mail
      notification for a violation is enabled for a workload. This value will
      be by default True, and if not present will be considered as true. This
      should only be updated via updateWorkload call. Any Changes to this
      field during the createWorkload call will not be honored. This will
      always be true while creating the workload.
  """

  class ComplianceRegimeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. Compliance Regime associated with this workload.

    Values:
      COMPLIANCE_REGIME_UNSPECIFIED: Unknown compliance regime.
      IL4: Information protection as per DoD IL4 requirements.
      CJIS: Criminal Justice Information Services (CJIS) Security policies.
      FEDRAMP_HIGH: FedRAMP High data protection controls
      FEDRAMP_MODERATE: FedRAMP Moderate data protection controls
      US_REGIONAL_ACCESS: Assured Workloads For US Regions data protection
        controls
      HIPAA: Health Insurance Portability and Accountability Act controls
      HITRUST: Health Information Trust Alliance controls
      EU_REGIONS_AND_SUPPORT: Assured Workloads For EU Regions and Support
        controls
      CA_REGIONS_AND_SUPPORT: Assured Workloads For Canada Regions and Support
        controls
      ITAR: International Traffic in Arms Regulations
      AU_REGIONS_AND_US_SUPPORT: Assured Workloads for Australia Regions and
        Support controls Available for public preview consumption. Don't
        create production workloads.
      ASSURED_WORKLOADS_FOR_PARTNERS: Assured Workloads for Partners;
      ISR_REGIONS: Assured Workloads for Israel
      ISR_REGIONS_AND_SUPPORT: Assured Workloads for Israel Regions
      CA_PROTECTED_B: Assured Workloads for Canada Protected B regime
      IL5: Information protection as per DoD IL5 requirements.
    """
    COMPLIANCE_REGIME_UNSPECIFIED = 0
    IL4 = 1
    CJIS = 2
    FEDRAMP_HIGH = 3
    FEDRAMP_MODERATE = 4
    US_REGIONAL_ACCESS = 5
    HIPAA = 6
    HITRUST = 7
    EU_REGIONS_AND_SUPPORT = 8
    CA_REGIONS_AND_SUPPORT = 9
    ITAR = 10
    AU_REGIONS_AND_US_SUPPORT = 11
    ASSURED_WORKLOADS_FOR_PARTNERS = 12
    ISR_REGIONS = 13
    ISR_REGIONS_AND_SUPPORT = 14
    CA_PROTECTED_B = 15
    IL5 = 16

  class KajEnrollmentStateValueValuesEnum(_messages.Enum):
    r"""Output only. Represents the KAJ enrollment state of the given
    workload.

    Values:
      KAJ_ENROLLMENT_STATE_UNSPECIFIED: Default State for KAJ Enrollment.
      KAJ_ENROLLMENT_STATE_PENDING: Pending State for KAJ Enrollment.
      KAJ_ENROLLMENT_STATE_COMPLETE: Complete State for KAJ Enrollment.
    """
    KAJ_ENROLLMENT_STATE_UNSPECIFIED = 0
    KAJ_ENROLLMENT_STATE_PENDING = 1
    KAJ_ENROLLMENT_STATE_COMPLETE = 2

  class PartnerValueValuesEnum(_messages.Enum):
    r"""Optional. Partner regime associated with this workload.

    Values:
      PARTNER_UNSPECIFIED: <no description>
      LOCAL_CONTROLS_BY_S3NS: Enum representing S3NS (Thales) partner.
      SOVEREIGN_CONTROLS_BY_T_SYSTEMS: Enum representing T_SYSTEM (TSI)
        partner.
      SOVEREIGN_CONTROLS_BY_SIA_MINSAIT: Enum representing SIA_MINSAIT (Indra)
        partner.
      SOVEREIGN_CONTROLS_BY_PSN: Enum representing PSN (TIM) partner.
    """
    PARTNER_UNSPECIFIED = 0
    LOCAL_CONTROLS_BY_S3NS = 1
    SOVEREIGN_CONTROLS_BY_T_SYSTEMS = 2
    SOVEREIGN_CONTROLS_BY_SIA_MINSAIT = 3
    SOVEREIGN_CONTROLS_BY_PSN = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels applied to the workload.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  billingAccount = _messages.StringField(1)
  cjisSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadCJISSettings', 2)
  complianceRegime = _messages.EnumField('ComplianceRegimeValueValuesEnum', 3)
  complianceStatus = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadComplianceStatus', 4)
  compliantButDisallowedServices = _messages.StringField(5, repeated=True)
  createTime = _messages.StringField(6)
  displayName = _messages.StringField(7)
  ekmProvisioningResponse = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadEkmProvisioningResponse', 8)
  enableSovereignControls = _messages.BooleanField(9)
  etag = _messages.StringField(10)
  fedrampHighSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampHighSettings', 11)
  fedrampModerateSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampModerateSettings', 12)
  il4Settings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadIL4Settings', 13)
  kajEnrollmentState = _messages.EnumField('KajEnrollmentStateValueValuesEnum', 14)
  kmsSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings', 15)
  labels = _messages.MessageField('LabelsValue', 16)
  name = _messages.StringField(17)
  partner = _messages.EnumField('PartnerValueValuesEnum', 18)
  partnerPermissions = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadPartnerPermissions', 19)
  provisionedResourcesParent = _messages.StringField(20)
  resourceMonitoringEnabled = _messages.BooleanField(21)
  resourceSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadResourceSettings', 22, repeated=True)
  resources = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadResourceInfo', 23, repeated=True)
  saaEnrollmentResponse = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadSaaEnrollmentResponse', 24)
  violationNotificationsEnabled = _messages.BooleanField(25)


class GoogleCloudAssuredworkloadsV1beta1WorkloadCJISSettings(_messages.Message):
  r"""Settings specific to resources needed for CJIS.

  Fields:
    kmsSettings: Input only. Immutable. Settings used to create a CMEK crypto
      key.
  """

  kmsSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings', 1)


class GoogleCloudAssuredworkloadsV1beta1WorkloadComplianceStatus(_messages.Message):
  r"""Represents the Compliance Status of this workload

  Fields:
    acknowledgedResourceViolationCount: Number of current resource violations
      which are not acknowledged.
    acknowledgedViolationCount: Number of current orgPolicy violations which
      are acknowledged.
    activeResourceViolationCount: Number of current resource violations which
      are acknowledged.
    activeViolationCount: Number of current orgPolicy violations which are not
      acknowledged.
  """

  acknowledgedResourceViolationCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  acknowledgedViolationCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  activeResourceViolationCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  activeViolationCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudAssuredworkloadsV1beta1WorkloadEkmProvisioningResponse(_messages.Message):
  r"""External key management systems(EKM) Provisioning response

  Enums:
    EkmProvisioningErrorDomainValueValuesEnum: Indicates Ekm provisioning
      error if any.
    EkmProvisioningErrorMappingValueValuesEnum: Detailed error message if Ekm
      provisioning fails
    EkmProvisioningStateValueValuesEnum: Indicates Ekm enrollment Provisioning
      of a given workload.

  Fields:
    ekmProvisioningErrorDomain: Indicates Ekm provisioning error if any.
    ekmProvisioningErrorMapping: Detailed error message if Ekm provisioning
      fails
    ekmProvisioningState: Indicates Ekm enrollment Provisioning of a given
      workload.
  """

  class EkmProvisioningErrorDomainValueValuesEnum(_messages.Enum):
    r"""Indicates Ekm provisioning error if any.

    Values:
      EKM_PROVISIONING_ERROR_DOMAIN_UNSPECIFIED: No error domain
      UNSPECIFIED_ERROR: Error but domain is unspecified.
      GOOGLE_SERVER_ERROR: Internal logic breaks within provisioning code.
      EXTERNAL_USER_ERROR: Error occurred with the customer not granting
        permission/creating resource.
      EXTERNAL_PARTNER_ERROR: Error occurred within the partner's provisioning
        cluster.
      TIMEOUT_ERROR: Resource wasn't provisioned in the required 7 day time
        period
    """
    EKM_PROVISIONING_ERROR_DOMAIN_UNSPECIFIED = 0
    UNSPECIFIED_ERROR = 1
    GOOGLE_SERVER_ERROR = 2
    EXTERNAL_USER_ERROR = 3
    EXTERNAL_PARTNER_ERROR = 4
    TIMEOUT_ERROR = 5

  class EkmProvisioningErrorMappingValueValuesEnum(_messages.Enum):
    r"""Detailed error message if Ekm provisioning fails

    Values:
      EKM_PROVISIONING_ERROR_MAPPING_UNSPECIFIED: Error is unspecified.
      INVALID_SERVICE_ACCOUNT: Service account is used is invalid.
      MISSING_METRICS_SCOPE_ADMIN_PERMISSION: Iam permission
        monitoring.MetricsScopeAdmin wasn't applied.
      MISSING_EKM_CONNECTION_ADMIN_PERMISSION: Iam permission
        cloudkms.ekmConnectionsAdmin wasn't applied.
    """
    EKM_PROVISIONING_ERROR_MAPPING_UNSPECIFIED = 0
    INVALID_SERVICE_ACCOUNT = 1
    MISSING_METRICS_SCOPE_ADMIN_PERMISSION = 2
    MISSING_EKM_CONNECTION_ADMIN_PERMISSION = 3

  class EkmProvisioningStateValueValuesEnum(_messages.Enum):
    r"""Indicates Ekm enrollment Provisioning of a given workload.

    Values:
      EKM_PROVISIONING_STATE_UNSPECIFIED: Default State for Ekm Provisioning
      EKM_PROVISIONING_STATE_PENDING: Pending State for Ekm Provisioning
      EKM_PROVISIONING_STATE_FAILED: Failed State for Ekm Provisioning
      EKM_PROVISIONING_STATE_COMPLETED: Completed State for Ekm Provisioning
    """
    EKM_PROVISIONING_STATE_UNSPECIFIED = 0
    EKM_PROVISIONING_STATE_PENDING = 1
    EKM_PROVISIONING_STATE_FAILED = 2
    EKM_PROVISIONING_STATE_COMPLETED = 3

  ekmProvisioningErrorDomain = _messages.EnumField('EkmProvisioningErrorDomainValueValuesEnum', 1)
  ekmProvisioningErrorMapping = _messages.EnumField('EkmProvisioningErrorMappingValueValuesEnum', 2)
  ekmProvisioningState = _messages.EnumField('EkmProvisioningStateValueValuesEnum', 3)


class GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampHighSettings(_messages.Message):
  r"""Settings specific to resources needed for FedRAMP High.

  Fields:
    kmsSettings: Input only. Immutable. Settings used to create a CMEK crypto
      key.
  """

  kmsSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings', 1)


class GoogleCloudAssuredworkloadsV1beta1WorkloadFedrampModerateSettings(_messages.Message):
  r"""Settings specific to resources needed for FedRAMP Moderate.

  Fields:
    kmsSettings: Input only. Immutable. Settings used to create a CMEK crypto
      key.
  """

  kmsSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings', 1)


class GoogleCloudAssuredworkloadsV1beta1WorkloadIL4Settings(_messages.Message):
  r"""Settings specific to resources needed for IL4.

  Fields:
    kmsSettings: Input only. Immutable. Settings used to create a CMEK crypto
      key.
  """

  kmsSettings = _messages.MessageField('GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings', 1)


class GoogleCloudAssuredworkloadsV1beta1WorkloadKMSSettings(_messages.Message):
  r"""Settings specific to the Key Management Service.

  Fields:
    nextRotationTime: Required. Input only. Immutable. The time at which the
      Key Management Service will automatically create a new version of the
      crypto key and mark it as the primary.
    rotationPeriod: Required. Input only. Immutable. [next_rotation_time] will
      be advanced by this period when the Key Management Service automatically
      rotates a key. Must be at least 24 hours and at most 876,000 hours.
  """

  nextRotationTime = _messages.StringField(1)
  rotationPeriod = _messages.StringField(2)


class GoogleCloudAssuredworkloadsV1beta1WorkloadPartnerPermissions(_messages.Message):
  r"""Permissions granted to the AW Partner SA account for the customer
  workload

  Fields:
    assuredWorkloadsMonitoring: Optional. Allow partner to view violation
      alerts.
    dataLogsViewer: Allow the partner to view inspectability logs and
      monitoring violations.
    serviceAccessApprover: Optional. Allow partner to view access approval
      logs.
  """

  assuredWorkloadsMonitoring = _messages.BooleanField(1)
  dataLogsViewer = _messages.BooleanField(2)
  serviceAccessApprover = _messages.BooleanField(3)


class GoogleCloudAssuredworkloadsV1beta1WorkloadResourceInfo(_messages.Message):
  r"""Represent the resources that are children of this Workload.

  Enums:
    ResourceTypeValueValuesEnum: Indicates the type of resource.

  Fields:
    resourceId: Resource identifier. For a project this represents
      project_number.
    resourceType: Indicates the type of resource.
  """

  class ResourceTypeValueValuesEnum(_messages.Enum):
    r"""Indicates the type of resource.

    Values:
      RESOURCE_TYPE_UNSPECIFIED: Unknown resource type.
      CONSUMER_PROJECT: Deprecated. Existing workloads will continue to
        support this, but new CreateWorkloadRequests should not specify this
        as an input value.
      CONSUMER_FOLDER: Consumer Folder.
      ENCRYPTION_KEYS_PROJECT: Consumer project containing encryption keys.
      KEYRING: Keyring resource that hosts encryption keys.
    """
    RESOURCE_TYPE_UNSPECIFIED = 0
    CONSUMER_PROJECT = 1
    CONSUMER_FOLDER = 2
    ENCRYPTION_KEYS_PROJECT = 3
    KEYRING = 4

  resourceId = _messages.IntegerField(1)
  resourceType = _messages.EnumField('ResourceTypeValueValuesEnum', 2)


class GoogleCloudAssuredworkloadsV1beta1WorkloadResourceSettings(_messages.Message):
  r"""Represent the custom settings for the resources to be created.

  Enums:
    ResourceTypeValueValuesEnum: Indicates the type of resource. This field
      should be specified to correspond the id to the right project type
      (CONSUMER_PROJECT or ENCRYPTION_KEYS_PROJECT)

  Fields:
    displayName: User-assigned resource display name. If not empty it will be
      used to create a resource with the specified name.
    resourceId: Resource identifier. For a project this represents project_id.
      If the project is already taken, the workload creation will fail. For
      KeyRing, this represents the keyring_id. For a folder, don't set this
      value as folder_id is assigned by Google.
    resourceType: Indicates the type of resource. This field should be
      specified to correspond the id to the right project type
      (CONSUMER_PROJECT or ENCRYPTION_KEYS_PROJECT)
  """

  class ResourceTypeValueValuesEnum(_messages.Enum):
    r"""Indicates the type of resource. This field should be specified to
    correspond the id to the right project type (CONSUMER_PROJECT or
    ENCRYPTION_KEYS_PROJECT)

    Values:
      RESOURCE_TYPE_UNSPECIFIED: Unknown resource type.
      CONSUMER_PROJECT: Deprecated. Existing workloads will continue to
        support this, but new CreateWorkloadRequests should not specify this
        as an input value.
      CONSUMER_FOLDER: Consumer Folder.
      ENCRYPTION_KEYS_PROJECT: Consumer project containing encryption keys.
      KEYRING: Keyring resource that hosts encryption keys.
    """
    RESOURCE_TYPE_UNSPECIFIED = 0
    CONSUMER_PROJECT = 1
    CONSUMER_FOLDER = 2
    ENCRYPTION_KEYS_PROJECT = 3
    KEYRING = 4

  displayName = _messages.StringField(1)
  resourceId = _messages.StringField(2)
  resourceType = _messages.EnumField('ResourceTypeValueValuesEnum', 3)


class GoogleCloudAssuredworkloadsV1beta1WorkloadSaaEnrollmentResponse(_messages.Message):
  r"""Signed Access Approvals (SAA) enrollment response.

  Enums:
    SetupErrorsValueListEntryValuesEnum:
    SetupStatusValueValuesEnum: Indicates SAA enrollment status of a given
      workload.

  Fields:
    setupErrors: Indicates SAA enrollment setup error if any.
    setupStatus: Indicates SAA enrollment status of a given workload.
  """

  class SetupErrorsValueListEntryValuesEnum(_messages.Enum):
    r"""SetupErrorsValueListEntryValuesEnum enum type.

    Values:
      SETUP_ERROR_UNSPECIFIED: Unspecified.
      ERROR_INVALID_BASE_SETUP: Invalid states for all customers, to be
        redirected to AA UI for additional details.
      ERROR_MISSING_EXTERNAL_SIGNING_KEY: Returned when there is not an EKM
        key configured.
      ERROR_NOT_ALL_SERVICES_ENROLLED: Returned when there are no enrolled
        services or the customer is enrolled in CAA only for a subset of
        services.
      ERROR_SETUP_CHECK_FAILED: Returned when exception was encountered during
        evaluation of other criteria.
    """
    SETUP_ERROR_UNSPECIFIED = 0
    ERROR_INVALID_BASE_SETUP = 1
    ERROR_MISSING_EXTERNAL_SIGNING_KEY = 2
    ERROR_NOT_ALL_SERVICES_ENROLLED = 3
    ERROR_SETUP_CHECK_FAILED = 4

  class SetupStatusValueValuesEnum(_messages.Enum):
    r"""Indicates SAA enrollment status of a given workload.

    Values:
      SETUP_STATE_UNSPECIFIED: Unspecified.
      STATUS_PENDING: SAA enrollment pending.
      STATUS_COMPLETE: SAA enrollment comopleted.
    """
    SETUP_STATE_UNSPECIFIED = 0
    STATUS_PENDING = 1
    STATUS_COMPLETE = 2

  setupErrors = _messages.EnumField('SetupErrorsValueListEntryValuesEnum', 1, repeated=True)
  setupStatus = _messages.EnumField('SetupStatusValueValuesEnum', 2)


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated message classes for servicemanagement version v1.

Google Service Management allows service producers to publish their services
on Google Cloud Platform so that they can be discovered and used by service
consumers.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'servicemanagement'


class Advice(_messages.Message):
  r"""Generated advice about this change, used for providing more information
  about how a change will affect the existing service.

  Fields:
    description: Useful description for why this advice was applied and what
      actions should be taken to mitigate any implied risks.
  """

  description = _messages.StringField(1)


class Api(_messages.Message):
  r"""Api is a light-weight descriptor for an API Interface. Interfaces are
  also described as "protocol buffer services" in some contexts, such as by
  the "service" keyword in a .proto file, but they are different from API
  Services, which represent a concrete implementation of an interface as
  opposed to simply a description of methods and bindings. They are also
  sometimes simply referred to as "APIs" in other contexts, such as the name
  of this message itself. See https://cloud.google.com/apis/design/glossary
  for detailed terminology.

  Enums:
    SyntaxValueValuesEnum: The source syntax of the service.

  Fields:
    methods: The methods of this interface, in unspecified order.
    mixins: Included interfaces. See Mixin.
    name: The fully qualified name of this interface, including package name
      followed by the interface's simple name.
    options: Any metadata attached to the interface.
    sourceContext: Source context for the protocol buffer service represented
      by this message.
    syntax: The source syntax of the service.
    version: A version string for this interface. If specified, must have the
      form `major-version.minor-version`, as in `1.10`. If the minor version
      is omitted, it defaults to zero. If the entire version field is empty,
      the major version is derived from the package name, as outlined below.
      If the field is not empty, the version in the package name will be
      verified to be consistent with what is provided here. The versioning
      schema uses [semantic versioning](http://semver.org) where the major
      version number indicates a breaking change and the minor version an
      additive, non-breaking change. Both version numbers are signals to users
      what to expect from different versions, and should be carefully chosen
      based on the product plan. The major version is also reflected in the
      package name of the interface, which must end in `v`, as in
      `google.feature.v1`. For major versions 0 and 1, the suffix can be
      omitted. Zero major versions must only be used for experimental, non-GA
      interfaces.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax of the service.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
      SYNTAX_EDITIONS: Syntax `editions`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1
    SYNTAX_EDITIONS = 2

  methods = _messages.MessageField('Method', 1, repeated=True)
  mixins = _messages.MessageField('Mixin', 2, repeated=True)
  name = _messages.StringField(3)
  options = _messages.MessageField('Option', 4, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 5)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 6)
  version = _messages.StringField(7)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AuthProvider(_messages.Message):
  r"""Configuration for an authentication provider, including support for
  [JSON Web Token (JWT)](https://tools.ietf.org/html/draft-ietf-oauth-json-
  web-token-32).

  Fields:
    audiences: The list of JWT [audiences](https://tools.ietf.org/html/draft-
      ietf-oauth-json-web-token-32#section-4.1.3). that are allowed to access.
      A JWT containing any of these audiences will be accepted. When this
      setting is absent, JWTs with audiences: -
      "https://[service.name]/[google.protobuf.Api.name]" -
      "https://[service.name]/" will be accepted. For example, if no audiences
      are in the setting, LibraryService API will accept JWTs with the
      following audiences: - https://library-
      example.googleapis.com/google.example.library.v1.LibraryService -
      https://library-example.googleapis.com/ Example: audiences:
      bookstore_android.apps.googleusercontent.com,
      bookstore_web.apps.googleusercontent.com
    authorizationUrl: Redirect URL if JWT token is required but not present or
      is expired. Implement authorizationUrl of securityDefinitions in OpenAPI
      spec.
    id: The unique identifier of the auth provider. It will be referred to by
      `AuthRequirement.provider_id`. Example: "bookstore_auth".
    issuer: Identifies the principal that issued the JWT. See
      https://tools.ietf.org/html/draft-ietf-oauth-json-web-
      token-32#section-4.1.1 Usually a URL or an email address. Example:
      https://securetoken.google.com Example:
      <EMAIL>
    jwksUri: URL of the provider's public key set to validate signature of the
      JWT. See [OpenID Discovery](https://openid.net/specs/openid-connect-
      discovery-1_0.html#ProviderMetadata). Optional if the key set document:
      - can be retrieved from [OpenID
      Discovery](https://openid.net/specs/openid-connect-discovery-1_0.html)
      of the issuer. - can be inferred from the email domain of the issuer
      (e.g. a Google service account). Example:
      https://www.googleapis.com/oauth2/v1/certs
    jwtLocations: Defines the locations to extract the JWT. For now it is only
      used by the Cloud Endpoints to store the OpenAPI extension [x-google-
      jwt-locations] (https://cloud.google.com/endpoints/docs/openapi/openapi-
      extensions#x-google-jwt-locations) JWT locations can be one of HTTP
      headers, URL query parameters or cookies. The rule is that the first
      match wins. If not specified, default to use following 3 locations: 1)
      Authorization: Bearer 2) x-goog-iap-jwt-assertion 3) access_token query
      parameter Default locations can be specified as followings:
      jwt_locations: - header: Authorization value_prefix: "Bearer " - header:
      x-goog-iap-jwt-assertion - query: access_token
  """

  audiences = _messages.StringField(1)
  authorizationUrl = _messages.StringField(2)
  id = _messages.StringField(3)
  issuer = _messages.StringField(4)
  jwksUri = _messages.StringField(5)
  jwtLocations = _messages.MessageField('JwtLocation', 6, repeated=True)


class AuthRequirement(_messages.Message):
  r"""User-defined authentication requirements, including support for [JSON
  Web Token (JWT)](https://tools.ietf.org/html/draft-ietf-oauth-json-web-
  token-32).

  Fields:
    audiences: NOTE: This will be deprecated soon, once AuthProvider.audiences
      is implemented and accepted in all the runtime components. The list of
      JWT [audiences](https://tools.ietf.org/html/draft-ietf-oauth-json-web-
      token-32#section-4.1.3). that are allowed to access. A JWT containing
      any of these audiences will be accepted. When this setting is absent,
      only JWTs with audience "https://Service_name/API_name" will be
      accepted. For example, if no audiences are in the setting,
      LibraryService API will only accept JWTs with the following audience
      "https://library-
      example.googleapis.com/google.example.library.v1.LibraryService".
      Example: audiences: bookstore_android.apps.googleusercontent.com,
      bookstore_web.apps.googleusercontent.com
    providerId: id from authentication provider. Example: provider_id:
      bookstore_auth
  """

  audiences = _messages.StringField(1)
  providerId = _messages.StringField(2)


class Authentication(_messages.Message):
  r"""`Authentication` defines the authentication configuration for API
  methods provided by an API service. Example: name: calendar.googleapis.com
  authentication: providers: - id: google_calendar_auth jwks_uri:
  https://www.googleapis.com/oauth2/v1/certs issuer:
  https://securetoken.google.com rules: - selector: "*" requirements:
  provider_id: google_calendar_auth - selector: google.calendar.Delegate
  oauth: canonical_scopes: https://www.googleapis.com/auth/calendar.read

  Fields:
    providers: Defines a set of authentication providers that a service
      supports.
    rules: A list of authentication rules that apply to individual API
      methods. **NOTE:** All service configuration rules follow "last one
      wins" order.
  """

  providers = _messages.MessageField('AuthProvider', 1, repeated=True)
  rules = _messages.MessageField('AuthenticationRule', 2, repeated=True)


class AuthenticationRule(_messages.Message):
  r"""Authentication rules for the service. By default, if a method has any
  authentication requirements, every request must include a valid credential
  matching one of the requirements. It's an error to include more than one
  kind of credential in a single request. If a method doesn't have any auth
  requirements, request credentials will be ignored.

  Fields:
    allowWithoutCredential: If true, the service accepts API keys without any
      other credential. This flag only applies to HTTP and gRPC requests.
    oauth: The requirements for OAuth credentials.
    requirements: Requirements for additional authentication providers.
    selector: Selects the methods to which this rule applies. Refer to
      selector for syntax details.
  """

  allowWithoutCredential = _messages.BooleanField(1)
  oauth = _messages.MessageField('OAuthRequirements', 2)
  requirements = _messages.MessageField('AuthRequirement', 3, repeated=True)
  selector = _messages.StringField(4)


class Backend(_messages.Message):
  r"""`Backend` defines the backend configuration for a service.

  Fields:
    rules: A list of API backend rules that apply to individual API methods.
      **NOTE:** All service configuration rules follow "last one wins" order.
  """

  rules = _messages.MessageField('BackendRule', 1, repeated=True)


class BackendRule(_messages.Message):
  r"""A backend rule provides configuration for an individual API element.

  Enums:
    PathTranslationValueValuesEnum:

  Messages:
    OverridesByRequestProtocolValue: The map between request protocol and the
      backend address.

  Fields:
    address: The address of the API backend. The scheme is used to determine
      the backend protocol and security. The following schemes are accepted:
      SCHEME PROTOCOL SECURITY http:// HTTP None https:// HTTP TLS grpc://
      gRPC None grpcs:// gRPC TLS It is recommended to explicitly include a
      scheme. Leaving out the scheme may cause constrasting behaviors across
      platforms. If the port is unspecified, the default is: - 80 for schemes
      without TLS - 443 for schemes with TLS For HTTP backends, use protocol
      to specify the protocol version.
    deadline: The number of seconds to wait for a response from a request. The
      default varies based on the request protocol and deployment environment.
    disableAuth: When disable_auth is true, a JWT ID token won't be generated
      and the original "Authorization" HTTP header will be preserved. If the
      header is used to carry the original token and is expected by the
      backend, this field must be set to true to preserve the header.
    jwtAudience: The JWT audience is used when generating a JWT ID token for
      the backend. This ID token will be added in the HTTP "authorization"
      header, and sent to the backend.
    minDeadline: Deprecated, do not use.
    operationDeadline: The number of seconds to wait for the completion of a
      long running operation. The default is no deadline.
    overridesByRequestProtocol: The map between request protocol and the
      backend address.
    pathTranslation: A PathTranslationValueValuesEnum attribute.
    protocol: The protocol used for sending a request to the backend. The
      supported values are "http/1.1" and "h2". The default value is inferred
      from the scheme in the address field: SCHEME PROTOCOL http:// http/1.1
      https:// http/1.1 grpc:// h2 grpcs:// h2 For secure HTTP backends
      (https://) that support HTTP/2, set this field to "h2" for improved
      performance. Configuring this field to non-default values is only
      supported for secure HTTP backends. This field will be ignored for all
      other backends. See https://www.iana.org/assignments/tls-extensiontype-
      values/tls-extensiontype-values.xhtml#alpn-protocol-ids for more details
      on the supported values.
    selector: Selects the methods to which this rule applies. Refer to
      selector for syntax details.
  """

  class PathTranslationValueValuesEnum(_messages.Enum):
    r"""PathTranslationValueValuesEnum enum type.

    Values:
      PATH_TRANSLATION_UNSPECIFIED: <no description>
      CONSTANT_ADDRESS: Use the backend address as-is, with no modification to
        the path. If the URL pattern contains variables, the variable names
        and values will be appended to the query string. If a query string
        parameter and a URL pattern variable have the same name, this may
        result in duplicate keys in the query string. # Examples Given the
        following operation config: Method path: /api/company/{cid}/user/{uid}
        Backend address: https://example.cloudfunctions.net/getUser Requests
        to the following request paths will call the backend at the translated
        path: Request path: /api/company/widgetworks/user/johndoe Translated:
        https://example.cloudfunctions.net/getUser?cid=widgetworks&uid=johndoe
        Request path: /api/company/widgetworks/user/johndoe?timezone=EST
        Translated: https://example.cloudfunctions.net/getUser?timezone=EST&ci
        d=widgetworks&uid=johndoe
      APPEND_PATH_TO_ADDRESS: The request path will be appended to the backend
        address. # Examples Given the following operation config: Method path:
        /api/company/{cid}/user/{uid} Backend address:
        https://example.appspot.com Requests to the following request paths
        will call the backend at the translated path: Request path:
        /api/company/widgetworks/user/johndoe Translated:
        https://example.appspot.com/api/company/widgetworks/user/johndoe
        Request path: /api/company/widgetworks/user/johndoe?timezone=EST
        Translated: https://example.appspot.com/api/company/widgetworks/user/j
        ohndoe?timezone=EST
    """
    PATH_TRANSLATION_UNSPECIFIED = 0
    CONSTANT_ADDRESS = 1
    APPEND_PATH_TO_ADDRESS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class OverridesByRequestProtocolValue(_messages.Message):
    r"""The map between request protocol and the backend address.

    Messages:
      AdditionalProperty: An additional property for a
        OverridesByRequestProtocolValue object.

    Fields:
      additionalProperties: Additional properties of type
        OverridesByRequestProtocolValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a OverridesByRequestProtocolValue object.

      Fields:
        key: Name of the additional property.
        value: A BackendRule attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('BackendRule', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  address = _messages.StringField(1)
  deadline = _messages.FloatField(2)
  disableAuth = _messages.BooleanField(3)
  jwtAudience = _messages.StringField(4)
  minDeadline = _messages.FloatField(5)
  operationDeadline = _messages.FloatField(6)
  overridesByRequestProtocol = _messages.MessageField('OverridesByRequestProtocolValue', 7)
  pathTranslation = _messages.EnumField('PathTranslationValueValuesEnum', 8)
  protocol = _messages.StringField(9)
  selector = _messages.StringField(10)


class Billing(_messages.Message):
  r"""Billing related configuration of the service. The following example
  shows how to configure monitored resources and metrics for billing,
  `consumer_destinations` is the only supported destination and the monitored
  resources need at least one label key `cloud.googleapis.com/location` to
  indicate the location of the billing usage, using different monitored
  resources between monitoring and billing is recommended so they can be
  evolved independently: monitored_resources: - type:
  library.googleapis.com/billing_branch labels: - key:
  cloud.googleapis.com/location description: | Predefined label to support
  billing location restriction. - key: city description: | Custom label to
  define the city where the library branch is located in. - key: name
  description: Custom label to define the name of the library branch. metrics:
  - name: library.googleapis.com/book/borrowed_count metric_kind: DELTA
  value_type: INT64 unit: "1" billing: consumer_destinations: -
  monitored_resource: library.googleapis.com/billing_branch metrics: -
  library.googleapis.com/book/borrowed_count

  Fields:
    consumerDestinations: Billing configurations for sending metrics to the
      consumer project. There can be multiple consumer destinations per
      service, each one must have a different monitored resource type. A
      metric can be used in at most one consumer destination.
  """

  consumerDestinations = _messages.MessageField('BillingDestination', 1, repeated=True)


class BillingDestination(_messages.Message):
  r"""Configuration of a specific billing destination (Currently only support
  bill against consumer project).

  Fields:
    metrics: Names of the metrics to report to this billing destination. Each
      name must be defined in Service.metrics section.
    monitoredResource: The monitored resource type. The type must be defined
      in Service.monitored_resources section.
  """

  metrics = _messages.StringField(1, repeated=True)
  monitoredResource = _messages.StringField(2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class ChangeReport(_messages.Message):
  r"""Change report associated with a particular service configuration. It
  contains a list of ConfigChanges based on the comparison between two service
  configurations.

  Fields:
    configChanges: List of changes between two service configurations. The
      changes will be alphabetically sorted based on the identifier of each
      change. A ConfigChange identifier is a dot separated path to the
      configuration. Example:
      visibility.rules[selector='LibraryService.CreateBook'].restriction
  """

  configChanges = _messages.MessageField('ConfigChange', 1, repeated=True)


class ClientLibrarySettings(_messages.Message):
  r"""Details about how and where to publish client libraries.

  Enums:
    LaunchStageValueValuesEnum: Launch stage of this version of the API.

  Fields:
    cppSettings: Settings for C++ client libraries.
    dotnetSettings: Settings for .NET client libraries.
    goSettings: Settings for Go client libraries.
    javaSettings: Settings for legacy Java features, supported in the Service
      YAML.
    launchStage: Launch stage of this version of the API.
    nodeSettings: Settings for Node client libraries.
    phpSettings: Settings for PHP client libraries.
    pythonSettings: Settings for Python client libraries.
    restNumericEnums: When using transport=rest, the client request will
      encode enums as numbers rather than strings.
    rubySettings: Settings for Ruby client libraries.
    version: Version of the API to apply these settings to. This is the full
      protobuf package for the API, ending in the version element. Examples:
      "google.cloud.speech.v1" and "google.spanner.admin.database.v1".
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Launch stage of this version of the API.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  cppSettings = _messages.MessageField('CppSettings', 1)
  dotnetSettings = _messages.MessageField('DotnetSettings', 2)
  goSettings = _messages.MessageField('GoSettings', 3)
  javaSettings = _messages.MessageField('JavaSettings', 4)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 5)
  nodeSettings = _messages.MessageField('NodeSettings', 6)
  phpSettings = _messages.MessageField('PhpSettings', 7)
  pythonSettings = _messages.MessageField('PythonSettings', 8)
  restNumericEnums = _messages.BooleanField(9)
  rubySettings = _messages.MessageField('RubySettings', 10)
  version = _messages.StringField(11)


class CommonLanguageSettings(_messages.Message):
  r"""Required information for every language.

  Enums:
    DestinationsValueListEntryValuesEnum:

  Fields:
    destinations: The destination where API teams want this client library to
      be published.
    referenceDocsUri: Link to automatically generated reference documentation.
      Example: https://cloud.google.com/nodejs/docs/reference/asset/latest
  """

  class DestinationsValueListEntryValuesEnum(_messages.Enum):
    r"""DestinationsValueListEntryValuesEnum enum type.

    Values:
      CLIENT_LIBRARY_DESTINATION_UNSPECIFIED: Client libraries will neither be
        generated nor published to package managers.
      GITHUB: Generate the client library in a repo under
        github.com/googleapis, but don't publish it to package managers.
      PACKAGE_MANAGER: Publish the library to package managers like nuget.org
        and npmjs.com.
    """
    CLIENT_LIBRARY_DESTINATION_UNSPECIFIED = 0
    GITHUB = 1
    PACKAGE_MANAGER = 2

  destinations = _messages.EnumField('DestinationsValueListEntryValuesEnum', 1, repeated=True)
  referenceDocsUri = _messages.StringField(2)


class CompositeOperationMetadata(_messages.Message):
  r"""Metadata for composite operations.

  Messages:
    OriginalRequestValue: Original request that triggered this operation.
    ResponseFieldMasksValue: Defines which part of the response a child
      operation will contribute. Each key of the map is the name of a child
      operation. Each value is a field mask that identifies what that child
      operation contributes to the response, for example, "quota_settings",
      "visibility_settings", etc.

  Fields:
    childOperations: The child operations. The details of the asynchronous
      child operations are stored in a separate row and not in this metadata.
      Only the operation name is stored here.
    originalRequest: Original request that triggered this operation.
    persisted: Indicates whether the requested state change has been
      persisted. Once this field is set, it is guaranteed to propagate to all
      backends eventually, but it may not be visible immediately. Clients that
      are not concerned with waiting on propagation can stop polling the
      operation once the persisted field is set
    responseFieldMasks: Defines which part of the response a child operation
      will contribute. Each key of the map is the name of a child operation.
      Each value is a field mask that identifies what that child operation
      contributes to the response, for example, "quota_settings",
      "visibility_settings", etc.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class OriginalRequestValue(_messages.Message):
    r"""Original request that triggered this operation.

    Messages:
      AdditionalProperty: An additional property for a OriginalRequestValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a OriginalRequestValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseFieldMasksValue(_messages.Message):
    r"""Defines which part of the response a child operation will contribute.
    Each key of the map is the name of a child operation. Each value is a
    field mask that identifies what that child operation contributes to the
    response, for example, "quota_settings", "visibility_settings", etc.

    Messages:
      AdditionalProperty: An additional property for a ResponseFieldMasksValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ResponseFieldMasksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseFieldMasksValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  childOperations = _messages.MessageField('Operation', 1, repeated=True)
  originalRequest = _messages.MessageField('OriginalRequestValue', 2)
  persisted = _messages.BooleanField(3)
  responseFieldMasks = _messages.MessageField('ResponseFieldMasksValue', 4)


class ConfigChange(_messages.Message):
  r"""Output generated from semantically comparing two versions of a service
  configuration. Includes detailed information about a field that have changed
  with applicable advice about potential consequences for the change, such as
  backwards-incompatibility.

  Enums:
    ChangeTypeValueValuesEnum: The type for this change, either ADDED,
      REMOVED, or MODIFIED.

  Fields:
    advices: Collection of advice provided for this change, useful for
      determining the possible impact of this change.
    changeType: The type for this change, either ADDED, REMOVED, or MODIFIED.
    element: Object hierarchy path to the change, with levels separated by a
      '.' character. For repeated fields, an applicable unique identifier
      field is used for the index (usually selector, name, or id). For maps,
      the term 'key' is used. If the field has no unique identifier, the
      numeric index is used. Examples: - visibility.rules[selector=="google.Li
      braryService.ListBooks"].restriction -
      quota.metric_rules[selector=="google"].metric_costs[key=="reads"].value
      - logging.producer_destinations[0]
    newValue: Value of the changed object in the new Service configuration, in
      JSON format. This field will not be populated if ChangeType == REMOVED.
    oldValue: Value of the changed object in the old Service configuration, in
      JSON format. This field will not be populated if ChangeType == ADDED.
  """

  class ChangeTypeValueValuesEnum(_messages.Enum):
    r"""The type for this change, either ADDED, REMOVED, or MODIFIED.

    Values:
      CHANGE_TYPE_UNSPECIFIED: No value was provided.
      ADDED: The changed object exists in the 'new' service configuration, but
        not in the 'old' service configuration.
      REMOVED: The changed object exists in the 'old' service configuration,
        but not in the 'new' service configuration.
      MODIFIED: The changed object exists in both service configurations, but
        its value is different.
    """
    CHANGE_TYPE_UNSPECIFIED = 0
    ADDED = 1
    REMOVED = 2
    MODIFIED = 3

  advices = _messages.MessageField('Advice', 1, repeated=True)
  changeType = _messages.EnumField('ChangeTypeValueValuesEnum', 2)
  element = _messages.StringField(3)
  newValue = _messages.StringField(4)
  oldValue = _messages.StringField(5)


class ConfigFile(_messages.Message):
  r"""Generic specification of a source configuration file

  Enums:
    FileTypeValueValuesEnum: The type of configuration file this represents.

  Fields:
    fileContents: The bytes that constitute the file.
    filePath: The file name of the configuration file (full or relative path).
    fileType: The type of configuration file this represents.
  """

  class FileTypeValueValuesEnum(_messages.Enum):
    r"""The type of configuration file this represents.

    Values:
      FILE_TYPE_UNSPECIFIED: Unknown file type.
      SERVICE_CONFIG_YAML: YAML-specification of service.
      OPEN_API_JSON: OpenAPI specification, serialized in JSON.
      OPEN_API_YAML: OpenAPI specification, serialized in YAML.
      FILE_DESCRIPTOR_SET_PROTO: FileDescriptorSet, generated by protoc. To
        generate, use protoc with imports and source info included. For an
        example test.proto file, the following command would put the value in
        a new file named out.pb. $protoc --include_imports
        --include_source_info test.proto -o out.pb
      PROTO_FILE: Uncompiled Proto file. Used for storage and display purposes
        only, currently server-side compilation is not supported. Should match
        the inputs to 'protoc' command used to generated
        FILE_DESCRIPTOR_SET_PROTO. A file of this type can only be included if
        at least one file of type FILE_DESCRIPTOR_SET_PROTO is included.
    """
    FILE_TYPE_UNSPECIFIED = 0
    SERVICE_CONFIG_YAML = 1
    OPEN_API_JSON = 2
    OPEN_API_YAML = 3
    FILE_DESCRIPTOR_SET_PROTO = 4
    PROTO_FILE = 5

  fileContents = _messages.BytesField(1)
  filePath = _messages.StringField(2)
  fileType = _messages.EnumField('FileTypeValueValuesEnum', 3)


class ConfigRef(_messages.Message):
  r"""Represents a service configuration with its name and id.

  Fields:
    name: Resource name of a service config. It must have the following
      format: "services/{service name}/configs/{config id}".
  """

  name = _messages.StringField(1)


class ConfigSource(_messages.Message):
  r"""Represents a source file which is used to generate the service
  configuration defined by `google.api.Service`.

  Fields:
    files: Set of source configuration files that are used to generate a
      service configuration (`google.api.Service`).
    id: A unique ID for a specific instance of this message, typically
      assigned by the client for tracking purpose. If empty, the server may
      choose to generate one instead.
  """

  files = _messages.MessageField('ConfigFile', 1, repeated=True)
  id = _messages.StringField(2)


class Context(_messages.Message):
  r"""`Context` defines which contexts an API requests. Example: context:
  rules: - selector: "*" requested: - google.rpc.context.ProjectContext -
  google.rpc.context.OriginContext The above specifies that all methods in the
  API request `google.rpc.context.ProjectContext` and
  `google.rpc.context.OriginContext`. Available context types are defined in
  package `google.rpc.context`. This also provides mechanism to allowlist any
  protobuf message extension that can be sent in grpc metadata using "x-goog-
  ext--bin" and "x-goog-ext--jspb" format. For example, list any service
  specific protobuf types that can appear in grpc metadata as follows in your
  yaml file: Example: context: rules: - selector:
  "google.example.library.v1.LibraryService.CreateBook"
  allowed_request_extensions: - google.foo.v1.NewExtension
  allowed_response_extensions: - google.foo.v1.NewExtension You can also
  specify extension ID instead of fully qualified extension name here.

  Fields:
    rules: A list of RPC context rules that apply to individual API methods.
      **NOTE:** All service configuration rules follow "last one wins" order.
  """

  rules = _messages.MessageField('ContextRule', 1, repeated=True)


class ContextRule(_messages.Message):
  r"""A context rule provides information about the context for an individual
  API element.

  Fields:
    allowedRequestExtensions: A list of full type names or extension IDs of
      extensions allowed in grpc side channel from client to backend.
    allowedResponseExtensions: A list of full type names or extension IDs of
      extensions allowed in grpc side channel from backend to client.
    provided: A list of full type names of provided contexts.
    requested: A list of full type names of requested contexts.
    selector: Selects the methods to which this rule applies. Refer to
      selector for syntax details.
  """

  allowedRequestExtensions = _messages.StringField(1, repeated=True)
  allowedResponseExtensions = _messages.StringField(2, repeated=True)
  provided = _messages.StringField(3, repeated=True)
  requested = _messages.StringField(4, repeated=True)
  selector = _messages.StringField(5)


class Control(_messages.Message):
  r"""Selects and configures the service controller used by the service.
  Example: control: environment: servicecontrol.googleapis.com

  Fields:
    environment: The service controller environment to use. If empty, no
      control plane feature (like quota and billing) will be enabled. The
      recommended value for most services is servicecontrol.googleapis.com
    methodPolicies: Defines policies applying to the API methods of the
      service.
  """

  environment = _messages.StringField(1)
  methodPolicies = _messages.MessageField('MethodPolicy', 2, repeated=True)


class CppSettings(_messages.Message):
  r"""Settings for C++ client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class CustomError(_messages.Message):
  r"""Customize service error responses. For example, list any service
  specific protobuf types that can appear in error detail lists of error
  responses. Example: custom_error: types: - google.foo.v1.CustomError -
  google.foo.v1.AnotherError

  Fields:
    rules: The list of custom error rules that apply to individual API
      messages. **NOTE:** All service configuration rules follow "last one
      wins" order.
    types: The list of custom error detail types, e.g.
      'google.foo.v1.CustomError'.
  """

  rules = _messages.MessageField('CustomErrorRule', 1, repeated=True)
  types = _messages.StringField(2, repeated=True)


class CustomErrorRule(_messages.Message):
  r"""A custom error rule.

  Fields:
    isErrorType: Mark this message as possible payload in error response.
      Otherwise, objects of this type will be filtered when they appear in
      error payload.
    selector: Selects messages to which this rule applies. Refer to selector
      for syntax details.
  """

  isErrorType = _messages.BooleanField(1)
  selector = _messages.StringField(2)


class CustomHttpPattern(_messages.Message):
  r"""A custom pattern is used for defining custom HTTP verb.

  Fields:
    kind: The name of this custom HTTP verb.
    path: The path matched by this custom verb.
  """

  kind = _messages.StringField(1)
  path = _messages.StringField(2)


class CustomerSettings(_messages.Message):
  r"""Settings that control how a customer (organization or folder) uses a
  service.

  Fields:
    customerId: ID for the customer that consumes the service (see above).
      Customer id is always in the format of a Gaia id.
    quotaSettings: Settings that control how much or how fast the service can
      be used by the consumer projects under the organization or folder
      collectively.
    serviceName: The name of the service. See the `ServiceManager` overview
      for naming requirements.
  """

  customerId = _messages.StringField(1)
  quotaSettings = _messages.MessageField('QuotaSettings', 2)
  serviceName = _messages.StringField(3)


class DeleteServiceStrategy(_messages.Message):
  r"""Strategy used to delete a service. This strategy is a placeholder only
  used by the system generated rollout to delete a service.
  """



class Diagnostic(_messages.Message):
  r"""Represents a diagnostic message (error or warning)

  Enums:
    KindValueValuesEnum: The kind of diagnostic information provided.

  Fields:
    kind: The kind of diagnostic information provided.
    location: File name and line number of the error or warning.
    message: Message describing the error or warning.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""The kind of diagnostic information provided.

    Values:
      WARNING: Warnings and errors
      ERROR: Only errors
    """
    WARNING = 0
    ERROR = 1

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  location = _messages.StringField(2)
  message = _messages.StringField(3)


class Documentation(_messages.Message):
  r"""`Documentation` provides the information for describing a service.
  Example: documentation: summary: > The Google Calendar API gives access to
  most calendar features. pages: - name: Overview content: (== include
  google/foo/overview.md ==) - name: Tutorial content: (== include
  google/foo/tutorial.md ==) subpages: - name: Java content: (== include
  google/foo/tutorial_java.md ==) rules: - selector:
  google.calendar.Calendar.Get description: > ... - selector:
  google.calendar.Calendar.Put description: > ... Documentation is provided in
  markdown syntax. In addition to standard markdown features, definition
  lists, tables and fenced code blocks are supported. Section headers can be
  provided and are interpreted relative to the section nesting of the context
  where a documentation fragment is embedded. Documentation from the IDL is
  merged with documentation defined via the config at normalization time,
  where documentation provided by config rules overrides IDL provided. A
  number of constructs specific to the API platform are supported in
  documentation text. In order to reference a proto element, the following
  notation can be used: [fully.qualified.proto.name][] To override the display
  text used for the link, this can be used: [display
  text][fully.qualified.proto.name] Text can be excluded from doc using the
  following notation: (-- internal comment --) A few directives are available
  in documentation. Note that directives must appear on a single line to be
  properly identified. The `include` directive includes a markdown file from
  an external source: (== include path/to/file ==) The `resource_for`
  directive marks a message to be the resource of a collection in REST view.
  If it is not specified, tools attempt to infer the resource from the
  operations in a collection: (== resource_for v1.shelves.books ==) The
  directive `suppress_warning` does not directly affect documentation and is
  documented together with service config validation.

  Fields:
    documentationRootUrl: The URL to the root of documentation.
    overview: Declares a single overview page. For example: documentation:
      summary: ... overview: (== include overview.md ==) This is a shortcut
      for the following declaration (using pages style): documentation:
      summary: ... pages: - name: Overview content: (== include overview.md
      ==) Note: you cannot specify both `overview` field and `pages` field.
    pages: The top level pages for the documentation set.
    rules: A list of documentation rules that apply to individual API
      elements. **NOTE:** All service configuration rules follow "last one
      wins" order.
    sectionOverrides: Specifies section and content to override boilerplate
      content provided by go/api-docgen. Currently overrides following
      sections: 1. rest.service.client_libraries
    serviceRootUrl: Specifies the service root url if the default one (the
      service name from the yaml file) is not suitable. This can be seen in
      any fully specified service urls as well as sections that show a base
      that other urls are relative to.
    summary: A short description of what the service does. The summary must be
      plain text. It becomes the overview of the service displayed in Google
      Cloud Console. NOTE: This field is equivalent to the standard field
      `description`.
  """

  documentationRootUrl = _messages.StringField(1)
  overview = _messages.StringField(2)
  pages = _messages.MessageField('Page', 3, repeated=True)
  rules = _messages.MessageField('DocumentationRule', 4, repeated=True)
  sectionOverrides = _messages.MessageField('Page', 5, repeated=True)
  serviceRootUrl = _messages.StringField(6)
  summary = _messages.StringField(7)


class DocumentationRule(_messages.Message):
  r"""A documentation rule provides information about individual API elements.

  Fields:
    deprecationDescription: Deprecation description of the selected
      element(s). It can be provided if an element is marked as `deprecated`.
    description: Description of the selected proto element (e.g. a message, a
      method, a 'service' definition, or a field). Defaults to leading &
      trailing comments taken from the proto source definition of the proto
      element.
    disableReplacementWords: String of comma or space separated case-sensitive
      words for which method/field name replacement will be disabled by
      go/api-docgen.
    selector: The selector is a comma-separated list of patterns for any
      element such as a method, a field, an enum value. Each pattern is a
      qualified name of the element which may end in "*", indicating a
      wildcard. Wildcards are only allowed at the end and for a whole
      component of the qualified name, i.e. "foo.*" is ok, but not "foo.b*" or
      "foo.*.bar". A wildcard will match one or more components. To specify a
      default for all applicable elements, the whole pattern "*" is used.
  """

  deprecationDescription = _messages.StringField(1)
  description = _messages.StringField(2)
  disableReplacementWords = _messages.StringField(3)
  selector = _messages.StringField(4)


class DotnetSettings(_messages.Message):
  r"""Settings for Dotnet client libraries.

  Messages:
    RenamedResourcesValue: Map from full resource types to the effective short
      name for the resource. This is used when otherwise resource named from
      different services would cause naming collisions. Example entry:
      "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
    RenamedServicesValue: Map from original service names to renamed versions.
      This is used when the default generated types would cause a naming
      conflict. (Neither name is fully-qualified.) Example: Subscriber to
      SubscriberServiceApi.

  Fields:
    common: Some settings.
    forcedNamespaceAliases: Namespaces which must be aliased in snippets due
      to a known (but non-generator-predictable) naming collision
    handwrittenSignatures: Method signatures (in the form
      "service.method(signature)") which are provided separately, so shouldn't
      be generated. Snippets *calling* these methods are still generated,
      however.
    ignoredResources: List of full resource types to ignore during generation.
      This is typically used for API-specific Location resources, which should
      be handled by the generator as if they were actually the common Location
      resources. Example entry: "documentai.googleapis.com/Location"
    renamedResources: Map from full resource types to the effective short name
      for the resource. This is used when otherwise resource named from
      different services would cause naming collisions. Example entry:
      "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
    renamedServices: Map from original service names to renamed versions. This
      is used when the default generated types would cause a naming conflict.
      (Neither name is fully-qualified.) Example: Subscriber to
      SubscriberServiceApi.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RenamedResourcesValue(_messages.Message):
    r"""Map from full resource types to the effective short name for the
    resource. This is used when otherwise resource named from different
    services would cause naming collisions. Example entry:
    "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"

    Messages:
      AdditionalProperty: An additional property for a RenamedResourcesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        RenamedResourcesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RenamedResourcesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RenamedServicesValue(_messages.Message):
    r"""Map from original service names to renamed versions. This is used when
    the default generated types would cause a naming conflict. (Neither name
    is fully-qualified.) Example: Subscriber to SubscriberServiceApi.

    Messages:
      AdditionalProperty: An additional property for a RenamedServicesValue
        object.

    Fields:
      additionalProperties: Additional properties of type RenamedServicesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RenamedServicesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  common = _messages.MessageField('CommonLanguageSettings', 1)
  forcedNamespaceAliases = _messages.StringField(2, repeated=True)
  handwrittenSignatures = _messages.StringField(3, repeated=True)
  ignoredResources = _messages.StringField(4, repeated=True)
  renamedResources = _messages.MessageField('RenamedResourcesValue', 5)
  renamedServices = _messages.MessageField('RenamedServicesValue', 6)


class EffectiveQuotaGroup(_messages.Message):
  r"""An effective quota group contains both the metadata for a quota group as
  derived from the service config, and the effective limits in that group as
  calculated from producer and consumer overrides together with service
  defaults.

  Enums:
    BillingInteractionValueValuesEnum:

  Fields:
    baseGroup: The service configuration for this quota group, minus the quota
      limits, which are replaced by the effective limits below.
    billingInteraction: A BillingInteractionValueValuesEnum attribute.
    quotas: The usage and limit information for each limit within this quota
      group.
  """

  class BillingInteractionValueValuesEnum(_messages.Enum):
    r"""BillingInteractionValueValuesEnum enum type.

    Values:
      BILLING_INTERACTION_UNSPECIFIED: The interaction between this quota
        group and the project billing status is unspecified.
      NONBILLABLE_ONLY: This quota group is enforced only when the consumer
        project is not billable.
      BILLABLE_ONLY: This quota group is enforced only when the consumer
        project is billable.
      ANY_BILLING_STATUS: This quota group is enforced regardless of the
        consumer project's billing status.
    """
    BILLING_INTERACTION_UNSPECIFIED = 0
    NONBILLABLE_ONLY = 1
    BILLABLE_ONLY = 2
    ANY_BILLING_STATUS = 3

  baseGroup = _messages.MessageField('QuotaGroup', 1)
  billingInteraction = _messages.EnumField('BillingInteractionValueValuesEnum', 2)
  quotas = _messages.MessageField('QuotaInfo', 3, repeated=True)


class EffectiveQuotaLimit(_messages.Message):
  r"""An effective quota limit contains the metadata for a quota limit as
  derived from the service config, together with fields that describe the
  effective limit value and what overrides can be applied to it.

  Fields:
    baseLimit: The service's configuration for this quota limit.
    effectiveLimit: The effective limit value, based on the stored producer
      and consumer overrides and the service defaults.
    key: The key used to identify this limit when applying overrides. The
      consumer_overrides and producer_overrides maps are keyed by strings of
      the form "QuotaGroupName/QuotaLimitName".
    maxConsumerOverrideAllowed: The maximum override value that a consumer may
      specify.
  """

  baseLimit = _messages.MessageField('QuotaLimit', 1)
  effectiveLimit = _messages.IntegerField(2)
  key = _messages.StringField(3)
  maxConsumerOverrideAllowed = _messages.IntegerField(4)


class EffectiveQuotaLimit2(_messages.Message):
  r"""An effective quota limit contains the metadata for a quota limit as
  derived from the service config, together with fields that describe the
  effective limit value and what overrides can be applied to it. This is used
  only for quota limits that are grouped by metrics instead of quota groups.

  Fields:
    allowAdminOverrides: whether admin overrides are allowed on this limit.
      Admin overrides are allowed if this limit is an organization level one,
      or if this limit is a project level one and there is an identical
      organizational limit.
    baseLimit: The service's configuration for this quota limit.
    defaultLimit: The default quota limit based on the consumer's reputation
      and billing status. Region and zone default limits are kept.
    quotaBuckets: Effective quota limit, maximum override allowed, and usage
      for each quota bucket.
  """

  allowAdminOverrides = _messages.BooleanField(1)
  baseLimit = _messages.MessageField('QuotaLimit', 2)
  defaultLimit = _messages.MessageField('QuotaLimit', 3)
  quotaBuckets = _messages.MessageField('QuotaBucket', 4, repeated=True)


class EffectiveQuotasForMetric(_messages.Message):
  r"""Effective quotas for a metric. It contains both the metadata for the
  metric as defined in the service config, and the effective limits for quota
  limits defined on the metric as calculated from service default, producer
  and consumer overrides, and adjusted by the reputation tier of the user.
  This is used only for quota limits that are grouped by metrics instead of
  quota groups.

  Messages:
    EffectiveLimitsValue: Effective limit values for all quota limits defined
      on the metric. The keys of the map are the name of the quota limits.

  Fields:
    effectiveLimits: Effective limit values for all quota limits defined on
      the metric. The keys of the map are the name of the quota limits.
    metric: The metric descriptor in service config.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EffectiveLimitsValue(_messages.Message):
    r"""Effective limit values for all quota limits defined on the metric. The
    keys of the map are the name of the quota limits.

    Messages:
      AdditionalProperty: An additional property for a EffectiveLimitsValue
        object.

    Fields:
      additionalProperties: Additional properties of type EffectiveLimitsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EffectiveLimitsValue object.

      Fields:
        key: Name of the additional property.
        value: A EffectiveQuotaLimit2 attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('EffectiveQuotaLimit2', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  effectiveLimits = _messages.MessageField('EffectiveLimitsValue', 1)
  metric = _messages.MessageField('MetricDescriptor', 2)


class EnableServiceRequest(_messages.Message):
  r"""Request message for EnableService method.

  Fields:
    consumerId: Required. The identity of consumer resource which service
      enablement will be applied to. The Google Service Management
      implementation accepts the following forms: - "project:" Note: this is
      made compatible with google.api.servicecontrol.v1.Operation.consumer_id.
  """

  consumerId = _messages.StringField(1)


class EnableServiceResponse(_messages.Message):
  r"""Operation payload for EnableService method."""


class Endpoint(_messages.Message):
  r"""`Endpoint` describes a network address of a service that serves a set of
  APIs. It is commonly known as a service endpoint. A service may expose any
  number of service endpoints, and all service endpoints share the same
  service definition, such as quota limits and monitoring metrics. Example:
  type: google.api.Service name: library-example.googleapis.com endpoints: #
  Declares network address `https://library-example.googleapis.com` # for
  service `library-example.googleapis.com`. The `https` scheme # is implicit
  for all service endpoints. Other schemes may be # supported in the future. -
  name: library-example.googleapis.com allow_cors: false - name: content-
  staging-library-example.googleapis.com # Allows HTTP OPTIONS calls to be
  passed to the API frontend, for it # to decide whether the subsequent cross-
  origin request is allowed # to proceed. allow_cors: true

  Fields:
    aliases: Unimplemented. Dot not use. DEPRECATED: This field is no longer
      supported. Instead of using aliases, please specify multiple
      google.api.Endpoint for each of the intended aliases. Additional names
      that this endpoint will be hosted on.
    allowCors: Allowing [CORS](https://en.wikipedia.org/wiki/Cross-
      origin_resource_sharing), aka cross-domain traffic, would allow the
      backends served from this endpoint to receive and respond to HTTP
      OPTIONS requests. The response will be used by the browser to determine
      whether the subsequent cross-origin request is allowed to proceed.
    name: The canonical name of this endpoint.
    target: The specification of an Internet routable address of API frontend
      that will handle requests to this [API
      Endpoint](https://cloud.google.com/apis/design/glossary). It should be
      either a valid IPv4 address or a fully-qualified domain name. For
      example, "*******" or "myservice.appspot.com".
  """

  aliases = _messages.StringField(1, repeated=True)
  allowCors = _messages.BooleanField(2)
  name = _messages.StringField(3)
  target = _messages.StringField(4)


class Enum(_messages.Message):
  r"""Enum type definition.

  Enums:
    SyntaxValueValuesEnum: The source syntax.

  Fields:
    edition: The source edition string, only valid when syntax is
      SYNTAX_EDITIONS.
    enumvalue: Enum value definitions.
    name: Enum type name.
    options: Protocol buffer options.
    sourceContext: The source context.
    syntax: The source syntax.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
      SYNTAX_EDITIONS: Syntax `editions`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1
    SYNTAX_EDITIONS = 2

  edition = _messages.StringField(1)
  enumvalue = _messages.MessageField('EnumValue', 2, repeated=True)
  name = _messages.StringField(3)
  options = _messages.MessageField('Option', 4, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 5)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 6)


class EnumValue(_messages.Message):
  r"""Enum value definition.

  Fields:
    name: Enum value name.
    number: Enum value number.
    options: Protocol buffer options.
  """

  name = _messages.StringField(1)
  number = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  options = _messages.MessageField('Option', 3, repeated=True)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Field(_messages.Message):
  r"""A single field of a message type.

  Enums:
    CardinalityValueValuesEnum: The field cardinality.
    KindValueValuesEnum: The field type.

  Fields:
    cardinality: The field cardinality.
    defaultValue: The string value of the default value of this field. Proto2
      syntax only.
    jsonName: The field JSON name.
    kind: The field type.
    name: The field name.
    number: The field number.
    oneofIndex: The index of the field type in `Type.oneofs`, for message or
      enumeration types. The first type has index 1; zero means the type is
      not in the list.
    options: The protocol buffer options.
    packed: Whether to use alternative packed wire representation.
    typeUrl: The field type URL, without the scheme, for message or
      enumeration types. Example:
      `"type.googleapis.com/google.protobuf.Timestamp"`.
  """

  class CardinalityValueValuesEnum(_messages.Enum):
    r"""The field cardinality.

    Values:
      CARDINALITY_UNKNOWN: For fields with unknown cardinality.
      CARDINALITY_OPTIONAL: For optional fields.
      CARDINALITY_REQUIRED: For required fields. Proto2 syntax only.
      CARDINALITY_REPEATED: For repeated fields.
    """
    CARDINALITY_UNKNOWN = 0
    CARDINALITY_OPTIONAL = 1
    CARDINALITY_REQUIRED = 2
    CARDINALITY_REPEATED = 3

  class KindValueValuesEnum(_messages.Enum):
    r"""The field type.

    Values:
      TYPE_UNKNOWN: Field type unknown.
      TYPE_DOUBLE: Field type double.
      TYPE_FLOAT: Field type float.
      TYPE_INT64: Field type int64.
      TYPE_UINT64: Field type uint64.
      TYPE_INT32: Field type int32.
      TYPE_FIXED64: Field type fixed64.
      TYPE_FIXED32: Field type fixed32.
      TYPE_BOOL: Field type bool.
      TYPE_STRING: Field type string.
      TYPE_GROUP: Field type group. Proto2 syntax only, and deprecated.
      TYPE_MESSAGE: Field type message.
      TYPE_BYTES: Field type bytes.
      TYPE_UINT32: Field type uint32.
      TYPE_ENUM: Field type enum.
      TYPE_SFIXED32: Field type sfixed32.
      TYPE_SFIXED64: Field type sfixed64.
      TYPE_SINT32: Field type sint32.
      TYPE_SINT64: Field type sint64.
    """
    TYPE_UNKNOWN = 0
    TYPE_DOUBLE = 1
    TYPE_FLOAT = 2
    TYPE_INT64 = 3
    TYPE_UINT64 = 4
    TYPE_INT32 = 5
    TYPE_FIXED64 = 6
    TYPE_FIXED32 = 7
    TYPE_BOOL = 8
    TYPE_STRING = 9
    TYPE_GROUP = 10
    TYPE_MESSAGE = 11
    TYPE_BYTES = 12
    TYPE_UINT32 = 13
    TYPE_ENUM = 14
    TYPE_SFIXED32 = 15
    TYPE_SFIXED64 = 16
    TYPE_SINT32 = 17
    TYPE_SINT64 = 18

  cardinality = _messages.EnumField('CardinalityValueValuesEnum', 1)
  defaultValue = _messages.StringField(2)
  jsonName = _messages.StringField(3)
  kind = _messages.EnumField('KindValueValuesEnum', 4)
  name = _messages.StringField(5)
  number = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  oneofIndex = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  options = _messages.MessageField('Option', 8, repeated=True)
  packed = _messages.BooleanField(9)
  typeUrl = _messages.StringField(10)


class FieldPolicy(_messages.Message):
  r"""Google API Policy Annotation This message defines a simple API policy
  annotation that can be used to annotate API request and response message
  fields with applicable policies. One field may have multiple applicable
  policies that must all be satisfied before a request can be processed. This
  policy annotation is used to generate the overall policy that will be used
  for automatic runtime policy enforcement and documentation generation.

  Fields:
    resourcePermission: Specifies the required permission(s) for the resource
      referred to by the field. It requires the field contains a valid
      resource reference, and the request must pass the permission checks to
      proceed. For example, "resourcemanager.projects.get".
    resourceType: Specifies the resource type for the resource referred to by
      the field.
    selector: Selects one or more request or response message fields to apply
      this `FieldPolicy`. When a `FieldPolicy` is used in proto annotation,
      the selector must be left as empty. The service config generator will
      automatically fill the correct value. When a `FieldPolicy` is used in
      service config, the selector must be a comma-separated string with valid
      request or response field paths, such as "foo.bar" or "foo.bar,foo.baz".
  """

  resourcePermission = _messages.StringField(1)
  resourceType = _messages.StringField(2)
  selector = _messages.StringField(3)


class FlowErrorDetails(_messages.Message):
  r"""Encapsulation of flow-specific error details for debugging. Used as a
  details field on an error Status, not intended for external use.

  Fields:
    exceptionType: The type of exception (as a class name).
    flowStepId: The step that failed.
  """

  exceptionType = _messages.StringField(1)
  flowStepId = _messages.StringField(2)


class GenerateConfigReportRequest(_messages.Message):
  r"""Request message for GenerateConfigReport method.

  Messages:
    NewConfigValue: Required. Service configuration for which we want to
      generate the report. For this version of API, the supported types are
      google.api.servicemanagement.v1.ConfigRef,
      google.api.servicemanagement.v1.ConfigSource, and google.api.Service
    OldConfigValue: Optional. Service configuration against which the
      comparison will be done. For this version of API, the supported types
      are google.api.servicemanagement.v1.ConfigRef,
      google.api.servicemanagement.v1.ConfigSource, and google.api.Service

  Fields:
    newConfig: Required. Service configuration for which we want to generate
      the report. For this version of API, the supported types are
      google.api.servicemanagement.v1.ConfigRef,
      google.api.servicemanagement.v1.ConfigSource, and google.api.Service
    oldConfig: Optional. Service configuration against which the comparison
      will be done. For this version of API, the supported types are
      google.api.servicemanagement.v1.ConfigRef,
      google.api.servicemanagement.v1.ConfigSource, and google.api.Service
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NewConfigValue(_messages.Message):
    r"""Required. Service configuration for which we want to generate the
    report. For this version of API, the supported types are
    google.api.servicemanagement.v1.ConfigRef,
    google.api.servicemanagement.v1.ConfigSource, and google.api.Service

    Messages:
      AdditionalProperty: An additional property for a NewConfigValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NewConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class OldConfigValue(_messages.Message):
    r"""Optional. Service configuration against which the comparison will be
    done. For this version of API, the supported types are
    google.api.servicemanagement.v1.ConfigRef,
    google.api.servicemanagement.v1.ConfigSource, and google.api.Service

    Messages:
      AdditionalProperty: An additional property for a OldConfigValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a OldConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  newConfig = _messages.MessageField('NewConfigValue', 1)
  oldConfig = _messages.MessageField('OldConfigValue', 2)


class GenerateConfigReportResponse(_messages.Message):
  r"""Response message for GenerateConfigReport method.

  Fields:
    changeReports: list of ChangeReport, each corresponding to comparison
      between two service configurations.
    diagnostics: Errors / Linter warnings associated with the service
      definition this report belongs to.
    id: ID of the service configuration this report belongs to.
    serviceName: Name of the service this report belongs to.
  """

  changeReports = _messages.MessageField('ChangeReport', 1, repeated=True)
  diagnostics = _messages.MessageField('Diagnostic', 2, repeated=True)
  id = _messages.StringField(3)
  serviceName = _messages.StringField(4)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoSettings(_messages.Message):
  r"""Settings for Go client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class Http(_messages.Message):
  r"""Defines the HTTP configuration for an API service. It contains a list of
  HttpRule, each specifying the mapping of an RPC method to one or more HTTP
  REST API methods.

  Fields:
    fullyDecodeReservedExpansion: When set to true, URL path parameters will
      be fully URI-decoded except in cases of single segment matches in
      reserved expansion, where "%2F" will be left encoded. The default
      behavior is to not decode RFC 6570 reserved characters in multi segment
      matches.
    rules: A list of HTTP configuration rules that apply to individual API
      methods. **NOTE:** All service configuration rules follow "last one
      wins" order.
  """

  fullyDecodeReservedExpansion = _messages.BooleanField(1)
  rules = _messages.MessageField('HttpRule', 2, repeated=True)


class HttpRule(_messages.Message):
  r"""# gRPC Transcoding gRPC Transcoding is a feature for mapping between a
  gRPC method and one or more HTTP REST endpoints. It allows developers to
  build a single API service that supports both gRPC APIs and REST APIs. Many
  systems, including [Google APIs](https://github.com/googleapis/googleapis),
  [Cloud Endpoints](https://cloud.google.com/endpoints), [gRPC
  Gateway](https://github.com/grpc-ecosystem/grpc-gateway), and
  [Envoy](https://github.com/envoyproxy/envoy) proxy support this feature and
  use it for large scale production services. `HttpRule` defines the schema of
  the gRPC/REST mapping. The mapping specifies how different portions of the
  gRPC request message are mapped to the URL path, URL query parameters, and
  HTTP request body. It also controls how the gRPC response message is mapped
  to the HTTP response body. `HttpRule` is typically specified as an
  `google.api.http` annotation on the gRPC method. Each mapping specifies a
  URL path template and an HTTP method. The path template may refer to one or
  more fields in the gRPC request message, as long as each field is a non-
  repeated field with a primitive (non-message) type. The path template
  controls how fields of the request message are mapped to the URL path.
  Example: service Messaging { rpc GetMessage(GetMessageRequest) returns
  (Message) { option (google.api.http) = { get: "/v1/{name=messages/*}" }; } }
  message GetMessageRequest { string name = 1; // Mapped to URL path. }
  message Message { string text = 1; // The resource content. } This enables
  an HTTP REST to gRPC mapping as below: HTTP | gRPC -----|----- `GET
  /v1/messages/123456` | `GetMessage(name: "messages/123456")` Any fields in
  the request message which are not bound by the path template automatically
  become HTTP query parameters if there is no HTTP request body. For example:
  service Messaging { rpc GetMessage(GetMessageRequest) returns (Message) {
  option (google.api.http) = { get:"/v1/messages/{message_id}" }; } } message
  GetMessageRequest { message SubMessage { string subfield = 1; } string
  message_id = 1; // Mapped to URL path. int64 revision = 2; // Mapped to URL
  query parameter `revision`. SubMessage sub = 3; // Mapped to URL query
  parameter `sub.subfield`. } This enables a HTTP JSON to RPC mapping as
  below: HTTP | gRPC -----|----- `GET
  /v1/messages/123456?revision=2&sub.subfield=foo` | `GetMessage(message_id:
  "123456" revision: 2 sub: SubMessage(subfield: "foo"))` Note that fields
  which are mapped to URL query parameters must have a primitive type or a
  repeated primitive type or a non-repeated message type. In the case of a
  repeated type, the parameter can be repeated in the URL as
  `...?param=A&param=B`. In the case of a message type, each field of the
  message is mapped to a separate parameter, such as
  `...?foo.a=A&foo.b=B&foo.c=C`. For HTTP methods that allow a request body,
  the `body` field specifies the mapping. Consider a REST update method on the
  message resource collection: service Messaging { rpc
  UpdateMessage(UpdateMessageRequest) returns (Message) { option
  (google.api.http) = { patch: "/v1/messages/{message_id}" body: "message" };
  } } message UpdateMessageRequest { string message_id = 1; // mapped to the
  URL Message message = 2; // mapped to the body } The following HTTP JSON to
  RPC mapping is enabled, where the representation of the JSON in the request
  body is determined by protos JSON encoding: HTTP | gRPC -----|----- `PATCH
  /v1/messages/123456 { "text": "Hi!" }` | `UpdateMessage(message_id: "123456"
  message { text: "Hi!" })` The special name `*` can be used in the body
  mapping to define that every field not bound by the path template should be
  mapped to the request body. This enables the following alternative
  definition of the update method: service Messaging { rpc
  UpdateMessage(Message) returns (Message) { option (google.api.http) = {
  patch: "/v1/messages/{message_id}" body: "*" }; } } message Message { string
  message_id = 1; string text = 2; } The following HTTP JSON to RPC mapping is
  enabled: HTTP | gRPC -----|----- `PATCH /v1/messages/123456 { "text": "Hi!"
  }` | `UpdateMessage(message_id: "123456" text: "Hi!")` Note that when using
  `*` in the body mapping, it is not possible to have HTTP parameters, as all
  fields not bound by the path end in the body. This makes this option more
  rarely used in practice when defining REST APIs. The common usage of `*` is
  in custom methods which don't use the URL at all for transferring data. It
  is possible to define multiple HTTP methods for one RPC by using the
  `additional_bindings` option. Example: service Messaging { rpc
  GetMessage(GetMessageRequest) returns (Message) { option (google.api.http) =
  { get: "/v1/messages/{message_id}" additional_bindings { get:
  "/v1/users/{user_id}/messages/{message_id}" } }; } } message
  GetMessageRequest { string message_id = 1; string user_id = 2; } This
  enables the following two alternative HTTP JSON to RPC mappings: HTTP | gRPC
  -----|----- `GET /v1/messages/123456` | `GetMessage(message_id: "123456")`
  `GET /v1/users/me/messages/123456` | `GetMessage(user_id: "me" message_id:
  "123456")` ## Rules for HTTP mapping 1. Leaf request fields (recursive
  expansion nested messages in the request message) are classified into three
  categories: - Fields referred by the path template. They are passed via the
  URL path. - Fields referred by the HttpRule.body. They are passed via the
  HTTP request body. - All other fields are passed via the URL query
  parameters, and the parameter name is the field path in the request message.
  A repeated field can be represented as multiple query parameters under the
  same name. 2. If HttpRule.body is "*", there is no URL query parameter, all
  fields are passed via URL path and HTTP request body. 3. If HttpRule.body is
  omitted, there is no HTTP request body, all fields are passed via URL path
  and URL query parameters. ### Path template syntax Template = "/" Segments [
  Verb ] ; Segments = Segment { "/" Segment } ; Segment = "*" | "**" | LITERAL
  | Variable ; Variable = "{" FieldPath [ "=" Segments ] "}" ; FieldPath =
  IDENT { "." IDENT } ; Verb = ":" LITERAL ; The syntax `*` matches a single
  URL path segment. The syntax `**` matches zero or more URL path segments,
  which must be the last part of the URL path except the `Verb`. The syntax
  `Variable` matches part of the URL path as specified by its template. A
  variable template must not contain other variables. If a variable matches a
  single path segment, its template may be omitted, e.g. `{var}` is equivalent
  to `{var=*}`. The syntax `LITERAL` matches literal text in the URL path. If
  the `LITERAL` contains any reserved character, such characters should be
  percent-encoded before the matching. If a variable contains exactly one path
  segment, such as `"{var}"` or `"{var=*}"`, when such a variable is expanded
  into a URL path on the client side, all characters except `[-_.~0-9a-zA-Z]`
  are percent-encoded. The server side does the reverse decoding. Such
  variables show up in the [Discovery
  Document](https://developers.google.com/discovery/v1/reference/apis) as
  `{var}`. If a variable contains multiple path segments, such as
  `"{var=foo/*}"` or `"{var=**}"`, when such a variable is expanded into a URL
  path on the client side, all characters except `[-_.~/0-9a-zA-Z]` are
  percent-encoded. The server side does the reverse decoding, except "%2F" and
  "%2f" are left unchanged. Such variables show up in the [Discovery
  Document](https://developers.google.com/discovery/v1/reference/apis) as
  `{+var}`. ## Using gRPC API Service Configuration gRPC API Service
  Configuration (service config) is a configuration language for configuring a
  gRPC service to become a user-facing product. The service config is simply
  the YAML representation of the `google.api.Service` proto message. As an
  alternative to annotating your proto file, you can configure gRPC
  transcoding in your service config YAML files. You do this by specifying a
  `HttpRule` that maps the gRPC method to a REST endpoint, achieving the same
  effect as the proto annotation. This can be particularly useful if you have
  a proto that is reused in multiple services. Note that any transcoding
  specified in the service config will override any matching transcoding
  configuration in the proto. Example: http: rules: # Selects a gRPC method
  and applies HttpRule to it. - selector: example.v1.Messaging.GetMessage get:
  /v1/messages/{message_id}/{sub.subfield} ## Special notes When gRPC
  Transcoding is used to map a gRPC to JSON REST endpoints, the proto to JSON
  conversion must follow the [proto3
  specification](https://developers.google.com/protocol-
  buffers/docs/proto3#json). While the single segment variable follows the
  semantics of [RFC 6570](https://tools.ietf.org/html/rfc6570) Section 3.2.2
  Simple String Expansion, the multi segment variable **does not** follow RFC
  6570 Section 3.2.3 Reserved Expansion. The reason is that the Reserved
  Expansion does not expand special characters like `?` and `#`, which would
  lead to invalid URLs. As the result, gRPC Transcoding uses a custom encoding
  for multi segment variables. The path variables **must not** refer to any
  repeated or mapped field, because client libraries are not capable of
  handling such variable expansion. The path variables **must not** capture
  the leading "/" character. The reason is that the most common use case
  "{var}" does not capture the leading "/" character. For consistency, all
  path variables must share the same behavior. Repeated message fields must
  not be mapped to URL query parameters, because no client library can support
  such complicated mapping. If an API needs to use a JSON array for request or
  response body, it can map the request or response body to a repeated field.
  However, some gRPC Transcoding implementations may not support this feature.

  Fields:
    additionalBindings: Additional HTTP bindings for the selector. Nested
      bindings must not contain an `additional_bindings` field themselves
      (that is, the nesting may only be one level deep).
    body: The name of the request field whose value is mapped to the HTTP
      request body, or `*` for mapping all request fields not captured by the
      path pattern to the HTTP body, or omitted for not having any HTTP
      request body. NOTE: the referred field must be present at the top-level
      of the request message type.
    custom: The custom pattern is used for specifying an HTTP method that is
      not included in the `pattern` field, such as HEAD, or "*" to leave the
      HTTP method unspecified for this rule. The wild-card rule is useful for
      services that provide content to Web (HTML) clients.
    delete: Maps to HTTP DELETE. Used for deleting a resource.
    get: Maps to HTTP GET. Used for listing and getting information about
      resources.
    patch: Maps to HTTP PATCH. Used for updating a resource.
    post: Maps to HTTP POST. Used for creating a resource or performing an
      action.
    put: Maps to HTTP PUT. Used for replacing a resource.
    responseBody: Optional. The name of the response field whose value is
      mapped to the HTTP response body. When omitted, the entire response
      message will be used as the HTTP response body. NOTE: The referred field
      must be present at the top-level of the response message type.
    selector: Selects a method to which this rule applies. Refer to selector
      for syntax details.
  """

  additionalBindings = _messages.MessageField('HttpRule', 1, repeated=True)
  body = _messages.StringField(2)
  custom = _messages.MessageField('CustomHttpPattern', 3)
  delete = _messages.StringField(4)
  get = _messages.StringField(5)
  patch = _messages.StringField(6)
  post = _messages.StringField(7)
  put = _messages.StringField(8)
  responseBody = _messages.StringField(9)
  selector = _messages.StringField(10)


class JavaSettings(_messages.Message):
  r"""Settings for Java client libraries.

  Messages:
    ServiceClassNamesValue: Configure the Java class name to use instead of
      the service's for its corresponding generated GAPIC client. Keys are
      fully-qualified service names as they appear in the protobuf (including
      the full the language_settings.java.interface_names" field in
      gapic.yaml. API teams should otherwise use the service name as it
      appears in the protobuf. Example of a YAML configuration:: publishing:
      java_settings: service_class_names: - google.pubsub.v1.Publisher:
      TopicAdmin - google.pubsub.v1.Subscriber: SubscriptionAdmin

  Fields:
    common: Some settings.
    libraryPackage: The package name to use in Java. Clobbers the java_package
      option set in the protobuf. This should be used **only** by APIs who
      have already set the language_settings.java.package_name" field in
      gapic.yaml. API teams should use the protobuf java_package option where
      possible. Example of a YAML configuration:: publishing: java_settings:
      library_package: com.google.cloud.pubsub.v1
    serviceClassNames: Configure the Java class name to use instead of the
      service's for its corresponding generated GAPIC client. Keys are fully-
      qualified service names as they appear in the protobuf (including the
      full the language_settings.java.interface_names" field in gapic.yaml.
      API teams should otherwise use the service name as it appears in the
      protobuf. Example of a YAML configuration:: publishing: java_settings:
      service_class_names: - google.pubsub.v1.Publisher: TopicAdmin -
      google.pubsub.v1.Subscriber: SubscriptionAdmin
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ServiceClassNamesValue(_messages.Message):
    r"""Configure the Java class name to use instead of the service's for its
    corresponding generated GAPIC client. Keys are fully-qualified service
    names as they appear in the protobuf (including the full the
    language_settings.java.interface_names" field in gapic.yaml. API teams
    should otherwise use the service name as it appears in the protobuf.
    Example of a YAML configuration:: publishing: java_settings:
    service_class_names: - google.pubsub.v1.Publisher: TopicAdmin -
    google.pubsub.v1.Subscriber: SubscriptionAdmin

    Messages:
      AdditionalProperty: An additional property for a ServiceClassNamesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ServiceClassNamesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ServiceClassNamesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  common = _messages.MessageField('CommonLanguageSettings', 1)
  libraryPackage = _messages.StringField(2)
  serviceClassNames = _messages.MessageField('ServiceClassNamesValue', 3)


class JwtLocation(_messages.Message):
  r"""Specifies a location to extract JWT from an API request.

  Fields:
    cookie: Specifies cookie name to extract JWT token.
    header: Specifies HTTP header name to extract JWT token.
    query: Specifies URL query parameter name to extract JWT token.
    valuePrefix: The value prefix. The value format is "value_prefix{token}"
      Only applies to "in" header type. Must be empty for "in" query type. If
      not empty, the header value has to match (case sensitive) this prefix.
      If not matched, JWT will not be extracted. If matched, JWT will be
      extracted after the prefix is removed. For example, for "Authorization:
      Bearer {JWT}", value_prefix="Bearer " with a space at the end.
  """

  cookie = _messages.StringField(1)
  header = _messages.StringField(2)
  query = _messages.StringField(3)
  valuePrefix = _messages.StringField(4)


class LabelDescriptor(_messages.Message):
  r"""A description of a label.

  Enums:
    ValueTypeValueValuesEnum: The type of data that can be assigned to the
      label.

  Fields:
    description: A human-readable description for the label.
    key: The label key.
    valueType: The type of data that can be assigned to the label.
  """

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""The type of data that can be assigned to the label.

    Values:
      STRING: A variable-length string. This is the default.
      BOOL: Boolean; true or false.
      INT64: A 64-bit signed integer.
    """
    STRING = 0
    BOOL = 1
    INT64 = 2

  description = _messages.StringField(1)
  key = _messages.StringField(2)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 3)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListServiceConfigsResponse(_messages.Message):
  r"""Response message for ListServiceConfigs method.

  Fields:
    nextPageToken: The token of the next page of results.
    serviceConfigs: The list of service configuration resources.
  """

  nextPageToken = _messages.StringField(1)
  serviceConfigs = _messages.MessageField('Service', 2, repeated=True)


class ListServiceRolloutsResponse(_messages.Message):
  r"""Response message for ListServiceRollouts method.

  Fields:
    nextPageToken: The token of the next page of results.
    rollouts: The list of rollout resources.
  """

  nextPageToken = _messages.StringField(1)
  rollouts = _messages.MessageField('Rollout', 2, repeated=True)


class ListServicesResponse(_messages.Message):
  r"""Response message for `ListServices` method.

  Fields:
    nextPageToken: Token that can be passed to `ListServices` to resume a
      paginated query.
    services: The returned services will only have the name field set.
  """

  nextPageToken = _messages.StringField(1)
  services = _messages.MessageField('ManagedService', 2, repeated=True)


class LogDescriptor(_messages.Message):
  r"""A description of a log type. Example in YAML format: - name:
  library.googleapis.com/activity_history description: The history of
  borrowing and returning library items. display_name: Activity labels: - key:
  /customer_id description: Identifier of a library customer

  Fields:
    description: A human-readable description of this log. This information
      appears in the documentation and can contain details.
    displayName: The human-readable name for this log. This information
      appears on the user interface and should be concise.
    labels: The set of labels that are available to describe a specific log
      entry. Runtime requests that contain labels not specified here are
      considered invalid.
    name: The name of the log. It must be less than 512 characters long and
      can include the following characters: upper- and lower-case alphanumeric
      characters [A-Za-z0-9], and punctuation characters including slash,
      underscore, hyphen, period [/_-.].
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  name = _messages.StringField(4)


class Logging(_messages.Message):
  r"""Logging configuration of the service. The following example shows how to
  configure logs to be sent to the producer and consumer projects. In the
  example, the `activity_history` log is sent to both the producer and
  consumer projects, whereas the `purchase_history` log is only sent to the
  producer project. monitored_resources: - type: library.googleapis.com/branch
  labels: - key: /city description: The city where the library branch is
  located in. - key: /name description: The name of the branch. logs: - name:
  activity_history labels: - key: /customer_id - name: purchase_history
  logging: producer_destinations: - monitored_resource:
  library.googleapis.com/branch logs: - activity_history - purchase_history
  consumer_destinations: - monitored_resource: library.googleapis.com/branch
  logs: - activity_history

  Fields:
    consumerDestinations: Logging configurations for sending logs to the
      consumer project. There can be multiple consumer destinations, each one
      must have a different monitored resource type. A log can be used in at
      most one consumer destination.
    producerDestinations: Logging configurations for sending logs to the
      producer project. There can be multiple producer destinations, each one
      must have a different monitored resource type. A log can be used in at
      most one producer destination.
  """

  consumerDestinations = _messages.MessageField('LoggingDestination', 1, repeated=True)
  producerDestinations = _messages.MessageField('LoggingDestination', 2, repeated=True)


class LoggingDestination(_messages.Message):
  r"""Configuration of a specific logging destination (the producer project or
  the consumer project).

  Fields:
    logs: Names of the logs to be sent to this destination. Each name must be
      defined in the Service.logs section. If the log name is not a domain
      scoped name, it will be automatically prefixed with the service name
      followed by "/".
    monitoredResource: The monitored resource type. The type must be defined
      in the Service.monitored_resources section.
  """

  logs = _messages.StringField(1, repeated=True)
  monitoredResource = _messages.StringField(2)


class LongRunning(_messages.Message):
  r"""Describes settings to use when generating API methods that use the long-
  running operation pattern. All default values below are from those used in
  the client library generators (e.g.
  [Java](https://github.com/googleapis/gapic-generator-java/blob/04c2faa191a9b
  5a10b92392fe8482279c4404803/src/main/java/com/google/api/generator/gapic/com
  poser/common/RetrySettingsComposer.java)).

  Fields:
    initialPollDelay: Initial delay after which the first poll request will be
      made. Default value: 5 seconds.
    maxPollDelay: Maximum time between two subsequent poll requests. Default
      value: 45 seconds.
    pollDelayMultiplier: Multiplier to gradually increase delay between
      subsequent polls until it reaches max_poll_delay. Default value: 1.5.
    totalPollTimeout: Total polling timeout. Default value: 5 minutes.
  """

  initialPollDelay = _messages.StringField(1)
  maxPollDelay = _messages.StringField(2)
  pollDelayMultiplier = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  totalPollTimeout = _messages.StringField(4)


class ManagedService(_messages.Message):
  r"""The full representation of a Service that is managed by Google Service
  Management.

  Fields:
    generation: A server-assigned monotonically increasing number that changes
      whenever a mutation is made to the `ManagedService` or any of its
      components via Google Service Management.
    producerProjectId: ID of the project that produces and owns this service.
    projectSettings: Read-only view of settings for a particular consumer
      project, if requested. DEPRECATED, should call GetProjectSettings
      instead.
    serviceConfig: The service's generated configuration. DEPRECATED, should
      call GetServiceConfig instead.
    serviceName: The name of the service. See the
      [overview](https://cloud.google.com/service-
      infrastructure/docs/overview) for naming requirements.
  """

  generation = _messages.IntegerField(1)
  producerProjectId = _messages.StringField(2)
  projectSettings = _messages.MessageField('ProjectSettings', 3)
  serviceConfig = _messages.MessageField('Service', 4)
  serviceName = _messages.StringField(5)


class Method(_messages.Message):
  r"""Method represents a method of an API interface.

  Enums:
    SyntaxValueValuesEnum: The source syntax of this method.

  Fields:
    name: The simple name of this method.
    options: Any metadata attached to the method.
    requestStreaming: If true, the request is streamed.
    requestTypeUrl: A URL of the input message type.
    responseStreaming: If true, the response is streamed.
    responseTypeUrl: The URL of the output message type.
    syntax: The source syntax of this method.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax of this method.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
      SYNTAX_EDITIONS: Syntax `editions`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1
    SYNTAX_EDITIONS = 2

  name = _messages.StringField(1)
  options = _messages.MessageField('Option', 2, repeated=True)
  requestStreaming = _messages.BooleanField(3)
  requestTypeUrl = _messages.StringField(4)
  responseStreaming = _messages.BooleanField(5)
  responseTypeUrl = _messages.StringField(6)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 7)


class MethodPolicy(_messages.Message):
  r"""Defines policies applying to an RPC method.

  Fields:
    requestPolicies: Policies that are applicable to the request message.
    selector: Selects a method to which these policies should be enforced, for
      example, "google.pubsub.v1.Subscriber.CreateSubscription". Refer to
      selector for syntax details. NOTE: This field must not be set in the
      proto annotation. It will be automatically filled by the service config
      compiler .
  """

  requestPolicies = _messages.MessageField('FieldPolicy', 1, repeated=True)
  selector = _messages.StringField(2)


class MethodSettings(_messages.Message):
  r"""Describes the generator configuration for a method.

  Fields:
    longRunning: Describes settings to use for long-running operations when
      generating API methods for RPCs. Complements RPCs that use the
      annotations in google/longrunning/operations.proto. Example of a YAML
      configuration:: publishing: method_settings: - selector:
      google.cloud.speech.v2.Speech.BatchRecognize long_running:
      initial_poll_delay: seconds: 60 # 1 minute poll_delay_multiplier: 1.5
      max_poll_delay: seconds: 360 # 6 minutes total_poll_timeout: seconds:
      54000 # 90 minutes
    selector: The fully qualified name of the method, for which the options
      below apply. This is used to find the method to apply the options.
  """

  longRunning = _messages.MessageField('LongRunning', 1)
  selector = _messages.StringField(2)


class MetricDescriptor(_messages.Message):
  r"""Defines a metric type and its schema. Once a metric descriptor is
  created, deleting or altering it stops data collection and makes the metric
  type's existing data unusable.

  Enums:
    LaunchStageValueValuesEnum: Optional. The launch stage of the metric
      definition.
    MetricKindValueValuesEnum: Whether the metric records instantaneous
      values, changes to a value, etc. Some combinations of `metric_kind` and
      `value_type` might not be supported.
    ValueTypeValueValuesEnum: Whether the measurement is an integer, a
      floating-point number, etc. Some combinations of `metric_kind` and
      `value_type` might not be supported.

  Fields:
    description: A detailed description of the metric, which can be used in
      documentation.
    displayName: A concise name for the metric, which can be displayed in user
      interfaces. Use sentence case without an ending period, for example
      "Request count". This field is optional but it is recommended to be set
      for any metrics associated with user-visible concepts, such as Quota.
    labels: The set of labels that can be used to describe a specific instance
      of this metric type. For example, the
      `appengine.googleapis.com/http/server/response_latencies` metric type
      has a label for the HTTP response code, `response_code`, so you can look
      at latencies for successful responses or just for responses that failed.
    launchStage: Optional. The launch stage of the metric definition.
    metadata: Optional. Metadata which can be used to guide usage of the
      metric.
    metricKind: Whether the metric records instantaneous values, changes to a
      value, etc. Some combinations of `metric_kind` and `value_type` might
      not be supported.
    monitoredResourceTypes: Read-only. If present, then a time series, which
      is identified partially by a metric type and a
      MonitoredResourceDescriptor, that is associated with this metric type
      can only be associated with one of the monitored resource types listed
      here.
    name: The resource name of the metric descriptor.
    type: The metric type, including its DNS name prefix. The type is not URL-
      encoded. All user-defined metric types have the DNS name
      `custom.googleapis.com` or `external.googleapis.com`. Metric types
      should use a natural hierarchical grouping. For example:
      "custom.googleapis.com/invoice/paid/amount"
      "external.googleapis.com/prometheus/up"
      "appengine.googleapis.com/http/server/response_latencies"
    unit: The units in which the metric value is reported. It is only
      applicable if the `value_type` is `INT64`, `DOUBLE`, or `DISTRIBUTION`.
      The `unit` defines the representation of the stored metric values.
      Different systems might scale the values to be more easily displayed (so
      a value of `0.02kBy` _might_ be displayed as `20By`, and a value of
      `3523kBy` _might_ be displayed as `3.5MBy`). However, if the `unit` is
      `kBy`, then the value of the metric is always in thousands of bytes, no
      matter how it might be displayed. If you want a custom metric to record
      the exact number of CPU-seconds used by a job, you can create an `INT64
      CUMULATIVE` metric whose `unit` is `s{CPU}` (or equivalently `1s{CPU}`
      or just `s`). If the job uses 12,005 CPU-seconds, then the value is
      written as `12005`. Alternatively, if you want a custom metric to record
      data in a more granular way, you can create a `DOUBLE CUMULATIVE` metric
      whose `unit` is `ks{CPU}`, and then write the value `12.005` (which is
      `12005/1000`), or use `Kis{CPU}` and write `11.723` (which is
      `12005/1024`). The supported units are a subset of [The Unified Code for
      Units of Measure](https://unitsofmeasure.org/ucum.html) standard:
      **Basic units (UNIT)** * `bit` bit * `By` byte * `s` second * `min`
      minute * `h` hour * `d` day * `1` dimensionless **Prefixes (PREFIX)** *
      `k` kilo (10^3) * `M` mega (10^6) * `G` giga (10^9) * `T` tera (10^12) *
      `P` peta (10^15) * `E` exa (10^18) * `Z` zetta (10^21) * `Y` yotta
      (10^24) * `m` milli (10^-3) * `u` micro (10^-6) * `n` nano (10^-9) * `p`
      pico (10^-12) * `f` femto (10^-15) * `a` atto (10^-18) * `z` zepto
      (10^-21) * `y` yocto (10^-24) * `Ki` kibi (2^10) * `Mi` mebi (2^20) *
      `Gi` gibi (2^30) * `Ti` tebi (2^40) * `Pi` pebi (2^50) **Grammar** The
      grammar also includes these connectors: * `/` division or ratio (as an
      infix operator). For examples, `kBy/{email}` or `MiBy/10ms` (although
      you should almost never have `/s` in a metric `unit`; rates should
      always be computed at query time from the underlying cumulative or delta
      value). * `.` multiplication or composition (as an infix operator). For
      examples, `GBy.d` or `k{watt}.h`. The grammar for a unit is as follows:
      Expression = Component { "." Component } { "/" Component } ; Component =
      ( [ PREFIX ] UNIT | "%" ) [ Annotation ] | Annotation | "1" ; Annotation
      = "{" NAME "}" ; Notes: * `Annotation` is just a comment if it follows a
      `UNIT`. If the annotation is used alone, then the unit is equivalent to
      `1`. For examples, `{request}/s == 1/s`, `By{transmitted}/s == By/s`. *
      `NAME` is a sequence of non-blank printable ASCII characters not
      containing `{` or `}`. * `1` represents a unitary [dimensionless
      unit](https://en.wikipedia.org/wiki/Dimensionless_quantity) of 1, such
      as in `1/s`. It is typically used when none of the basic units are
      appropriate. For example, "new users per day" can be represented as
      `1/d` or `{new-users}/d` (and a metric value `5` would mean "5 new
      users). Alternatively, "thousands of page views per day" would be
      represented as `1000/d` or `k1/d` or `k{page_views}/d` (and a metric
      value of `5.3` would mean "5300 page views per day"). * `%` represents
      dimensionless value of 1/100, and annotates values giving a percentage
      (so the metric values are typically in the range of 0..100, and a metric
      value `3` means "3 percent"). * `10^2.%` indicates a metric contains a
      ratio, typically in the range 0..1, that will be multiplied by 100 and
      displayed as a percentage (so a metric value `0.03` means "3 percent").
    valueType: Whether the measurement is an integer, a floating-point number,
      etc. Some combinations of `metric_kind` and `value_type` might not be
      supported.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Optional. The launch stage of the metric definition.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  class MetricKindValueValuesEnum(_messages.Enum):
    r"""Whether the metric records instantaneous values, changes to a value,
    etc. Some combinations of `metric_kind` and `value_type` might not be
    supported.

    Values:
      METRIC_KIND_UNSPECIFIED: Do not use this default value.
      GAUGE: An instantaneous measurement of a value.
      DELTA: The change in a value during a time interval.
      CUMULATIVE: A value accumulated over a time interval. Cumulative
        measurements in a time series should have the same start time and
        increasing end times, until an event resets the cumulative value to
        zero and sets a new start time for the following points.
    """
    METRIC_KIND_UNSPECIFIED = 0
    GAUGE = 1
    DELTA = 2
    CUMULATIVE = 3

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""Whether the measurement is an integer, a floating-point number, etc.
    Some combinations of `metric_kind` and `value_type` might not be
    supported.

    Values:
      VALUE_TYPE_UNSPECIFIED: Do not use this default value.
      BOOL: The value is a boolean. This value type can be used only if the
        metric kind is `GAUGE`.
      INT64: The value is a signed 64-bit integer.
      DOUBLE: The value is a double precision floating point number.
      STRING: The value is a text string. This value type can be used only if
        the metric kind is `GAUGE`.
      DISTRIBUTION: The value is a `Distribution`.
      MONEY: The value is money.
    """
    VALUE_TYPE_UNSPECIFIED = 0
    BOOL = 1
    INT64 = 2
    DOUBLE = 3
    STRING = 4
    DISTRIBUTION = 5
    MONEY = 6

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 4)
  metadata = _messages.MessageField('MetricDescriptorMetadata', 5)
  metricKind = _messages.EnumField('MetricKindValueValuesEnum', 6)
  monitoredResourceTypes = _messages.StringField(7, repeated=True)
  name = _messages.StringField(8)
  type = _messages.StringField(9)
  unit = _messages.StringField(10)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 11)


class MetricDescriptorMetadata(_messages.Message):
  r"""Additional annotations that can be used to guide the usage of a metric.

  Enums:
    LaunchStageValueValuesEnum: Deprecated. Must use the
      MetricDescriptor.launch_stage instead.

  Fields:
    ingestDelay: The delay of data points caused by ingestion. Data points
      older than this age are guaranteed to be ingested and available to be
      read, excluding data loss due to errors.
    launchStage: Deprecated. Must use the MetricDescriptor.launch_stage
      instead.
    samplePeriod: The sampling period of metric data points. For metrics which
      are written periodically, consecutive data points are stored at this
      time interval, excluding data loss due to errors. Metrics with a higher
      granularity have a smaller sampling period.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Deprecated. Must use the MetricDescriptor.launch_stage instead.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  ingestDelay = _messages.StringField(1)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 2)
  samplePeriod = _messages.StringField(3)


class MetricRule(_messages.Message):
  r"""Bind API methods to metrics. Binding a method to a metric causes that
  metric's configured quota behaviors to apply to the method call.

  Messages:
    MetricCostsValue: Metrics to update when the selected methods are called,
      and the associated cost applied to each metric. The key of the map is
      the metric name, and the values are the amount increased for the metric
      against which the quota limits are defined. The value must not be
      negative.

  Fields:
    metricCosts: Metrics to update when the selected methods are called, and
      the associated cost applied to each metric. The key of the map is the
      metric name, and the values are the amount increased for the metric
      against which the quota limits are defined. The value must not be
      negative.
    selector: Selects the methods to which this rule applies. Refer to
      selector for syntax details.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetricCostsValue(_messages.Message):
    r"""Metrics to update when the selected methods are called, and the
    associated cost applied to each metric. The key of the map is the metric
    name, and the values are the amount increased for the metric against which
    the quota limits are defined. The value must not be negative.

    Messages:
      AdditionalProperty: An additional property for a MetricCostsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MetricCostsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetricCostsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  metricCosts = _messages.MessageField('MetricCostsValue', 1)
  selector = _messages.StringField(2)


class Mixin(_messages.Message):
  r"""Declares an API Interface to be included in this interface. The
  including interface must redeclare all the methods from the included
  interface, but documentation and options are inherited as follows: - If
  after comment and whitespace stripping, the documentation string of the
  redeclared method is empty, it will be inherited from the original method. -
  Each annotation belonging to the service config (http, visibility) which is
  not set in the redeclared method will be inherited. - If an http annotation
  is inherited, the path pattern will be modified as follows. Any version
  prefix will be replaced by the version of the including interface plus the
  root path if specified. Example of a simple mixin: package google.acl.v1;
  service AccessControl { // Get the underlying ACL object. rpc
  GetAcl(GetAclRequest) returns (Acl) { option (google.api.http).get =
  "/v1/{resource=**}:getAcl"; } } package google.storage.v2; service Storage {
  // rpc GetAcl(GetAclRequest) returns (Acl); // Get a data record. rpc
  GetData(GetDataRequest) returns (Data) { option (google.api.http).get =
  "/v2/{resource=**}"; } } Example of a mixin configuration: apis: - name:
  google.storage.v2.Storage mixins: - name: google.acl.v1.AccessControl The
  mixin construct implies that all methods in `AccessControl` are also
  declared with same name and request/response types in `Storage`. A
  documentation generator or annotation processor will see the effective
  `Storage.GetAcl` method after inherting documentation and annotations as
  follows: service Storage { // Get the underlying ACL object. rpc
  GetAcl(GetAclRequest) returns (Acl) { option (google.api.http).get =
  "/v2/{resource=**}:getAcl"; } ... } Note how the version in the path pattern
  changed from `v1` to `v2`. If the `root` field in the mixin is specified, it
  should be a relative path under which inherited HTTP paths are placed.
  Example: apis: - name: google.storage.v2.Storage mixins: - name:
  google.acl.v1.AccessControl root: acls This implies the following inherited
  HTTP annotation: service Storage { // Get the underlying ACL object. rpc
  GetAcl(GetAclRequest) returns (Acl) { option (google.api.http).get =
  "/v2/acls/{resource=**}:getAcl"; } ... }

  Fields:
    name: The fully qualified name of the interface which is included.
    root: If non-empty specifies a path under which inherited HTTP paths are
      rooted.
  """

  name = _messages.StringField(1)
  root = _messages.StringField(2)


class MonitoredResourceDescriptor(_messages.Message):
  r"""An object that describes the schema of a MonitoredResource object using
  a type name and a set of labels. For example, the monitored resource
  descriptor for Google Compute Engine VM instances has a type of
  `"gce_instance"` and specifies the use of the labels `"instance_id"` and
  `"zone"` to identify particular VM instances. Different APIs can support
  different monitored resource types. APIs generally provide a `list` method
  that returns the monitored resource descriptors used by the API.

  Enums:
    LaunchStageValueValuesEnum: Optional. The launch stage of the monitored
      resource definition.

  Fields:
    description: Optional. A detailed description of the monitored resource
      type that might be used in documentation.
    displayName: Optional. A concise name for the monitored resource type that
      might be displayed in user interfaces. It should be a Title Cased Noun
      Phrase, without any article or other determiners. For example, `"Google
      Cloud SQL Database"`.
    labels: Required. A set of labels used to describe instances of this
      monitored resource type. For example, an individual Google Cloud SQL
      database is identified by values for the labels `"database_id"` and
      `"zone"`.
    launchStage: Optional. The launch stage of the monitored resource
      definition.
    name: Optional. The resource name of the monitored resource descriptor:
      `"projects/{project_id}/monitoredResourceDescriptors/{type}"` where
      {type} is the value of the `type` field in this object and {project_id}
      is a project ID that provides API-specific context for accessing the
      type. APIs that do not use project information can use the resource name
      format `"monitoredResourceDescriptors/{type}"`.
    type: Required. The monitored resource type. For example, the type
      `"cloudsql_database"` represents databases in Google Cloud SQL. For a
      list of types, see [Monitoring resource
      types](https://cloud.google.com/monitoring/api/resources) and [Logging
      resource types](https://cloud.google.com/logging/docs/api/v2/resource-
      list).
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Optional. The launch stage of the monitored resource definition.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 4)
  name = _messages.StringField(5)
  type = _messages.StringField(6)


class Monitoring(_messages.Message):
  r"""Monitoring configuration of the service. The example below shows how to
  configure monitored resources and metrics for monitoring. In the example, a
  monitored resource and two metrics are defined. The
  `library.googleapis.com/book/returned_count` metric is sent to both producer
  and consumer projects, whereas the `library.googleapis.com/book/num_overdue`
  metric is only sent to the consumer project. monitored_resources: - type:
  library.googleapis.com/Branch display_name: "Library Branch" description: "A
  branch of a library." launch_stage: GA labels: - key: resource_container
  description: "The Cloud container (ie. project id) for the Branch." - key:
  location description: "The location of the library branch." - key: branch_id
  description: "The id of the branch." metrics: - name:
  library.googleapis.com/book/returned_count display_name: "Books Returned"
  description: "The count of books that have been returned." launch_stage: GA
  metric_kind: DELTA value_type: INT64 unit: "1" labels: - key: customer_id
  description: "The id of the customer." - name:
  library.googleapis.com/book/num_overdue display_name: "Books Overdue"
  description: "The current number of overdue books." launch_stage: GA
  metric_kind: GAUGE value_type: INT64 unit: "1" labels: - key: customer_id
  description: "The id of the customer." monitoring: producer_destinations: -
  monitored_resource: library.googleapis.com/Branch metrics: -
  library.googleapis.com/book/returned_count consumer_destinations: -
  monitored_resource: library.googleapis.com/Branch metrics: -
  library.googleapis.com/book/returned_count -
  library.googleapis.com/book/num_overdue

  Fields:
    consumerDestinations: Monitoring configurations for sending metrics to the
      consumer project. There can be multiple consumer destinations. A
      monitored resource type may appear in multiple monitoring destinations
      if different aggregations are needed for different sets of metrics
      associated with that monitored resource type. A monitored resource and
      metric pair may only be used once in the Monitoring configuration.
    producerDestinations: Monitoring configurations for sending metrics to the
      producer project. There can be multiple producer destinations. A
      monitored resource type may appear in multiple monitoring destinations
      if different aggregations are needed for different sets of metrics
      associated with that monitored resource type. A monitored resource and
      metric pair may only be used once in the Monitoring configuration.
  """

  consumerDestinations = _messages.MessageField('MonitoringDestination', 1, repeated=True)
  producerDestinations = _messages.MessageField('MonitoringDestination', 2, repeated=True)


class MonitoringDestination(_messages.Message):
  r"""Configuration of a specific monitoring destination (the producer project
  or the consumer project).

  Fields:
    metrics: Types of the metrics to report to this monitoring destination.
      Each type must be defined in Service.metrics section.
    monitoredResource: The monitored resource type. The type must be defined
      in Service.monitored_resources section.
  """

  metrics = _messages.StringField(1, repeated=True)
  monitoredResource = _messages.StringField(2)


class NodeSettings(_messages.Message):
  r"""Settings for Node client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class OAuthRequirements(_messages.Message):
  r"""OAuth scopes are a way to define data and permissions on data. For
  example, there are scopes defined for "Read-only access to Google Calendar"
  and "Access to Cloud Platform". Users can consent to a scope for an
  application, giving it permission to access that data on their behalf. OAuth
  scope specifications should be fairly coarse grained; a user will need to
  see and understand the text description of what your scope means. In most
  cases: use one or at most two OAuth scopes for an entire family of products.
  If your product has multiple APIs, you should probably be sharing the OAuth
  scope across all of those APIs. When you need finer grained OAuth consent
  screens: talk with your product management about how developers will use
  them in practice. Please note that even though each of the canonical scopes
  is enough for a request to be accepted and passed to the backend, a request
  can still fail due to the backend requiring additional scopes or
  permissions.

  Fields:
    canonicalScopes: The list of publicly documented OAuth scopes that are
      allowed access. An OAuth token containing any of these scopes will be
      accepted. Example: canonical_scopes:
      https://www.googleapis.com/auth/calendar,
      https://www.googleapis.com/auth/calendar.read
  """

  canonicalScopes = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationInfo(_messages.Message):
  r"""A message representing the message types used by a long-running
  operation. Example: rpc Export(ExportRequest) returns
  (google.longrunning.Operation) { option (google.longrunning.operation_info)
  = { response_type: "ExportResponse" metadata_type: "ExportMetadata" }; }

  Fields:
    metadataType: Required. The message name of the metadata type for this
      long-running operation. If the response is in a different package from
      the rpc, a fully-qualified message name must be used (e.g.
      `google.protobuf.Struct`). Note: Altering this value constitutes a
      breaking change.
    responseType: Required. The message name of the primary return type for
      this long-running operation. This type will be used to deserialize the
      LRO's response. If the response is in a different package from the rpc,
      a fully-qualified message name must be used (e.g.
      `google.protobuf.Struct`). Note: Altering this value constitutes a
      breaking change.
  """

  metadataType = _messages.StringField(1)
  responseType = _messages.StringField(2)


class OperationMetadata(_messages.Message):
  r"""The metadata associated with a long running operation resource.

  Fields:
    progressPercentage: Percentage of completion of this operation, ranging
      from 0 to 100.
    resourceNames: The full name of the resources that this operation is
      directly associated with.
    startTime: The start time of the operation.
    steps: Detailed status information for each step. The order is
      undetermined.
  """

  progressPercentage = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resourceNames = _messages.StringField(2, repeated=True)
  startTime = _messages.StringField(3)
  steps = _messages.MessageField('Step', 4, repeated=True)


class Option(_messages.Message):
  r"""A protocol buffer option, which can be attached to a message, field,
  enumeration, etc.

  Messages:
    ValueValue: The option's value packed in an Any message. If the value is a
      primitive, the corresponding wrapper type defined in
      google/protobuf/wrappers.proto should be used. If the value is an enum,
      it should be stored as an int32 value using the
      google.protobuf.Int32Value type.

  Fields:
    name: The option's name. For protobuf built-in options (options defined in
      descriptor.proto), this is the short name. For example, `"map_entry"`.
      For custom options, it should be the fully-qualified name. For example,
      `"google.api.http"`.
    value: The option's value packed in an Any message. If the value is a
      primitive, the corresponding wrapper type defined in
      google/protobuf/wrappers.proto should be used. If the value is an enum,
      it should be stored as an int32 value using the
      google.protobuf.Int32Value type.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValueValue(_messages.Message):
    r"""The option's value packed in an Any message. If the value is a
    primitive, the corresponding wrapper type defined in
    google/protobuf/wrappers.proto should be used. If the value is an enum, it
    should be stored as an int32 value using the google.protobuf.Int32Value
    type.

    Messages:
      AdditionalProperty: An additional property for a ValueValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValueValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  name = _messages.StringField(1)
  value = _messages.MessageField('ValueValue', 2)


class Page(_messages.Message):
  r"""Represents a documentation page. A page can contain subpages to
  represent nested documentation set structure.

  Fields:
    content: The Markdown content of the page. You can use (== include {path}
      ==) to include content from a Markdown file. The content can be used to
      produce the documentation page such as HTML format page.
    name: The name of the page. It will be used as an identity of the page to
      generate URI of the page, text of the link to this page in navigation,
      etc. The full page name (start from the root page name to this page
      concatenated with `.`) can be used as reference to the page in your
      documentation. For example: pages: - name: Tutorial content: (== include
      tutorial.md ==) subpages: - name: Java content: (== include
      tutorial_java.md ==) You can reference `Java` page using Markdown
      reference link syntax: `Java`.
    subpages: Subpages of this page. The order of subpages specified here will
      be honored in the generated docset.
  """

  content = _messages.StringField(1)
  name = _messages.StringField(2)
  subpages = _messages.MessageField('Page', 3, repeated=True)


class PhpSettings(_messages.Message):
  r"""Settings for Php client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ProjectSettings(_messages.Message):
  r"""Settings that control how a consumer project uses a service.

  Messages:
    PropertiesValue: Service-defined per-consumer properties. A key-value
      mapping a string key to a google.protobuf.ListValue proto. Values in the
      list are typed as defined in the Service configuration's
      consumer.properties field.

  Fields:
    consumerProjectId: ID for the project consuming this service.
    operations: Read-only view of pending operations affecting this resource,
      if requested.
    properties: Service-defined per-consumer properties. A key-value mapping a
      string key to a google.protobuf.ListValue proto. Values in the list are
      typed as defined in the Service configuration's consumer.properties
      field.
    quotaSettings: Settings that control how much or how fast the service can
      be used by the consumer project.
    serviceName: The name of the service. See the `ServiceManager` overview
      for naming requirements.
    usageSettings: Settings that control whether this service is usable by the
      consumer project.
    visibilitySettings: Settings that control which features of the service
      are visible to the consumer project.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""Service-defined per-consumer properties. A key-value mapping a string
    key to a google.protobuf.ListValue proto. Values in the list are typed as
    defined in the Service configuration's consumer.properties field.

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2, repeated=True)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consumerProjectId = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)
  properties = _messages.MessageField('PropertiesValue', 3)
  quotaSettings = _messages.MessageField('QuotaSettings', 4)
  serviceName = _messages.StringField(5)
  usageSettings = _messages.MessageField('UsageSettings', 6)
  visibilitySettings = _messages.MessageField('VisibilitySettings', 7)


class Publishing(_messages.Message):
  r"""This message configures the settings for publishing [Google Cloud Client
  libraries](https://cloud.google.com/apis/docs/cloud-client-libraries)
  generated from the service config.

  Enums:
    OrganizationValueValuesEnum: For whom the client library is being
      published.

  Fields:
    apiShortName: Used as a tracking tag when collecting data about the APIs
      developer relations artifacts like docs, packages delivered to package
      managers, etc. Example: "speech".
    codeownerGithubTeams: GitHub teams to be added to CODEOWNERS in the
      directory in GitHub containing source code for the client libraries for
      this API.
    docTagPrefix: A prefix used in sample code when demarking regions to be
      included in documentation.
    documentationUri: Link to product home page. Example:
      https://cloud.google.com/asset-inventory/docs/overview
    githubLabel: GitHub label to apply to issues and pull requests opened for
      this API.
    librarySettings: Client library settings. If the same version string
      appears multiple times in this list, then the last one wins. Settings
      from earlier settings with the same version string are discarded.
    methodSettings: A list of API method settings, e.g. the behavior for
      methods that use the long-running operation pattern.
    newIssueUri: Link to a *public* URI where users can report issues.
      Example: https://issuetracker.google.com/issues/new?component=190865&tem
      plate=1161103
    organization: For whom the client library is being published.
    protoReferenceDocumentationUri: Optional link to proto reference
      documentation. Example:
      https://cloud.google.com/pubsub/lite/docs/reference/rpc
  """

  class OrganizationValueValuesEnum(_messages.Enum):
    r"""For whom the client library is being published.

    Values:
      CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED: Not useful.
      CLOUD: Google Cloud Platform Org.
      ADS: Ads (Advertising) Org.
      PHOTOS: Photos Org.
      STREET_VIEW: Street View Org.
      SHOPPING: Shopping Org.
      GEO: Geo Org.
      GENERATIVE_AI: Generative AI - https://developers.generativeai.google
    """
    CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED = 0
    CLOUD = 1
    ADS = 2
    PHOTOS = 3
    STREET_VIEW = 4
    SHOPPING = 5
    GEO = 6
    GENERATIVE_AI = 7

  apiShortName = _messages.StringField(1)
  codeownerGithubTeams = _messages.StringField(2, repeated=True)
  docTagPrefix = _messages.StringField(3)
  documentationUri = _messages.StringField(4)
  githubLabel = _messages.StringField(5)
  librarySettings = _messages.MessageField('ClientLibrarySettings', 6, repeated=True)
  methodSettings = _messages.MessageField('MethodSettings', 7, repeated=True)
  newIssueUri = _messages.StringField(8)
  organization = _messages.EnumField('OrganizationValueValuesEnum', 9)
  protoReferenceDocumentationUri = _messages.StringField(10)


class PythonSettings(_messages.Message):
  r"""Settings for Python client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class QueryUserAccessResponse(_messages.Message):
  r"""Request message for QueryUserAccess method.

  Fields:
    accessibleVisibilityLabels: Any visibility labels on the service that are
      accessible by the user.
    canAccessService: True if the user can access the service and any
      unrestricted API surface.
  """

  accessibleVisibilityLabels = _messages.StringField(1, repeated=True)
  canAccessService = _messages.BooleanField(2)


class Quota(_messages.Message):
  r"""Quota configuration helps to achieve fairness and budgeting in service
  usage. The metric based quota configuration works this way: - The service
  configuration defines a set of metrics. - For API calls, the
  quota.metric_rules maps methods to metrics with corresponding costs. - The
  quota.limits defines limits on the metrics, which will be used for quota
  checks at runtime. An example quota configuration in yaml format: quota:
  limits: - name: apiWriteQpsPerProject metric:
  library.googleapis.com/write_calls unit: "1/min/{project}" # rate limit for
  consumer projects values: STANDARD: 10000 (The metric rules bind all methods
  to the read_calls metric, except for the UpdateBook and DeleteBook methods.
  These two methods are mapped to the write_calls metric, with the UpdateBook
  method consuming at twice rate as the DeleteBook method.) metric_rules: -
  selector: "*" metric_costs: library.googleapis.com/read_calls: 1 - selector:
  google.example.library.v1.LibraryService.UpdateBook metric_costs:
  library.googleapis.com/write_calls: 2 - selector:
  google.example.library.v1.LibraryService.DeleteBook metric_costs:
  library.googleapis.com/write_calls: 1 Corresponding Metric definition:
  metrics: - name: library.googleapis.com/read_calls display_name: Read
  requests metric_kind: DELTA value_type: INT64 - name:
  library.googleapis.com/write_calls display_name: Write requests metric_kind:
  DELTA value_type: INT64

  Fields:
    limits: List of QuotaLimit definitions for the service.
    metricRules: List of MetricRule definitions, each one mapping a selected
      method to one or more metrics.
  """

  limits = _messages.MessageField('QuotaLimit', 1, repeated=True)
  metricRules = _messages.MessageField('MetricRule', 2, repeated=True)


class QuotaBucket(_messages.Message):
  r"""The quota limit value and current usage for a quota bucket.

  Fields:
    bucketId: The quota bucket id
    limitValue: Limit value of the bucket, i.e., the maximum number of tokens
      in the bucket.
    maxConsumerOverrideAllowed: The maximum overrides value that a consumer
      may specify on the bucket.
    usage: The usage data for the bucket.
  """

  bucketId = _messages.MessageField('QuotaBucketId', 1)
  limitValue = _messages.IntegerField(2)
  maxConsumerOverrideAllowed = _messages.IntegerField(3)
  usage = _messages.MessageField('QuotaUsage', 4)


class QuotaBucketId(_messages.Message):
  r"""A quota bucket is an instance of a quota limit.

  Fields:
    containerId: A Quota limit is defined at container level ORGANIZATION,
      PROJECT. The container of a quota bucket for a quota limit is identified
      by organization id, or project id respectively.
    region: If a quota limit is defined on PER_REGION dimension, then each
      region will have its own quota bucket. This field is non-empty only if
      the quota limit is defined on PER_REGION dimension.
    zone: If a quota limit is defined on PER_ZONE dimension, then each zone
      will have its own quota bucket. This field is non-empty only if the
      quota limit is defined on PER_ZONE dimension.
  """

  containerId = _messages.StringField(1)
  region = _messages.StringField(2)
  zone = _messages.StringField(3)


class QuotaGroup(_messages.Message):
  r"""QuotaGroup is deprecated. Do not use. Used by group-based quotas only.

  Fields:
    billable: Indicates if the quota limits defined in this quota group apply
      to consumers who have active billing. Quota limits defined in billable
      groups will be applied only to consumers who have active billing. The
      amount of tokens consumed from billable quota group will also be
      reported for billing. Quota limits defined in non-billable groups will
      be applied only to consumers who have no active billing.
    description: User-visible description of this quota group.
    limits: Quota limits to be enforced when this quota group is used. A
      request must satisfy all the limits in a group for it to be permitted.
    name: Name of this quota group. Must be unique within the service. Quota
      group name is used as part of the id for quota limits. Once the quota
      group has been put into use, the name of the quota group should be
      immutable.
  """

  billable = _messages.BooleanField(1)
  description = _messages.StringField(2)
  limits = _messages.MessageField('QuotaLimit', 3, repeated=True)
  name = _messages.StringField(4)


class QuotaInfo(_messages.Message):
  r"""Metadata about an individual quota, containing usage and limit
  information.

  Fields:
    currentUsage: The usage data for this quota as it applies to the current
      limit.
    historicalUsage: The historical usage data of this quota limit. Currently
      it is only available for daily quota limit, that is, base_limit.duration
      = "1d".
    limit: The effective limit for this quota.
  """

  currentUsage = _messages.MessageField('QuotaUsage', 1)
  historicalUsage = _messages.MessageField('QuotaUsage', 2, repeated=True)
  limit = _messages.MessageField('EffectiveQuotaLimit', 3)


class QuotaLimit(_messages.Message):
  r"""`QuotaLimit` defines a specific limit that applies over a specified
  duration for a limit type. There can be at most one limit for a duration and
  limit type combination defined within a `QuotaGroup`.

  Messages:
    ValuesValue: Tiered limit values. You must specify this as a key:value
      pair, with an integer value that is the maximum number of requests
      allowed for the specified unit. Currently only STANDARD is supported.

  Fields:
    defaultLimit: Default number of tokens that can be consumed during the
      specified duration. This is the number of tokens assigned when a client
      application developer activates the service for his/her project.
      Specifying a value of 0 will block all requests. This can be used if you
      are provisioning quota to selected consumers and blocking others.
      Similarly, a value of -1 will indicate an unlimited quota. No other
      negative values are allowed. Used by group-based quotas only.
    description: Optional. User-visible, extended description for this quota
      limit. Should be used only when more context is needed to understand
      this limit than provided by the limit's display name (see:
      `display_name`).
    displayName: User-visible display name for this limit. Optional. If not
      set, the UI will provide a default display name based on the quota
      configuration. This field can be used to override the default display
      name generated from the configuration.
    duration: Duration of this limit in textual notation. Must be "100s" or
      "1d". Used by group-based quotas only.
    freeTier: Free tier value displayed in the Developers Console for this
      limit. The free tier is the number of tokens that will be subtracted
      from the billed amount when billing is enabled. This field can only be
      set on a limit with duration "1d", in a billable group; it is invalid on
      any other limit. If this field is not set, it defaults to 0, indicating
      that there is no free tier for this service. Used by group-based quotas
      only.
    maxLimit: Maximum number of tokens that can be consumed during the
      specified duration. Client application developers can override the
      default limit up to this maximum. If specified, this value cannot be set
      to a value less than the default limit. If not specified, it is set to
      the default limit. To allow clients to apply overrides with no upper
      bound, set this to -1, indicating unlimited maximum quota. Used by
      group-based quotas only.
    metric: The name of the metric this quota limit applies to. The quota
      limits with the same metric will be checked together during runtime. The
      metric must be defined within the service config.
    name: Name of the quota limit. The name must be provided, and it must be
      unique within the service. The name can only include alphanumeric
      characters as well as '-'. The maximum length of the limit name is 64
      characters.
    unit: Specify the unit of the quota limit. It uses the same syntax as
      Metric.unit. The supported unit kinds are determined by the quota
      backend system. Here are some examples: * "1/min/{project}" for quota
      per minute per project. Note: the order of unit components is
      insignificant. The "1" at the beginning is required to follow the metric
      unit syntax.
    values: Tiered limit values. You must specify this as a key:value pair,
      with an integer value that is the maximum number of requests allowed for
      the specified unit. Currently only STANDARD is supported.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValuesValue(_messages.Message):
    r"""Tiered limit values. You must specify this as a key:value pair, with
    an integer value that is the maximum number of requests allowed for the
    specified unit. Currently only STANDARD is supported.

    Messages:
      AdditionalProperty: An additional property for a ValuesValue object.

    Fields:
      additionalProperties: Additional properties of type ValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  defaultLimit = _messages.IntegerField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  duration = _messages.StringField(4)
  freeTier = _messages.IntegerField(5)
  maxLimit = _messages.IntegerField(6)
  metric = _messages.StringField(7)
  name = _messages.StringField(8)
  unit = _messages.StringField(9)
  values = _messages.MessageField('ValuesValue', 10)


class QuotaLimitOverride(_messages.Message):
  r"""Specifies a custom quota limit that is applied for this consumer
  project. This overrides the default value in google.api.QuotaLimit.

  Fields:
    limit: The new limit for this project. May be -1 (unlimited), 0 (block),
      or any positive integer.
    requesterResource: The resource id of the ancestry folder or organization
      the administrator of which requested the override. For example:
      "organizations/12345" or "folders/67890". Used by admin overrides only.
    unlimited: Indicates the override is to provide unlimited quota. If true,
      any value set for limit will be ignored. DEPRECATED. Use a limit value
      of -1 instead.
  """

  limit = _messages.IntegerField(1)
  requesterResource = _messages.StringField(2)
  unlimited = _messages.BooleanField(3)


class QuotaSettings(_messages.Message):
  r"""Per-consumer overrides for quota settings. See google/api/quota.proto
  for the corresponding service configuration which provides the default
  values.

  Messages:
    AdminOverridesValue: Quota overrides set by an administrator of a consumer
      organization or folder. The administrator of an organization or folder
      can set admin overrides for any folders or projects beneath it. When a
      project or folder moves out of the folder or organization that sets the
      admin override, the admin override will be removed. The key for this map
      is the same as the key for consumer_overrides.
    ConsumerOverridesValue: Quota overrides set by the consumer. Consumer
      overrides will only have an effect up to the max_limit specified in the
      service config, or the the producer override, if one exists. The key for
      this map is one of the following: - '/' for group-based quotas, where
      GROUP_NAME is the google.api.QuotaGroup.name field and LIMIT_NAME is the
      google.api.QuotaLimit.name field from the service config. For example:
      'ReadGroup/ProjectDaily'. - '' for metric-based quotas, where LIMIT_NAME
      is the google.api.QuotaLimit.name field from the service config. For
      example: 'borrowedCountPerOrganization'. - '[@DIMENSION_SETTINGS]+ for
      dimensional set overrides Where DIMENSION_SETTING is :. For example
      Limit1@region:us-central1 is an override for limit 1, for region us-
      central1 limit1@zone:us-central1-a is an override for limit 1, for zone
      us-central1-a. NOTE that for backwards compatibility, this is the same
      as region:us-central1-a limit2@region:us-east1@user:12345 is an override
      for region us-east1, and user set to 12345. Only metric-based quotas can
      have these overrides. Per-region override takes effect if both per-
      region override and global override are defined.
    EffectiveQuotasValue: The effective quota limits for each group, derived
      from the service defaults together with any producer or consumer
      overrides. For each limit, the effective value is the minimum of the
      producer and consumer overrides if either is present, or else the
      service default if neither is present. DEPRECATED. Use
      effective_quota_groups instead.
    ProducerOverridesValue: Quota overrides set by the producer. Note that if
      a consumer override is also specified, then the minimum of the two will
      be used. This allows consumers to cap their usage voluntarily. The key
      for this map is the same as the key for consumer_overrides.

  Fields:
    adminOverrides: Quota overrides set by an administrator of a consumer
      organization or folder. The administrator of an organization or folder
      can set admin overrides for any folders or projects beneath it. When a
      project or folder moves out of the folder or organization that sets the
      admin override, the admin override will be removed. The key for this map
      is the same as the key for consumer_overrides.
    consumerOverrides: Quota overrides set by the consumer. Consumer overrides
      will only have an effect up to the max_limit specified in the service
      config, or the the producer override, if one exists. The key for this
      map is one of the following: - '/' for group-based quotas, where
      GROUP_NAME is the google.api.QuotaGroup.name field and LIMIT_NAME is the
      google.api.QuotaLimit.name field from the service config. For example:
      'ReadGroup/ProjectDaily'. - '' for metric-based quotas, where LIMIT_NAME
      is the google.api.QuotaLimit.name field from the service config. For
      example: 'borrowedCountPerOrganization'. - '[@DIMENSION_SETTINGS]+ for
      dimensional set overrides Where DIMENSION_SETTING is :. For example
      Limit1@region:us-central1 is an override for limit 1, for region us-
      central1 limit1@zone:us-central1-a is an override for limit 1, for zone
      us-central1-a. NOTE that for backwards compatibility, this is the same
      as region:us-central1-a limit2@region:us-east1@user:12345 is an override
      for region us-east1, and user set to 12345. Only metric-based quotas can
      have these overrides. Per-region override takes effect if both per-
      region override and global override are defined.
    effectiveQuotaForMetrics: Use this field for metric-based quota limits.
      Combines service quota configuration and project-specific settings, as a
      map from metric name to the effective quota information for quota limits
      on that metric. Output-only
    effectiveQuotaGroups: Use this field for group-based quota limits.
      Combines service quota configuration and project-specific settings, as a
      map from quota group name to the effective quota information for that
      group. Output-only.
    effectiveQuotas: The effective quota limits for each group, derived from
      the service defaults together with any producer or consumer overrides.
      For each limit, the effective value is the minimum of the producer and
      consumer overrides if either is present, or else the service default if
      neither is present. DEPRECATED. Use effective_quota_groups instead.
    force: Whether to force applying the quota settings, even if this may
      decrease effective limit for some quotas over the safe threshold, which
      is currently set to 10 percent. When unset or set to false, the whole
      request fails if quota overrides changes decrease effective limit for
      any quota over the safe threshold. When set to true, the reason for
      forcing the change must be specified in the 'request_reason' field of
      the RPC system parameter context. Input only.
    producerOverrides: Quota overrides set by the producer. Note that if a
      consumer override is also specified, then the minimum of the two will be
      used. This allows consumers to cap their usage voluntarily. The key for
      this map is the same as the key for consumer_overrides.
    variableTermQuotas: Quotas that are active over a specified time period.
      Only writeable by the producer.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdminOverridesValue(_messages.Message):
    r"""Quota overrides set by an administrator of a consumer organization or
    folder. The administrator of an organization or folder can set admin
    overrides for any folders or projects beneath it. When a project or folder
    moves out of the folder or organization that sets the admin override, the
    admin override will be removed. The key for this map is the same as the
    key for consumer_overrides.

    Messages:
      AdditionalProperty: An additional property for a AdminOverridesValue
        object.

    Fields:
      additionalProperties: Additional properties of type AdminOverridesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdminOverridesValue object.

      Fields:
        key: Name of the additional property.
        value: A QuotaLimitOverride attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('QuotaLimitOverride', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConsumerOverridesValue(_messages.Message):
    r"""Quota overrides set by the consumer. Consumer overrides will only have
    an effect up to the max_limit specified in the service config, or the the
    producer override, if one exists. The key for this map is one of the
    following: - '/' for group-based quotas, where GROUP_NAME is the
    google.api.QuotaGroup.name field and LIMIT_NAME is the
    google.api.QuotaLimit.name field from the service config. For example:
    'ReadGroup/ProjectDaily'. - '' for metric-based quotas, where LIMIT_NAME
    is the google.api.QuotaLimit.name field from the service config. For
    example: 'borrowedCountPerOrganization'. - '[@DIMENSION_SETTINGS]+ for
    dimensional set overrides Where DIMENSION_SETTING is :. For example
    Limit1@region:us-central1 is an override for limit 1, for region us-
    central1 limit1@zone:us-central1-a is an override for limit 1, for zone
    us-central1-a. NOTE that for backwards compatibility, this is the same as
    region:us-central1-a limit2@region:us-east1@user:12345 is an override for
    region us-east1, and user set to 12345. Only metric-based quotas can have
    these overrides. Per-region override takes effect if both per-region
    override and global override are defined.

    Messages:
      AdditionalProperty: An additional property for a ConsumerOverridesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ConsumerOverridesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConsumerOverridesValue object.

      Fields:
        key: Name of the additional property.
        value: A QuotaLimitOverride attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('QuotaLimitOverride', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EffectiveQuotasValue(_messages.Message):
    r"""The effective quota limits for each group, derived from the service
    defaults together with any producer or consumer overrides. For each limit,
    the effective value is the minimum of the producer and consumer overrides
    if either is present, or else the service default if neither is present.
    DEPRECATED. Use effective_quota_groups instead.

    Messages:
      AdditionalProperty: An additional property for a EffectiveQuotasValue
        object.

    Fields:
      additionalProperties: Additional properties of type EffectiveQuotasValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EffectiveQuotasValue object.

      Fields:
        key: Name of the additional property.
        value: A QuotaLimitOverride attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('QuotaLimitOverride', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ProducerOverridesValue(_messages.Message):
    r"""Quota overrides set by the producer. Note that if a consumer override
    is also specified, then the minimum of the two will be used. This allows
    consumers to cap their usage voluntarily. The key for this map is the same
    as the key for consumer_overrides.

    Messages:
      AdditionalProperty: An additional property for a ProducerOverridesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ProducerOverridesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ProducerOverridesValue object.

      Fields:
        key: Name of the additional property.
        value: A QuotaLimitOverride attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('QuotaLimitOverride', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  adminOverrides = _messages.MessageField('AdminOverridesValue', 1)
  consumerOverrides = _messages.MessageField('ConsumerOverridesValue', 2)
  effectiveQuotaForMetrics = _messages.MessageField('EffectiveQuotasForMetric', 3, repeated=True)
  effectiveQuotaGroups = _messages.MessageField('EffectiveQuotaGroup', 4, repeated=True)
  effectiveQuotas = _messages.MessageField('EffectiveQuotasValue', 5)
  force = _messages.BooleanField(6)
  producerOverrides = _messages.MessageField('ProducerOverridesValue', 7)
  variableTermQuotas = _messages.MessageField('VariableTermQuota', 8, repeated=True)


class QuotaUsage(_messages.Message):
  r"""Specifies the used quota amount for a quota limit at a particular time.

  Enums:
    HierarchyLimitWarningValueValuesEnum: Somewhere in hierarchy a limit is
      close to full. Readonly

  Fields:
    endTime: The time the quota duration ended.
    hierarchyLimitWarning: Somewhere in hierarchy a limit is close to full.
      Readonly
    queryTime: The time the quota usage data was queried.
    startTime: The time the quota duration started.
    usage: The used quota value at the "query_time".
  """

  class HierarchyLimitWarningValueValuesEnum(_messages.Enum):
    r"""Somewhere in hierarchy a limit is close to full. Readonly

    Values:
      HIERARCHY_LIMIT_WARNING_UNSPECIFIED: <no description>
      NO_WARNING: <no description>
      WARNING: <no description>
    """
    HIERARCHY_LIMIT_WARNING_UNSPECIFIED = 0
    NO_WARNING = 1
    WARNING = 2

  endTime = _messages.StringField(1)
  hierarchyLimitWarning = _messages.EnumField('HierarchyLimitWarningValueValuesEnum', 2)
  queryTime = _messages.StringField(3)
  startTime = _messages.StringField(4)
  usage = _messages.IntegerField(5)


class ResourceReference(_messages.Message):
  r"""Defines a proto annotation that describes a string field that refers to
  an API resource.

  Fields:
    childType: The resource type of a child collection that the annotated
      field references. This is useful for annotating the `parent` field that
      doesn't have a fixed resource type. Example: message
      ListLogEntriesRequest { string parent = 1
      [(google.api.resource_reference) = { child_type:
      "logging.googleapis.com/LogEntry" }; }
    type: The resource type that the annotated field references. Example:
      message Subscription { string topic = 2 [(google.api.resource_reference)
      = { type: "pubsub.googleapis.com/Topic" }]; } Occasionally, a field may
      reference an arbitrary resource. In this case, APIs use the special
      value * in their resource reference. Example: message
      GetIamPolicyRequest { string resource = 2
      [(google.api.resource_reference) = { type: "*" }]; }
  """

  childType = _messages.StringField(1)
  type = _messages.StringField(2)


class Rollout(_messages.Message):
  r"""A rollout resource that defines how service configuration versions are
  pushed to control plane systems. Typically, you create a new version of the
  service config, and then create a Rollout to push the service config.

  Enums:
    StatusValueValuesEnum: The status of this rollout. Readonly. In case of a
      failed rollout, the system will automatically rollback to the current
      Rollout version. Readonly.

  Fields:
    createTime: Creation time of the rollout. Readonly.
    createdBy: The user who created the Rollout. Readonly.
    deleteServiceStrategy: The strategy associated with a rollout to delete a
      `ManagedService`. Readonly.
    rolloutId: Optional. Unique identifier of this Rollout. Must be no longer
      than 63 characters and only lower case letters, digits, '.', '_' and '-'
      are allowed. If not specified by client, the server will generate one.
      The generated id will have the form of , where "date" is the create date
      in ISO 8601 format. "revision number" is a monotonically increasing
      positive number that is reset every day for each service. An example of
      the generated rollout_id is '2016-02-16r1'
    serviceName: The name of the service associated with this Rollout.
    status: The status of this rollout. Readonly. In case of a failed rollout,
      the system will automatically rollback to the current Rollout version.
      Readonly.
    trafficPercentStrategy: Google Service Control selects service
      configurations based on traffic percentage.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status of this rollout. Readonly. In case of a failed rollout, the
    system will automatically rollback to the current Rollout version.
    Readonly.

    Values:
      ROLLOUT_STATUS_UNSPECIFIED: No status specified.
      IN_PROGRESS: The Rollout is in progress.
      SUCCESS: The Rollout has completed successfully.
      CANCELLED: The Rollout has been cancelled. This can happen if you have
        overlapping Rollout pushes, and the previous ones will be cancelled.
      FAILED: The Rollout has failed and the rollback attempt has failed too.
      PENDING: The Rollout has not started yet and is pending for execution.
      FAILED_ROLLED_BACK: The Rollout has failed and rolled back to the
        previous successful Rollout.
    """
    ROLLOUT_STATUS_UNSPECIFIED = 0
    IN_PROGRESS = 1
    SUCCESS = 2
    CANCELLED = 3
    FAILED = 4
    PENDING = 5
    FAILED_ROLLED_BACK = 6

  createTime = _messages.StringField(1)
  createdBy = _messages.StringField(2)
  deleteServiceStrategy = _messages.MessageField('DeleteServiceStrategy', 3)
  rolloutId = _messages.StringField(4)
  serviceName = _messages.StringField(5)
  status = _messages.EnumField('StatusValueValuesEnum', 6)
  trafficPercentStrategy = _messages.MessageField('TrafficPercentStrategy', 7)


class RubySettings(_messages.Message):
  r"""Settings for Ruby client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class Service(_messages.Message):
  r"""`Service` is the root object of Google API service configuration
  (service config). It describes the basic information about a logical
  service, such as the service name and the user-facing title, and delegates
  other aspects to sub-sections. Each sub-section is either a proto message or
  a repeated proto message that configures a specific aspect, such as auth.
  For more information, see each proto message definition. Example: type:
  google.api.Service name: calendar.googleapis.com title: Google Calendar API
  apis: - name: google.calendar.v3.Calendar visibility: rules: - selector:
  "google.calendar.v3.*" restriction: PREVIEW backend: rules: - selector:
  "google.calendar.v3.*" address: calendar.example.com authentication:
  providers: - id: google_calendar_auth jwks_uri:
  https://www.googleapis.com/oauth2/v1/certs issuer:
  https://securetoken.google.com rules: - selector: "*" requirements:
  provider_id: google_calendar_auth

  Fields:
    apis: A list of API interfaces exported by this service. Only the `name`
      field of the google.protobuf.Api needs to be provided by the
      configuration author, as the remaining fields will be derived from the
      IDL during the normalization process. It is an error to specify an API
      interface here which cannot be resolved against the associated IDL
      files.
    authentication: Auth configuration.
    backend: API backend configuration.
    billing: Billing configuration.
    configVersion: Obsolete. Do not use. This field has no semantic meaning.
      The service config compiler always sets this field to `3`.
    context: Context configuration.
    control: Configuration for the service control plane.
    customError: Custom error configuration.
    documentation: Additional API documentation.
    endpoints: Configuration for network endpoints. If this is empty, then an
      endpoint with the same name as the service is automatically generated to
      service all defined APIs.
    enums: A list of all enum types included in this API service. Enums
      referenced directly or indirectly by the `apis` are automatically
      included. Enums which are not referenced but shall be included should be
      listed here by name by the configuration author. Example: enums: - name:
      google.someapi.v1.SomeEnum
    http: HTTP configuration.
    id: A unique ID for a specific instance of this message, typically
      assigned by the client for tracking purpose. Must be no longer than 63
      characters and only lower case letters, digits, '.', '_' and '-' are
      allowed. If empty, the server may choose to generate one instead.
    logging: Logging configuration.
    logs: Defines the logs used by this service.
    metrics: Defines the metrics used by this service.
    monitoredResources: Defines the monitored resources used by this service.
      This is required by the Service.monitoring and Service.logging
      configurations.
    monitoring: Monitoring configuration.
    name: The service name, which is a DNS-like logical identifier for the
      service, such as `calendar.googleapis.com`. The service name typically
      goes through DNS verification to make sure the owner of the service also
      owns the DNS name.
    producerProjectId: The Google project that owns this service.
    publishing: Settings for [Google Cloud Client
      libraries](https://cloud.google.com/apis/docs/cloud-client-libraries)
      generated from APIs defined as protocol buffers.
    quota: Quota configuration.
    sourceInfo: Output only. The source information for this configuration if
      available.
    systemParameters: System parameter configuration.
    systemTypes: A list of all proto message types included in this API
      service. It serves similar purpose as [google.api.Service.types], except
      that these types are not needed by user-defined APIs. Therefore, they
      will not show up in the generated discovery doc. This field should only
      be used to define system APIs in ESF.
    title: The product title for this service, it is the name displayed in
      Google Cloud Console.
    types: A list of all proto message types included in this API service.
      Types referenced directly or indirectly by the `apis` are automatically
      included. Messages which are not referenced but shall be included, such
      as types used by the `google.protobuf.Any` type, should be listed here
      by name by the configuration author. Example: types: - name:
      google.protobuf.Int32
    usage: Configuration controlling usage of this service.
  """

  apis = _messages.MessageField('Api', 1, repeated=True)
  authentication = _messages.MessageField('Authentication', 2)
  backend = _messages.MessageField('Backend', 3)
  billing = _messages.MessageField('Billing', 4)
  configVersion = _messages.IntegerField(5, variant=_messages.Variant.UINT32)
  context = _messages.MessageField('Context', 6)
  control = _messages.MessageField('Control', 7)
  customError = _messages.MessageField('CustomError', 8)
  documentation = _messages.MessageField('Documentation', 9)
  endpoints = _messages.MessageField('Endpoint', 10, repeated=True)
  enums = _messages.MessageField('Enum', 11, repeated=True)
  http = _messages.MessageField('Http', 12)
  id = _messages.StringField(13)
  logging = _messages.MessageField('Logging', 14)
  logs = _messages.MessageField('LogDescriptor', 15, repeated=True)
  metrics = _messages.MessageField('MetricDescriptor', 16, repeated=True)
  monitoredResources = _messages.MessageField('MonitoredResourceDescriptor', 17, repeated=True)
  monitoring = _messages.MessageField('Monitoring', 18)
  name = _messages.StringField(19)
  producerProjectId = _messages.StringField(20)
  publishing = _messages.MessageField('Publishing', 21)
  quota = _messages.MessageField('Quota', 22)
  sourceInfo = _messages.MessageField('SourceInfo', 23)
  systemParameters = _messages.MessageField('SystemParameters', 24)
  systemTypes = _messages.MessageField('Type', 25, repeated=True)
  title = _messages.StringField(26)
  types = _messages.MessageField('Type', 27, repeated=True)
  usage = _messages.MessageField('Usage', 28)


class ServicemanagementOperationsGetRequest(_messages.Message):
  r"""A ServicemanagementOperationsGetRequest object.

  Fields:
    operationsId: Part of `name`. The name of the operation resource.
  """

  operationsId = _messages.StringField(1, required=True)


class ServicemanagementOperationsListRequest(_messages.Message):
  r"""A ServicemanagementOperationsListRequest object.

  Fields:
    filter: A string for filtering Operations. The following filter fields are
      supported: * serviceName: Required. Only `=` operator is allowed. *
      startTime: The time this job was started, in ISO 8601 format. Allowed
      operators are `>=`, `>`, `<=`, and `<`. * status: Can be `done`,
      `in_progress`, or `failed`. Allowed operators are `=`, and `!=`. Filter
      expression supports conjunction (AND) and disjunction (OR) logical
      operators. However, the serviceName restriction must be at the top-level
      and can only be combined with other restrictions via the AND logical
      operator. Examples: * `serviceName={some-service}.googleapis.com` *
      `serviceName={some-service}.googleapis.com AND startTime>="2017-02-01"`
      * `serviceName={some-service}.googleapis.com AND status=done` *
      `serviceName={some-service}.googleapis.com AND (status=done OR
      startTime>="2017-02-01")`
    name: Not used.
    pageSize: The maximum number of operations to return. If unspecified,
      defaults to 50. The maximum value is 100.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ServicemanagementServicesAccessPolicyQueryRequest(_messages.Message):
  r"""A ServicemanagementServicesAccessPolicyQueryRequest object.

  Fields:
    serviceName: The service to query access for.
    userEmail: The user to query access for.
  """

  serviceName = _messages.StringField(1, required=True)
  userEmail = _messages.StringField(2)


class ServicemanagementServicesConfigsCreateRequest(_messages.Message):
  r"""A ServicemanagementServicesConfigsCreateRequest object.

  Fields:
    service: A Service resource to be passed as the request body.
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
  """

  service = _messages.MessageField('Service', 1)
  serviceName = _messages.StringField(2, required=True)


class ServicemanagementServicesConfigsGetRequest(_messages.Message):
  r"""A ServicemanagementServicesConfigsGetRequest object.

  Enums:
    ViewValueValuesEnum: Specifies which parts of the Service Config should be
      returned in the response.

  Fields:
    configId: Required. The id of the service configuration resource. This
      field must be specified for the server to return all fields, including
      `SourceInfo`.
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
    view: Specifies which parts of the Service Config should be returned in
      the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which parts of the Service Config should be returned in the
    response.

    Values:
      BASIC: Server response includes all fields except SourceInfo.
      FULL: Server response includes all fields including SourceInfo.
        SourceFiles are of type 'google.api.servicemanagement.v1.ConfigFile'
        and are only available for configs created using the
        SubmitConfigSource method.
    """
    BASIC = 0
    FULL = 1

  configId = _messages.StringField(1, required=True)
  serviceName = _messages.StringField(2, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 3)


class ServicemanagementServicesConfigsListRequest(_messages.Message):
  r"""A ServicemanagementServicesConfigsListRequest object.

  Fields:
    pageSize: The max number of items to include in the response list. Page
      size is 50 if not specified. Maximum value is 100.
    pageToken: The token of the page to retrieve.
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  serviceName = _messages.StringField(3, required=True)


class ServicemanagementServicesConfigsSubmitRequest(_messages.Message):
  r"""A ServicemanagementServicesConfigsSubmitRequest object.

  Fields:
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
    submitConfigSourceRequest: A SubmitConfigSourceRequest resource to be
      passed as the request body.
  """

  serviceName = _messages.StringField(1, required=True)
  submitConfigSourceRequest = _messages.MessageField('SubmitConfigSourceRequest', 2)


class ServicemanagementServicesConsumersGetIamPolicyRequest(_messages.Message):
  r"""A ServicemanagementServicesConsumersGetIamPolicyRequest object.

  Fields:
    consumersId: Part of `resource`. See documentation of `servicesId`.
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    servicesId: Part of `resource`. REQUIRED: The resource for which the
      policy is being requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  consumersId = _messages.StringField(1, required=True)
  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 2)
  servicesId = _messages.StringField(3, required=True)


class ServicemanagementServicesConsumersSetIamPolicyRequest(_messages.Message):
  r"""A ServicemanagementServicesConsumersSetIamPolicyRequest object.

  Fields:
    consumersId: Part of `resource`. See documentation of `servicesId`.
    servicesId: Part of `resource`. REQUIRED: The resource for which the
      policy is being specified. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  consumersId = _messages.StringField(1, required=True)
  servicesId = _messages.StringField(2, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 3)


class ServicemanagementServicesConsumersTestIamPermissionsRequest(_messages.Message):
  r"""A ServicemanagementServicesConsumersTestIamPermissionsRequest object.

  Fields:
    consumersId: Part of `resource`. See documentation of `servicesId`.
    servicesId: Part of `resource`. REQUIRED: The resource for which the
      policy detail is being requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  consumersId = _messages.StringField(1, required=True)
  servicesId = _messages.StringField(2, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 3)


class ServicemanagementServicesCustomerSettingsGetRequest(_messages.Message):
  r"""A ServicemanagementServicesCustomerSettingsGetRequest object.

  Enums:
    ViewValueValuesEnum: Request only fields for the specified view.

  Fields:
    customerId: ID for the customer. See the comment for
      `CustomerSettings.customer_id` field of message for its format. This
      field is required.
    expand: Fields to expand in any results.
    serviceName: The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`. This field
      is required.
    view: Request only fields for the specified view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Request only fields for the specified view.

    Values:
      PROJECT_SETTINGS_VIEW_UNSPECIFIED: View not specified. Requests with an
        unspecified view will be rejected.
      CONSUMER_VIEW: View only the fields accessible to the service consumer.
      PRODUCER_VIEW: View only the fields accessible to the service producer.
      ALL: View all settings. This will result in an authorization error if
        the user does not have read access on both the consumer project and
        the producer project.
    """
    PROJECT_SETTINGS_VIEW_UNSPECIFIED = 0
    CONSUMER_VIEW = 1
    PRODUCER_VIEW = 2
    ALL = 3

  customerId = _messages.StringField(1, required=True)
  expand = _messages.StringField(2)
  serviceName = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class ServicemanagementServicesCustomerSettingsPatchRequest(_messages.Message):
  r"""A ServicemanagementServicesCustomerSettingsPatchRequest object.

  Fields:
    customerId: ID for the customer. See the comment for
      `CustomerSettings.customer_id` field of message for its format. This
      field is required.
    customerSettings: A CustomerSettings resource to be passed as the request
      body.
    serviceName: The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`. This field
      is required.
    updateMask: The field mask specifying which fields are to be updated.
  """

  customerId = _messages.StringField(1, required=True)
  customerSettings = _messages.MessageField('CustomerSettings', 2)
  serviceName = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)


class ServicemanagementServicesDeleteRequest(_messages.Message):
  r"""A ServicemanagementServicesDeleteRequest object.

  Fields:
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
  """

  serviceName = _messages.StringField(1, required=True)


class ServicemanagementServicesEnableRequest(_messages.Message):
  r"""A ServicemanagementServicesEnableRequest object.

  Fields:
    enableServiceRequest: A EnableServiceRequest resource to be passed as the
      request body.
    serviceName: Required. Name of the service to enable. Specifying an
      unknown service name will cause the request to fail.
  """

  enableServiceRequest = _messages.MessageField('EnableServiceRequest', 1)
  serviceName = _messages.StringField(2, required=True)


class ServicemanagementServicesGetConfigRequest(_messages.Message):
  r"""A ServicemanagementServicesGetConfigRequest object.

  Enums:
    ViewValueValuesEnum: Specifies which parts of the Service Config should be
      returned in the response.

  Fields:
    configId: Required. The id of the service configuration resource. This
      field must be specified for the server to return all fields, including
      `SourceInfo`.
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
    view: Specifies which parts of the Service Config should be returned in
      the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which parts of the Service Config should be returned in the
    response.

    Values:
      BASIC: Server response includes all fields except SourceInfo.
      FULL: Server response includes all fields including SourceInfo.
        SourceFiles are of type 'google.api.servicemanagement.v1.ConfigFile'
        and are only available for configs created using the
        SubmitConfigSource method.
    """
    BASIC = 0
    FULL = 1

  configId = _messages.StringField(1)
  serviceName = _messages.StringField(2, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 3)


class ServicemanagementServicesGetIamPolicyRequest(_messages.Message):
  r"""A ServicemanagementServicesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    servicesId: Part of `resource`. REQUIRED: The resource for which the
      policy is being requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  servicesId = _messages.StringField(2, required=True)


class ServicemanagementServicesGetRequest(_messages.Message):
  r"""A ServicemanagementServicesGetRequest object.

  Enums:
    ViewValueValuesEnum: If project_settings is expanded, request only fields
      for the specified view.

  Fields:
    consumerProjectId: If project_settings is expanded, return settings for
      the specified consumer project.
    expand: Fields to expand in any results. By default, the following fields
      are not present in the result: - `project_settings`
    serviceName: Required. The name of the service. See the `ServiceManager`
      overview for naming requirements. For example: `example.googleapis.com`.
    view: If project_settings is expanded, request only fields for the
      specified view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""If project_settings is expanded, request only fields for the specified
    view.

    Values:
      PROJECT_SETTINGS_VIEW_UNSPECIFIED: View not specified. Requests with an
        unspecified view will be rejected.
      CONSUMER_VIEW: View only the fields accessible to the service consumer.
      PRODUCER_VIEW: View only the fields accessible to the service producer.
      ALL: View all settings. This will result in an authorization error if
        the user does not have read access on both the consumer project and
        the producer project.
    """
    PROJECT_SETTINGS_VIEW_UNSPECIFIED = 0
    CONSUMER_VIEW = 1
    PRODUCER_VIEW = 2
    ALL = 3

  consumerProjectId = _messages.StringField(1)
  expand = _messages.StringField(2)
  serviceName = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class ServicemanagementServicesListRequest(_messages.Message):
  r"""A ServicemanagementServicesListRequest object.

  Fields:
    category: Include services only in the specified category. Supported
      categories are servicemanagement.googleapis.com/categories/google-
      services or servicemanagement.googleapis.com/categories/play-games.
    consumerId: Include services consumed by the specified consumer. The
      Google Service Management implementation accepts the following forms: -
      project:
    consumerProjectId: Include services consumed by the specified project. If
      project_settings is expanded, then this field controls which project
      project_settings is populated for.
    pageSize: The max number of items to include in the response list. Page
      size is 50 if not specified. Maximum value is 500.
    pageToken: Token identifying which result to start with; returned by a
      previous list call.
    producerProjectId: Include services produced by the specified project.
  """

  category = _messages.StringField(1)
  consumerId = _messages.StringField(2)
  consumerProjectId = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  producerProjectId = _messages.StringField(6)


class ServicemanagementServicesProjectSettingsGetRequest(_messages.Message):
  r"""A ServicemanagementServicesProjectSettingsGetRequest object.

  Enums:
    ViewValueValuesEnum: Request only the fields for the specified view.

  Fields:
    consumerProjectId: The project ID of the consumer.
    expand: Fields to expand in any results. By default, the following fields
      are not present in the result: - `operations` - `quota_usage`
    serviceName: The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
    view: Request only the fields for the specified view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Request only the fields for the specified view.

    Values:
      PROJECT_SETTINGS_VIEW_UNSPECIFIED: View not specified. Requests with an
        unspecified view will be rejected.
      CONSUMER_VIEW: View only the fields accessible to the service consumer.
      PRODUCER_VIEW: View only the fields accessible to the service producer.
      ALL: View all settings. This will result in an authorization error if
        the user does not have read access on both the consumer project and
        the producer project.
    """
    PROJECT_SETTINGS_VIEW_UNSPECIFIED = 0
    CONSUMER_VIEW = 1
    PRODUCER_VIEW = 2
    ALL = 3

  consumerProjectId = _messages.StringField(1, required=True)
  expand = _messages.StringField(2)
  serviceName = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class ServicemanagementServicesProjectSettingsPatchRequest(_messages.Message):
  r"""A ServicemanagementServicesProjectSettingsPatchRequest object.

  Fields:
    consumerProjectId: The project ID of the consumer.
    excludeFinalQuotaSettingsInResponse: Do not include updated quota setting
      in the operation response.
    projectSettings: A ProjectSettings resource to be passed as the request
      body.
    serviceName: The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
    updateMask: The field mask specifying which fields are to be updated.
  """

  consumerProjectId = _messages.StringField(1, required=True)
  excludeFinalQuotaSettingsInResponse = _messages.BooleanField(2)
  projectSettings = _messages.MessageField('ProjectSettings', 3)
  serviceName = _messages.StringField(4, required=True)
  updateMask = _messages.StringField(5)


class ServicemanagementServicesRolloutsCreateRequest(_messages.Message):
  r"""A ServicemanagementServicesRolloutsCreateRequest object.

  Fields:
    force: Optional. This flag will skip safety checks for this rollout. The
      current safety check is whether to skip default quota limit validation.
      Quota limit validation verifies that the default quota limits defined in
      the configs that are effective in this rollout don't decrease by more
      than a specific percentage (10% right now) from the configs that are
      effective in the current rollout. For group-based quota limits, the
      default limit for a quota limit cannot decrease by more than 10%. For
      metric-based quota limits, the assigned quota for each reputation tier
      cannot decrease by more than 10%. Regional quota is assigned per region,
      and the quota for each region cannot decrease by more than 10%. Removing
      a regional quota can cause an effective decrease for that region, if the
      global quota for that tier is lower. For example, if the current rollout
      has a quota limit with values: {STANDARD: 50, STANDARD/us-central1: 100}
      and it is to be changed in the new rollout to: {STANDARD: 50} The net
      effect is the STANDARD tier in us-central1 is decreased by 50%. Adding a
      regional quota can have a similar effect for that region. In order to
      gradually dial down default quota limit, the recommended practice is to
      create multiple rollouts at least 1 hour apart.
    rollout: A Rollout resource to be passed as the request body.
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
  """

  force = _messages.BooleanField(1)
  rollout = _messages.MessageField('Rollout', 2)
  serviceName = _messages.StringField(3, required=True)


class ServicemanagementServicesRolloutsGetRequest(_messages.Message):
  r"""A ServicemanagementServicesRolloutsGetRequest object.

  Fields:
    rolloutId: Required. The id of the rollout resource.
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
  """

  rolloutId = _messages.StringField(1, required=True)
  serviceName = _messages.StringField(2, required=True)


class ServicemanagementServicesRolloutsListRequest(_messages.Message):
  r"""A ServicemanagementServicesRolloutsListRequest object.

  Fields:
    filter: Required. Use `filter` to return subset of rollouts. The following
      filters are supported: -- By status. For example,
      `filter='status=SUCCESS'` -- By strategy. For example,
      `filter='strategy=TrafficPercentStrategy'`
    pageSize: The max number of items to include in the response list. Page
      size is 50 if not specified. Maximum value is 100.
    pageToken: The token of the page to retrieve.
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  serviceName = _messages.StringField(4, required=True)


class ServicemanagementServicesSetIamPolicyRequest(_messages.Message):
  r"""A ServicemanagementServicesSetIamPolicyRequest object.

  Fields:
    servicesId: Part of `resource`. REQUIRED: The resource for which the
      policy is being specified. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  servicesId = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ServicemanagementServicesTestIamPermissionsRequest(_messages.Message):
  r"""A ServicemanagementServicesTestIamPermissionsRequest object.

  Fields:
    servicesId: Part of `resource`. REQUIRED: The resource for which the
      policy detail is being requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  servicesId = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ServicemanagementServicesUndeleteRequest(_messages.Message):
  r"""A ServicemanagementServicesUndeleteRequest object.

  Fields:
    serviceName: Required. The name of the service. See the
      [overview](https://cloud.google.com/service-management/overview) for
      naming requirements. For example: `example.googleapis.com`.
  """

  serviceName = _messages.StringField(1, required=True)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SourceContext(_messages.Message):
  r"""`SourceContext` represents information about the source of a protobuf
  element, like the file in which it is defined.

  Fields:
    fileName: The path-qualified name of the .proto file that contained the
      associated protobuf element. For example:
      `"google/protobuf/source_context.proto"`.
  """

  fileName = _messages.StringField(1)


class SourceInfo(_messages.Message):
  r"""Source information used to create a Service Config

  Messages:
    SourceFilesValueListEntry: A SourceFilesValueListEntry object.

  Fields:
    sourceFiles: All files used during config generation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourceFilesValueListEntry(_messages.Message):
    r"""A SourceFilesValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        SourceFilesValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourceFilesValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  sourceFiles = _messages.MessageField('SourceFilesValueListEntry', 1, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Step(_messages.Message):
  r"""Represents the status of one operation step.

  Enums:
    StatusValueValuesEnum: The status code.

  Fields:
    description: The short description of the step.
    status: The status code.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status code.

    Values:
      STATUS_UNSPECIFIED: Unspecifed code.
      DONE: The operation or step has completed without errors.
      NOT_STARTED: The operation or step has not started yet.
      IN_PROGRESS: The operation or step is in progress.
      FAILED: The operation or step has completed with errors. If the
        operation is rollbackable, the rollback completed with errors too.
      CANCELLED: The operation or step has completed with cancellation.
    """
    STATUS_UNSPECIFIED = 0
    DONE = 1
    NOT_STARTED = 2
    IN_PROGRESS = 3
    FAILED = 4
    CANCELLED = 5

  description = _messages.StringField(1)
  status = _messages.EnumField('StatusValueValuesEnum', 2)


class SubmitConfigSourceRequest(_messages.Message):
  r"""Request message for SubmitConfigSource method.

  Fields:
    configSource: Required. The source configuration for the service.
    validateOnly: Optional. If set, this will result in the generation of a
      `google.api.Service` configuration based on the `ConfigSource` provided,
      but the generated config and the sources will NOT be persisted.
  """

  configSource = _messages.MessageField('ConfigSource', 1)
  validateOnly = _messages.BooleanField(2)


class SubmitConfigSourceResponse(_messages.Message):
  r"""Response message for SubmitConfigSource method.

  Fields:
    diagnostics: Diagnostics occurred during config conversion.
    serviceConfig: The generated service configuration.
  """

  diagnostics = _messages.MessageField('Diagnostic', 1, repeated=True)
  serviceConfig = _messages.MessageField('Service', 2)


class SystemParameter(_messages.Message):
  r"""Define a parameter's name and location. The parameter may be passed as
  either an HTTP header or a URL query parameter, and if both are passed the
  behavior is implementation-dependent.

  Fields:
    httpHeader: Define the HTTP header name to use for the parameter. It is
      case insensitive.
    name: Define the name of the parameter, such as "api_key" . It is case
      sensitive.
    urlQueryParameter: Define the URL query parameter name to use for the
      parameter. It is case sensitive.
  """

  httpHeader = _messages.StringField(1)
  name = _messages.StringField(2)
  urlQueryParameter = _messages.StringField(3)


class SystemParameterRule(_messages.Message):
  r"""Define a system parameter rule mapping system parameter definitions to
  methods.

  Fields:
    parameters: Define parameters. Multiple names may be defined for a
      parameter. For a given method call, only one of them should be used. If
      multiple names are used the behavior is implementation-dependent. If
      none of the specified names are present the behavior is parameter-
      dependent.
    selector: Selects the methods to which this rule applies. Use '*' to
      indicate all methods in all APIs. Refer to selector for syntax details.
  """

  parameters = _messages.MessageField('SystemParameter', 1, repeated=True)
  selector = _messages.StringField(2)


class SystemParameters(_messages.Message):
  r"""### System parameter configuration A system parameter is a special kind
  of parameter defined by the API system, not by an individual API. It is
  typically mapped to an HTTP header and/or a URL query parameter. This
  configuration specifies which methods change the names of the system
  parameters.

  Fields:
    rules: Define system parameters. The parameters defined here will override
      the default parameters implemented by the system. If this field is
      missing from the service config, default system parameters will be used.
      Default system parameters and names is implementation-dependent.
      Example: define api key for all methods system_parameters rules: -
      selector: "*" parameters: - name: api_key url_query_parameter: api_key
      Example: define 2 api key names for a specific method. system_parameters
      rules: - selector: "/ListShelves" parameters: - name: api_key
      http_header: Api-Key1 - name: api_key http_header: Api-Key2 **NOTE:**
      All service configuration rules follow "last one wins" order.
  """

  rules = _messages.MessageField('SystemParameterRule', 1, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TrafficPercentStrategy(_messages.Message):
  r"""Strategy that specifies how clients of Google Service Controller want to
  send traffic to use different config versions. This is generally used by API
  proxy to split traffic based on your configured percentage for each config
  version. One example of how to gradually rollout a new service configuration
  using this strategy: Day 1 Rollout { id:
  "example.googleapis.com/rollout_20160206" traffic_percent_strategy {
  percentages: { "example.googleapis.com/20160201": 70.00
  "example.googleapis.com/20160206": 30.00 } } } Day 2 Rollout { id:
  "example.googleapis.com/rollout_20160207" traffic_percent_strategy: {
  percentages: { "example.googleapis.com/20160206": 100.00 } } }

  Messages:
    PercentagesValue: Maps service configuration IDs to their corresponding
      traffic percentage. Key is the service configuration ID, Value is the
      traffic percentage which must be greater than 0.0 and the sum must equal
      to 100.0.

  Fields:
    percentages: Maps service configuration IDs to their corresponding traffic
      percentage. Key is the service configuration ID, Value is the traffic
      percentage which must be greater than 0.0 and the sum must equal to
      100.0.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PercentagesValue(_messages.Message):
    r"""Maps service configuration IDs to their corresponding traffic
    percentage. Key is the service configuration ID, Value is the traffic
    percentage which must be greater than 0.0 and the sum must equal to 100.0.

    Messages:
      AdditionalProperty: An additional property for a PercentagesValue
        object.

    Fields:
      additionalProperties: Additional properties of type PercentagesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PercentagesValue object.

      Fields:
        key: Name of the additional property.
        value: A number attribute.
      """

      key = _messages.StringField(1)
      value = _messages.FloatField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  percentages = _messages.MessageField('PercentagesValue', 1)


class Type(_messages.Message):
  r"""A protocol buffer message type.

  Enums:
    SyntaxValueValuesEnum: The source syntax.

  Fields:
    edition: The source edition string, only valid when syntax is
      SYNTAX_EDITIONS.
    fields: The list of fields.
    name: The fully qualified message name.
    oneofs: The list of types appearing in `oneof` definitions in this type.
    options: The protocol buffer options.
    sourceContext: The source context.
    syntax: The source syntax.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
      SYNTAX_EDITIONS: Syntax `editions`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1
    SYNTAX_EDITIONS = 2

  edition = _messages.StringField(1)
  fields = _messages.MessageField('Field', 2, repeated=True)
  name = _messages.StringField(3)
  oneofs = _messages.StringField(4, repeated=True)
  options = _messages.MessageField('Option', 5, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 6)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 7)


class UndeleteServiceResponse(_messages.Message):
  r"""Response message for UndeleteService method.

  Fields:
    service: Revived service resource.
  """

  service = _messages.MessageField('ManagedService', 1)


class Usage(_messages.Message):
  r"""Configuration controlling usage of a service.

  Fields:
    producerNotificationChannel: The full resource name of a channel used for
      sending notifications to the service producer. Google Service Management
      currently only supports [Google Cloud
      Pub/Sub](https://cloud.google.com/pubsub) as a notification channel. To
      use Google Cloud Pub/Sub as the channel, this must be the name of a
      Cloud Pub/Sub topic that uses the Cloud Pub/Sub topic name format
      documented in https://cloud.google.com/pubsub/docs/overview.
    requirements: Requirements that must be satisfied before a consumer
      project can use the service. Each requirement is of the form /; for
      example 'serviceusage.googleapis.com/billing-enabled'. For Google APIs,
      a Terms of Service requirement must be included here. Google Cloud APIs
      must include "serviceusage.googleapis.com/tos/cloud". Other Google APIs
      should include "serviceusage.googleapis.com/tos/universal". Additional
      ToS can be included based on the business needs.
    rules: A list of usage rules that apply to individual API methods.
      **NOTE:** All service configuration rules follow "last one wins" order.
  """

  producerNotificationChannel = _messages.StringField(1)
  requirements = _messages.StringField(2, repeated=True)
  rules = _messages.MessageField('UsageRule', 3, repeated=True)


class UsageRule(_messages.Message):
  r"""Usage configuration rules for the service. NOTE: Under development. Use
  this rule to configure unregistered calls for the service. Unregistered
  calls are calls that do not contain consumer project identity. (Example:
  calls that do not contain an API key). By default, API methods do not allow
  unregistered calls, and each method call must be identified by a consumer
  project identity. Use this rule to allow/disallow unregistered calls.
  Example of an API that wants to allow unregistered calls for entire service.
  usage: rules: - selector: "*" allow_unregistered_calls: true Example of a
  method that wants to allow unregistered calls. usage: rules: - selector:
  "google.example.library.v1.LibraryService.CreateBook"
  allow_unregistered_calls: true

  Fields:
    allowUnregisteredCalls: If true, the selected method allows unregistered
      calls, e.g. calls that don't identify any user or application.
    selector: Selects the methods to which this rule applies. Use '*' to
      indicate all methods in all APIs. Refer to selector for syntax details.
    skipServiceControl: If true, the selected method should skip service
      control and the control plane features, such as quota and billing, will
      not be available. This flag is used by Google Cloud Endpoints to bypass
      checks for internal methods, such as service health check methods.
  """

  allowUnregisteredCalls = _messages.BooleanField(1)
  selector = _messages.StringField(2)
  skipServiceControl = _messages.BooleanField(3)


class UsageSettings(_messages.Message):
  r"""Usage settings for a consumer of a service.

  Enums:
    ConsumerEnableStatusValueValuesEnum: Consumer controlled setting to
      enable/disable use of this service by the consumer project. The default
      value of this is controlled by the service configuration.

  Fields:
    consumerEnableStatus: Consumer controlled setting to enable/disable use of
      this service by the consumer project. The default value of this is
      controlled by the service configuration.
  """

  class ConsumerEnableStatusValueValuesEnum(_messages.Enum):
    r"""Consumer controlled setting to enable/disable use of this service by
    the consumer project. The default value of this is controlled by the
    service configuration.

    Values:
      DISABLED: The service is disabled.
      ENABLED: The service is enabled.
    """
    DISABLED = 0
    ENABLED = 1

  consumerEnableStatus = _messages.EnumField('ConsumerEnableStatusValueValuesEnum', 1)


class VariableTermQuota(_messages.Message):
  r"""A variable term quota is a bucket of tokens that is consumed over a
  specified (usually long) time period. When present, it overrides any "1d"
  duration per-project quota specified on the group. Variable terms run from
  midnight to midnight, start_date to end_date (inclusive) in the
  America/Los_Angeles time zone.

  Fields:
    createTime: Time when this variable term quota was created. If multiple
      quotas are simultaneously active, then the quota with the latest
      create_time is the effective one.
    displayEndDate: The displayed end of the active period for the variable
      term quota. This may be before the effective end to give the user a
      grace period. YYYYMMdd date format, e.g. 20140730.
    endDate: The effective end of the active period for the variable term
      quota (inclusive). This must be no more than 5 years after start_date.
      YYYYMMdd date format, e.g. 20140730.
    groupName: The quota group that has the variable term quota applied to it.
      This must be a google.api.QuotaGroup.name specified in the service
      configuration.
    limit: The number of tokens available during the configured term.
    quotaUsage: The usage data of this quota.
    startDate: The beginning of the active period for the variable term quota.
      YYYYMMdd date format, e.g. 20140730.
  """

  createTime = _messages.StringField(1)
  displayEndDate = _messages.StringField(2)
  endDate = _messages.StringField(3)
  groupName = _messages.StringField(4)
  limit = _messages.IntegerField(5)
  quotaUsage = _messages.MessageField('QuotaUsage', 6)
  startDate = _messages.StringField(7)


class VisibilitySettings(_messages.Message):
  r"""Settings that control which features of the service surface are visible
  to the consumer project. More details: go/api/visibility.md That is the only
  intended usecase for Visibility Labels.

  Fields:
    visibilityLabels: The set of visibility labels that are used to determine
      what API surface is visible to calls made by this project. The visible
      surface is a union of the surface features associated with each label
      listed here, plus the publicly visible (unrestricted) surface. The
      service producer may add or remove labels at any time. The service
      consumer may add a label if the calling user has been granted permission
      to do so by the producer. The service consumer may also remove any label
      at any time.
  """

  visibilityLabels = _messages.StringField(1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

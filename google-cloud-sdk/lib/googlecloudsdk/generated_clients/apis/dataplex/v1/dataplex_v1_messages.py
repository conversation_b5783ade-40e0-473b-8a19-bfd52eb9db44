"""Generated message classes for dataplex version v1.

Dataplex API is used to manage the lifecycle of data lakes.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'dataplex'


class DataplexProjectsLocationsAspectTypesGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsAspectTypesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsAspectTypesSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsAspectTypesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsAspectTypesTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsAspectTypesTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataAttributeBindingsCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataAttributeBindingsCreateRequest object.

  Fields:
    dataAttributeBindingId: Required. DataAttributeBinding identifier. * Must
      contain only lowercase letters, numbers and hyphens. * Must start with a
      letter. * Must be between 1-63 characters. * Must end with a number or a
      letter. * Must be unique within the Location.
    googleCloudDataplexV1DataAttributeBinding: A
      GoogleCloudDataplexV1DataAttributeBinding resource to be passed as the
      request body.
    parent: Required. The resource name of the parent data taxonomy
      projects/{project_number}/locations/{location_id}
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  dataAttributeBindingId = _messages.StringField(1)
  googleCloudDataplexV1DataAttributeBinding = _messages.MessageField('GoogleCloudDataplexV1DataAttributeBinding', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsDataAttributeBindingsDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataAttributeBindingsDeleteRequest object.

  Fields:
    etag: Required. If the client provided etag value does not match the
      current etag value, the DeleteDataAttributeBindingRequest method returns
      an ABORTED error response. Etags must be used when calling the
      DeleteDataAttributeBinding.
    name: Required. The resource name of the DataAttributeBinding: projects/{p
      roject_number}/locations/{location_id}/dataAttributeBindings/{data_attri
      bute_binding_id}
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataAttributeBindingsGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataAttributeBindingsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataAttributeBindingsGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataAttributeBindingsGetRequest object.

  Fields:
    name: Required. The resource name of the DataAttributeBinding: projects/{p
      roject_number}/locations/{location_id}/dataAttributeBindings/{data_attri
      bute_binding_id}
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsDataAttributeBindingsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataAttributeBindingsListRequest object.

  Fields:
    filter: Optional. Filter request. Filter using resource:
      filter=resource:"resource-name" Filter using attribute:
      filter=attributes:"attribute-name" Filter using attribute in paths list:
      filter=paths.attributes:"attribute-name"
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. Maximum number of DataAttributeBindings to return. The
      service may return fewer than this value. If unspecified, at most 10
      DataAttributeBindings will be returned. The maximum value is 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. Page token received from a previous
      ListDataAttributeBindings call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      ListDataAttributeBindings must match the call that provided the page
      token.
    parent: Required. The resource name of the Location:
      projects/{project_number}/locations/{location_id}
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DataplexProjectsLocationsDataAttributeBindingsPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataAttributeBindingsPatchRequest object.

  Fields:
    googleCloudDataplexV1DataAttributeBinding: A
      GoogleCloudDataplexV1DataAttributeBinding resource to be passed as the
      request body.
    name: Output only. The relative resource name of the Data Attribute
      Binding, of the form: projects/{project_number}/locations/{location}/dat
      aAttributeBindings/{data_attribute_binding_id}
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1DataAttributeBinding = _messages.MessageField('GoogleCloudDataplexV1DataAttributeBinding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsDataAttributeBindingsSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataAttributeBindingsSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataAttributeBindingsTestIamPermissionsRequest(_messages.Message):
  r"""A
  DataplexProjectsLocationsDataAttributeBindingsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataScansCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansCreateRequest object.

  Fields:
    dataScanId: Required. DataScan identifier. Must contain only lowercase
      letters, numbers and hyphens. Must start with a letter. Must end with a
      number or a letter. Must be between 1-63 characters. Must be unique
      within the customer project / location.
    googleCloudDataplexV1DataScan: A GoogleCloudDataplexV1DataScan resource to
      be passed as the request body.
    parent: Required. The resource name of the parent location:
      projects/{project}/locations/{location_id} where project refers to a
      project_id or project_number and location_id refers to a GCP region.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  dataScanId = _messages.StringField(1)
  googleCloudDataplexV1DataScan = _messages.MessageField('GoogleCloudDataplexV1DataScan', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsDataScansDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansDeleteRequest object.

  Fields:
    name: Required. The resource name of the dataScan:
      projects/{project}/locations/{location_id}/dataScans/{data_scan_id}
      where project refers to a project_id or project_number and location_id
      refers to a GCP region.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsDataScansGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataScansGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. Select the DataScan view to return.
      Defaults to BASIC.

  Fields:
    name: Required. The resource name of the dataScan:
      projects/{project}/locations/{location_id}/dataScans/{data_scan_id}
      where project refers to a project_id or project_number and location_id
      refers to a GCP region.
    view: Optional. Select the DataScan view to return. Defaults to BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Select the DataScan view to return. Defaults to BASIC.

    Values:
      DATA_SCAN_VIEW_UNSPECIFIED: The API will default to the BASIC view.
      BASIC: Basic view that does not include spec and result.
      FULL: Include everything.
    """
    DATA_SCAN_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class DataplexProjectsLocationsDataScansJobsGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansJobsGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. Select the DataScanJob view to return.
      Defaults to BASIC.

  Fields:
    name: Required. The resource name of the DataScanJob: projects/{project}/l
      ocations/{location_id}/dataScans/{data_scan_id}/jobs/{data_scan_job_id}
      where project refers to a project_id or project_number and location_id
      refers to a GCP region.
    view: Optional. Select the DataScanJob view to return. Defaults to BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Select the DataScanJob view to return. Defaults to BASIC.

    Values:
      DATA_SCAN_JOB_VIEW_UNSPECIFIED: The API will default to the BASIC view.
      BASIC: Basic view that does not include spec and result.
      FULL: Include everything.
    """
    DATA_SCAN_JOB_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class DataplexProjectsLocationsDataScansJobsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansJobsListRequest object.

  Fields:
    filter: Optional. An expression for filtering the results of the
      ListDataScanJobs request.If unspecified, all datascan jobs will be
      returned. Multiple filters can be applied (with AND, OR logical
      operators). Filters are case-sensitive.Allowed fields are: start_time
      end_timestart_time and end_time expect RFC-3339 formatted strings (e.g.
      2018-10-08T18:30:00-07:00).For instance, 'start_time >
      2018-10-08T00:00:00.123456789Z AND end_time <
      2018-10-09T00:00:00.123456789Z' limits results to DataScanJobs between
      specified start and end times.
    pageSize: Optional. Maximum number of DataScanJobs to return. The service
      may return fewer than this value. If unspecified, at most 10
      DataScanJobs will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Optional. Page token received from a previous ListDataScanJobs
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to ListDataScanJobs must match the call that
      provided the page token.
    parent: Required. The resource name of the parent environment:
      projects/{project}/locations/{location_id}/dataScans/{data_scan_id}
      where project refers to a project_id or project_number and location_id
      refers to a GCP region.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DataplexProjectsLocationsDataScansListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansListRequest object.

  Fields:
    filter: Optional. Filter request.
    orderBy: Optional. Order by fields (name or create_time) for the result.
      If not specified, the ordering is undefined.
    pageSize: Optional. Maximum number of dataScans to return. The service may
      return fewer than this value. If unspecified, at most 500 scans will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListDataScans
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to ListDataScans must match the call that
      provided the page token.
    parent: Required. The resource name of the parent location:
      projects/{project}/locations/{location_id} where project refers to a
      project_id or project_number and location_id refers to a GCP region.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DataplexProjectsLocationsDataScansPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansPatchRequest object.

  Fields:
    googleCloudDataplexV1DataScan: A GoogleCloudDataplexV1DataScan resource to
      be passed as the request body.
    name: Output only. The relative resource name of the scan, of the form:
      projects/{project}/locations/{location_id}/dataScans/{datascan_id},
      where project refers to a project_id or project_number and location_id
      refers to a GCP region.
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1DataScan = _messages.MessageField('GoogleCloudDataplexV1DataScan', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsDataScansRunRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansRunRequest object.

  Fields:
    googleCloudDataplexV1RunDataScanRequest: A
      GoogleCloudDataplexV1RunDataScanRequest resource to be passed as the
      request body.
    name: Required. The resource name of the DataScan:
      projects/{project}/locations/{location_id}/dataScans/{data_scan_id}.
      where project refers to a project_id or project_number and location_id
      refers to a GCP region.Only OnDemand data scans are allowed.
  """

  googleCloudDataplexV1RunDataScanRequest = _messages.MessageField('GoogleCloudDataplexV1RunDataScanRequest', 1)
  name = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataScansSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataScansTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataScansTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataTaxonomiesAttributesCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesAttributesCreateRequest object.

  Fields:
    dataAttributeId: Required. DataAttribute identifier. * Must contain only
      lowercase letters, numbers and hyphens. * Must start with a letter. *
      Must be between 1-63 characters. * Must end with a number or a letter. *
      Must be unique within the DataTaxonomy.
    googleCloudDataplexV1DataAttribute: A GoogleCloudDataplexV1DataAttribute
      resource to be passed as the request body.
    parent: Required. The resource name of the parent data taxonomy projects/{
      project_number}/locations/{location_id}/dataTaxonomies/{data_taxonomy_id
      }
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  dataAttributeId = _messages.StringField(1)
  googleCloudDataplexV1DataAttribute = _messages.MessageField('GoogleCloudDataplexV1DataAttribute', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsDataTaxonomiesAttributesDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesAttributesDeleteRequest object.

  Fields:
    etag: Optional. If the client provided etag value does not match the
      current etag value, the DeleteDataAttribute method returns an ABORTED
      error response.
    name: Required. The resource name of the DataAttribute: projects/{project_
      number}/locations/{location_id}/dataTaxonomies/{dataTaxonomy}/attributes
      /{data_attribute_id}
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataTaxonomiesAttributesGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesAttributesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataTaxonomiesAttributesGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesAttributesGetRequest object.

  Fields:
    name: Required. The resource name of the dataAttribute: projects/{project_
      number}/locations/{location_id}/dataTaxonomies/{dataTaxonomy}/attributes
      /{data_attribute_id}
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsDataTaxonomiesAttributesListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesAttributesListRequest object.

  Fields:
    filter: Optional. Filter request.
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. Maximum number of DataAttributes to return. The
      service may return fewer than this value. If unspecified, at most 10
      dataAttributes will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Optional. Page token received from a previous
      ListDataAttributes call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to ListDataAttributes
      must match the call that provided the page token.
    parent: Required. The resource name of the DataTaxonomy: projects/{project
      _number}/locations/{location_id}/dataTaxonomies/{data_taxonomy_id}
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DataplexProjectsLocationsDataTaxonomiesAttributesPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesAttributesPatchRequest object.

  Fields:
    googleCloudDataplexV1DataAttribute: A GoogleCloudDataplexV1DataAttribute
      resource to be passed as the request body.
    name: Output only. The relative resource name of the dataAttribute, of the
      form: projects/{project_number}/locations/{location_id}/dataTaxonomies/{
      dataTaxonomy}/attributes/{data_attribute_id}.
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1DataAttribute = _messages.MessageField('GoogleCloudDataplexV1DataAttribute', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsDataTaxonomiesAttributesSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesAttributesSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataTaxonomiesAttributesTestIamPermissionsRequest(_messages.Message):
  r"""A
  DataplexProjectsLocationsDataTaxonomiesAttributesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataTaxonomiesCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesCreateRequest object.

  Fields:
    dataTaxonomyId: Required. DataTaxonomy identifier. * Must contain only
      lowercase letters, numbers and hyphens. * Must start with a letter. *
      Must be between 1-63 characters. * Must end with a number or a letter. *
      Must be unique within the Project.
    googleCloudDataplexV1DataTaxonomy: A GoogleCloudDataplexV1DataTaxonomy
      resource to be passed as the request body.
    parent: Required. The resource name of the data taxonomy location, of the
      form: projects/{project_number}/locations/{location_id} where
      location_id refers to a GCP region.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  dataTaxonomyId = _messages.StringField(1)
  googleCloudDataplexV1DataTaxonomy = _messages.MessageField('GoogleCloudDataplexV1DataTaxonomy', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsDataTaxonomiesDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesDeleteRequest object.

  Fields:
    etag: Optional. If the client provided etag value does not match the
      current etag value,the DeleteDataTaxonomy method returns an ABORTED
      error.
    name: Required. The resource name of the DataTaxonomy: projects/{project_n
      umber}/locations/{location_id}/dataTaxonomies/{data_taxonomy_id}
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataTaxonomiesGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataTaxonomiesGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesGetRequest object.

  Fields:
    name: Required. The resource name of the DataTaxonomy: projects/{project_n
      umber}/locations/{location_id}/dataTaxonomies/{data_taxonomy_id}
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsDataTaxonomiesListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesListRequest object.

  Fields:
    filter: Optional. Filter request.
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. Maximum number of DataTaxonomies to return. The
      service may return fewer than this value. If unspecified, at most 10
      DataTaxonomies will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Optional. Page token received from a previous
      ListDataTaxonomies call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to ListDataTaxonomies
      must match the call that provided the page token.
    parent: Required. The resource name of the DataTaxonomy location, of the
      form: projects/{project_number}/locations/{location_id} where
      location_id refers to a GCP region.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DataplexProjectsLocationsDataTaxonomiesPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesPatchRequest object.

  Fields:
    googleCloudDataplexV1DataTaxonomy: A GoogleCloudDataplexV1DataTaxonomy
      resource to be passed as the request body.
    name: Output only. The relative resource name of the DataTaxonomy, of the
      form: projects/{project_number}/locations/{location_id}/dataTaxonomies/{
      data_taxonomy_id}.
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1DataTaxonomy = _messages.MessageField('GoogleCloudDataplexV1DataTaxonomy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsDataTaxonomiesSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsDataTaxonomiesTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsDataTaxonomiesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsEntryGroupsGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsEntryGroupsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsEntryGroupsSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsEntryGroupsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsEntryGroupsTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsEntryGroupsTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsEntryTypesGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsEntryTypesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsEntryTypesSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsEntryTypesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsEntryTypesTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsEntryTypesTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesActionsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesActionsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of actions to return. The service may
      return fewer than this value. If unspecified, at most 10 actions will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListLakeActions
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to ListLakeActions must match the call that
      provided the page token.
    parent: Required. The resource name of the parent lake:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DataplexProjectsLocationsLakesContentCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentCreateRequest object.

  Fields:
    googleCloudDataplexV1Content: A GoogleCloudDataplexV1Content resource to
      be passed as the request body.
    parent: Required. The resource name of the parent lake:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Content = _messages.MessageField('GoogleCloudDataplexV1Content', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class DataplexProjectsLocationsLakesContentDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentDeleteRequest object.

  Fields:
    name: Required. The resource name of the content: projects/{project_id}/lo
      cations/{location_id}/lakes/{lake_id}/content/{content_id}
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesContentGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesContentGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. Specify content view to make a partial
      request.

  Fields:
    name: Required. The resource name of the content: projects/{project_id}/lo
      cations/{location_id}/lakes/{lake_id}/content/{content_id}
    view: Optional. Specify content view to make a partial request.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Specify content view to make a partial request.

    Values:
      CONTENT_VIEW_UNSPECIFIED: Content view not specified. Defaults to BASIC.
        The API will default to the BASIC view.
      BASIC: Will not return the data_text field.
      FULL: Returns the complete proto.
    """
    CONTENT_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class DataplexProjectsLocationsLakesContentListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentListRequest object.

  Fields:
    filter: Optional. Filter request. Filters are case-sensitive. The
      following formats are supported:labels.key1 = "value1" labels:key1 type
      = "NOTEBOOK" type = "SQL_SCRIPT"These restrictions can be coinjoined
      with AND, OR and NOT conjunctions.
    pageSize: Optional. Maximum number of content to return. The service may
      return fewer than this value. If unspecified, at most 10 content will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListContent call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListContent must match the call that provided the
      page token.
    parent: Required. The resource name of the parent lake:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DataplexProjectsLocationsLakesContentPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentPatchRequest object.

  Fields:
    googleCloudDataplexV1Content: A GoogleCloudDataplexV1Content resource to
      be passed as the request body.
    name: Output only. The relative resource name of the content, of the form:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}/content/{c
      ontent_id}
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Content = _messages.MessageField('GoogleCloudDataplexV1Content', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesContentSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesContentTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesContentitemsCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentitemsCreateRequest object.

  Fields:
    googleCloudDataplexV1Content: A GoogleCloudDataplexV1Content resource to
      be passed as the request body.
    parent: Required. The resource name of the parent lake:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Content = _messages.MessageField('GoogleCloudDataplexV1Content', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class DataplexProjectsLocationsLakesContentitemsDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentitemsDeleteRequest object.

  Fields:
    name: Required. The resource name of the content: projects/{project_id}/lo
      cations/{location_id}/lakes/{lake_id}/content/{content_id}
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesContentitemsGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentitemsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesContentitemsGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentitemsGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. Specify content view to make a partial
      request.

  Fields:
    name: Required. The resource name of the content: projects/{project_id}/lo
      cations/{location_id}/lakes/{lake_id}/content/{content_id}
    view: Optional. Specify content view to make a partial request.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Specify content view to make a partial request.

    Values:
      CONTENT_VIEW_UNSPECIFIED: Content view not specified. Defaults to BASIC.
        The API will default to the BASIC view.
      BASIC: Will not return the data_text field.
      FULL: Returns the complete proto.
    """
    CONTENT_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class DataplexProjectsLocationsLakesContentitemsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentitemsListRequest object.

  Fields:
    filter: Optional. Filter request. Filters are case-sensitive. The
      following formats are supported:labels.key1 = "value1" labels:key1 type
      = "NOTEBOOK" type = "SQL_SCRIPT"These restrictions can be coinjoined
      with AND, OR and NOT conjunctions.
    pageSize: Optional. Maximum number of content to return. The service may
      return fewer than this value. If unspecified, at most 10 content will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListContent call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListContent must match the call that provided the
      page token.
    parent: Required. The resource name of the parent lake:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DataplexProjectsLocationsLakesContentitemsPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentitemsPatchRequest object.

  Fields:
    googleCloudDataplexV1Content: A GoogleCloudDataplexV1Content resource to
      be passed as the request body.
    name: Output only. The relative resource name of the content, of the form:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}/content/{c
      ontent_id}
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Content = _messages.MessageField('GoogleCloudDataplexV1Content', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesContentitemsSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentitemsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesContentitemsTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesContentitemsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesCreateRequest object.

  Fields:
    googleCloudDataplexV1Lake: A GoogleCloudDataplexV1Lake resource to be
      passed as the request body.
    lakeId: Required. Lake identifier. This ID will be used to generate names
      such as database and dataset names when publishing metadata to Hive
      Metastore and BigQuery. * Must contain only lowercase letters, numbers
      and hyphens. * Must start with a letter. * Must end with a number or a
      letter. * Must be between 1-63 characters. * Must be unique within the
      customer project / location.
    parent: Required. The resource name of the lake location, of the form:
      projects/{project_number}/locations/{location_id} where location_id
      refers to a GCP region.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Lake = _messages.MessageField('GoogleCloudDataplexV1Lake', 1)
  lakeId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesDeleteRequest object.

  Fields:
    name: Required. The resource name of the lake:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesEnvironmentsCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesEnvironmentsCreateRequest object.

  Fields:
    environmentId: Required. Environment identifier. * Must contain only
      lowercase letters, numbers and hyphens. * Must start with a letter. *
      Must be between 1-63 characters. * Must end with a number or a letter. *
      Must be unique within the lake.
    googleCloudDataplexV1Environment: A GoogleCloudDataplexV1Environment
      resource to be passed as the request body.
    parent: Required. The resource name of the parent lake:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  environmentId = _messages.StringField(1)
  googleCloudDataplexV1Environment = _messages.MessageField('GoogleCloudDataplexV1Environment', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesEnvironmentsDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesEnvironmentsDeleteRequest object.

  Fields:
    name: Required. The resource name of the environment: projects/{project_id
      }/locations/{location_id}/lakes/{lake_id}/environments/{environment_id}.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesEnvironmentsGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesEnvironmentsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesEnvironmentsGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesEnvironmentsGetRequest object.

  Fields:
    name: Required. The resource name of the environment: projects/{project_id
      }/locations/{location_id}/lakes/{lake_id}/environments/{environment_id}.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesEnvironmentsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesEnvironmentsListRequest object.

  Fields:
    filter: Optional. Filter request.
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. Maximum number of environments to return. The service
      may return fewer than this value. If unspecified, at most 10
      environments will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Optional. Page token received from a previous ListEnvironments
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to ListEnvironments must match the call that
      provided the page token.
    parent: Required. The resource name of the parent lake:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DataplexProjectsLocationsLakesEnvironmentsPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesEnvironmentsPatchRequest object.

  Fields:
    googleCloudDataplexV1Environment: A GoogleCloudDataplexV1Environment
      resource to be passed as the request body.
    name: Output only. The relative resource name of the environment, of the
      form: projects/{project_id}/locations/{location_id}/lakes/{lake_id}/envi
      ronment/{environment_id}
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Environment = _messages.MessageField('GoogleCloudDataplexV1Environment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesEnvironmentsSessionsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesEnvironmentsSessionsListRequest object.

  Fields:
    filter: Optional. Filter request. The following mode filter is supported
      to return only the sessions belonging to the requester when the mode is
      USER and return sessions of all the users when the mode is ADMIN. When
      no filter is sent default to USER mode. NOTE: When the mode is ADMIN,
      the requester should have dataplex.environments.listAllSessions
      permission to list all sessions, in absence of the permission, the
      request fails.mode = ADMIN | USER
    pageSize: Optional. Maximum number of sessions to return. The service may
      return fewer than this value. If unspecified, at most 10 sessions will
      be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. Page token received from a previous ListSessions
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to ListSessions must match the call that
      provided the page token.
    parent: Required. The resource name of the parent environment: projects/{p
      roject_number}/locations/{location_id}/lakes/{lake_id}/environment/{envi
      ronment_id}.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DataplexProjectsLocationsLakesEnvironmentsSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesEnvironmentsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesEnvironmentsTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesEnvironmentsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesGetRequest object.

  Fields:
    name: Required. The resource name of the lake:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesListRequest object.

  Fields:
    filter: Optional. Filter request.
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. Maximum number of Lakes to return. The service may
      return fewer than this value. If unspecified, at most 10 lakes will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListLakes call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListLakes must match the call that provided the
      page token.
    parent: Required. The resource name of the lake location, of the form:
      projects/{project_number}/locations/{location_id} where location_id
      refers to a GCP region.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DataplexProjectsLocationsLakesPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesPatchRequest object.

  Fields:
    googleCloudDataplexV1Lake: A GoogleCloudDataplexV1Lake resource to be
      passed as the request body.
    name: Output only. The relative resource name of the lake, of the form:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Lake = _messages.MessageField('GoogleCloudDataplexV1Lake', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesTasksCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksCreateRequest object.

  Fields:
    googleCloudDataplexV1Task: A GoogleCloudDataplexV1Task resource to be
      passed as the request body.
    parent: Required. The resource name of the parent lake:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
    taskId: Required. Task identifier.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Task = _messages.MessageField('GoogleCloudDataplexV1Task', 1)
  parent = _messages.StringField(2, required=True)
  taskId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesTasksDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksDeleteRequest object.

  Fields:
    name: Required. The resource name of the task: projects/{project_number}/l
      ocations/{location_id}/lakes/{lake_id}/task/{task_id}.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesTasksGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesTasksGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksGetRequest object.

  Fields:
    name: Required. The resource name of the task: projects/{project_number}/l
      ocations/{location_id}/lakes/{lake_id}/tasks/{tasks_id}.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesTasksJobsCancelRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksJobsCancelRequest object.

  Fields:
    googleCloudDataplexV1CancelJobRequest: A
      GoogleCloudDataplexV1CancelJobRequest resource to be passed as the
      request body.
    name: Required. The resource name of the job: projects/{project_number}/lo
      cations/{location_id}/lakes/{lake_id}/task/{task_id}/job/{job_id}.
  """

  googleCloudDataplexV1CancelJobRequest = _messages.MessageField('GoogleCloudDataplexV1CancelJobRequest', 1)
  name = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesTasksJobsGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksJobsGetRequest object.

  Fields:
    name: Required. The resource name of the job: projects/{project_number}/lo
      cations/{location_id}/lakes/{lake_id}/tasks/{task_id}/jobs/{job_id}.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesTasksJobsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksJobsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of jobs to return. The service may
      return fewer than this value. If unspecified, at most 10 jobs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListJobs call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListJobs must match the call that provided the
      page token.
    parent: Required. The resource name of the parent environment: projects/{p
      roject_number}/locations/{location_id}/lakes/{lake_id}/tasks/{task_id}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DataplexProjectsLocationsLakesTasksListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksListRequest object.

  Fields:
    filter: Optional. Filter request.
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. Maximum number of tasks to return. The service may
      return fewer than this value. If unspecified, at most 10 tasks will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListZones call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListZones must match the call that provided the
      page token.
    parent: Required. The resource name of the parent lake:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DataplexProjectsLocationsLakesTasksPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksPatchRequest object.

  Fields:
    googleCloudDataplexV1Task: A GoogleCloudDataplexV1Task resource to be
      passed as the request body.
    name: Output only. The relative resource name of the task, of the form:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}/
      tasks/{task_id}.
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Task = _messages.MessageField('GoogleCloudDataplexV1Task', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesTasksRunRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksRunRequest object.

  Fields:
    googleCloudDataplexV1RunTaskRequest: A GoogleCloudDataplexV1RunTaskRequest
      resource to be passed as the request body.
    name: Required. The resource name of the task: projects/{project_number}/l
      ocations/{location_id}/lakes/{lake_id}/tasks/{task_id}.
  """

  googleCloudDataplexV1RunTaskRequest = _messages.MessageField('GoogleCloudDataplexV1RunTaskRequest', 1)
  name = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesTasksSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesTasksTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTasksTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesZonesActionsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesActionsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of actions to return. The service may
      return fewer than this value. If unspecified, at most 10 actions will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListZoneActions
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to ListZoneActions must match the call that
      provided the page token.
    parent: Required. The resource name of the parent zone: projects/{project_
      number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DataplexProjectsLocationsLakesZonesAssetsActionsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesAssetsActionsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of actions to return. The service may
      return fewer than this value. If unspecified, at most 10 actions will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListAssetActions
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to ListAssetActions must match the call that
      provided the page token.
    parent: Required. The resource name of the parent asset: projects/{project
      _number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/assets/
      {asset_id}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DataplexProjectsLocationsLakesZonesAssetsCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesAssetsCreateRequest object.

  Fields:
    assetId: Required. Asset identifier. This ID will be used to generate
      names such as table names when publishing metadata to Hive Metastore and
      BigQuery. * Must contain only lowercase letters, numbers and hyphens. *
      Must start with a letter. * Must end with a number or a letter. * Must
      be between 1-63 characters. * Must be unique within the zone.
    googleCloudDataplexV1Asset: A GoogleCloudDataplexV1Asset resource to be
      passed as the request body.
    parent: Required. The resource name of the parent zone: projects/{project_
      number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  assetId = _messages.StringField(1)
  googleCloudDataplexV1Asset = _messages.MessageField('GoogleCloudDataplexV1Asset', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesZonesAssetsDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesAssetsDeleteRequest object.

  Fields:
    name: Required. The resource name of the asset: projects/{project_number}/
      locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/assets/{asset_id
      }.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesZonesAssetsGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesAssetsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesZonesAssetsGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesAssetsGetRequest object.

  Fields:
    name: Required. The resource name of the asset: projects/{project_number}/
      locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/assets/{asset_id
      }.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesZonesAssetsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesAssetsListRequest object.

  Fields:
    filter: Optional. Filter request.
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. Maximum number of asset to return. The service may
      return fewer than this value. If unspecified, at most 10 assets will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListAssets call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListAssets must match the call that provided the
      page token.
    parent: Required. The resource name of the parent zone: projects/{project_
      number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DataplexProjectsLocationsLakesZonesAssetsPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesAssetsPatchRequest object.

  Fields:
    googleCloudDataplexV1Asset: A GoogleCloudDataplexV1Asset resource to be
      passed as the request body.
    name: Output only. The relative resource name of the asset, of the form: p
      rojects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{
      zone_id}/assets/{asset_id}.
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Asset = _messages.MessageField('GoogleCloudDataplexV1Asset', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesZonesAssetsSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesAssetsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesZonesAssetsTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesAssetsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesZonesCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesCreateRequest object.

  Fields:
    googleCloudDataplexV1Zone: A GoogleCloudDataplexV1Zone resource to be
      passed as the request body.
    parent: Required. The resource name of the parent lake:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
    zoneId: Required. Zone identifier. This ID will be used to generate names
      such as database and dataset names when publishing metadata to Hive
      Metastore and BigQuery. * Must contain only lowercase letters, numbers
      and hyphens. * Must start with a letter. * Must end with a number or a
      letter. * Must be between 1-63 characters. * Must be unique across all
      lakes from all locations in a project. * Must not be one of the reserved
      IDs (i.e. "default", "global-temp")
  """

  googleCloudDataplexV1Zone = _messages.MessageField('GoogleCloudDataplexV1Zone', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)
  zoneId = _messages.StringField(4)


class DataplexProjectsLocationsLakesZonesDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesDeleteRequest object.

  Fields:
    name: Required. The resource name of the zone: projects/{project_number}/l
      ocations/{location_id}/lakes/{lake_id}/zones/{zone_id}.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesZonesEntitiesCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesEntitiesCreateRequest object.

  Fields:
    googleCloudDataplexV1Entity: A GoogleCloudDataplexV1Entity resource to be
      passed as the request body.
    parent: Required. The resource name of the parent zone: projects/{project_
      number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Entity = _messages.MessageField('GoogleCloudDataplexV1Entity', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class DataplexProjectsLocationsLakesZonesEntitiesDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesEntitiesDeleteRequest object.

  Fields:
    etag: Required. The etag associated with the entity, which can be
      retrieved with a GetEntity request.
    name: Required. The resource name of the entity: projects/{project_number}
      /locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/entities/{entit
      y_id}.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesZonesEntitiesGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesEntitiesGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. Used to select the subset of entity
      information to return. Defaults to BASIC.

  Fields:
    name: Required. The resource name of the entity: projects/{project_number}
      /locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/entities/{entit
      y_id}.
    view: Optional. Used to select the subset of entity information to return.
      Defaults to BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Used to select the subset of entity information to return.
    Defaults to BASIC.

    Values:
      ENTITY_VIEW_UNSPECIFIED: The API will default to the BASIC view.
      BASIC: Minimal view that does not include the schema.
      SCHEMA: Include basic information and schema.
      FULL: Include everything. Currently, this is the same as the SCHEMA
        view.
    """
    ENTITY_VIEW_UNSPECIFIED = 0
    BASIC = 1
    SCHEMA = 2
    FULL = 3

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class DataplexProjectsLocationsLakesZonesEntitiesListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesEntitiesListRequest object.

  Enums:
    ViewValueValuesEnum: Required. Specify the entity view to make a partial
      list request.

  Fields:
    filter: Optional. The following filter parameters can be added to the URL
      to limit the entities returned by the API: Entity ID:
      ?filter="id=entityID" Asset ID: ?filter="asset=assetID" Data path
      ?filter="data_path=gs://my-bucket" Is HIVE compatible:
      ?filter="hive_compatible=true" Is BigQuery compatible:
      ?filter="bigquery_compatible=true"
    pageSize: Optional. Maximum number of entities to return. The service may
      return fewer than this value. If unspecified, 100 entities will be
      returned by default. The maximum value is 500; larger values will will
      be truncated to 500.
    pageToken: Optional. Page token received from a previous ListEntities
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to ListEntities must match the call that
      provided the page token.
    parent: Required. The resource name of the parent zone: projects/{project_
      number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}.
    view: Required. Specify the entity view to make a partial list request.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Required. Specify the entity view to make a partial list request.

    Values:
      ENTITY_VIEW_UNSPECIFIED: The default unset value. Return both table and
        fileset entities if unspecified.
      TABLES: Only list table entities.
      FILESETS: Only list fileset entities.
    """
    ENTITY_VIEW_UNSPECIFIED = 0
    TABLES = 1
    FILESETS = 2

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class DataplexProjectsLocationsLakesZonesEntitiesPartitionsCreateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesEntitiesPartitionsCreateRequest
  object.

  Fields:
    googleCloudDataplexV1Partition: A GoogleCloudDataplexV1Partition resource
      to be passed as the request body.
    parent: Required. The resource name of the parent zone: projects/{project_
      number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/entities
      /{entity_id}.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Partition = _messages.MessageField('GoogleCloudDataplexV1Partition', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class DataplexProjectsLocationsLakesZonesEntitiesPartitionsDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesEntitiesPartitionsDeleteRequest
  object.

  Fields:
    etag: Optional. The etag associated with the partition.
    name: Required. The resource name of the partition. format: projects/{proj
      ect_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/enti
      ties/{entity_id}/partitions/{partition_value_path}. The
      {partition_value_path} segment consists of an ordered sequence of
      partition values separated by "/". All values must be provided.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesZonesEntitiesPartitionsGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesEntitiesPartitionsGetRequest
  object.

  Fields:
    name: Required. The resource name of the partition: projects/{project_numb
      er}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/entities/{en
      tity_id}/partitions/{partition_value_path}. The {partition_value_path}
      segment consists of an ordered sequence of partition values separated by
      "/". All values must be provided.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesZonesEntitiesPartitionsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesEntitiesPartitionsListRequest
  object.

  Fields:
    filter: Optional. Filter the partitions returned to the caller using a key
      value pair expression. Supported operators and syntax: logic operators:
      AND, OR comparison operators: <, >, >=, <= ,=, != LIKE operators: The
      right hand of a LIKE operator supports "." and "*" for wildcard
      searches, for example "value1 LIKE ".*oo.*" parenthetical grouping: (
      )Sample filter expression: `?filter="key1 < value1 OR key2 >
      value2"Notes: Keys to the left of operators are case insensitive.
      Partition results are sorted first by creation time, then by
      lexicographic order. Up to 20 key value filter pairs are allowed, but
      due to performance considerations, only the first 10 will be used as a
      filter.
    pageSize: Optional. Maximum number of partitions to return. The service
      may return fewer than this value. If unspecified, 100 partitions will be
      returned by default. The maximum page size is 500; larger values will
      will be truncated to 500.
    pageToken: Optional. Page token received from a previous ListPartitions
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to ListPartitions must match the call that
      provided the page token.
    parent: Required. The resource name of the parent entity: projects/{projec
      t_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/entiti
      es/{entity_id}.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DataplexProjectsLocationsLakesZonesEntitiesUpdateRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesEntitiesUpdateRequest object.

  Fields:
    googleCloudDataplexV1Entity: A GoogleCloudDataplexV1Entity resource to be
      passed as the request body.
    name: Output only. The resource name of the entity, of the form: projects/
      {project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}
      /entities/{id}.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Entity = _messages.MessageField('GoogleCloudDataplexV1Entity', 1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class DataplexProjectsLocationsLakesZonesGetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy.Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected.Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset.The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1.To learn which resources support conditions in
      their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesZonesGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesGetRequest object.

  Fields:
    name: Required. The resource name of the zone: projects/{project_number}/l
      ocations/{location_id}/lakes/{lake_id}/zones/{zone_id}.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsLakesZonesListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesListRequest object.

  Fields:
    filter: Optional. Filter request.
    orderBy: Optional. Order by fields for the result.
    pageSize: Optional. Maximum number of zones to return. The service may
      return fewer than this value. If unspecified, at most 10 zones will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: Optional. Page token received from a previous ListZones call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to ListZones must match the call that provided the
      page token.
    parent: Required. The resource name of the parent lake:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DataplexProjectsLocationsLakesZonesPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesPatchRequest object.

  Fields:
    googleCloudDataplexV1Zone: A GoogleCloudDataplexV1Zone resource to be
      passed as the request body.
    name: Output only. The relative resource name of the zone, of the form: pr
      ojects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{z
      one_id}.
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1Zone = _messages.MessageField('GoogleCloudDataplexV1Zone', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsLakesZonesSetIamPolicyRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See Resource names (https://cloud.google.com/apis/design/resource_names)
      for the appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsLakesZonesTestIamPermissionsRequest(_messages.Message):
  r"""A DataplexProjectsLocationsLakesZonesTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See Resource names
      (https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DataplexProjectsLocationsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like "displayName=tokyo", and is
      documented in more detail in AIP-160 (https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the next_page_token field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class DataplexProjectsLocationsManagedEntriesGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsManagedEntriesGetRequest object.

  Fields:
    name: Required. The entry name: projects/{project_number}/locations/{locat
      ion_id}/managedEntries/{resource_name}. The resource name follows the
      standard Google Cloud full resource name pattern. - Google Cloud Storage
      bucket: //storage.googleapis.com/projects//buckets/.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsManagedEntriesPatchRequest(_messages.Message):
  r"""A DataplexProjectsLocationsManagedEntriesPatchRequest object.

  Fields:
    googleCloudDataplexV1ManagedEntry: A GoogleCloudDataplexV1ManagedEntry
      resource to be passed as the request body.
    name: Output only. Name of the resource to which the config is applied. pr
      ojects/{project_number}/locations/{location_id}/managedEntries/{resource
      _name}.
    updateMask: Required. Mask of fields to update.
    validateOnly: Optional. Only validate the request, but do not perform
      mutations. The default is false.
  """

  googleCloudDataplexV1ManagedEntry = _messages.MessageField('GoogleCloudDataplexV1ManagedEntry', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class DataplexProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A DataplexProjectsLocationsOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class DataplexProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A DataplexProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A DataplexProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class DataplexProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A DataplexProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleCloudDataplexV1Action(_messages.Message):
  r"""Action represents an issue requiring administrator action for
  resolution.

  Enums:
    CategoryValueValuesEnum: The category of issue associated with the action.

  Fields:
    asset: Output only. The relative resource name of the asset, of the form:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/
      {zone_id}/assets/{asset_id}.
    category: The category of issue associated with the action.
    dataLocations: The list of data locations associated with this action.
      Cloud Storage locations are represented as URI paths(E.g.
      gs://bucket/table1/year=2020/month=Jan/). BigQuery locations refer to
      resource names(E.g. bigquery.googleapis.com/projects/project-
      id/datasets/dataset-id).
    detectTime: The time that the issue was detected.
    failedSecurityPolicyApply: Details for issues related to applying security
      policy.
    incompatibleDataSchema: Details for issues related to incompatible schemas
      detected within data.
    invalidDataFormat: Details for issues related to invalid or unsupported
      data formats.
    invalidDataOrganization: Details for issues related to invalid data
      arrangement.
    invalidDataPartition: Details for issues related to invalid or unsupported
      data partition structure.
    issue: Detailed description of the issue requiring action.
    lake: Output only. The relative resource name of the lake, of the form:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
    missingData: Details for issues related to absence of data within managed
      resources.
    missingResource: Details for issues related to absence of a managed
      resource.
    name: Output only. The relative resource name of the action, of the form:
      projects/{project}/locations/{location}/lakes/{lake}/actions/{action} pr
      ojects/{project}/locations/{location}/lakes/{lake}/zones/{zone}/actions/
      {action} projects/{project}/locations/{location}/lakes/{lake}/zones/{zon
      e}/assets/{asset}/actions/{action}.
    unauthorizedResource: Details for issues related to lack of permissions to
      access data resources.
    zone: Output only. The relative resource name of the zone, of the form: pr
      ojects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{z
      one_id}.
  """

  class CategoryValueValuesEnum(_messages.Enum):
    r"""The category of issue associated with the action.

    Values:
      CATEGORY_UNSPECIFIED: Unspecified category.
      RESOURCE_MANAGEMENT: Resource management related issues.
      SECURITY_POLICY: Security policy related issues.
      DATA_DISCOVERY: Data and discovery related issues.
    """
    CATEGORY_UNSPECIFIED = 0
    RESOURCE_MANAGEMENT = 1
    SECURITY_POLICY = 2
    DATA_DISCOVERY = 3

  asset = _messages.StringField(1)
  category = _messages.EnumField('CategoryValueValuesEnum', 2)
  dataLocations = _messages.StringField(3, repeated=True)
  detectTime = _messages.StringField(4)
  failedSecurityPolicyApply = _messages.MessageField('GoogleCloudDataplexV1ActionFailedSecurityPolicyApply', 5)
  incompatibleDataSchema = _messages.MessageField('GoogleCloudDataplexV1ActionIncompatibleDataSchema', 6)
  invalidDataFormat = _messages.MessageField('GoogleCloudDataplexV1ActionInvalidDataFormat', 7)
  invalidDataOrganization = _messages.MessageField('GoogleCloudDataplexV1ActionInvalidDataOrganization', 8)
  invalidDataPartition = _messages.MessageField('GoogleCloudDataplexV1ActionInvalidDataPartition', 9)
  issue = _messages.StringField(10)
  lake = _messages.StringField(11)
  missingData = _messages.MessageField('GoogleCloudDataplexV1ActionMissingData', 12)
  missingResource = _messages.MessageField('GoogleCloudDataplexV1ActionMissingResource', 13)
  name = _messages.StringField(14)
  unauthorizedResource = _messages.MessageField('GoogleCloudDataplexV1ActionUnauthorizedResource', 15)
  zone = _messages.StringField(16)


class GoogleCloudDataplexV1ActionFailedSecurityPolicyApply(_messages.Message):
  r"""Failed to apply security policy to the managed resource(s) under a lake,
  zone or an asset. For a lake or zone resource, one or more underlying assets
  has a failure applying security policy to the associated managed resource.

  Fields:
    asset: Resource name of one of the assets with failing security policy
      application. Populated for a lake or zone resource only.
  """

  asset = _messages.StringField(1)


class GoogleCloudDataplexV1ActionIncompatibleDataSchema(_messages.Message):
  r"""Action details for incompatible schemas detected by discovery.

  Enums:
    SchemaChangeValueValuesEnum: Whether the action relates to a schema that
      is incompatible or modified.

  Fields:
    existingSchema: The existing and expected schema of the table. The schema
      is provided as a JSON formatted structure listing columns and data
      types.
    newSchema: The new and incompatible schema within the table. The schema is
      provided as a JSON formatted structured listing columns and data types.
    sampledDataLocations: The list of data locations sampled and used for
      format/schema inference.
    schemaChange: Whether the action relates to a schema that is incompatible
      or modified.
    table: The name of the table containing invalid data.
  """

  class SchemaChangeValueValuesEnum(_messages.Enum):
    r"""Whether the action relates to a schema that is incompatible or
    modified.

    Values:
      SCHEMA_CHANGE_UNSPECIFIED: Schema change unspecified.
      INCOMPATIBLE: Newly discovered schema is incompatible with existing
        schema.
      MODIFIED: Newly discovered schema has changed from existing schema for
        data in a curated zone.
    """
    SCHEMA_CHANGE_UNSPECIFIED = 0
    INCOMPATIBLE = 1
    MODIFIED = 2

  existingSchema = _messages.StringField(1)
  newSchema = _messages.StringField(2)
  sampledDataLocations = _messages.StringField(3, repeated=True)
  schemaChange = _messages.EnumField('SchemaChangeValueValuesEnum', 4)
  table = _messages.StringField(5)


class GoogleCloudDataplexV1ActionInvalidDataFormat(_messages.Message):
  r"""Action details for invalid or unsupported data files detected by
  discovery.

  Fields:
    expectedFormat: The expected data format of the entity.
    newFormat: The new unexpected data format within the entity.
    sampledDataLocations: The list of data locations sampled and used for
      format/schema inference.
  """

  expectedFormat = _messages.StringField(1)
  newFormat = _messages.StringField(2)
  sampledDataLocations = _messages.StringField(3, repeated=True)


class GoogleCloudDataplexV1ActionInvalidDataOrganization(_messages.Message):
  r"""Action details for invalid data arrangement."""


class GoogleCloudDataplexV1ActionInvalidDataPartition(_messages.Message):
  r"""Action details for invalid or unsupported partitions detected by
  discovery.

  Enums:
    ExpectedStructureValueValuesEnum: The issue type of InvalidDataPartition.

  Fields:
    expectedStructure: The issue type of InvalidDataPartition.
  """

  class ExpectedStructureValueValuesEnum(_messages.Enum):
    r"""The issue type of InvalidDataPartition.

    Values:
      PARTITION_STRUCTURE_UNSPECIFIED: PartitionStructure unspecified.
      CONSISTENT_KEYS: Consistent hive-style partition definition (both raw
        and curated zone).
      HIVE_STYLE_KEYS: Hive style partition definition (curated zone only).
    """
    PARTITION_STRUCTURE_UNSPECIFIED = 0
    CONSISTENT_KEYS = 1
    HIVE_STYLE_KEYS = 2

  expectedStructure = _messages.EnumField('ExpectedStructureValueValuesEnum', 1)


class GoogleCloudDataplexV1ActionMissingData(_messages.Message):
  r"""Action details for absence of data detected by discovery."""


class GoogleCloudDataplexV1ActionMissingResource(_messages.Message):
  r"""Action details for resource references in assets that cannot be located.
  """



class GoogleCloudDataplexV1ActionUnauthorizedResource(_messages.Message):
  r"""Action details for unauthorized resource issues raised to indicate that
  the service account associated with the lake instance is not authorized to
  access or manage the resource associated with an asset.
  """



class GoogleCloudDataplexV1Asset(_messages.Message):
  r"""An asset represents a cloud resource that is being managed within a lake
  as a member of a zone.

  Enums:
    StateValueValuesEnum: Output only. Current state of the asset.

  Messages:
    LabelsValue: Optional. User defined labels for the asset.

  Fields:
    createTime: Output only. The time when the asset was created.
    description: Optional. Description of the asset.
    discoverySpec: Optional. Specification of the discovery feature applied to
      data referenced by this asset. When this spec is left unset, the asset
      will use the spec set on the parent zone.
    discoveryStatus: Output only. Status of the discovery feature applied to
      data referenced by this asset.
    displayName: Optional. User friendly display name.
    labels: Optional. User defined labels for the asset.
    name: Output only. The relative resource name of the asset, of the form: p
      rojects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{
      zone_id}/assets/{asset_id}.
    resourceSpec: Required. Specification of the resource that is referenced
      by this asset.
    resourceStatus: Output only. Status of the resource referenced by this
      asset.
    securityStatus: Output only. Status of the security policy applied to
      resource referenced by this asset.
    state: Output only. Current state of the asset.
    uid: Output only. System generated globally unique ID for the asset. This
      ID will be different if the asset is deleted and re-created with the
      same name.
    updateTime: Output only. The time when the asset was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the asset.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      ACTIVE: Resource is active, i.e., ready to use.
      CREATING: Resource is under creation.
      DELETING: Resource is under deletion.
      ACTION_REQUIRED: Resource is active but has unresolved actions.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    ACTION_REQUIRED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User defined labels for the asset.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  discoverySpec = _messages.MessageField('GoogleCloudDataplexV1AssetDiscoverySpec', 3)
  discoveryStatus = _messages.MessageField('GoogleCloudDataplexV1AssetDiscoveryStatus', 4)
  displayName = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  resourceSpec = _messages.MessageField('GoogleCloudDataplexV1AssetResourceSpec', 8)
  resourceStatus = _messages.MessageField('GoogleCloudDataplexV1AssetResourceStatus', 9)
  securityStatus = _messages.MessageField('GoogleCloudDataplexV1AssetSecurityStatus', 10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  uid = _messages.StringField(12)
  updateTime = _messages.StringField(13)


class GoogleCloudDataplexV1AssetDiscoverySpec(_messages.Message):
  r"""Settings to manage the metadata discovery and publishing for an asset.

  Fields:
    csvOptions: Optional. Configuration for CSV data.
    enabled: Optional. Whether discovery is enabled.
    excludePatterns: Optional. The list of patterns to apply for selecting
      data to exclude during discovery. For Cloud Storage bucket assets, these
      are interpreted as glob patterns used to match object names. For
      BigQuery dataset assets, these are interpreted as patterns to match
      table names.
    includePatterns: Optional. The list of patterns to apply for selecting
      data to include during discovery if only a subset of the data should
      considered. For Cloud Storage bucket assets, these are interpreted as
      glob patterns used to match object names. For BigQuery dataset assets,
      these are interpreted as patterns to match table names.
    jsonOptions: Optional. Configuration for Json data.
    schedule: Optional. Cron schedule (https://en.wikipedia.org/wiki/Cron) for
      running discovery periodically. Successive discovery runs must be
      scheduled at least 60 minutes apart. The default value is to run
      discovery every 60 minutes. To explicitly set a timezone to the cron
      tab, apply a prefix in the cron tab: "CRON_TZ=${IANA_TIME_ZONE}" or
      TZ=${IANA_TIME_ZONE}". The ${IANA_TIME_ZONE} may only be a valid string
      from IANA time zone database. For example, CRON_TZ=America/New_York 1 *
      * * *, or TZ=America/New_York 1 * * * *.
  """

  csvOptions = _messages.MessageField('GoogleCloudDataplexV1AssetDiscoverySpecCsvOptions', 1)
  enabled = _messages.BooleanField(2)
  excludePatterns = _messages.StringField(3, repeated=True)
  includePatterns = _messages.StringField(4, repeated=True)
  jsonOptions = _messages.MessageField('GoogleCloudDataplexV1AssetDiscoverySpecJsonOptions', 5)
  schedule = _messages.StringField(6)


class GoogleCloudDataplexV1AssetDiscoverySpecCsvOptions(_messages.Message):
  r"""Describe CSV and similar semi-structured data formats.

  Fields:
    delimiter: Optional. The delimiter being used to separate values. This
      defaults to ','.
    disableTypeInference: Optional. Whether to disable the inference of data
      type for CSV data. If true, all columns will be registered as strings.
    encoding: Optional. The character encoding of the data. The default is
      UTF-8.
    headerRows: Optional. The number of rows to interpret as header rows that
      should be skipped when reading data rows.
  """

  delimiter = _messages.StringField(1)
  disableTypeInference = _messages.BooleanField(2)
  encoding = _messages.StringField(3)
  headerRows = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDataplexV1AssetDiscoverySpecJsonOptions(_messages.Message):
  r"""Describe JSON data format.

  Fields:
    disableTypeInference: Optional. Whether to disable the inference of data
      type for Json data. If true, all columns will be registered as their
      primitive types (strings, number or boolean).
    encoding: Optional. The character encoding of the data. The default is
      UTF-8.
  """

  disableTypeInference = _messages.BooleanField(1)
  encoding = _messages.StringField(2)


class GoogleCloudDataplexV1AssetDiscoveryStatus(_messages.Message):
  r"""Status of discovery for an asset.

  Enums:
    StateValueValuesEnum: The current status of the discovery feature.

  Fields:
    lastRunDuration: The duration of the last discovery run.
    lastRunTime: The start time of the last discovery run.
    message: Additional information about the current state.
    state: The current status of the discovery feature.
    stats: Data Stats of the asset reported by discovery.
    updateTime: Last update time of the status.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current status of the discovery feature.

    Values:
      STATE_UNSPECIFIED: State is unspecified.
      SCHEDULED: Discovery for the asset is scheduled.
      IN_PROGRESS: Discovery for the asset is running.
      PAUSED: Discovery for the asset is currently paused (e.g. due to a lack
        of available resources). It will be automatically resumed.
      DISABLED: Discovery for the asset is disabled.
    """
    STATE_UNSPECIFIED = 0
    SCHEDULED = 1
    IN_PROGRESS = 2
    PAUSED = 3
    DISABLED = 4

  lastRunDuration = _messages.StringField(1)
  lastRunTime = _messages.StringField(2)
  message = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  stats = _messages.MessageField('GoogleCloudDataplexV1AssetDiscoveryStatusStats', 5)
  updateTime = _messages.StringField(6)


class GoogleCloudDataplexV1AssetDiscoveryStatusStats(_messages.Message):
  r"""The aggregated data statistics for the asset reported by discovery.

  Fields:
    dataItems: The count of data items within the referenced resource.
    dataSize: The number of stored data bytes within the referenced resource.
    filesets: The count of fileset entities within the referenced resource.
    tables: The count of table entities within the referenced resource.
  """

  dataItems = _messages.IntegerField(1)
  dataSize = _messages.IntegerField(2)
  filesets = _messages.IntegerField(3)
  tables = _messages.IntegerField(4)


class GoogleCloudDataplexV1AssetResourceSpec(_messages.Message):
  r"""Identifies the cloud resource that is referenced by this asset.

  Enums:
    ReadAccessModeValueValuesEnum: Optional. Determines how read permissions
      are handled for each asset and their associated tables. Only available
      to storage buckets assets.
    TypeValueValuesEnum: Required. Immutable. Type of resource.

  Fields:
    name: Immutable. Relative name of the cloud resource that contains the
      data that is being managed within a lake. For example:
      projects/{project_number}/buckets/{bucket_id}
      projects/{project_number}/datasets/{dataset_id}
    readAccessMode: Optional. Determines how read permissions are handled for
      each asset and their associated tables. Only available to storage
      buckets assets.
    type: Required. Immutable. Type of resource.
  """

  class ReadAccessModeValueValuesEnum(_messages.Enum):
    r"""Optional. Determines how read permissions are handled for each asset
    and their associated tables. Only available to storage buckets assets.

    Values:
      ACCESS_MODE_UNSPECIFIED: Access mode unspecified.
      DIRECT: Default. Data is accessed directly using storage APIs.
      MANAGED: Data is accessed through a managed interface using BigQuery
        APIs.
    """
    ACCESS_MODE_UNSPECIFIED = 0
    DIRECT = 1
    MANAGED = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. Type of resource.

    Values:
      TYPE_UNSPECIFIED: Type not specified.
      STORAGE_BUCKET: Cloud Storage bucket.
      BIGQUERY_DATASET: BigQuery dataset.
    """
    TYPE_UNSPECIFIED = 0
    STORAGE_BUCKET = 1
    BIGQUERY_DATASET = 2

  name = _messages.StringField(1)
  readAccessMode = _messages.EnumField('ReadAccessModeValueValuesEnum', 2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class GoogleCloudDataplexV1AssetResourceStatus(_messages.Message):
  r"""Status of the resource referenced by an asset.

  Enums:
    StateValueValuesEnum: The current state of the managed resource.

  Fields:
    managedAccessIdentity: Output only. Service account associated with the
      BigQuery Connection.
    message: Additional information about the current state.
    state: The current state of the managed resource.
    updateTime: Last update time of the status.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the managed resource.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      READY: Resource does not have any errors.
      ERROR: Resource has errors.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    ERROR = 2

  managedAccessIdentity = _messages.StringField(1)
  message = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class GoogleCloudDataplexV1AssetSecurityStatus(_messages.Message):
  r"""Security policy status of the asset. Data security policy, i.e.,
  readers, writers & owners, should be specified in the lake/zone/asset IAM
  policy.

  Enums:
    StateValueValuesEnum: The current state of the security policy applied to
      the attached resource.

  Fields:
    message: Additional information about the current state.
    state: The current state of the security policy applied to the attached
      resource.
    updateTime: Last update time of the status.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the security policy applied to the attached
    resource.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      READY: Security policy has been successfully applied to the attached
        resource.
      APPLYING: Security policy is in the process of being applied to the
        attached resource.
      ERROR: Security policy could not be applied to the attached resource due
        to errors.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    APPLYING = 2
    ERROR = 3

  message = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  updateTime = _messages.StringField(3)


class GoogleCloudDataplexV1AssetStatus(_messages.Message):
  r"""Aggregated status of the underlying assets of a lake or zone.

  Fields:
    activeAssets: Number of active assets.
    securityPolicyApplyingAssets: Number of assets that are in process of
      updating the security policy on attached resources.
    updateTime: Last update time of the status.
  """

  activeAssets = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  securityPolicyApplyingAssets = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  updateTime = _messages.StringField(3)


class GoogleCloudDataplexV1CancelJobRequest(_messages.Message):
  r"""Cancel task jobs."""


class GoogleCloudDataplexV1Content(_messages.Message):
  r"""Content represents a user-visible notebook or a sql script

  Messages:
    LabelsValue: Optional. User defined labels for the content.

  Fields:
    createTime: Output only. Content creation time.
    dataText: Required. Content data in string format.
    description: Optional. Description of the content.
    labels: Optional. User defined labels for the content.
    name: Output only. The relative resource name of the content, of the form:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}/content/{c
      ontent_id}
    notebook: Notebook related configurations.
    path: Required. The path for the Content file, represented as directory
      structure. Unique within a lake. Limited to alphanumerics, hyphens,
      underscores, dots and slashes.
    sqlScript: Sql Script related configurations.
    uid: Output only. System generated globally unique ID for the content.
      This ID will be different if the content is deleted and re-created with
      the same name.
    updateTime: Output only. The time when the content was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User defined labels for the content.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  dataText = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  notebook = _messages.MessageField('GoogleCloudDataplexV1ContentNotebook', 6)
  path = _messages.StringField(7)
  sqlScript = _messages.MessageField('GoogleCloudDataplexV1ContentSqlScript', 8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class GoogleCloudDataplexV1ContentNotebook(_messages.Message):
  r"""Configuration for Notebook content.

  Enums:
    KernelTypeValueValuesEnum: Required. Kernel Type of the notebook.

  Fields:
    kernelType: Required. Kernel Type of the notebook.
  """

  class KernelTypeValueValuesEnum(_messages.Enum):
    r"""Required. Kernel Type of the notebook.

    Values:
      KERNEL_TYPE_UNSPECIFIED: Kernel Type unspecified.
      PYTHON3: Python 3 Kernel.
    """
    KERNEL_TYPE_UNSPECIFIED = 0
    PYTHON3 = 1

  kernelType = _messages.EnumField('KernelTypeValueValuesEnum', 1)


class GoogleCloudDataplexV1ContentSqlScript(_messages.Message):
  r"""Configuration for the Sql Script content.

  Enums:
    EngineValueValuesEnum: Required. Query Engine to be used for the Sql
      Query.

  Fields:
    engine: Required. Query Engine to be used for the Sql Query.
  """

  class EngineValueValuesEnum(_messages.Enum):
    r"""Required. Query Engine to be used for the Sql Query.

    Values:
      QUERY_ENGINE_UNSPECIFIED: Value was unspecified.
      SPARK: Spark SQL Query.
    """
    QUERY_ENGINE_UNSPECIFIED = 0
    SPARK = 1

  engine = _messages.EnumField('EngineValueValuesEnum', 1)


class GoogleCloudDataplexV1DataAccessSpec(_messages.Message):
  r"""DataAccessSpec holds the access control configuration to be enforced on
  data stored within resources (eg: rows, columns in BigQuery Tables). When
  associated with data, the data is only accessible to principals explicitly
  granted access through the DataAccessSpec. Principals with access to the
  containing resource are not implicitly granted access.

  Fields:
    readers: Optional. The format of strings follows the pattern followed by
      IAM in the bindings. user:{email}, serviceAccount:{email} group:{email}.
      The set of principals to be granted reader role on data stored within
      resources.
  """

  readers = _messages.StringField(1, repeated=True)


class GoogleCloudDataplexV1DataAttribute(_messages.Message):
  r"""Denotes one dataAttribute in a dataTaxonomy, for example, PII.
  DataAttribute resources can be defined in a hierarchy. A single
  dataAttribute resource can contain specs of multiple types PII -
  ResourceAccessSpec : - readers :<EMAIL> - DataAccessSpec : - readers
  :<EMAIL>

  Messages:
    LabelsValue: Optional. User-defined labels for the DataAttribute.

  Fields:
    attributeCount: Output only. The number of child attributes present for
      this attribute.
    createTime: Output only. The time when the DataAttribute was created.
    dataAccessSpec: Optional. Specified when applied to data stored on the
      resource (eg: rows, columns in BigQuery Tables).
    description: Optional. Description of the DataAttribute.
    displayName: Optional. User friendly display name.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    labels: Optional. User-defined labels for the DataAttribute.
    name: Output only. The relative resource name of the dataAttribute, of the
      form: projects/{project_number}/locations/{location_id}/dataTaxonomies/{
      dataTaxonomy}/attributes/{data_attribute_id}.
    parentId: Optional. The ID of the parent DataAttribute resource, should
      belong to the same data taxonomy. Circular dependency in parent chain is
      not valid. Maximum depth of the hierarchy allowed is 4. a -> b -> c -> d
      -> e, depth = 4
    resourceAccessSpec: Optional. Specified when applied to a resource (eg:
      Cloud Storage bucket, BigQuery dataset, BigQuery table).
    uid: Output only. System generated globally unique ID for the
      DataAttribute. This ID will be different if the DataAttribute is deleted
      and re-created with the same name.
    updateTime: Output only. The time when the DataAttribute was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels for the DataAttribute.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  createTime = _messages.StringField(2)
  dataAccessSpec = _messages.MessageField('GoogleCloudDataplexV1DataAccessSpec', 3)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  parentId = _messages.StringField(9)
  resourceAccessSpec = _messages.MessageField('GoogleCloudDataplexV1ResourceAccessSpec', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleCloudDataplexV1DataAttributeBinding(_messages.Message):
  r"""DataAttributeBinding represents binding of attributes to resources. Eg:
  Bind 'CustomerInfo' entity with 'PII' attribute.

  Messages:
    LabelsValue: Optional. User-defined labels for the DataAttributeBinding.

  Fields:
    attributes: Optional. List of attributes to be associated with the
      resource, provided in the form: projects/{project}/locations/{location}/
      dataTaxonomies/{dataTaxonomy}/attributes/{data_attribute_id}
    createTime: Output only. The time when the DataAttributeBinding was
      created.
    description: Optional. Description of the DataAttributeBinding.
    displayName: Optional. User friendly display name.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding. Etags must be used
      when calling the DeleteDataAttributeBinding and the
      UpdateDataAttributeBinding method.
    labels: Optional. User-defined labels for the DataAttributeBinding.
    name: Output only. The relative resource name of the Data Attribute
      Binding, of the form: projects/{project_number}/locations/{location}/dat
      aAttributeBindings/{data_attribute_binding_id}
    paths: Optional. The list of paths for items within the associated
      resource (eg. columns and partitions within a table) along with
      attribute bindings.
    resource: Optional. Immutable. The resource name of the resource that is
      associated to attributes. Presently, only entity resource is supported
      in the form: projects/{project}/locations/{location}/lakes/{lake}/zones/
      {zone}/entities/{entity_id} Must belong in the same project and region
      as the attribute binding, and there can only exist one active binding
      for a resource.
    uid: Output only. System generated globally unique ID for the
      DataAttributeBinding. This ID will be different if the
      DataAttributeBinding is deleted and re-created with the same name.
    updateTime: Output only. The time when the DataAttributeBinding was last
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels for the DataAttributeBinding.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.StringField(1, repeated=True)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  paths = _messages.MessageField('GoogleCloudDataplexV1DataAttributeBindingPath', 8, repeated=True)
  resource = _messages.StringField(9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class GoogleCloudDataplexV1DataAttributeBindingPath(_messages.Message):
  r"""Represents a subresource of the given resource, and associated bindings
  with it. Currently supported subresources are column and partition schema
  fields within a table.

  Fields:
    attributes: Optional. List of attributes to be associated with the path of
      the resource, provided in the form: projects/{project}/locations/{locati
      on}/dataTaxonomies/{dataTaxonomy}/attributes/{data_attribute_id}
    name: Required. The name identifier of the path. Nested columns should be
      of the form: 'address.city'.
  """

  attributes = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)


class GoogleCloudDataplexV1DataProfileResult(_messages.Message):
  r"""DataProfileResult defines the output of DataProfileScan. Each field of
  the table will have field type specific profile result.

  Fields:
    postScanActionsResult: Output only. The result of post scan actions.
    profile: The profile information per field.
    rowCount: The count of rows scanned.
    scannedData: The data scanned for this result.
  """

  postScanActionsResult = _messages.MessageField('GoogleCloudDataplexV1DataProfileResultPostScanActionsResult', 1)
  profile = _messages.MessageField('GoogleCloudDataplexV1DataProfileResultProfile', 2)
  rowCount = _messages.IntegerField(3)
  scannedData = _messages.MessageField('GoogleCloudDataplexV1ScannedData', 4)


class GoogleCloudDataplexV1DataProfileResultPostScanActionsResult(_messages.Message):
  r"""The result of post scan actions of DataProfileScan job.

  Fields:
    bigqueryExportResult: Output only. The result of BigQuery export post scan
      action.
  """

  bigqueryExportResult = _messages.MessageField('GoogleCloudDataplexV1DataProfileResultPostScanActionsResultBigQueryExportResult', 1)


class GoogleCloudDataplexV1DataProfileResultPostScanActionsResultBigQueryExportResult(_messages.Message):
  r"""The result of BigQuery export post scan action.

  Enums:
    StateValueValuesEnum: Output only. Execution state for the BigQuery
      exporting.

  Fields:
    message: Output only. Additional information about the BigQuery exporting.
    state: Output only. Execution state for the BigQuery exporting.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Execution state for the BigQuery exporting.

    Values:
      STATE_UNSPECIFIED: The exporting state is unspecified.
      SUCCEEDED: The exporting completed successfully.
      FAILED: The exporting is no longer running due to an error.
      SKIPPED: The exporting is skipped due to no valid scan result to export
        (usually caused by scan failed).
    """
    STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    SKIPPED = 3

  message = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudDataplexV1DataProfileResultProfile(_messages.Message):
  r"""Contains name, type, mode and field type specific profile information.

  Fields:
    fields: List of fields with structural and profile information for each
      field.
  """

  fields = _messages.MessageField('GoogleCloudDataplexV1DataProfileResultProfileField', 1, repeated=True)


class GoogleCloudDataplexV1DataProfileResultProfileField(_messages.Message):
  r"""A field within a table.

  Fields:
    mode: The mode of the field. Possible values include: REQUIRED, if it is a
      required field. NULLABLE, if it is an optional field. REPEATED, if it is
      a repeated field.
    name: The name of the field.
    profile: Profile information for the corresponding field.
    type: The data type retrieved from the schema of the data source. For
      instance, for a BigQuery native table, it is the BigQuery Table Schema (
      https://cloud.google.com/bigquery/docs/reference/rest/v2/tables#tablefie
      ldschema). For a Dataplex Entity, it is the Entity Schema (https://cloud
      .google.com/dataplex/docs/reference/rpc/google.cloud.dataplex.v1#type_3)
      .
  """

  mode = _messages.StringField(1)
  name = _messages.StringField(2)
  profile = _messages.MessageField('GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfo', 3)
  type = _messages.StringField(4)


class GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfo(_messages.Message):
  r"""The profile information for each field type.

  Fields:
    distinctRatio: Ratio of rows with distinct values against total scanned
      rows. Not available for complex non-groupable field type RECORD and
      fields with REPEATABLE mode.
    doubleProfile: Double type field information.
    integerProfile: Integer type field information.
    nullRatio: Ratio of rows with null value against total scanned rows.
    stringProfile: String type field information.
    topNValues: The list of top N non-null values, frequency and ratio with
      which they occur in the scanned data. N is 10 or equal to the number of
      distinct values in the field, whichever is smaller. Not available for
      complex non-groupable field type RECORD and fields with REPEATABLE mode.
  """

  distinctRatio = _messages.FloatField(1)
  doubleProfile = _messages.MessageField('GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfoDoubleFieldInfo', 2)
  integerProfile = _messages.MessageField('GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfoIntegerFieldInfo', 3)
  nullRatio = _messages.FloatField(4)
  stringProfile = _messages.MessageField('GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfoStringFieldInfo', 5)
  topNValues = _messages.MessageField('GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfoTopNValue', 6, repeated=True)


class GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfoDoubleFieldInfo(_messages.Message):
  r"""The profile information for a double type field.

  Fields:
    average: Average of non-null values in the scanned data. NaN, if the field
      has a NaN.
    max: Maximum of non-null values in the scanned data. NaN, if the field has
      a NaN.
    min: Minimum of non-null values in the scanned data. NaN, if the field has
      a NaN.
    quartiles: A quartile divides the number of data points into four parts,
      or quarters, of more-or-less equal size. Three main quartiles used are:
      The first quartile (Q1) splits off the lowest 25% of data from the
      highest 75%. It is also known as the lower or 25th empirical quartile,
      as 25% of the data is below this point. The second quartile (Q2) is the
      median of a data set. So, 50% of the data lies below this point. The
      third quartile (Q3) splits off the highest 25% of data from the lowest
      75%. It is known as the upper or 75th empirical quartile, as 75% of the
      data lies below this point. Here, the quartiles is provided as an
      ordered list of quartile values for the scanned data, occurring in order
      Q1, median, Q3.
    standardDeviation: Standard deviation of non-null values in the scanned
      data. NaN, if the field has a NaN.
  """

  average = _messages.FloatField(1)
  max = _messages.FloatField(2)
  min = _messages.FloatField(3)
  quartiles = _messages.FloatField(4, repeated=True)
  standardDeviation = _messages.FloatField(5)


class GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfoIntegerFieldInfo(_messages.Message):
  r"""The profile information for an integer type field.

  Fields:
    average: Average of non-null values in the scanned data. NaN, if the field
      has a NaN.
    max: Maximum of non-null values in the scanned data. NaN, if the field has
      a NaN.
    min: Minimum of non-null values in the scanned data. NaN, if the field has
      a NaN.
    quartiles: A quartile divides the number of data points into four parts,
      or quarters, of more-or-less equal size. Three main quartiles used are:
      The first quartile (Q1) splits off the lowest 25% of data from the
      highest 75%. It is also known as the lower or 25th empirical quartile,
      as 25% of the data is below this point. The second quartile (Q2) is the
      median of a data set. So, 50% of the data lies below this point. The
      third quartile (Q3) splits off the highest 25% of data from the lowest
      75%. It is known as the upper or 75th empirical quartile, as 75% of the
      data lies below this point. Here, the quartiles is provided as an
      ordered list of approximate quartile values for the scanned data,
      occurring in order Q1, median, Q3.
    standardDeviation: Standard deviation of non-null values in the scanned
      data. NaN, if the field has a NaN.
  """

  average = _messages.FloatField(1)
  max = _messages.IntegerField(2)
  min = _messages.IntegerField(3)
  quartiles = _messages.IntegerField(4, repeated=True)
  standardDeviation = _messages.FloatField(5)


class GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfoStringFieldInfo(_messages.Message):
  r"""The profile information for a string type field.

  Fields:
    averageLength: Average length of non-null values in the scanned data.
    maxLength: Maximum length of non-null values in the scanned data.
    minLength: Minimum length of non-null values in the scanned data.
  """

  averageLength = _messages.FloatField(1)
  maxLength = _messages.IntegerField(2)
  minLength = _messages.IntegerField(3)


class GoogleCloudDataplexV1DataProfileResultProfileFieldProfileInfoTopNValue(_messages.Message):
  r"""Top N non-null values in the scanned data.

  Fields:
    count: Count of the corresponding value in the scanned data.
    ratio: Ratio of the corresponding value in the field against the total
      number of rows in the scanned data.
    value: String value of a top N non-null value.
  """

  count = _messages.IntegerField(1)
  ratio = _messages.FloatField(2)
  value = _messages.StringField(3)


class GoogleCloudDataplexV1DataProfileSpec(_messages.Message):
  r"""DataProfileScan related setting.

  Fields:
    excludeFields: Optional. The fields to exclude from data profile.If
      specified, the fields will be excluded from data profile, regardless of
      include_fields value.
    includeFields: Optional. The fields to include in data profile.If not
      specified, all fields at the time of profile scan job execution are
      included, except for ones listed in exclude_fields.
    postScanActions: Optional. Actions to take upon job completion..
    rowFilter: Optional. A filter applied to all rows in a single DataScan
      job. The filter needs to be a valid SQL expression for a WHERE clause in
      BigQuery standard SQL syntax. Example: col1 >= 0 AND col2 < 10
    samplingPercent: Optional. The percentage of the records to be selected
      from the dataset for DataScan. Value can range between 0.0 and 100.0
      with up to 3 significant decimal digits. Sampling is not applied if
      sampling_percent is not specified, 0 or 100.
  """

  excludeFields = _messages.MessageField('GoogleCloudDataplexV1DataProfileSpecSelectedFields', 1)
  includeFields = _messages.MessageField('GoogleCloudDataplexV1DataProfileSpecSelectedFields', 2)
  postScanActions = _messages.MessageField('GoogleCloudDataplexV1DataProfileSpecPostScanActions', 3)
  rowFilter = _messages.StringField(4)
  samplingPercent = _messages.FloatField(5, variant=_messages.Variant.FLOAT)


class GoogleCloudDataplexV1DataProfileSpecPostScanActions(_messages.Message):
  r"""The configuration of post scan actions of DataProfileScan job.

  Fields:
    bigqueryExport: Optional. If set, results will be exported to the provided
      BigQuery table.
  """

  bigqueryExport = _messages.MessageField('GoogleCloudDataplexV1DataProfileSpecPostScanActionsBigQueryExport', 1)


class GoogleCloudDataplexV1DataProfileSpecPostScanActionsBigQueryExport(_messages.Message):
  r"""The configuration of BigQuery export post scan action.

  Fields:
    resultsTable: Optional. The BigQuery table to export DataProfileScan
      results to. Format: projects/{project}/datasets/{dataset}/tables/{table}
  """

  resultsTable = _messages.StringField(1)


class GoogleCloudDataplexV1DataProfileSpecSelectedFields(_messages.Message):
  r"""The specification for fields to include or exclude in data profile scan.

  Fields:
    fieldNames: Optional. Expected input is a list of fully qualified names of
      fields as in the schema.Only top-level field names for nested fields are
      supported. For instance, if 'x' is of nested field type, listing 'x' is
      supported but 'x.y.z' is not supported. Here 'y' and 'y.z' are nested
      fields of 'x'.
  """

  fieldNames = _messages.StringField(1, repeated=True)


class GoogleCloudDataplexV1DataQualityDimensionResult(_messages.Message):
  r"""DataQualityDimensionResult provides a more detailed, per-dimension view
  of the results.

  Fields:
    passed: Whether the dimension passed or failed.
  """

  passed = _messages.BooleanField(1)


class GoogleCloudDataplexV1DataQualityResult(_messages.Message):
  r"""The output of a DataQualityScan.

  Fields:
    dimensions: A list of results at the dimension level.
    passed: Overall data quality result -- true if all rules passed.
    postScanActionsResult: Output only. The result of post scan actions.
    rowCount: The count of rows processed.
    rules: A list of all the rules in a job, and their results.
    scannedData: The data scanned for this result.
  """

  dimensions = _messages.MessageField('GoogleCloudDataplexV1DataQualityDimensionResult', 1, repeated=True)
  passed = _messages.BooleanField(2)
  postScanActionsResult = _messages.MessageField('GoogleCloudDataplexV1DataQualityResultPostScanActionsResult', 3)
  rowCount = _messages.IntegerField(4)
  rules = _messages.MessageField('GoogleCloudDataplexV1DataQualityRuleResult', 5, repeated=True)
  scannedData = _messages.MessageField('GoogleCloudDataplexV1ScannedData', 6)


class GoogleCloudDataplexV1DataQualityResultPostScanActionsResult(_messages.Message):
  r"""The result of post scan actions of DataQualityScan job.

  Fields:
    bigqueryExportResult: Output only. The result of BigQuery export post scan
      action.
  """

  bigqueryExportResult = _messages.MessageField('GoogleCloudDataplexV1DataQualityResultPostScanActionsResultBigQueryExportResult', 1)


class GoogleCloudDataplexV1DataQualityResultPostScanActionsResultBigQueryExportResult(_messages.Message):
  r"""The result of BigQuery export post scan action.

  Enums:
    StateValueValuesEnum: Output only. Execution state for the BigQuery
      exporting.

  Fields:
    message: Output only. Additional information about the BigQuery exporting.
    state: Output only. Execution state for the BigQuery exporting.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Execution state for the BigQuery exporting.

    Values:
      STATE_UNSPECIFIED: The exporting state is unspecified.
      SUCCEEDED: The exporting completed successfully.
      FAILED: The exporting is no longer running due to an error.
      SKIPPED: The exporting is skipped due to no valid scan result to export
        (usually caused by scan failed).
    """
    STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    SKIPPED = 3

  message = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudDataplexV1DataQualityRule(_messages.Message):
  r"""A rule captures data quality intent about a data source.

  Fields:
    column: Optional. The unnested column which this rule is evaluated
      against.
    description: Optional. Description of the rule. The maximum length is
      1,024 characters.
    dimension: Required. The dimension a rule belongs to. Results are also
      aggregated at the dimension level. Supported dimensions are
      "COMPLETENESS", "ACCURACY", "CONSISTENCY", "VALIDITY", "UNIQUENESS",
      "INTEGRITY"
    ignoreNull: Optional. Rows with null values will automatically fail a
      rule, unless ignore_null is true. In that case, such null rows are
      trivially considered passing.This field is only valid for row-level type
      rules.
    name: Optional. A mutable name for the rule. The name must contain only
      letters (a-z, A-Z), numbers (0-9), or hyphens (-). The maximum length is
      63 characters. Must start with a letter. Must end with a number or a
      letter.
    nonNullExpectation: Row-level rule which evaluates whether each column
      value is null.
    rangeExpectation: Row-level rule which evaluates whether each column value
      lies between a specified range.
    regexExpectation: Row-level rule which evaluates whether each column value
      matches a specified regex.
    rowConditionExpectation: Row-level rule which evaluates whether each row
      in a table passes the specified condition.
    setExpectation: Row-level rule which evaluates whether each column value
      is contained by a specified set.
    statisticRangeExpectation: Aggregate rule which evaluates whether the
      column aggregate statistic lies between a specified range.
    tableConditionExpectation: Aggregate rule which evaluates whether the
      provided expression is true for a table.
    threshold: Optional. The minimum ratio of passing_rows / total_rows
      required to pass this rule, with a range of 0.0, 1.0.0 indicates default
      value (i.e. 1.0).This field is only valid for row-level type rules.
    uniquenessExpectation: Row-level rule which evaluates whether each column
      value is unique.
  """

  column = _messages.StringField(1)
  description = _messages.StringField(2)
  dimension = _messages.StringField(3)
  ignoreNull = _messages.BooleanField(4)
  name = _messages.StringField(5)
  nonNullExpectation = _messages.MessageField('GoogleCloudDataplexV1DataQualityRuleNonNullExpectation', 6)
  rangeExpectation = _messages.MessageField('GoogleCloudDataplexV1DataQualityRuleRangeExpectation', 7)
  regexExpectation = _messages.MessageField('GoogleCloudDataplexV1DataQualityRuleRegexExpectation', 8)
  rowConditionExpectation = _messages.MessageField('GoogleCloudDataplexV1DataQualityRuleRowConditionExpectation', 9)
  setExpectation = _messages.MessageField('GoogleCloudDataplexV1DataQualityRuleSetExpectation', 10)
  statisticRangeExpectation = _messages.MessageField('GoogleCloudDataplexV1DataQualityRuleStatisticRangeExpectation', 11)
  tableConditionExpectation = _messages.MessageField('GoogleCloudDataplexV1DataQualityRuleTableConditionExpectation', 12)
  threshold = _messages.FloatField(13)
  uniquenessExpectation = _messages.MessageField('GoogleCloudDataplexV1DataQualityRuleUniquenessExpectation', 14)


class GoogleCloudDataplexV1DataQualityRuleNonNullExpectation(_messages.Message):
  r"""Evaluates whether each column value is null."""


class GoogleCloudDataplexV1DataQualityRuleRangeExpectation(_messages.Message):
  r"""Evaluates whether each column value lies between a specified range.

  Fields:
    maxValue: Optional. The maximum column value allowed for a row to pass
      this validation. At least one of min_value and max_value need to be
      provided.
    minValue: Optional. The minimum column value allowed for a row to pass
      this validation. At least one of min_value and max_value need to be
      provided.
    strictMaxEnabled: Optional. Whether each value needs to be strictly lesser
      than ('<') the maximum, or if equality is allowed.Only relevant if a
      max_value has been defined. Default = false.
    strictMinEnabled: Optional. Whether each value needs to be strictly
      greater than ('>') the minimum, or if equality is allowed.Only relevant
      if a min_value has been defined. Default = false.
  """

  maxValue = _messages.StringField(1)
  minValue = _messages.StringField(2)
  strictMaxEnabled = _messages.BooleanField(3)
  strictMinEnabled = _messages.BooleanField(4)


class GoogleCloudDataplexV1DataQualityRuleRegexExpectation(_messages.Message):
  r"""Evaluates whether each column value matches a specified regex.

  Fields:
    regex: Optional. A regular expression the column value is expected to
      match.
  """

  regex = _messages.StringField(1)


class GoogleCloudDataplexV1DataQualityRuleResult(_messages.Message):
  r"""DataQualityRuleResult provides a more detailed, per-rule view of the
  results.

  Fields:
    evaluatedCount: The number of rows a rule was evaluated against.This field
      is only valid for row-level type rules.Evaluated count can be configured
      to either include all rows (default) - with null rows automatically
      failing rule evaluation, or exclude null rows from the evaluated_count,
      by setting ignore_nulls = true.
    failingRowsQuery: The query to find rows that did not pass this rule.This
      field is only valid for row-level type rules.
    nullCount: The number of rows with null values in the specified column.
    passRatio: The ratio of passed_count / evaluated_count.This field is only
      valid for row-level type rules.
    passed: Whether the rule passed or failed.
    passedCount: The number of rows which passed a rule evaluation.This field
      is only valid for row-level type rules.
    rule: The rule specified in the DataQualitySpec, as is.
  """

  evaluatedCount = _messages.IntegerField(1)
  failingRowsQuery = _messages.StringField(2)
  nullCount = _messages.IntegerField(3)
  passRatio = _messages.FloatField(4)
  passed = _messages.BooleanField(5)
  passedCount = _messages.IntegerField(6)
  rule = _messages.MessageField('GoogleCloudDataplexV1DataQualityRule', 7)


class GoogleCloudDataplexV1DataQualityRuleRowConditionExpectation(_messages.Message):
  r"""Evaluates whether each row passes the specified condition.The SQL
  expression needs to use BigQuery standard SQL syntax and should produce a
  boolean value per row as the result.Example: col1 >= 0 AND col2 < 10

  Fields:
    sqlExpression: Optional. The SQL expression.
  """

  sqlExpression = _messages.StringField(1)


class GoogleCloudDataplexV1DataQualityRuleSetExpectation(_messages.Message):
  r"""Evaluates whether each column value is contained by a specified set.

  Fields:
    values: Optional. Expected values for the column value.
  """

  values = _messages.StringField(1, repeated=True)


class GoogleCloudDataplexV1DataQualityRuleStatisticRangeExpectation(_messages.Message):
  r"""Evaluates whether the column aggregate statistic lies between a
  specified range.

  Enums:
    StatisticValueValuesEnum: Optional. The aggregate metric to evaluate.

  Fields:
    maxValue: Optional. The maximum column statistic value allowed for a row
      to pass this validation.At least one of min_value and max_value need to
      be provided.
    minValue: Optional. The minimum column statistic value allowed for a row
      to pass this validation.At least one of min_value and max_value need to
      be provided.
    statistic: Optional. The aggregate metric to evaluate.
    strictMaxEnabled: Optional. Whether column statistic needs to be strictly
      lesser than ('<') the maximum, or if equality is allowed.Only relevant
      if a max_value has been defined. Default = false.
    strictMinEnabled: Optional. Whether column statistic needs to be strictly
      greater than ('>') the minimum, or if equality is allowed.Only relevant
      if a min_value has been defined. Default = false.
  """

  class StatisticValueValuesEnum(_messages.Enum):
    r"""Optional. The aggregate metric to evaluate.

    Values:
      STATISTIC_UNDEFINED: Unspecified statistic type
      MEAN: Evaluate the column mean
      MIN: Evaluate the column min
      MAX: Evaluate the column max
    """
    STATISTIC_UNDEFINED = 0
    MEAN = 1
    MIN = 2
    MAX = 3

  maxValue = _messages.StringField(1)
  minValue = _messages.StringField(2)
  statistic = _messages.EnumField('StatisticValueValuesEnum', 3)
  strictMaxEnabled = _messages.BooleanField(4)
  strictMinEnabled = _messages.BooleanField(5)


class GoogleCloudDataplexV1DataQualityRuleTableConditionExpectation(_messages.Message):
  r"""Evaluates whether the provided expression is true.The SQL expression
  needs to use BigQuery standard SQL syntax and should produce a scalar
  boolean result.Example: MIN(col1) >= 0

  Fields:
    sqlExpression: Optional. The SQL expression.
  """

  sqlExpression = _messages.StringField(1)


class GoogleCloudDataplexV1DataQualityRuleUniquenessExpectation(_messages.Message):
  r"""Evaluates whether the column has duplicates."""


class GoogleCloudDataplexV1DataQualityScanRuleResult(_messages.Message):
  r"""Information about the result of a data quality rule for data quality
  scan. The monitored resource is 'DataScan'.

  Enums:
    EvalutionTypeValueValuesEnum: The evaluation type of the data quality
      rule.
    ResultValueValuesEnum: The result of the data quality rule.
    RuleTypeValueValuesEnum: The type of the data quality rule.

  Fields:
    column: The column which this rule is evaluated against.
    dataSource: The data source of the data scan (e.g. BigQuery table name).
    evaluatedRowCount: The number of rows evaluated against the data quality
      rule. This field is only valid for rules of PER_ROW evaluation type.
    evalutionType: The evaluation type of the data quality rule.
    jobId: Identifier of the specific data scan job this log entry is for.
    nullRowCount: The number of rows with null values in the specified column.
    passedRowCount: The number of rows which passed a rule evaluation. This
      field is only valid for rules of PER_ROW evaluation type.
    result: The result of the data quality rule.
    ruleDimension: The dimension of the data quality rule.
    ruleName: The name of the data quality rule.
    ruleType: The type of the data quality rule.
    thresholdPercent: The passing threshold (0.0, 100.0) of the data quality
      rule.
  """

  class EvalutionTypeValueValuesEnum(_messages.Enum):
    r"""The evaluation type of the data quality rule.

    Values:
      EVALUATION_TYPE_UNSPECIFIED: An unspecified evaluation type.
      PER_ROW: The rule evaluation is done at per row level.
      AGGREGATE: The rule evaluation is done for an aggregate of rows.
    """
    EVALUATION_TYPE_UNSPECIFIED = 0
    PER_ROW = 1
    AGGREGATE = 2

  class ResultValueValuesEnum(_messages.Enum):
    r"""The result of the data quality rule.

    Values:
      RESULT_UNSPECIFIED: An unspecified result.
      PASSED: The data quality rule passed.
      FAILED: The data quality rule failed.
    """
    RESULT_UNSPECIFIED = 0
    PASSED = 1
    FAILED = 2

  class RuleTypeValueValuesEnum(_messages.Enum):
    r"""The type of the data quality rule.

    Values:
      RULE_TYPE_UNSPECIFIED: An unspecified rule type.
      NON_NULL_EXPECTATION: Please see https://cloud.google.com/dataplex/docs/
        reference/rest/v1/DataQualityRule#nonnullexpectation.
      RANGE_EXPECTATION: Please see https://cloud.google.com/dataplex/docs/ref
        erence/rest/v1/DataQualityRule#rangeexpectation.
      REGEX_EXPECTATION: Please see https://cloud.google.com/dataplex/docs/ref
        erence/rest/v1/DataQualityRule#regexexpectation.
      ROW_CONDITION_EXPECTATION: Please see https://cloud.google.com/dataplex/
        docs/reference/rest/v1/DataQualityRule#rowconditionexpectation.
      SET_EXPECTATION: Please see https://cloud.google.com/dataplex/docs/refer
        ence/rest/v1/DataQualityRule#setexpectation.
      STATISTIC_RANGE_EXPECTATION: Please see https://cloud.google.com/dataple
        x/docs/reference/rest/v1/DataQualityRule#statisticrangeexpectation.
      TABLE_CONDITION_EXPECTATION: Please see https://cloud.google.com/dataple
        x/docs/reference/rest/v1/DataQualityRule#tableconditionexpectation.
      UNIQUENESS_EXPECTATION: Please see https://cloud.google.com/dataplex/doc
        s/reference/rest/v1/DataQualityRule#uniquenessexpectation.
    """
    RULE_TYPE_UNSPECIFIED = 0
    NON_NULL_EXPECTATION = 1
    RANGE_EXPECTATION = 2
    REGEX_EXPECTATION = 3
    ROW_CONDITION_EXPECTATION = 4
    SET_EXPECTATION = 5
    STATISTIC_RANGE_EXPECTATION = 6
    TABLE_CONDITION_EXPECTATION = 7
    UNIQUENESS_EXPECTATION = 8

  column = _messages.StringField(1)
  dataSource = _messages.StringField(2)
  evaluatedRowCount = _messages.IntegerField(3)
  evalutionType = _messages.EnumField('EvalutionTypeValueValuesEnum', 4)
  jobId = _messages.StringField(5)
  nullRowCount = _messages.IntegerField(6)
  passedRowCount = _messages.IntegerField(7)
  result = _messages.EnumField('ResultValueValuesEnum', 8)
  ruleDimension = _messages.StringField(9)
  ruleName = _messages.StringField(10)
  ruleType = _messages.EnumField('RuleTypeValueValuesEnum', 11)
  thresholdPercent = _messages.FloatField(12)


class GoogleCloudDataplexV1DataQualitySpec(_messages.Message):
  r"""DataQualityScan related setting.

  Fields:
    postScanActions: Optional. Actions to take upon job completion.
    rowFilter: Optional. A filter applied to all rows in a single DataScan
      job. The filter needs to be a valid SQL expression for a WHERE clause in
      BigQuery standard SQL syntax. Example: col1 >= 0 AND col2 < 10
    rules: Required. The list of rules to evaluate against a data source. At
      least one rule is required.
    samplingPercent: Optional. The percentage of the records to be selected
      from the dataset for DataScan. Value can range between 0.0 and 100.0
      with up to 3 significant decimal digits. Sampling is not applied if
      sampling_percent is not specified, 0 or 100.
  """

  postScanActions = _messages.MessageField('GoogleCloudDataplexV1DataQualitySpecPostScanActions', 1)
  rowFilter = _messages.StringField(2)
  rules = _messages.MessageField('GoogleCloudDataplexV1DataQualityRule', 3, repeated=True)
  samplingPercent = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class GoogleCloudDataplexV1DataQualitySpecPostScanActions(_messages.Message):
  r"""The configuration of post scan actions of DataQualityScan.

  Fields:
    bigqueryExport: Optional. If set, results will be exported to the provided
      BigQuery table.
  """

  bigqueryExport = _messages.MessageField('GoogleCloudDataplexV1DataQualitySpecPostScanActionsBigQueryExport', 1)


class GoogleCloudDataplexV1DataQualitySpecPostScanActionsBigQueryExport(_messages.Message):
  r"""The configuration of BigQuery export post scan action.

  Fields:
    resultsTable: Optional. The BigQuery table to export DataQualityScan
      results to. Format: projects/{project}/datasets/{dataset}/tables/{table}
  """

  resultsTable = _messages.StringField(1)


class GoogleCloudDataplexV1DataScan(_messages.Message):
  r"""Represents a user-visible job which provides the insights for the
  related data source.For example: Data Quality: generates queries based on
  the rules and runs against the data to get data quality check results. Data
  Profile: analyzes the data in table(s) and generates insights about the
  structure, content and relationships (such as null percent, cardinality,
  min/max/mean, etc).

  Enums:
    StateValueValuesEnum: Output only. Current state of the DataScan.
    TypeValueValuesEnum: Output only. The type of DataScan.

  Messages:
    LabelsValue: Optional. User-defined labels for the scan.

  Fields:
    createTime: Output only. The time when the scan was created.
    data: Required. The data source for DataScan.
    dataProfileResult: Output only. The result of the data profile scan.
    dataProfileSpec: DataProfileScan related setting.
    dataQualityResult: Output only. The result of the data quality scan.
    dataQualitySpec: DataQualityScan related setting.
    description: Optional. Description of the scan. Must be between 1-1024
      characters.
    displayName: Optional. User friendly display name. Must be between 1-256
      characters.
    executionSpec: Optional. DataScan execution settings.If not specified, the
      fields in it will use their default values.
    executionStatus: Output only. Status of the data scan execution.
    labels: Optional. User-defined labels for the scan.
    name: Output only. The relative resource name of the scan, of the form:
      projects/{project}/locations/{location_id}/dataScans/{datascan_id},
      where project refers to a project_id or project_number and location_id
      refers to a GCP region.
    state: Output only. Current state of the DataScan.
    type: Output only. The type of DataScan.
    uid: Output only. System generated globally unique ID for the scan. This
      ID will be different if the scan is deleted and re-created with the same
      name.
    updateTime: Output only. The time when the scan was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the DataScan.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      ACTIVE: Resource is active, i.e., ready to use.
      CREATING: Resource is under creation.
      DELETING: Resource is under deletion.
      ACTION_REQUIRED: Resource is active but has unresolved actions.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    ACTION_REQUIRED = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of DataScan.

    Values:
      DATA_SCAN_TYPE_UNSPECIFIED: The DataScan type is unspecified.
      DATA_QUALITY: Data Quality scan.
      DATA_PROFILE: Data Profile scan.
    """
    DATA_SCAN_TYPE_UNSPECIFIED = 0
    DATA_QUALITY = 1
    DATA_PROFILE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels for the scan.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  data = _messages.MessageField('GoogleCloudDataplexV1DataSource', 2)
  dataProfileResult = _messages.MessageField('GoogleCloudDataplexV1DataProfileResult', 3)
  dataProfileSpec = _messages.MessageField('GoogleCloudDataplexV1DataProfileSpec', 4)
  dataQualityResult = _messages.MessageField('GoogleCloudDataplexV1DataQualityResult', 5)
  dataQualitySpec = _messages.MessageField('GoogleCloudDataplexV1DataQualitySpec', 6)
  description = _messages.StringField(7)
  displayName = _messages.StringField(8)
  executionSpec = _messages.MessageField('GoogleCloudDataplexV1DataScanExecutionSpec', 9)
  executionStatus = _messages.MessageField('GoogleCloudDataplexV1DataScanExecutionStatus', 10)
  labels = _messages.MessageField('LabelsValue', 11)
  name = _messages.StringField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  type = _messages.EnumField('TypeValueValuesEnum', 14)
  uid = _messages.StringField(15)
  updateTime = _messages.StringField(16)


class GoogleCloudDataplexV1DataScanEvent(_messages.Message):
  r"""These messages contain information about the execution of a datascan.
  The monitored resource is 'DataScan' Next ID: 13

  Enums:
    ScopeValueValuesEnum: The scope of the data scan (e.g. full, incremental).
    StateValueValuesEnum: The status of the data scan job.
    TriggerValueValuesEnum: The trigger type of the data scan job.
    TypeValueValuesEnum: The type of the data scan.

  Fields:
    createTime: The time when the data scan job was created.
    dataProfile: Data profile result for data profile type data scan.
    dataProfileConfigs: Applied configs for data profile type data scan.
    dataQuality: Data quality result for data quality type data scan.
    dataQualityConfigs: Applied configs for data quality type data scan.
    dataSource: The data source of the data scan
    endTime: The time when the data scan job finished.
    jobId: The identifier of the specific data scan job this log entry is for.
    message: The message describing the data scan job event.
    postScanActionsResult: The result of post scan actions.
    scope: The scope of the data scan (e.g. full, incremental).
    specVersion: A version identifier of the spec which was used to execute
      this job.
    startTime: The time when the data scan job started to run.
    state: The status of the data scan job.
    trigger: The trigger type of the data scan job.
    type: The type of the data scan.
  """

  class ScopeValueValuesEnum(_messages.Enum):
    r"""The scope of the data scan (e.g. full, incremental).

    Values:
      SCOPE_UNSPECIFIED: An unspecified scope type.
      FULL: Data scan runs on all of the data.
      INCREMENTAL: Data scan runs on incremental data.
    """
    SCOPE_UNSPECIFIED = 0
    FULL = 1
    INCREMENTAL = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""The status of the data scan job.

    Values:
      STATE_UNSPECIFIED: Unspecified job state.
      STARTED: Data scan job started.
      SUCCEEDED: Data scan job successfully completed.
      FAILED: Data scan job was unsuccessful.
      CANCELLED: Data scan job was cancelled.
      CREATED: Data scan job was createed.
    """
    STATE_UNSPECIFIED = 0
    STARTED = 1
    SUCCEEDED = 2
    FAILED = 3
    CANCELLED = 4
    CREATED = 5

  class TriggerValueValuesEnum(_messages.Enum):
    r"""The trigger type of the data scan job.

    Values:
      TRIGGER_UNSPECIFIED: An unspecified trigger type.
      ON_DEMAND: Data scan triggers on demand.
      SCHEDULE: Data scan triggers as per schedule.
    """
    TRIGGER_UNSPECIFIED = 0
    ON_DEMAND = 1
    SCHEDULE = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the data scan.

    Values:
      SCAN_TYPE_UNSPECIFIED: An unspecified data scan type.
      DATA_PROFILE: Data scan for data profile.
      DATA_QUALITY: Data scan for data quality.
    """
    SCAN_TYPE_UNSPECIFIED = 0
    DATA_PROFILE = 1
    DATA_QUALITY = 2

  createTime = _messages.StringField(1)
  dataProfile = _messages.MessageField('GoogleCloudDataplexV1DataScanEventDataProfileResult', 2)
  dataProfileConfigs = _messages.MessageField('GoogleCloudDataplexV1DataScanEventDataProfileAppliedConfigs', 3)
  dataQuality = _messages.MessageField('GoogleCloudDataplexV1DataScanEventDataQualityResult', 4)
  dataQualityConfigs = _messages.MessageField('GoogleCloudDataplexV1DataScanEventDataQualityAppliedConfigs', 5)
  dataSource = _messages.StringField(6)
  endTime = _messages.StringField(7)
  jobId = _messages.StringField(8)
  message = _messages.StringField(9)
  postScanActionsResult = _messages.MessageField('GoogleCloudDataplexV1DataScanEventPostScanActionsResult', 10)
  scope = _messages.EnumField('ScopeValueValuesEnum', 11)
  specVersion = _messages.StringField(12)
  startTime = _messages.StringField(13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  trigger = _messages.EnumField('TriggerValueValuesEnum', 15)
  type = _messages.EnumField('TypeValueValuesEnum', 16)


class GoogleCloudDataplexV1DataScanEventDataProfileAppliedConfigs(_messages.Message):
  r"""Applied configs for data profile type data scan job.

  Fields:
    columnFilterApplied: Boolean indicating whether a column filter was
      applied in the DataScan job.
    rowFilterApplied: Boolean indicating whether a row filter was applied in
      the DataScan job.
    samplingPercent: The percentage of the records selected from the dataset
      for DataScan. Value ranges between 0.0 and 100.0. Value 0.0 or 100.0
      imply that sampling was not applied.
  """

  columnFilterApplied = _messages.BooleanField(1)
  rowFilterApplied = _messages.BooleanField(2)
  samplingPercent = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudDataplexV1DataScanEventDataProfileResult(_messages.Message):
  r"""Data profile result for data scan job.

  Fields:
    rowCount: The count of rows processed in the data scan job.
  """

  rowCount = _messages.IntegerField(1)


class GoogleCloudDataplexV1DataScanEventDataQualityAppliedConfigs(_messages.Message):
  r"""Applied configs for data quality type data scan job.

  Fields:
    rowFilterApplied: Boolean indicating whether a row filter was applied in
      the DataScan job.
    samplingPercent: The percentage of the records selected from the dataset
      for DataScan. Value ranges between 0.0 and 100.0. Value 0.0 or 100.0
      imply that sampling was not applied.
  """

  rowFilterApplied = _messages.BooleanField(1)
  samplingPercent = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudDataplexV1DataScanEventDataQualityResult(_messages.Message):
  r"""Data quality result for data scan job.

  Messages:
    DimensionPassedValue: The result of each dimension for data quality
      result. The key of the map is the name of the dimension. The value is
      the bool value depicting whether the dimension result was pass or not.

  Fields:
    dimensionPassed: The result of each dimension for data quality result. The
      key of the map is the name of the dimension. The value is the bool value
      depicting whether the dimension result was pass or not.
    passed: Whether the data quality result was pass or not.
    rowCount: The count of rows processed in the data scan job.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DimensionPassedValue(_messages.Message):
    r"""The result of each dimension for data quality result. The key of the
    map is the name of the dimension. The value is the bool value depicting
    whether the dimension result was pass or not.

    Messages:
      AdditionalProperty: An additional property for a DimensionPassedValue
        object.

    Fields:
      additionalProperties: Additional properties of type DimensionPassedValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DimensionPassedValue object.

      Fields:
        key: Name of the additional property.
        value: A boolean attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BooleanField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  dimensionPassed = _messages.MessageField('DimensionPassedValue', 1)
  passed = _messages.BooleanField(2)
  rowCount = _messages.IntegerField(3)


class GoogleCloudDataplexV1DataScanEventPostScanActionsResult(_messages.Message):
  r"""Post scan actions result for data scan job.

  Fields:
    bigqueryExportResult: The result of BigQuery export post scan action.
  """

  bigqueryExportResult = _messages.MessageField('GoogleCloudDataplexV1DataScanEventPostScanActionsResultBigQueryExportResult', 1)


class GoogleCloudDataplexV1DataScanEventPostScanActionsResultBigQueryExportResult(_messages.Message):
  r"""The result of BigQuery export post scan action.

  Enums:
    StateValueValuesEnum: Execution state for the BigQuery exporting.

  Fields:
    message: Additional information about the BigQuery exporting.
    state: Execution state for the BigQuery exporting.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Execution state for the BigQuery exporting.

    Values:
      STATE_UNSPECIFIED: The exporting state is unspecified.
      SUCCEEDED: The exporting completed successfully.
      FAILED: The exporting is no longer running due to an error.
      SKIPPED: The exporting is skipped due to no valid scan result to export
        (usually caused by scan failed).
    """
    STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    SKIPPED = 3

  message = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudDataplexV1DataScanExecutionSpec(_messages.Message):
  r"""DataScan execution settings.

  Fields:
    field: Immutable. The unnested field (of type Date or Timestamp) that
      contains values which monotonically increase over time.If not specified,
      a data scan will run for all data in the table.
    trigger: Optional. Spec related to how often and when a scan should be
      triggered.If not specified, the default is OnDemand, which means the
      scan will not run until the user calls RunDataScan API.
  """

  field = _messages.StringField(1)
  trigger = _messages.MessageField('GoogleCloudDataplexV1Trigger', 2)


class GoogleCloudDataplexV1DataScanExecutionStatus(_messages.Message):
  r"""Status of the data scan execution.

  Fields:
    latestJobEndTime: The time when the latest DataScanJob ended.
    latestJobStartTime: The time when the latest DataScanJob started.
  """

  latestJobEndTime = _messages.StringField(1)
  latestJobStartTime = _messages.StringField(2)


class GoogleCloudDataplexV1DataScanJob(_messages.Message):
  r"""A DataScanJob represents an instance of DataScan execution.

  Enums:
    StateValueValuesEnum: Output only. Execution state for the DataScanJob.
    TypeValueValuesEnum: Output only. The type of the parent DataScan.

  Fields:
    dataProfileResult: Output only. The result of the data profile scan.
    dataProfileSpec: Output only. DataProfileScan related setting.
    dataQualityResult: Output only. The result of the data quality scan.
    dataQualitySpec: Output only. DataQualityScan related setting.
    endTime: Output only. The time when the DataScanJob ended.
    message: Output only. Additional information about the current state.
    name: Output only. The relative resource name of the DataScanJob, of the
      form: projects/{project}/locations/{location_id}/dataScans/{datascan_id}
      /jobs/{job_id}, where project refers to a project_id or project_number
      and location_id refers to a GCP region.
    startTime: Output only. The time when the DataScanJob was started.
    state: Output only. Execution state for the DataScanJob.
    type: Output only. The type of the parent DataScan.
    uid: Output only. System generated globally unique ID for the DataScanJob.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Execution state for the DataScanJob.

    Values:
      STATE_UNSPECIFIED: The DataScanJob state is unspecified.
      RUNNING: The DataScanJob is running.
      CANCELING: The DataScanJob is canceling.
      CANCELLED: The DataScanJob cancellation was successful.
      SUCCEEDED: The DataScanJob completed successfully.
      FAILED: The DataScanJob is no longer running due to an error.
      PENDING: The DataScanJob has been created but not started to run yet.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    CANCELING = 2
    CANCELLED = 3
    SUCCEEDED = 4
    FAILED = 5
    PENDING = 6

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the parent DataScan.

    Values:
      DATA_SCAN_TYPE_UNSPECIFIED: The DataScan type is unspecified.
      DATA_QUALITY: Data Quality scan.
      DATA_PROFILE: Data Profile scan.
    """
    DATA_SCAN_TYPE_UNSPECIFIED = 0
    DATA_QUALITY = 1
    DATA_PROFILE = 2

  dataProfileResult = _messages.MessageField('GoogleCloudDataplexV1DataProfileResult', 1)
  dataProfileSpec = _messages.MessageField('GoogleCloudDataplexV1DataProfileSpec', 2)
  dataQualityResult = _messages.MessageField('GoogleCloudDataplexV1DataQualityResult', 3)
  dataQualitySpec = _messages.MessageField('GoogleCloudDataplexV1DataQualitySpec', 4)
  endTime = _messages.StringField(5)
  message = _messages.StringField(6)
  name = _messages.StringField(7)
  startTime = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  type = _messages.EnumField('TypeValueValuesEnum', 10)
  uid = _messages.StringField(11)


class GoogleCloudDataplexV1DataSource(_messages.Message):
  r"""The data source for DataScan.

  Fields:
    entity: Immutable. The Dataplex entity that represents the data source
      (e.g. BigQuery table) for DataScan, of the form: projects/{project_numbe
      r}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/entities/{ent
      ity_id}.
    resource: Immutable. The service-qualified full resource name of the cloud
      resource for a DataScan job to scan against. The field could be:
      BigQuery table of type "TABLE" for DataProfileScan/DataQualityScan
      Format: //bigquery.googleapis.com/projects/PROJECT_ID/datasets/DATASET_I
      D/tables/TABLE_ID
  """

  entity = _messages.StringField(1)
  resource = _messages.StringField(2)


class GoogleCloudDataplexV1DataTaxonomy(_messages.Message):
  r"""DataTaxonomy represents a set of hierarchical DataAttributes resources,
  grouped with a common theme Eg: 'SensitiveDataTaxonomy' can have attributes
  to manage PII data. It is defined at project level.

  Messages:
    LabelsValue: Optional. User-defined labels for the DataTaxonomy.

  Fields:
    attributeCount: Output only. The number of attributes in the DataTaxonomy.
    classCount: Output only. The number of classes in the DataTaxonomy.
    createTime: Output only. The time when the DataTaxonomy was created.
    description: Optional. Description of the DataTaxonomy.
    displayName: Optional. User friendly display name.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    labels: Optional. User-defined labels for the DataTaxonomy.
    name: Output only. The relative resource name of the DataTaxonomy, of the
      form: projects/{project_number}/locations/{location_id}/dataTaxonomies/{
      data_taxonomy_id}.
    uid: Output only. System generated globally unique ID for the
      dataTaxonomy. This ID will be different if the DataTaxonomy is deleted
      and re-created with the same name.
    updateTime: Output only. The time when the DataTaxonomy was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels for the DataTaxonomy.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  classCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class GoogleCloudDataplexV1DiscoveryEvent(_messages.Message):
  r"""The payload associated with Discovery data processing.

  Enums:
    TypeValueValuesEnum: The type of the event being logged.

  Fields:
    action: Details about the action associated with the event.
    assetId: The id of the associated asset.
    config: Details about discovery configuration in effect.
    dataLocation: The data location associated with the event.
    entity: Details about the entity associated with the event.
    lakeId: The id of the associated lake.
    message: The log message.
    partition: Details about the partition associated with the event.
    type: The type of the event being logged.
    zoneId: The id of the associated zone.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the event being logged.

    Values:
      EVENT_TYPE_UNSPECIFIED: An unspecified event type.
      CONFIG: An event representing discovery configuration in effect.
      ENTITY_CREATED: An event representing a metadata entity being created.
      ENTITY_UPDATED: An event representing a metadata entity being updated.
      ENTITY_DELETED: An event representing a metadata entity being deleted.
      PARTITION_CREATED: An event representing a partition being created.
      PARTITION_UPDATED: An event representing a partition being updated.
      PARTITION_DELETED: An event representing a partition being deleted.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    CONFIG = 1
    ENTITY_CREATED = 2
    ENTITY_UPDATED = 3
    ENTITY_DELETED = 4
    PARTITION_CREATED = 5
    PARTITION_UPDATED = 6
    PARTITION_DELETED = 7

  action = _messages.MessageField('GoogleCloudDataplexV1DiscoveryEventActionDetails', 1)
  assetId = _messages.StringField(2)
  config = _messages.MessageField('GoogleCloudDataplexV1DiscoveryEventConfigDetails', 3)
  dataLocation = _messages.StringField(4)
  entity = _messages.MessageField('GoogleCloudDataplexV1DiscoveryEventEntityDetails', 5)
  lakeId = _messages.StringField(6)
  message = _messages.StringField(7)
  partition = _messages.MessageField('GoogleCloudDataplexV1DiscoveryEventPartitionDetails', 8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)
  zoneId = _messages.StringField(10)


class GoogleCloudDataplexV1DiscoveryEventActionDetails(_messages.Message):
  r"""Details about the action.

  Fields:
    type: The type of action. Eg. IncompatibleDataSchema, InvalidDataFormat
  """

  type = _messages.StringField(1)


class GoogleCloudDataplexV1DiscoveryEventConfigDetails(_messages.Message):
  r"""Details about configuration events.

  Messages:
    ParametersValue: A list of discovery configuration parameters in effect.
      The keys are the field paths within DiscoverySpec. Eg. includePatterns,
      excludePatterns, csvOptions.disableTypeInference, etc.

  Fields:
    parameters: A list of discovery configuration parameters in effect. The
      keys are the field paths within DiscoverySpec. Eg. includePatterns,
      excludePatterns, csvOptions.disableTypeInference, etc.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""A list of discovery configuration parameters in effect. The keys are
    the field paths within DiscoverySpec. Eg. includePatterns,
    excludePatterns, csvOptions.disableTypeInference, etc.

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Additional properties of type ParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  parameters = _messages.MessageField('ParametersValue', 1)


class GoogleCloudDataplexV1DiscoveryEventEntityDetails(_messages.Message):
  r"""Details about the entity.

  Enums:
    TypeValueValuesEnum: The type of the entity resource.

  Fields:
    entity: The name of the entity resource. The name is the fully-qualified
      resource name.
    type: The type of the entity resource.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the entity resource.

    Values:
      ENTITY_TYPE_UNSPECIFIED: An unspecified event type.
      TABLE: Entities representing structured data.
      FILESET: Entities representing unstructured data.
    """
    ENTITY_TYPE_UNSPECIFIED = 0
    TABLE = 1
    FILESET = 2

  entity = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudDataplexV1DiscoveryEventPartitionDetails(_messages.Message):
  r"""Details about the partition.

  Enums:
    TypeValueValuesEnum: The type of the containing entity resource.

  Fields:
    entity: The name to the containing entity resource. The name is the fully-
      qualified resource name.
    partition: The name to the partition resource. The name is the fully-
      qualified resource name.
    sampledDataLocations: The locations of the data items (e.g., a Cloud
      Storage objects) sampled for metadata inference.
    type: The type of the containing entity resource.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the containing entity resource.

    Values:
      ENTITY_TYPE_UNSPECIFIED: An unspecified event type.
      TABLE: Entities representing structured data.
      FILESET: Entities representing unstructured data.
    """
    ENTITY_TYPE_UNSPECIFIED = 0
    TABLE = 1
    FILESET = 2

  entity = _messages.StringField(1)
  partition = _messages.StringField(2)
  sampledDataLocations = _messages.StringField(3, repeated=True)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class GoogleCloudDataplexV1Entity(_messages.Message):
  r"""Represents tables and fileset metadata contained within a zone.

  Enums:
    SystemValueValuesEnum: Required. Immutable. Identifies the storage system
      of the entity data.
    TypeValueValuesEnum: Required. Immutable. The type of entity.

  Fields:
    access: Output only. Identifies the access mechanism to the entity. Not
      user settable.
    asset: Required. Immutable. The ID of the asset associated with the
      storage location containing the entity data. The entity must be with in
      the same zone with the asset.
    catalogEntry: Output only. The name of the associated Data Catalog entry.
    compatibility: Output only. Metadata stores that the entity is compatible
      with.
    createTime: Output only. The time when the entity was created.
    dataPath: Required. Immutable. The storage path of the entity data. For
      Cloud Storage data, this is the fully-qualified path to the entity, such
      as gs://bucket/path/to/data. For BigQuery data, this is the name of the
      table resource, such as
      projects/project_id/datasets/dataset_id/tables/table_id.
    dataPathPattern: Optional. The set of items within the data path
      constituting the data in the entity, represented as a glob path.
      Example: gs://bucket/path/to/data/**/*.csv.
    description: Optional. User friendly longer description text. Must be
      shorter than or equal to 1024 characters.
    displayName: Optional. Display name must be shorter than or equal to 256
      characters.
    etag: Optional. The etag associated with the entity, which can be
      retrieved with a GetEntity request. Required for update and delete
      requests.
    format: Required. Identifies the storage format of the entity data. It
      does not apply to entities with data stored in BigQuery.
    id: Required. A user-provided entity ID. It is mutable, and will be used
      as the published table name. Specifying a new ID in an update entity
      request will override the existing value. The ID must contain only
      letters (a-z, A-Z), numbers (0-9), and underscores, and consist of 256
      or fewer characters.
    name: Output only. The resource name of the entity, of the form: projects/
      {project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}
      /entities/{id}.
    schema: Required. The description of the data structure and layout. The
      schema is not included in list responses. It is only included in SCHEMA
      and FULL entity views of a GetEntity response.
    system: Required. Immutable. Identifies the storage system of the entity
      data.
    type: Required. Immutable. The type of entity.
    uid: Output only. System generated unique ID for the Entity. This ID will
      be different if the Entity is deleted and re-created with the same name.
    updateTime: Output only. The time when the entity was last updated.
  """

  class SystemValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. Identifies the storage system of the entity data.

    Values:
      STORAGE_SYSTEM_UNSPECIFIED: Storage system unspecified.
      CLOUD_STORAGE: The entity data is contained within a Cloud Storage
        bucket.
      BIGQUERY: The entity data is contained within a BigQuery dataset.
    """
    STORAGE_SYSTEM_UNSPECIFIED = 0
    CLOUD_STORAGE = 1
    BIGQUERY = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The type of entity.

    Values:
      TYPE_UNSPECIFIED: Type unspecified.
      TABLE: Structured and semi-structured data.
      FILESET: Unstructured data.
    """
    TYPE_UNSPECIFIED = 0
    TABLE = 1
    FILESET = 2

  access = _messages.MessageField('GoogleCloudDataplexV1StorageAccess', 1)
  asset = _messages.StringField(2)
  catalogEntry = _messages.StringField(3)
  compatibility = _messages.MessageField('GoogleCloudDataplexV1EntityCompatibilityStatus', 4)
  createTime = _messages.StringField(5)
  dataPath = _messages.StringField(6)
  dataPathPattern = _messages.StringField(7)
  description = _messages.StringField(8)
  displayName = _messages.StringField(9)
  etag = _messages.StringField(10)
  format = _messages.MessageField('GoogleCloudDataplexV1StorageFormat', 11)
  id = _messages.StringField(12)
  name = _messages.StringField(13)
  schema = _messages.MessageField('GoogleCloudDataplexV1Schema', 14)
  system = _messages.EnumField('SystemValueValuesEnum', 15)
  type = _messages.EnumField('TypeValueValuesEnum', 16)
  uid = _messages.StringField(17)
  updateTime = _messages.StringField(18)


class GoogleCloudDataplexV1EntityCompatibilityStatus(_messages.Message):
  r"""Provides compatibility information for various metadata stores.

  Fields:
    bigquery: Output only. Whether this entity is compatible with BigQuery.
    hiveMetastore: Output only. Whether this entity is compatible with Hive
      Metastore.
  """

  bigquery = _messages.MessageField('GoogleCloudDataplexV1EntityCompatibilityStatusCompatibility', 1)
  hiveMetastore = _messages.MessageField('GoogleCloudDataplexV1EntityCompatibilityStatusCompatibility', 2)


class GoogleCloudDataplexV1EntityCompatibilityStatusCompatibility(_messages.Message):
  r"""Provides compatibility information for a specific metadata store.

  Fields:
    compatible: Output only. Whether the entity is compatible and can be
      represented in the metadata store.
    reason: Output only. Provides additional detail if the entity is
      incompatible with the metadata store.
  """

  compatible = _messages.BooleanField(1)
  reason = _messages.StringField(2)


class GoogleCloudDataplexV1Environment(_messages.Message):
  r"""Environment represents a user-visible compute infrastructure for
  analytics within a lake.

  Enums:
    StateValueValuesEnum: Output only. Current state of the environment.

  Messages:
    LabelsValue: Optional. User defined labels for the environment.

  Fields:
    createTime: Output only. Environment creation time.
    description: Optional. Description of the environment.
    displayName: Optional. User friendly display name.
    endpoints: Output only. URI Endpoints to access sessions associated with
      the Environment.
    infrastructureSpec: Required. Infrastructure specification for the
      Environment.
    labels: Optional. User defined labels for the environment.
    name: Output only. The relative resource name of the environment, of the
      form: projects/{project_id}/locations/{location_id}/lakes/{lake_id}/envi
      ronment/{environment_id}
    sessionSpec: Optional. Configuration for sessions created for this
      environment.
    sessionStatus: Output only. Status of sessions created for this
      environment.
    state: Output only. Current state of the environment.
    uid: Output only. System generated globally unique ID for the environment.
      This ID will be different if the environment is deleted and re-created
      with the same name.
    updateTime: Output only. The time when the environment was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the environment.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      ACTIVE: Resource is active, i.e., ready to use.
      CREATING: Resource is under creation.
      DELETING: Resource is under deletion.
      ACTION_REQUIRED: Resource is active but has unresolved actions.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    ACTION_REQUIRED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User defined labels for the environment.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  endpoints = _messages.MessageField('GoogleCloudDataplexV1EnvironmentEndpoints', 4)
  infrastructureSpec = _messages.MessageField('GoogleCloudDataplexV1EnvironmentInfrastructureSpec', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  sessionSpec = _messages.MessageField('GoogleCloudDataplexV1EnvironmentSessionSpec', 8)
  sessionStatus = _messages.MessageField('GoogleCloudDataplexV1EnvironmentSessionStatus', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleCloudDataplexV1EnvironmentEndpoints(_messages.Message):
  r"""URI Endpoints to access sessions associated with the Environment.

  Fields:
    notebooks: Output only. URI to serve notebook APIs
    sql: Output only. URI to serve SQL APIs
  """

  notebooks = _messages.StringField(1)
  sql = _messages.StringField(2)


class GoogleCloudDataplexV1EnvironmentInfrastructureSpec(_messages.Message):
  r"""Configuration for the underlying infrastructure used to run workloads.

  Fields:
    compute: Optional. Compute resources needed for analyze interactive
      workloads.
    osImage: Required. Software Runtime Configuration for analyze interactive
      workloads.
  """

  compute = _messages.MessageField('GoogleCloudDataplexV1EnvironmentInfrastructureSpecComputeResources', 1)
  osImage = _messages.MessageField('GoogleCloudDataplexV1EnvironmentInfrastructureSpecOsImageRuntime', 2)


class GoogleCloudDataplexV1EnvironmentInfrastructureSpecComputeResources(_messages.Message):
  r"""Compute resources associated with the analyze interactive workloads.

  Fields:
    diskSizeGb: Optional. Size in GB of the disk. Default is 100 GB.
    maxNodeCount: Optional. Max configurable nodes. If max_node_count >
      node_count, then auto-scaling is enabled.
    nodeCount: Optional. Total number of nodes in the sessions created for
      this environment.
  """

  diskSizeGb = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  maxNodeCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nodeCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudDataplexV1EnvironmentInfrastructureSpecOsImageRuntime(_messages.Message):
  r"""Software Runtime Configuration to run Analyze.

  Messages:
    PropertiesValue: Optional. Spark properties to provide configuration for
      use in sessions created for this environment. The properties to set on
      daemon config files. Property keys are specified in prefix:property
      format. The prefix must be "spark".

  Fields:
    imageVersion: Required. Dataplex Image version.
    javaLibraries: Optional. List of Java jars to be included in the runtime
      environment. Valid input includes Cloud Storage URIs to Jar binaries.
      For example, gs://bucket-name/my/path/to/file.jar
    properties: Optional. Spark properties to provide configuration for use in
      sessions created for this environment. The properties to set on daemon
      config files. Property keys are specified in prefix:property format. The
      prefix must be "spark".
    pythonPackages: Optional. A list of python packages to be installed. Valid
      formats include Cloud Storage URI to a PIP installable library. For
      example, gs://bucket-name/my/path/to/lib.tar.gz
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""Optional. Spark properties to provide configuration for use in
    sessions created for this environment. The properties to set on daemon
    config files. Property keys are specified in prefix:property format. The
    prefix must be "spark".

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  imageVersion = _messages.StringField(1)
  javaLibraries = _messages.StringField(2, repeated=True)
  properties = _messages.MessageField('PropertiesValue', 3)
  pythonPackages = _messages.StringField(4, repeated=True)


class GoogleCloudDataplexV1EnvironmentSessionSpec(_messages.Message):
  r"""Configuration for sessions created for this environment.

  Fields:
    enableFastStartup: Optional. If True, this causes sessions to be pre-
      created and available for faster startup to enable interactive
      exploration use-cases. This defaults to False to avoid additional billed
      charges. These can only be set to True for the environment with name set
      to "default", and with default configuration.
    maxIdleDuration: Optional. The idle time configuration of the session. The
      session will be auto-terminated at the end of this period.
  """

  enableFastStartup = _messages.BooleanField(1)
  maxIdleDuration = _messages.StringField(2)


class GoogleCloudDataplexV1EnvironmentSessionStatus(_messages.Message):
  r"""Status of sessions created for this environment.

  Fields:
    active: Output only. Queries over sessions to mark whether the environment
      is currently active or not
  """

  active = _messages.BooleanField(1)


class GoogleCloudDataplexV1Job(_messages.Message):
  r"""A job represents an instance of a task.

  Enums:
    ServiceValueValuesEnum: Output only. The underlying service running a job.
    StateValueValuesEnum: Output only. Execution state for the job.
    TriggerValueValuesEnum: Output only. Job execution trigger.

  Messages:
    LabelsValue: Output only. User-defined labels for the task.

  Fields:
    endTime: Output only. The time when the job ended.
    executionSpec: Output only. Spec related to how a task is executed.
    labels: Output only. User-defined labels for the task.
    message: Output only. Additional information about the current state.
    name: Output only. The relative resource name of the job, of the form: pro
      jects/{project_number}/locations/{location_id}/lakes/{lake_id}/tasks/{ta
      sk_id}/jobs/{job_id}.
    retryCount: Output only. The number of times the job has been retried
      (excluding the initial attempt).
    service: Output only. The underlying service running a job.
    serviceJob: Output only. The full resource name for the job run under a
      particular service.
    startTime: Output only. The time when the job was started.
    state: Output only. Execution state for the job.
    trigger: Output only. Job execution trigger.
    uid: Output only. System generated globally unique ID for the job.
  """

  class ServiceValueValuesEnum(_messages.Enum):
    r"""Output only. The underlying service running a job.

    Values:
      SERVICE_UNSPECIFIED: Service used to run the job is unspecified.
      DATAPROC: Dataproc service is used to run this job.
    """
    SERVICE_UNSPECIFIED = 0
    DATAPROC = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Execution state for the job.

    Values:
      STATE_UNSPECIFIED: The job state is unknown.
      RUNNING: The job is running.
      CANCELLING: The job is cancelling.
      CANCELLED: The job cancellation was successful.
      SUCCEEDED: The job completed successfully.
      FAILED: The job is no longer running due to an error.
      ABORTED: The job was cancelled outside of Dataplex.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    CANCELLING = 2
    CANCELLED = 3
    SUCCEEDED = 4
    FAILED = 5
    ABORTED = 6

  class TriggerValueValuesEnum(_messages.Enum):
    r"""Output only. Job execution trigger.

    Values:
      TRIGGER_UNSPECIFIED: The trigger is unspecified.
      TASK_CONFIG: The job was triggered by Dataplex based on trigger spec
        from task definition.
      RUN_REQUEST: The job was triggered by the explicit call of Task API.
    """
    TRIGGER_UNSPECIFIED = 0
    TASK_CONFIG = 1
    RUN_REQUEST = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Output only. User-defined labels for the task.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  endTime = _messages.StringField(1)
  executionSpec = _messages.MessageField('GoogleCloudDataplexV1TaskExecutionSpec', 2)
  labels = _messages.MessageField('LabelsValue', 3)
  message = _messages.StringField(4)
  name = _messages.StringField(5)
  retryCount = _messages.IntegerField(6, variant=_messages.Variant.UINT32)
  service = _messages.EnumField('ServiceValueValuesEnum', 7)
  serviceJob = _messages.StringField(8)
  startTime = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  trigger = _messages.EnumField('TriggerValueValuesEnum', 11)
  uid = _messages.StringField(12)


class GoogleCloudDataplexV1JobEvent(_messages.Message):
  r"""The payload associated with Job logs that contains events describing
  jobs that have run within a Lake.

  Enums:
    ExecutionTriggerValueValuesEnum: Job execution trigger.
    ServiceValueValuesEnum: The service used to execute the job.
    StateValueValuesEnum: The job state on completion.
    TypeValueValuesEnum: The type of the job.

  Fields:
    endTime: The time when the job ended running.
    executionTrigger: Job execution trigger.
    jobId: The unique id identifying the job.
    message: The log message.
    retries: The number of retries.
    service: The service used to execute the job.
    serviceJob: The reference to the job within the service.
    startTime: The time when the job started running.
    state: The job state on completion.
    type: The type of the job.
  """

  class ExecutionTriggerValueValuesEnum(_messages.Enum):
    r"""Job execution trigger.

    Values:
      EXECUTION_TRIGGER_UNSPECIFIED: The job execution trigger is unspecified.
      TASK_CONFIG: The job was triggered by Dataplex based on trigger spec
        from task definition.
      RUN_REQUEST: The job was triggered by the explicit call of Task API.
    """
    EXECUTION_TRIGGER_UNSPECIFIED = 0
    TASK_CONFIG = 1
    RUN_REQUEST = 2

  class ServiceValueValuesEnum(_messages.Enum):
    r"""The service used to execute the job.

    Values:
      SERVICE_UNSPECIFIED: Unspecified service.
      DATAPROC: Cloud Dataproc.
    """
    SERVICE_UNSPECIFIED = 0
    DATAPROC = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""The job state on completion.

    Values:
      STATE_UNSPECIFIED: Unspecified job state.
      SUCCEEDED: Job successfully completed.
      FAILED: Job was unsuccessful.
      CANCELLED: Job was cancelled by the user.
      ABORTED: Job was cancelled or aborted via the service executing the job.
    """
    STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    CANCELLED = 3
    ABORTED = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the job.

    Values:
      TYPE_UNSPECIFIED: Unspecified job type.
      SPARK: Spark jobs.
      NOTEBOOK: Notebook jobs.
    """
    TYPE_UNSPECIFIED = 0
    SPARK = 1
    NOTEBOOK = 2

  endTime = _messages.StringField(1)
  executionTrigger = _messages.EnumField('ExecutionTriggerValueValuesEnum', 2)
  jobId = _messages.StringField(3)
  message = _messages.StringField(4)
  retries = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  service = _messages.EnumField('ServiceValueValuesEnum', 6)
  serviceJob = _messages.StringField(7)
  startTime = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  type = _messages.EnumField('TypeValueValuesEnum', 10)


class GoogleCloudDataplexV1Lake(_messages.Message):
  r"""A lake is a centralized repository for managing enterprise data across
  the organization distributed across many cloud projects, and stored in a
  variety of storage services such as Google Cloud Storage and BigQuery. The
  resources attached to a lake are referred to as managed resources. Data
  within these managed resources can be structured or unstructured. A lake
  provides data admins with tools to organize, secure and manage their data at
  scale, and provides data scientists and data engineers an integrated
  experience to easily search, discover, analyze and transform data and
  associated metadata.

  Enums:
    StateValueValuesEnum: Output only. Current state of the lake.

  Messages:
    LabelsValue: Optional. User-defined labels for the lake.

  Fields:
    assetStatus: Output only. Aggregated status of the underlying assets of
      the lake.
    createTime: Output only. The time when the lake was created.
    description: Optional. Description of the lake.
    displayName: Optional. User friendly display name.
    labels: Optional. User-defined labels for the lake.
    metastore: Optional. Settings to manage lake and Dataproc Metastore
      service instance association.
    metastoreStatus: Output only. Metastore status of the lake.
    name: Output only. The relative resource name of the lake, of the form:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}.
    serviceAccount: Output only. Service account associated with this lake.
      This service account must be authorized to access or operate on
      resources managed by the lake.
    state: Output only. Current state of the lake.
    uid: Output only. System generated globally unique ID for the lake. This
      ID will be different if the lake is deleted and re-created with the same
      name.
    updateTime: Output only. The time when the lake was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the lake.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      ACTIVE: Resource is active, i.e., ready to use.
      CREATING: Resource is under creation.
      DELETING: Resource is under deletion.
      ACTION_REQUIRED: Resource is active but has unresolved actions.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    ACTION_REQUIRED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels for the lake.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetStatus = _messages.MessageField('GoogleCloudDataplexV1AssetStatus', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  metastore = _messages.MessageField('GoogleCloudDataplexV1LakeMetastore', 6)
  metastoreStatus = _messages.MessageField('GoogleCloudDataplexV1LakeMetastoreStatus', 7)
  name = _messages.StringField(8)
  serviceAccount = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleCloudDataplexV1LakeMetastore(_messages.Message):
  r"""Settings to manage association of Dataproc Metastore with a lake.

  Fields:
    service: Optional. A relative reference to the Dataproc Metastore
      (https://cloud.google.com/dataproc-metastore/docs) service associated
      with the lake:
      projects/{project_id}/locations/{location_id}/services/{service_id}
  """

  service = _messages.StringField(1)


class GoogleCloudDataplexV1LakeMetastoreStatus(_messages.Message):
  r"""Status of Lake and Dataproc Metastore service instance association.

  Enums:
    StateValueValuesEnum: Current state of association.

  Fields:
    endpoint: The URI of the endpoint used to access the Metastore service.
    message: Additional information about the current status.
    state: Current state of association.
    updateTime: Last update time of the metastore status of the lake.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of association.

    Values:
      STATE_UNSPECIFIED: Unspecified.
      NONE: A Metastore service instance is not associated with the lake.
      READY: A Metastore service instance is attached to the lake.
      UPDATING: Attach/detach is in progress.
      ERROR: Attach/detach could not be done due to errors.
    """
    STATE_UNSPECIFIED = 0
    NONE = 1
    READY = 2
    UPDATING = 3
    ERROR = 4

  endpoint = _messages.StringField(1)
  message = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class GoogleCloudDataplexV1ListActionsResponse(_messages.Message):
  r"""List actions response.

  Fields:
    actions: Actions under the given parent lake/zone/asset.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  actions = _messages.MessageField('GoogleCloudDataplexV1Action', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDataplexV1ListAssetsResponse(_messages.Message):
  r"""List assets response.

  Fields:
    assets: Asset under the given parent zone.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  assets = _messages.MessageField('GoogleCloudDataplexV1Asset', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDataplexV1ListContentResponse(_messages.Message):
  r"""List content response.

  Fields:
    content: Content under the given parent lake.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  content = _messages.MessageField('GoogleCloudDataplexV1Content', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDataplexV1ListDataAttributeBindingsResponse(_messages.Message):
  r"""List DataAttributeBindings response.

  Fields:
    dataAttributeBindings: DataAttributeBindings under the given parent
      Location.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachableLocations: Locations that could not be reached.
  """

  dataAttributeBindings = _messages.MessageField('GoogleCloudDataplexV1DataAttributeBinding', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachableLocations = _messages.StringField(3, repeated=True)


class GoogleCloudDataplexV1ListDataAttributesResponse(_messages.Message):
  r"""List DataAttributes response.

  Fields:
    dataAttributes: DataAttributes under the given parent DataTaxonomy.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachableLocations: Locations that could not be reached.
  """

  dataAttributes = _messages.MessageField('GoogleCloudDataplexV1DataAttribute', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachableLocations = _messages.StringField(3, repeated=True)


class GoogleCloudDataplexV1ListDataScanJobsResponse(_messages.Message):
  r"""List DataScanJobs response.

  Fields:
    dataScanJobs: DataScanJobs (BASIC view only) under a given dataScan.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  dataScanJobs = _messages.MessageField('GoogleCloudDataplexV1DataScanJob', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDataplexV1ListDataScansResponse(_messages.Message):
  r"""List dataScans response.

  Fields:
    dataScans: DataScans (BASIC view only) under the given parent location.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  dataScans = _messages.MessageField('GoogleCloudDataplexV1DataScan', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudDataplexV1ListDataTaxonomiesResponse(_messages.Message):
  r"""List DataTaxonomies response.

  Fields:
    dataTaxonomies: DataTaxonomies under the given parent location.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachableLocations: Locations that could not be reached.
  """

  dataTaxonomies = _messages.MessageField('GoogleCloudDataplexV1DataTaxonomy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachableLocations = _messages.StringField(3, repeated=True)


class GoogleCloudDataplexV1ListEntitiesResponse(_messages.Message):
  r"""List metadata entities response.

  Fields:
    entities: Entities in the specified parent zone.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no remaining results in the list.
  """

  entities = _messages.MessageField('GoogleCloudDataplexV1Entity', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDataplexV1ListEnvironmentsResponse(_messages.Message):
  r"""List environments response.

  Fields:
    environments: Environments under the given parent lake.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  environments = _messages.MessageField('GoogleCloudDataplexV1Environment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDataplexV1ListJobsResponse(_messages.Message):
  r"""List jobs response.

  Fields:
    jobs: Jobs under a given task.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  jobs = _messages.MessageField('GoogleCloudDataplexV1Job', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDataplexV1ListLakesResponse(_messages.Message):
  r"""List lakes response.

  Fields:
    lakes: Lakes under the given parent location.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachableLocations: Locations that could not be reached.
  """

  lakes = _messages.MessageField('GoogleCloudDataplexV1Lake', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachableLocations = _messages.StringField(3, repeated=True)


class GoogleCloudDataplexV1ListPartitionsResponse(_messages.Message):
  r"""List metadata partitions response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no remaining results in the list.
    partitions: Partitions under the specified parent entity.
  """

  nextPageToken = _messages.StringField(1)
  partitions = _messages.MessageField('GoogleCloudDataplexV1Partition', 2, repeated=True)


class GoogleCloudDataplexV1ListSessionsResponse(_messages.Message):
  r"""List sessions response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    sessions: Sessions under a given environment.
  """

  nextPageToken = _messages.StringField(1)
  sessions = _messages.MessageField('GoogleCloudDataplexV1Session', 2, repeated=True)


class GoogleCloudDataplexV1ListTasksResponse(_messages.Message):
  r"""List tasks response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    tasks: Tasks under the given parent lake.
    unreachableLocations: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  tasks = _messages.MessageField('GoogleCloudDataplexV1Task', 2, repeated=True)
  unreachableLocations = _messages.StringField(3, repeated=True)


class GoogleCloudDataplexV1ListZonesResponse(_messages.Message):
  r"""List zones response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    zones: Zones under the given parent lake.
  """

  nextPageToken = _messages.StringField(1)
  zones = _messages.MessageField('GoogleCloudDataplexV1Zone', 2, repeated=True)


class GoogleCloudDataplexV1ManagedEntry(_messages.Message):
  r"""Resource config defines the properties of a variety of other resources.

  Fields:
    bucket: Google Cloud Storage bucket ID will be used as the key to access
      the config. The bucket must be exist.
    name: Output only. Name of the resource to which the config is applied. pr
      ojects/{project_number}/locations/{location_id}/managedEntries/{resource
      _name}.
  """

  bucket = _messages.MessageField('GoogleCloudDataplexV1StorageBucket', 1)
  name = _messages.StringField(2)


class GoogleCloudDataplexV1OperationMetadata(_messages.Message):
  r"""Represents the metadata of a long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to Code.CANCELLED.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudDataplexV1Partition(_messages.Message):
  r"""Represents partition metadata contained within entity instances.

  Fields:
    etag: Optional. The etag for this partition.
    location: Required. Immutable. The location of the entity data within the
      partition, for example,
      gs://bucket/path/to/entity/key1=value1/key2=value2. Or
      projects//datasets//tables/
    name: Output only. Partition values used in the HTTP URL must be double
      encoded. For example, url_encode(url_encode(value)) can be used to
      encode "US:CA/CA#Sunnyvale so that the request URL ends with
      "/partitions/US%253ACA/CA%2523Sunnyvale". The name field in the response
      retains the encoded format.
    values: Required. Immutable. The set of values representing the partition,
      which correspond to the partition schema defined in the parent entity.
  """

  etag = _messages.StringField(1)
  location = _messages.StringField(2)
  name = _messages.StringField(3)
  values = _messages.StringField(4, repeated=True)


class GoogleCloudDataplexV1ResourceAccessSpec(_messages.Message):
  r"""ResourceAccessSpec holds the access control configuration to be enforced
  on the resources, for example, Cloud Storage bucket, BigQuery dataset,
  BigQuery table.

  Fields:
    owners: Optional. The set of principals to be granted owner role on the
      resource.
    readers: Optional. The format of strings follows the pattern followed by
      IAM in the bindings. user:{email}, serviceAccount:{email} group:{email}.
      The set of principals to be granted reader role on the resource.
    writers: Optional. The set of principals to be granted writer role on the
      resource.
  """

  owners = _messages.StringField(1, repeated=True)
  readers = _messages.StringField(2, repeated=True)
  writers = _messages.StringField(3, repeated=True)


class GoogleCloudDataplexV1RunDataScanRequest(_messages.Message):
  r"""Run DataScan Request"""


class GoogleCloudDataplexV1RunDataScanResponse(_messages.Message):
  r"""Run DataScan Response.

  Fields:
    job: DataScanJob created by RunDataScan request.
  """

  job = _messages.MessageField('GoogleCloudDataplexV1DataScanJob', 1)


class GoogleCloudDataplexV1RunTaskRequest(_messages.Message):
  r"""A GoogleCloudDataplexV1RunTaskRequest object.

  Messages:
    ArgsValue: Optional. Execution spec arguments. If the map is left empty,
      the task will run with existing execution spec args from task
      definition. If the map contains an entry with a new key, the same will
      be added to existing set of args. If the map contains an entry with an
      existing arg key in task definition, the task will run with new arg
      value for that entry. Clearing an existing arg will require arg value to
      be explicitly set to a hyphen "-". The arg value cannot be empty.
    LabelsValue: Optional. User-defined labels for the task. If the map is
      left empty, the task will run with existing labels from task definition.
      If the map contains an entry with a new key, the same will be added to
      existing set of labels. If the map contains an entry with an existing
      label key in task definition, the task will run with new label value for
      that entry. Clearing an existing label will require label value to be
      explicitly set to a hyphen "-". The label value cannot be empty.

  Fields:
    args: Optional. Execution spec arguments. If the map is left empty, the
      task will run with existing execution spec args from task definition. If
      the map contains an entry with a new key, the same will be added to
      existing set of args. If the map contains an entry with an existing arg
      key in task definition, the task will run with new arg value for that
      entry. Clearing an existing arg will require arg value to be explicitly
      set to a hyphen "-". The arg value cannot be empty.
    labels: Optional. User-defined labels for the task. If the map is left
      empty, the task will run with existing labels from task definition. If
      the map contains an entry with a new key, the same will be added to
      existing set of labels. If the map contains an entry with an existing
      label key in task definition, the task will run with new label value for
      that entry. Clearing an existing label will require label value to be
      explicitly set to a hyphen "-". The label value cannot be empty.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ArgsValue(_messages.Message):
    r"""Optional. Execution spec arguments. If the map is left empty, the task
    will run with existing execution spec args from task definition. If the
    map contains an entry with a new key, the same will be added to existing
    set of args. If the map contains an entry with an existing arg key in task
    definition, the task will run with new arg value for that entry. Clearing
    an existing arg will require arg value to be explicitly set to a hyphen
    "-". The arg value cannot be empty.

    Messages:
      AdditionalProperty: An additional property for a ArgsValue object.

    Fields:
      additionalProperties: Additional properties of type ArgsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ArgsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels for the task. If the map is left empty,
    the task will run with existing labels from task definition. If the map
    contains an entry with a new key, the same will be added to existing set
    of labels. If the map contains an entry with an existing label key in task
    definition, the task will run with new label value for that entry.
    Clearing an existing label will require label value to be explicitly set
    to a hyphen "-". The label value cannot be empty.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  args = _messages.MessageField('ArgsValue', 1)
  labels = _messages.MessageField('LabelsValue', 2)


class GoogleCloudDataplexV1RunTaskResponse(_messages.Message):
  r"""A GoogleCloudDataplexV1RunTaskResponse object.

  Fields:
    job: Jobs created by RunTask API.
  """

  job = _messages.MessageField('GoogleCloudDataplexV1Job', 1)


class GoogleCloudDataplexV1ScannedData(_messages.Message):
  r"""The data scanned during processing (e.g. in incremental DataScan)

  Fields:
    incrementalField: The range denoted by values of an incremental field
  """

  incrementalField = _messages.MessageField('GoogleCloudDataplexV1ScannedDataIncrementalField', 1)


class GoogleCloudDataplexV1ScannedDataIncrementalField(_messages.Message):
  r"""A data range denoted by a pair of start/end values of a field.

  Fields:
    end: Value that marks the end of the range.
    field: The field that contains values which monotonically increases over
      time (e.g. a timestamp column).
    start: Value that marks the start of the range.
  """

  end = _messages.StringField(1)
  field = _messages.StringField(2)
  start = _messages.StringField(3)


class GoogleCloudDataplexV1Schema(_messages.Message):
  r"""Schema information describing the structure and layout of the data.

  Enums:
    PartitionStyleValueValuesEnum: Optional. The structure of paths containing
      partition data within the entity.

  Fields:
    fields: Optional. The sequence of fields describing data in table
      entities. Note: BigQuery SchemaFields are immutable.
    partitionFields: Optional. The sequence of fields describing the partition
      structure in entities. If this field is empty, there are no partitions
      within the data.
    partitionStyle: Optional. The structure of paths containing partition data
      within the entity.
    userManaged: Required. Set to true if user-managed or false if managed by
      Dataplex. The default is false (managed by Dataplex). Set to falseto
      enable Dataplex discovery to update the schema. including new data
      discovery, schema inference, and schema evolution. Users retain the
      ability to input and edit the schema. Dataplex treats schema input by
      the user as though produced by a previous Dataplex discovery operation,
      and it will evolve the schema and take action based on that treatment.
      Set to true to fully manage the entity schema. This setting guarantees
      that Dataplex will not change schema fields.
  """

  class PartitionStyleValueValuesEnum(_messages.Enum):
    r"""Optional. The structure of paths containing partition data within the
    entity.

    Values:
      PARTITION_STYLE_UNSPECIFIED: PartitionStyle unspecified
      HIVE_COMPATIBLE: Partitions are hive-compatible. Examples:
        gs://bucket/path/to/table/dt=2019-10-31/lang=en,
        gs://bucket/path/to/table/dt=2019-10-31/lang=en/late.
    """
    PARTITION_STYLE_UNSPECIFIED = 0
    HIVE_COMPATIBLE = 1

  fields = _messages.MessageField('GoogleCloudDataplexV1SchemaSchemaField', 1, repeated=True)
  partitionFields = _messages.MessageField('GoogleCloudDataplexV1SchemaPartitionField', 2, repeated=True)
  partitionStyle = _messages.EnumField('PartitionStyleValueValuesEnum', 3)
  userManaged = _messages.BooleanField(4)


class GoogleCloudDataplexV1SchemaPartitionField(_messages.Message):
  r"""Represents a key field within the entity's partition structure. You
  could have up to 20 partition fields, but only the first 10 partitions have
  the filtering ability due to performance consideration. Note: Partition
  fields are immutable.

  Enums:
    TypeValueValuesEnum: Required. Immutable. The type of field.

  Fields:
    name: Required. Partition field name must consist of letters, numbers, and
      underscores only, with a maximum of length of 256 characters, and must
      begin with a letter or underscore..
    type: Required. Immutable. The type of field.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The type of field.

    Values:
      TYPE_UNSPECIFIED: SchemaType unspecified.
      BOOLEAN: Boolean field.
      BYTE: Single byte numeric field.
      INT16: 16-bit numeric field.
      INT32: 32-bit numeric field.
      INT64: 64-bit numeric field.
      FLOAT: Floating point numeric field.
      DOUBLE: Double precision numeric field.
      DECIMAL: Real value numeric field.
      STRING: Sequence of characters field.
      BINARY: Sequence of bytes field.
      TIMESTAMP: Date and time field.
      DATE: Date field.
      TIME: Time field.
      RECORD: Structured field. Nested fields that define the structure of the
        map. If all nested fields are nullable, this field represents a union.
      NULL: Null field that does not have values.
    """
    TYPE_UNSPECIFIED = 0
    BOOLEAN = 1
    BYTE = 2
    INT16 = 3
    INT32 = 4
    INT64 = 5
    FLOAT = 6
    DOUBLE = 7
    DECIMAL = 8
    STRING = 9
    BINARY = 10
    TIMESTAMP = 11
    DATE = 12
    TIME = 13
    RECORD = 14
    NULL = 15

  name = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudDataplexV1SchemaSchemaField(_messages.Message):
  r"""Represents a column field within a table schema.

  Enums:
    ModeValueValuesEnum: Required. Additional field semantics.
    TypeValueValuesEnum: Required. The type of field.

  Fields:
    description: Optional. User friendly field description. Must be less than
      or equal to 1024 characters.
    fields: Optional. Any nested field for complex types.
    mode: Required. Additional field semantics.
    name: Required. The name of the field. Must contain only letters, numbers
      and underscores, with a maximum length of 767 characters, and must begin
      with a letter or underscore.
    type: Required. The type of field.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Required. Additional field semantics.

    Values:
      MODE_UNSPECIFIED: Mode unspecified.
      REQUIRED: The field has required semantics.
      NULLABLE: The field has optional semantics, and may be null.
      REPEATED: The field has repeated (0 or more) semantics, and is a list of
        values.
    """
    MODE_UNSPECIFIED = 0
    REQUIRED = 1
    NULLABLE = 2
    REPEATED = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of field.

    Values:
      TYPE_UNSPECIFIED: SchemaType unspecified.
      BOOLEAN: Boolean field.
      BYTE: Single byte numeric field.
      INT16: 16-bit numeric field.
      INT32: 32-bit numeric field.
      INT64: 64-bit numeric field.
      FLOAT: Floating point numeric field.
      DOUBLE: Double precision numeric field.
      DECIMAL: Real value numeric field.
      STRING: Sequence of characters field.
      BINARY: Sequence of bytes field.
      TIMESTAMP: Date and time field.
      DATE: Date field.
      TIME: Time field.
      RECORD: Structured field. Nested fields that define the structure of the
        map. If all nested fields are nullable, this field represents a union.
      NULL: Null field that does not have values.
    """
    TYPE_UNSPECIFIED = 0
    BOOLEAN = 1
    BYTE = 2
    INT16 = 3
    INT32 = 4
    INT64 = 5
    FLOAT = 6
    DOUBLE = 7
    DECIMAL = 8
    STRING = 9
    BINARY = 10
    TIMESTAMP = 11
    DATE = 12
    TIME = 13
    RECORD = 14
    NULL = 15

  description = _messages.StringField(1)
  fields = _messages.MessageField('GoogleCloudDataplexV1SchemaSchemaField', 2, repeated=True)
  mode = _messages.EnumField('ModeValueValuesEnum', 3)
  name = _messages.StringField(4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class GoogleCloudDataplexV1Session(_messages.Message):
  r"""Represents an active analyze session running for a user.

  Enums:
    StateValueValuesEnum: Output only. State of Session

  Fields:
    createTime: Output only. Session start time.
    name: Output only. The relative resource name of the content, of the form:
      projects/{project_id}/locations/{location_id}/lakes/{lake_id}/environmen
      t/{environment_id}/sessions/{session_id}
    state: Output only. State of Session
    userId: Output only. Email of user running the session.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of Session

    Values:
      STATE_UNSPECIFIED: State is not specified.
      ACTIVE: Resource is active, i.e., ready to use.
      CREATING: Resource is under creation.
      DELETING: Resource is under deletion.
      ACTION_REQUIRED: Resource is active but has unresolved actions.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    ACTION_REQUIRED = 4

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  userId = _messages.StringField(4)


class GoogleCloudDataplexV1SessionEvent(_messages.Message):
  r"""These messages contain information about sessions within an environment.
  The monitored resource is 'Environment'.

  Enums:
    TypeValueValuesEnum: The type of the event.

  Fields:
    eventSucceeded: The status of the event.
    fastStartupEnabled: If the session is associated with an environment with
      fast startup enabled, and was created before being assigned to a user.
    message: The log message.
    query: The execution details of the query.
    sessionId: Unique identifier for the session.
    type: The type of the event.
    unassignedDuration: The idle duration of a warm pooled session before it
      is assigned to user.
    userId: The information about the user that created the session. It will
      be the email address of the user.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the event.

    Values:
      EVENT_TYPE_UNSPECIFIED: An unspecified event type.
      START: Event when the session is assigned to a user.
      STOP: Event for stop of a session.
      QUERY: Query events in the session.
      CREATE: Event for creation of a cluster. It is not yet assigned to a
        user. This comes before START in the sequence
    """
    EVENT_TYPE_UNSPECIFIED = 0
    START = 1
    STOP = 2
    QUERY = 3
    CREATE = 4

  eventSucceeded = _messages.BooleanField(1)
  fastStartupEnabled = _messages.BooleanField(2)
  message = _messages.StringField(3)
  query = _messages.MessageField('GoogleCloudDataplexV1SessionEventQueryDetail', 4)
  sessionId = _messages.StringField(5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)
  unassignedDuration = _messages.StringField(7)
  userId = _messages.StringField(8)


class GoogleCloudDataplexV1SessionEventQueryDetail(_messages.Message):
  r"""Execution details of the query.

  Enums:
    EngineValueValuesEnum: Query Execution engine.

  Fields:
    dataProcessedBytes: The data processed by the query.
    duration: Time taken for execution of the query.
    engine: Query Execution engine.
    queryId: The unique Query id identifying the query.
    queryText: The query text executed.
    resultSizeBytes: The size of results the query produced.
  """

  class EngineValueValuesEnum(_messages.Enum):
    r"""Query Execution engine.

    Values:
      ENGINE_UNSPECIFIED: An unspecified Engine type.
      SPARK_SQL: Spark-sql engine is specified in Query.
      BIGQUERY: BigQuery engine is specified in Query.
    """
    ENGINE_UNSPECIFIED = 0
    SPARK_SQL = 1
    BIGQUERY = 2

  dataProcessedBytes = _messages.IntegerField(1)
  duration = _messages.StringField(2)
  engine = _messages.EnumField('EngineValueValuesEnum', 3)
  queryId = _messages.StringField(4)
  queryText = _messages.StringField(5)
  resultSizeBytes = _messages.IntegerField(6)


class GoogleCloudDataplexV1StorageAccess(_messages.Message):
  r"""Describes the access mechanism of the data within its storage location.

  Enums:
    ReadValueValuesEnum: Output only. Describes the read access mechanism of
      the data. Not user settable.

  Fields:
    read: Output only. Describes the read access mechanism of the data. Not
      user settable.
  """

  class ReadValueValuesEnum(_messages.Enum):
    r"""Output only. Describes the read access mechanism of the data. Not user
    settable.

    Values:
      ACCESS_MODE_UNSPECIFIED: Access mode unspecified.
      DIRECT: Default. Data is accessed directly using storage APIs.
      MANAGED: Data is accessed through a managed interface using BigQuery
        APIs.
    """
    ACCESS_MODE_UNSPECIFIED = 0
    DIRECT = 1
    MANAGED = 2

  read = _messages.EnumField('ReadValueValuesEnum', 1)


class GoogleCloudDataplexV1StorageBucket(_messages.Message):
  r"""Resource config defines properties of a resource used by Dataplex.

  Enums:
    AccessModeValueValuesEnum: Optional. The access mode of the bucket.
      Default to DIRECT. When set to MANAGED_READ, dataplex will create a
      dataset to hold BigLake tables and a BigQuery connection.

  Fields:
    accessMode: Optional. The access mode of the bucket. Default to DIRECT.
      When set to MANAGED_READ, dataplex will create a dataset to hold BigLake
      tables and a BigQuery connection.
    connection: Output only. The BigQuery connection created by Dataplex. Only
      valid when the AccessMode is set to MANAGED_READ. The connection ID is
      default to dataplex-connection. If that exists, Dataplex adds the
      smallest natual number suffix to the it to make a unique and valid
      connection ID
    dataset: Output only. The resource name of the dataset in the format of
      `projects/project_id/datasets/dataset_idThe dataset is created by
      Dataplex to hold BigLake tables. If the dataset already exists, Dataplex
      will append the smallest natual number suffix to the bucket ID to make a
      unique and valid dataset ID.Only valid if access mode is DIRECT.
    etag: Optional. The etag associated with the managed entry, which can be
      retrieved with a GetManagedEntry request. Required for update requests.
  """

  class AccessModeValueValuesEnum(_messages.Enum):
    r"""Optional. The access mode of the bucket. Default to DIRECT. When set
    to MANAGED_READ, dataplex will create a dataset to hold BigLake tables and
    a BigQuery connection.

    Values:
      ACCESS_MODE_UNSPECIFIED: Access mode unspecified.
      DIRECT: The resource data is accessible via Gloud Storage or BigQuery
        vanilla external table. DIRECT is the default value for any bucket.
      MANAGED_READ: The resource data is only readable via BigLake table.
    """
    ACCESS_MODE_UNSPECIFIED = 0
    DIRECT = 1
    MANAGED_READ = 2

  accessMode = _messages.EnumField('AccessModeValueValuesEnum', 1)
  connection = _messages.StringField(2)
  dataset = _messages.StringField(3)
  etag = _messages.StringField(4)


class GoogleCloudDataplexV1StorageFormat(_messages.Message):
  r"""Describes the format of the data within its storage location.

  Enums:
    CompressionFormatValueValuesEnum: Optional. The compression type
      associated with the stored data. If unspecified, the data is
      uncompressed.
    FormatValueValuesEnum: Output only. The data format associated with the
      stored data, which represents content type values. The value is inferred
      from mime type.

  Fields:
    compressionFormat: Optional. The compression type associated with the
      stored data. If unspecified, the data is uncompressed.
    csv: Optional. Additional information about CSV formatted data.
    format: Output only. The data format associated with the stored data,
      which represents content type values. The value is inferred from mime
      type.
    iceberg: Optional. Additional information about iceberg tables.
    json: Optional. Additional information about CSV formatted data.
    mimeType: Required. The mime type descriptor for the data. Must match the
      pattern {type}/{subtype}. Supported values: application/x-parquet
      application/x-avro application/x-orc application/x-tfrecord
      application/x-parquet+iceberg application/x-avro+iceberg
      application/x-orc+iceberg application/json application/{subtypes}
      text/csv text/ image/{image subtype} video/{video subtype} audio/{audio
      subtype}
  """

  class CompressionFormatValueValuesEnum(_messages.Enum):
    r"""Optional. The compression type associated with the stored data. If
    unspecified, the data is uncompressed.

    Values:
      COMPRESSION_FORMAT_UNSPECIFIED: CompressionFormat unspecified. Implies
        uncompressed data.
      GZIP: GZip compressed set of files.
      BZIP2: BZip2 compressed set of files.
    """
    COMPRESSION_FORMAT_UNSPECIFIED = 0
    GZIP = 1
    BZIP2 = 2

  class FormatValueValuesEnum(_messages.Enum):
    r"""Output only. The data format associated with the stored data, which
    represents content type values. The value is inferred from mime type.

    Values:
      FORMAT_UNSPECIFIED: Format unspecified.
      PARQUET: Parquet-formatted structured data.
      AVRO: Avro-formatted structured data.
      ORC: Orc-formatted structured data.
      CSV: Csv-formatted semi-structured data.
      JSON: Json-formatted semi-structured data.
      IMAGE: Image data formats (such as jpg and png).
      AUDIO: Audio data formats (such as mp3, and wav).
      VIDEO: Video data formats (such as mp4 and mpg).
      TEXT: Textual data formats (such as txt and xml).
      TFRECORD: TensorFlow record format.
      OTHER: Data that doesn't match a specific format.
      UNKNOWN: Data of an unknown format.
    """
    FORMAT_UNSPECIFIED = 0
    PARQUET = 1
    AVRO = 2
    ORC = 3
    CSV = 4
    JSON = 5
    IMAGE = 6
    AUDIO = 7
    VIDEO = 8
    TEXT = 9
    TFRECORD = 10
    OTHER = 11
    UNKNOWN = 12

  compressionFormat = _messages.EnumField('CompressionFormatValueValuesEnum', 1)
  csv = _messages.MessageField('GoogleCloudDataplexV1StorageFormatCsvOptions', 2)
  format = _messages.EnumField('FormatValueValuesEnum', 3)
  iceberg = _messages.MessageField('GoogleCloudDataplexV1StorageFormatIcebergOptions', 4)
  json = _messages.MessageField('GoogleCloudDataplexV1StorageFormatJsonOptions', 5)
  mimeType = _messages.StringField(6)


class GoogleCloudDataplexV1StorageFormatCsvOptions(_messages.Message):
  r"""Describes CSV and similar semi-structured data formats.

  Fields:
    delimiter: Optional. The delimiter used to separate values. Defaults to
      ','.
    encoding: Optional. The character encoding of the data. Accepts "US-
      ASCII", "UTF-8", and "ISO-8859-1". Defaults to UTF-8 if unspecified.
    headerRows: Optional. The number of rows to interpret as header rows that
      should be skipped when reading data rows. Defaults to 0.
    quote: Optional. The character used to quote column values. Accepts '"'
      (double quotation mark) or ''' (single quotation mark). Defaults to '"'
      (double quotation mark) if unspecified.
  """

  delimiter = _messages.StringField(1)
  encoding = _messages.StringField(2)
  headerRows = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  quote = _messages.StringField(4)


class GoogleCloudDataplexV1StorageFormatIcebergOptions(_messages.Message):
  r"""Describes Iceberg data format.

  Fields:
    metadataLocation: Optional. The location of where the iceberg metadata is
      present, must be within the table path
  """

  metadataLocation = _messages.StringField(1)


class GoogleCloudDataplexV1StorageFormatJsonOptions(_messages.Message):
  r"""Describes JSON data format.

  Fields:
    encoding: Optional. The character encoding of the data. Accepts "US-
      ASCII", "UTF-8" and "ISO-8859-1". Defaults to UTF-8 if not specified.
  """

  encoding = _messages.StringField(1)


class GoogleCloudDataplexV1Task(_messages.Message):
  r"""A task represents a user-visible job.

  Enums:
    StateValueValuesEnum: Output only. Current state of the task.

  Messages:
    LabelsValue: Optional. User-defined labels for the task.

  Fields:
    createTime: Output only. The time when the task was created.
    description: Optional. Description of the task.
    displayName: Optional. User friendly display name.
    executionSpec: Required. Spec related to how a task is executed.
    executionStatus: Output only. Status of the latest task executions.
    labels: Optional. User-defined labels for the task.
    name: Output only. The relative resource name of the task, of the form:
      projects/{project_number}/locations/{location_id}/lakes/{lake_id}/
      tasks/{task_id}.
    notebook: Config related to running scheduled Notebooks.
    spark: Config related to running custom Spark tasks.
    state: Output only. Current state of the task.
    triggerSpec: Required. Spec related to how often and when a task should be
      triggered.
    uid: Output only. System generated globally unique ID for the task. This
      ID will be different if the task is deleted and re-created with the same
      name.
    updateTime: Output only. The time when the task was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the task.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      ACTIVE: Resource is active, i.e., ready to use.
      CREATING: Resource is under creation.
      DELETING: Resource is under deletion.
      ACTION_REQUIRED: Resource is active but has unresolved actions.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    ACTION_REQUIRED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels for the task.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  executionSpec = _messages.MessageField('GoogleCloudDataplexV1TaskExecutionSpec', 4)
  executionStatus = _messages.MessageField('GoogleCloudDataplexV1TaskExecutionStatus', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  notebook = _messages.MessageField('GoogleCloudDataplexV1TaskNotebookTaskConfig', 8)
  spark = _messages.MessageField('GoogleCloudDataplexV1TaskSparkTaskConfig', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  triggerSpec = _messages.MessageField('GoogleCloudDataplexV1TaskTriggerSpec', 11)
  uid = _messages.StringField(12)
  updateTime = _messages.StringField(13)


class GoogleCloudDataplexV1TaskExecutionSpec(_messages.Message):
  r"""Execution related settings, like retry and service_account.

  Messages:
    ArgsValue: Optional. The arguments to pass to the task. The args can use
      placeholders of the format ${placeholder} as part of key/value string.
      These will be interpolated before passing the args to the driver.
      Currently supported placeholders: - ${task_id} - ${job_time} To pass
      positional args, set the key as TASK_ARGS. The value should be a comma-
      separated string of all the positional arguments. To use a delimiter
      other than comma, refer to
      https://cloud.google.com/sdk/gcloud/reference/topic/escaping. In case of
      other keys being present in the args, then TASK_ARGS will be passed as
      the last argument.

  Fields:
    args: Optional. The arguments to pass to the task. The args can use
      placeholders of the format ${placeholder} as part of key/value string.
      These will be interpolated before passing the args to the driver.
      Currently supported placeholders: - ${task_id} - ${job_time} To pass
      positional args, set the key as TASK_ARGS. The value should be a comma-
      separated string of all the positional arguments. To use a delimiter
      other than comma, refer to
      https://cloud.google.com/sdk/gcloud/reference/topic/escaping. In case of
      other keys being present in the args, then TASK_ARGS will be passed as
      the last argument.
    kmsKey: Optional. The Cloud KMS key to use for encryption, of the form:
      projects/{project_number}/locations/{location_id}/keyRings/{key-ring-
      name}/cryptoKeys/{key-name}.
    maxJobExecutionLifetime: Optional. The maximum duration after which the
      job execution is expired.
    project: Optional. The project in which jobs are run. By default, the
      project containing the Lake is used. If a project is provided, the
      ExecutionSpec.service_account must belong to this project.
    serviceAccount: Required. Service account to use to execute a task. If not
      provided, the default Compute service account for the project is used.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ArgsValue(_messages.Message):
    r"""Optional. The arguments to pass to the task. The args can use
    placeholders of the format ${placeholder} as part of key/value string.
    These will be interpolated before passing the args to the driver.
    Currently supported placeholders: - ${task_id} - ${job_time} To pass
    positional args, set the key as TASK_ARGS. The value should be a comma-
    separated string of all the positional arguments. To use a delimiter other
    than comma, refer to
    https://cloud.google.com/sdk/gcloud/reference/topic/escaping. In case of
    other keys being present in the args, then TASK_ARGS will be passed as the
    last argument.

    Messages:
      AdditionalProperty: An additional property for a ArgsValue object.

    Fields:
      additionalProperties: Additional properties of type ArgsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ArgsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  args = _messages.MessageField('ArgsValue', 1)
  kmsKey = _messages.StringField(2)
  maxJobExecutionLifetime = _messages.StringField(3)
  project = _messages.StringField(4)
  serviceAccount = _messages.StringField(5)


class GoogleCloudDataplexV1TaskExecutionStatus(_messages.Message):
  r"""Status of the task execution (e.g. Jobs).

  Fields:
    latestJob: Output only. latest job execution
    updateTime: Output only. Last update time of the status.
  """

  latestJob = _messages.MessageField('GoogleCloudDataplexV1Job', 1)
  updateTime = _messages.StringField(2)


class GoogleCloudDataplexV1TaskInfrastructureSpec(_messages.Message):
  r"""Configuration for the underlying infrastructure used to run workloads.

  Fields:
    batch: Compute resources needed for a Task when using Dataproc Serverless.
    containerImage: Container Image Runtime Configuration.
    vpcNetwork: Vpc network.
  """

  batch = _messages.MessageField('GoogleCloudDataplexV1TaskInfrastructureSpecBatchComputeResources', 1)
  containerImage = _messages.MessageField('GoogleCloudDataplexV1TaskInfrastructureSpecContainerImageRuntime', 2)
  vpcNetwork = _messages.MessageField('GoogleCloudDataplexV1TaskInfrastructureSpecVpcNetwork', 3)


class GoogleCloudDataplexV1TaskInfrastructureSpecBatchComputeResources(_messages.Message):
  r"""Batch compute resources associated with the task.

  Fields:
    executorsCount: Optional. Total number of job executors. Executor Count
      should be between 2 and 100. Default=2
    maxExecutorsCount: Optional. Max configurable executors. If
      max_executors_count > executors_count, then auto-scaling is enabled. Max
      Executor Count should be between 2 and 1000. Default=1000
  """

  executorsCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  maxExecutorsCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudDataplexV1TaskInfrastructureSpecContainerImageRuntime(_messages.Message):
  r"""Container Image Runtime Configuration used with Batch execution.

  Messages:
    PropertiesValue: Optional. Override to common configuration of open source
      components installed on the Dataproc cluster. The properties to set on
      daemon config files. Property keys are specified in prefix:property
      format, for example core:hadoop.tmp.dir. For more information, see
      Cluster properties
      (https://cloud.google.com/dataproc/docs/concepts/cluster-properties).

  Fields:
    image: Optional. Container image to use.
    javaJars: Optional. A list of Java JARS to add to the classpath. Valid
      input includes Cloud Storage URIs to Jar binaries. For example,
      gs://bucket-name/my/path/to/file.jar
    properties: Optional. Override to common configuration of open source
      components installed on the Dataproc cluster. The properties to set on
      daemon config files. Property keys are specified in prefix:property
      format, for example core:hadoop.tmp.dir. For more information, see
      Cluster properties
      (https://cloud.google.com/dataproc/docs/concepts/cluster-properties).
    pythonPackages: Optional. A list of python packages to be installed. Valid
      formats include Cloud Storage URI to a PIP installable library. For
      example, gs://bucket-name/my/path/to/lib.tar.gz
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""Optional. Override to common configuration of open source components
    installed on the Dataproc cluster. The properties to set on daemon config
    files. Property keys are specified in prefix:property format, for example
    core:hadoop.tmp.dir. For more information, see Cluster properties
    (https://cloud.google.com/dataproc/docs/concepts/cluster-properties).

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  image = _messages.StringField(1)
  javaJars = _messages.StringField(2, repeated=True)
  properties = _messages.MessageField('PropertiesValue', 3)
  pythonPackages = _messages.StringField(4, repeated=True)


class GoogleCloudDataplexV1TaskInfrastructureSpecVpcNetwork(_messages.Message):
  r"""Cloud VPC Network used to run the infrastructure.

  Fields:
    network: Optional. The Cloud VPC network in which the job is run. By
      default, the Cloud VPC network named Default within the project is used.
    networkTags: Optional. List of network tags to apply to the job.
    subNetwork: Optional. The Cloud VPC sub-network in which the job is run.
  """

  network = _messages.StringField(1)
  networkTags = _messages.StringField(2, repeated=True)
  subNetwork = _messages.StringField(3)


class GoogleCloudDataplexV1TaskNotebookTaskConfig(_messages.Message):
  r"""Config for running scheduled notebooks.

  Fields:
    archiveUris: Optional. Cloud Storage URIs of archives to be extracted into
      the working directory of each executor. Supported file types: .jar,
      .tar, .tar.gz, .tgz, and .zip.
    fileUris: Optional. Cloud Storage URIs of files to be placed in the
      working directory of each executor.
    infrastructureSpec: Optional. Infrastructure specification for the
      execution.
    notebook: Required. Path to input notebook. This can be the Cloud Storage
      URI of the notebook file or the path to a Notebook Content. The
      execution args are accessible as environment variables (TASK_key=value).
  """

  archiveUris = _messages.StringField(1, repeated=True)
  fileUris = _messages.StringField(2, repeated=True)
  infrastructureSpec = _messages.MessageField('GoogleCloudDataplexV1TaskInfrastructureSpec', 3)
  notebook = _messages.StringField(4)


class GoogleCloudDataplexV1TaskSparkTaskConfig(_messages.Message):
  r"""User-specified config for running a Spark task.

  Fields:
    archiveUris: Optional. Cloud Storage URIs of archives to be extracted into
      the working directory of each executor. Supported file types: .jar,
      .tar, .tar.gz, .tgz, and .zip.
    fileUris: Optional. Cloud Storage URIs of files to be placed in the
      working directory of each executor.
    infrastructureSpec: Optional. Infrastructure specification for the
      execution.
    mainClass: The name of the driver's main class. The jar file that contains
      the class must be in the default CLASSPATH or specified in
      jar_file_uris. The execution args are passed in as a sequence of named
      process arguments (--key=value).
    mainJarFileUri: The Cloud Storage URI of the jar file that contains the
      main class. The execution args are passed in as a sequence of named
      process arguments (--key=value).
    pythonScriptFile: The Gcloud Storage URI of the main Python file to use as
      the driver. Must be a .py file. The execution args are passed in as a
      sequence of named process arguments (--key=value).
    sqlScript: The query text. The execution args are used to declare a set of
      script variables (set key="value";).
    sqlScriptFile: A reference to a query file. This can be the Cloud Storage
      URI of the query file or it can the path to a SqlScript Content. The
      execution args are used to declare a set of script variables (set
      key="value";).
  """

  archiveUris = _messages.StringField(1, repeated=True)
  fileUris = _messages.StringField(2, repeated=True)
  infrastructureSpec = _messages.MessageField('GoogleCloudDataplexV1TaskInfrastructureSpec', 3)
  mainClass = _messages.StringField(4)
  mainJarFileUri = _messages.StringField(5)
  pythonScriptFile = _messages.StringField(6)
  sqlScript = _messages.StringField(7)
  sqlScriptFile = _messages.StringField(8)


class GoogleCloudDataplexV1TaskTriggerSpec(_messages.Message):
  r"""Task scheduling and trigger settings.

  Enums:
    TypeValueValuesEnum: Required. Immutable. Trigger type of the user-
      specified Task.

  Fields:
    disabled: Optional. Prevent the task from executing. This does not cancel
      already running tasks. It is intended to temporarily disable RECURRING
      tasks.
    maxRetries: Optional. Number of retry attempts before aborting. Set to
      zero to never attempt to retry a failed task.
    schedule: Optional. Cron schedule (https://en.wikipedia.org/wiki/Cron) for
      running tasks periodically. To explicitly set a timezone to the cron
      tab, apply a prefix in the cron tab: "CRON_TZ=${IANA_TIME_ZONE}" or
      "TZ=${IANA_TIME_ZONE}". The ${IANA_TIME_ZONE} may only be a valid string
      from IANA time zone database. For example, CRON_TZ=America/New_York 1 *
      * * *, or TZ=America/New_York 1 * * * *. This field is required for
      RECURRING tasks.
    startTime: Optional. The first run of the task will be after this time. If
      not specified, the task will run shortly after being submitted if
      ON_DEMAND and based on the schedule if RECURRING.
    type: Required. Immutable. Trigger type of the user-specified Task.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. Trigger type of the user-specified Task.

    Values:
      TYPE_UNSPECIFIED: Unspecified trigger type.
      ON_DEMAND: The task runs one-time shortly after Task Creation.
      RECURRING: The task is scheduled to run periodically.
    """
    TYPE_UNSPECIFIED = 0
    ON_DEMAND = 1
    RECURRING = 2

  disabled = _messages.BooleanField(1)
  maxRetries = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  schedule = _messages.StringField(3)
  startTime = _messages.StringField(4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class GoogleCloudDataplexV1Trigger(_messages.Message):
  r"""DataScan scheduling and trigger settings.

  Fields:
    onDemand: The scan runs once via RunDataScan API.
    schedule: The scan is scheduled to run periodically.
  """

  onDemand = _messages.MessageField('GoogleCloudDataplexV1TriggerOnDemand', 1)
  schedule = _messages.MessageField('GoogleCloudDataplexV1TriggerSchedule', 2)


class GoogleCloudDataplexV1TriggerOnDemand(_messages.Message):
  r"""The scan runs once via RunDataScan API."""


class GoogleCloudDataplexV1TriggerSchedule(_messages.Message):
  r"""The scan is scheduled to run periodically.

  Fields:
    cron: Required. Cron (https://en.wikipedia.org/wiki/Cron) schedule for
      running scans periodically.To explicitly set a timezone in the cron tab,
      apply a prefix in the cron tab: "CRON_TZ=${IANA_TIME_ZONE}" or
      "TZ=${IANA_TIME_ZONE}". The ${IANA_TIME_ZONE} may only be a valid string
      from IANA time zone database (wikipedia
      (https://en.wikipedia.org/wiki/List_of_tz_database_time_zones#List)).
      For example, CRON_TZ=America/New_York 1 * * * *, or TZ=America/New_York
      1 * * * *.This field is required for Schedule scans.
  """

  cron = _messages.StringField(1)


class GoogleCloudDataplexV1Zone(_messages.Message):
  r"""A zone represents a logical group of related assets within a lake. A
  zone can be used to map to organizational structure or represent stages of
  data readiness from raw to curated. It provides managing behavior that is
  shared or inherited by all contained assets.

  Enums:
    StateValueValuesEnum: Output only. Current state of the zone.
    TypeValueValuesEnum: Required. Immutable. The type of the zone.

  Messages:
    LabelsValue: Optional. User defined labels for the zone.

  Fields:
    assetStatus: Output only. Aggregated status of the underlying assets of
      the zone.
    createTime: Output only. The time when the zone was created.
    description: Optional. Description of the zone.
    discoverySpec: Optional. Specification of the discovery feature applied to
      data in this zone.
    displayName: Optional. User friendly display name.
    labels: Optional. User defined labels for the zone.
    name: Output only. The relative resource name of the zone, of the form: pr
      ojects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{z
      one_id}.
    resourceSpec: Required. Specification of the resources that are referenced
      by the assets within this zone.
    state: Output only. Current state of the zone.
    type: Required. Immutable. The type of the zone.
    uid: Output only. System generated globally unique ID for the zone. This
      ID will be different if the zone is deleted and re-created with the same
      name.
    updateTime: Output only. The time when the zone was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the zone.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      ACTIVE: Resource is active, i.e., ready to use.
      CREATING: Resource is under creation.
      DELETING: Resource is under deletion.
      ACTION_REQUIRED: Resource is active but has unresolved actions.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    ACTION_REQUIRED = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The type of the zone.

    Values:
      TYPE_UNSPECIFIED: Zone type not specified.
      RAW: A zone that contains data that needs further processing before it
        is considered generally ready for consumption and analytics workloads.
      CURATED: A zone that contains data that is considered to be ready for
        broader consumption and analytics workloads. Curated structured data
        stored in Cloud Storage must conform to certain file formats (parquet,
        avro and orc) and organized in a hive-compatible directory layout.
    """
    TYPE_UNSPECIFIED = 0
    RAW = 1
    CURATED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User defined labels for the zone.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetStatus = _messages.MessageField('GoogleCloudDataplexV1AssetStatus', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  discoverySpec = _messages.MessageField('GoogleCloudDataplexV1ZoneDiscoverySpec', 4)
  displayName = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  resourceSpec = _messages.MessageField('GoogleCloudDataplexV1ZoneResourceSpec', 8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  type = _messages.EnumField('TypeValueValuesEnum', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleCloudDataplexV1ZoneDiscoverySpec(_messages.Message):
  r"""Settings to manage the metadata discovery and publishing in a zone.

  Fields:
    csvOptions: Optional. Configuration for CSV data.
    enabled: Required. Whether discovery is enabled.
    excludePatterns: Optional. The list of patterns to apply for selecting
      data to exclude during discovery. For Cloud Storage bucket assets, these
      are interpreted as glob patterns used to match object names. For
      BigQuery dataset assets, these are interpreted as patterns to match
      table names.
    includePatterns: Optional. The list of patterns to apply for selecting
      data to include during discovery if only a subset of the data should
      considered. For Cloud Storage bucket assets, these are interpreted as
      glob patterns used to match object names. For BigQuery dataset assets,
      these are interpreted as patterns to match table names.
    jsonOptions: Optional. Configuration for Json data.
    schedule: Optional. Cron schedule (https://en.wikipedia.org/wiki/Cron) for
      running discovery periodically. Successive discovery runs must be
      scheduled at least 60 minutes apart. The default value is to run
      discovery every 60 minutes. To explicitly set a timezone to the cron
      tab, apply a prefix in the cron tab: "CRON_TZ=${IANA_TIME_ZONE}" or
      TZ=${IANA_TIME_ZONE}". The ${IANA_TIME_ZONE} may only be a valid string
      from IANA time zone database. For example, CRON_TZ=America/New_York 1 *
      * * *, or TZ=America/New_York 1 * * * *.
  """

  csvOptions = _messages.MessageField('GoogleCloudDataplexV1ZoneDiscoverySpecCsvOptions', 1)
  enabled = _messages.BooleanField(2)
  excludePatterns = _messages.StringField(3, repeated=True)
  includePatterns = _messages.StringField(4, repeated=True)
  jsonOptions = _messages.MessageField('GoogleCloudDataplexV1ZoneDiscoverySpecJsonOptions', 5)
  schedule = _messages.StringField(6)


class GoogleCloudDataplexV1ZoneDiscoverySpecCsvOptions(_messages.Message):
  r"""Describe CSV and similar semi-structured data formats.

  Fields:
    delimiter: Optional. The delimiter being used to separate values. This
      defaults to ','.
    disableTypeInference: Optional. Whether to disable the inference of data
      type for CSV data. If true, all columns will be registered as strings.
    encoding: Optional. The character encoding of the data. The default is
      UTF-8.
    headerRows: Optional. The number of rows to interpret as header rows that
      should be skipped when reading data rows.
  """

  delimiter = _messages.StringField(1)
  disableTypeInference = _messages.BooleanField(2)
  encoding = _messages.StringField(3)
  headerRows = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudDataplexV1ZoneDiscoverySpecJsonOptions(_messages.Message):
  r"""Describe JSON data format.

  Fields:
    disableTypeInference: Optional. Whether to disable the inference of data
      type for Json data. If true, all columns will be registered as their
      primitive types (strings, number or boolean).
    encoding: Optional. The character encoding of the data. The default is
      UTF-8.
  """

  disableTypeInference = _messages.BooleanField(1)
  encoding = _messages.StringField(2)


class GoogleCloudDataplexV1ZoneResourceSpec(_messages.Message):
  r"""Settings for resources attached as assets within a zone.

  Enums:
    LocationTypeValueValuesEnum: Required. Immutable. The location type of the
      resources that are allowed to be attached to the assets within this
      zone.

  Fields:
    locationType: Required. Immutable. The location type of the resources that
      are allowed to be attached to the assets within this zone.
  """

  class LocationTypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The location type of the resources that are
    allowed to be attached to the assets within this zone.

    Values:
      LOCATION_TYPE_UNSPECIFIED: Unspecified location type.
      SINGLE_REGION: Resources that are associated with a single region.
      MULTI_REGION: Resources that are associated with a multi-region
        location.
    """
    LOCATION_TYPE_UNSPECIFIED = 0
    SINGLE_REGION = 1
    MULTI_REGION = 2

  locationType = _messages.EnumField('LocationTypeValueValuesEnum', 1)


class GoogleCloudLocationListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('GoogleCloudLocationLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudLocationLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: "us-east1".
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: "projects/example-project/locations/us-
      east1"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs.If there are AuditConfigs for both allServices and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted.Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It <NAME_EMAIL> from DATA_READ logging, and
  <EMAIL> from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, storage.googleapis.com, cloudsql.googleapis.com. allServices is
      a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates members, or principals, with a role.

  Fields:
    condition: The condition that is associated with this binding.If the
      condition evaluates to true, then this binding applies to the current
      request.If the condition evaluates to false, then this binding does not
      apply to the current request. However, a different role binding might
      grant the same role to one or more of the principals in this binding.To
      learn which resources support conditions in their IAM policies, see the
      IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. members can have the following values: allUsers: A special
      identifier that represents anyone who is on the internet; with or
      without a Google account. allAuthenticatedUsers: A special identifier
      that represents anyone who is authenticated with a Google account or a
      service account. Does not include identities that come from external
      identity providers (IdPs) through identity federation. user:{emailid}:
      An email address that represents a specific Google account. For example,
      <EMAIL> . serviceAccount:{emailid}: An email address that
      represents a Google service account. For example, my-other-
      <EMAIL>.
      serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]: An
      identifier for a Kubernetes service account
      (https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-
      service-accounts). For example, my-project.svc.id.goog[my-namespace/my-
      kubernetes-sa]. group:{emailid}: An email address that represents a
      Google group. For example, <EMAIL>. domain:{domain}: The G
      Suite domain (primary) that represents all the users of that domain. For
      example, google.com or example.com.
      deleted:user:{emailid}?uid={uniqueid}: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, <EMAIL>?uid=123456789012345678901. If the user is
      recovered, this value reverts to user:{emailid} and the recovered user
      retains the role in the binding.
      deleted:serviceAccount:{emailid}?uid={uniqueid}: An email address (plus
      unique identifier) representing a service account that has been recently
      deleted. For example, my-other-
      <EMAIL>?uid=123456789012345678901. If the
      service account is undeleted, this value reverts to
      serviceAccount:{emailid} and the undeleted service account retains the
      role in the binding. deleted:group:{emailid}?uid={uniqueid}: An email
      address (plus unique identifier) representing a Google group that has
      been recently deleted. For example,
      <EMAIL>?uid=123456789012345678901. If the group is recovered,
      this value reverts to group:{emailid} and the recovered group retains
      the role in the binding.
    role: Role that is assigned to the list of members, or principals. For
      example, roles/viewer, roles/editor, or roles/owner.
  """

  condition = _messages.MessageField('GoogleTypeExpr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources.A Policy is a collection of bindings. A
  binding binds one or more members, or principals, to a single role.
  Principals can be user accounts, service accounts, Google groups, and
  domains (such as G Suite). A role is a named list of permissions; each role
  can be an IAM predefined role or a user-created custom role.For some types
  of Google Cloud resources, a binding can also specify a condition, which is
  a logical expression that allows access to a resource only if the expression
  evaluates to true. A condition can add constraints based on attributes of
  the request, the resource, or both. To learn which resources support
  conditions in their IAM policies, see the IAM documentation
  (https://cloud.google.com/iam/help/conditions/resource-policies).JSON
  example: { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } YAML example: bindings: - members: -
  user:<EMAIL> - group:<EMAIL> - domain:google.com -
  serviceAccount:<EMAIL> role:
  roles/resourcemanager.organizationAdmin - members: - user:<EMAIL>
  role: roles/resourcemanager.organizationViewer condition: title: expirable
  access description: Does not grant access after Sep 2020 expression:
  request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA=
  version: 3 For a description of IAM and its features, see the IAM
  documentation (https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of members, or principals, with a role.
      Optionally, may specify a condition that determines how and when the
      bindings are applied. Each of the bindings must contain at least one
      principal.The bindings in a Policy can refer to up to 1,500 principals;
      up to 250 of these principals can be Google groups. Each occurrence of a
      principal counts towards these limits. For example, if the bindings
      grant 50 different roles to user:<EMAIL>, and not to any other
      principal, then you can add another 1,450 principals to the bindings in
      the Policy.
    etag: etag is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the etag in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An etag is returned in the response to getIamPolicy, and
      systems are expected to put that etag in the request to setIamPolicy to
      ensure that their change will be applied to the same version of the
      policy.Important: If you use IAM Conditions, you must include the etag
      field whenever you call setIamPolicy. If you omit this field, then IAM
      allows you to overwrite a version 3 policy with a version 1 policy, and
      all of the conditions in the version 3 policy are lost.
    version: Specifies the format of the policy.Valid values are 0, 1, and 3.
      Requests that specify an invalid value are rejected.Any operation that
      affects conditional role bindings must specify version 3. This
      requirement applies to the following operations: Getting a policy that
      includes a conditional role binding Adding a conditional role binding to
      a policy Changing a conditional role binding in a policy Removing any
      role binding, with or without a condition, from a policy that includes
      conditionsImportant: If you use IAM Conditions, you must include the
      etag field whenever you call setIamPolicy. If you omit this field, then
      IAM allows you to overwrite a version 3 policy with a version 1 policy,
      and all of the conditions in the version 3 policy are lost.If a policy
      does not include any conditions, operations on that policy may specify
      any valid version or leave the field unset.To learn which resources
      support conditions in their IAM policies, see the IAM documentation
      (https://cloud.google.com/iam/help/conditions/resource-policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for SetIamPolicy method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the resource. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used:paths: "bindings, etag"
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for TestIamPermissions method.

  Fields:
    permissions: The set of permissions to check for the resource. Permissions
      with wildcards (such as * or storage.*) are not allowed. For more
      information see IAM Overview
      (https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for TestIamPermissions method.

  Fields:
    permissions: A subset of TestPermissionsRequest.permissions that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleLongrunningCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as Delete, the response
      is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type XxxResponse, where Xxx is the
      original method name. For example, if the original method name is
      TakeSnapshot(), the inferred response type is TakeSnapshotResponse.

  Fields:
    done: If the value is false, it means the operation is still in progress.
      If true, the operation is completed, and either error or response is
      available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the name should be a resource name ending with operations/{unique_id}.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as Delete, the response
      is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type XxxResponse, where Xxx is the
      original method name. For example, if the original method name is
      TakeSnapshot(), the inferred response type is TakeSnapshotResponse.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as Delete, the response is
    google.protobuf.Empty. If the original method is standard
    Get/Create/Update, the response should be the resource. For other methods,
    the response should have the type XxxResponse, where Xxx is the original
    method name. For example, if the original method name is TakeSnapshot(),
    the inferred response type is TakeSnapshotResponse.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The Status type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by gRPC (https://github.com/grpc). Each Status message contains three
  pieces of data: error code, error message, and error details.You can find
  out more about this error model and how to work with it in the API Design
  Guide (https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec.Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated message classes for netapp version v1.

Google Cloud NetApp Volumes is a fully-managed, cloud-based data storage
service that provides advanced data management capabilities and highly
scalable performance with global availability.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'netapp'


class ActiveDirectory(_messages.Message):
  r"""ActiveDirectory is the public representation of the active directory
  config.

  Enums:
    StateValueValuesEnum: Output only. The state of the AD.

  Messages:
    LabelsValue: Labels for the active directory.

  Fields:
    aesEncryption: If enabled, AES encryption will be enabled for SMB
      communication.
    backupOperators: Users to be added to the Built-in Backup Operator active
      directory group.
    createTime: Output only. Create time of the active directory.
    description: Description of the active directory.
    dns: Required. Comma separated list of DNS server IP addresses for the
      Active Directory domain.
    domain: Required. Name of the Active Directory domain
    encryptDcConnections: If enabled, traffic between the SMB server to Domain
      Controller (DC) will be encrypted.
    kdcHostname: Name of the active directory machine. This optional parameter
      is used only while creating kerberos volume
    kdcIp: KDC server IP address for the active directory machine.
    labels: Labels for the active directory.
    ldapSigning: Specifies whether or not the LDAP traffic needs to be signed.
    name: Output only. The resource name of the active directory. Format: `pro
      jects/{project_number}/locations/{location_id}/activeDirectories/{active
      _directory_id}`.
    netBiosPrefix: Required. NetBIOSPrefix is used as a prefix for SMB server
      name.
    nfsUsersWithLdap: If enabled, will allow access to local users and LDAP
      users. If access is needed for only LDAP users, it has to be disabled.
    organizationalUnit: The Organizational Unit (OU) within the Windows Active
      Directory the user belongs to.
    password: Required. Password of the Active Directory domain administrator.
    securityOperators: Domain users to be given the SeSecurityPrivilege.
    site: The Active Directory site the service will limit Domain Controller
      discovery too.
    state: Output only. The state of the AD.
    stateDetails: Output only. The state details of the Active Directory.
    username: Required. Username of the Active Directory domain administrator.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the AD.

    Values:
      STATE_UNSPECIFIED: Unspecified Active Directory State
      CREATING: Active Directory State is Creating
      READY: Active Directory State is Ready
      UPDATING: Active Directory State is Updating
      IN_USE: Active Directory State is In use
      DELETING: Active Directory State is Deleting
      ERROR: Active Directory State is Error
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    IN_USE = 4
    DELETING = 5
    ERROR = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for the active directory.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  aesEncryption = _messages.BooleanField(1)
  backupOperators = _messages.StringField(2, repeated=True)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  dns = _messages.StringField(5)
  domain = _messages.StringField(6)
  encryptDcConnections = _messages.BooleanField(7)
  kdcHostname = _messages.StringField(8)
  kdcIp = _messages.StringField(9)
  labels = _messages.MessageField('LabelsValue', 10)
  ldapSigning = _messages.BooleanField(11)
  name = _messages.StringField(12)
  netBiosPrefix = _messages.StringField(13)
  nfsUsersWithLdap = _messages.BooleanField(14)
  organizationalUnit = _messages.StringField(15)
  password = _messages.StringField(16)
  securityOperators = _messages.StringField(17, repeated=True)
  site = _messages.StringField(18)
  state = _messages.EnumField('StateValueValuesEnum', 19)
  stateDetails = _messages.StringField(20)
  username = _messages.StringField(21)


class Backup(_messages.Message):
  r"""A NetApp Backup.

  Enums:
    BackupTypeValueValuesEnum: Output only. Type of backup, manually created
      or created by a backup policy.
    StateValueValuesEnum: Output only. The backup state.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata.

  Fields:
    backupType: Output only. Type of backup, manually created or created by a
      backup policy.
    createTime: Output only. The time when the backup was created.
    description: A description of the backup with 2048 characters or less.
      Requests with longer descriptions will be rejected.
    labels: Resource labels to represent user provided metadata.
    name: Output only. The resource name of the backup. Format: `projects/{pro
      ject_id}/locations/{location}/backupVaults/{backup_vault_id}/backups/{ba
      ckup_id}`.
    sourceSnapshot: If specified, backup will be created from the given
      snapshot. If not specified, there will be a new snapshot taken to
      initiate the backup creation. Format: `projects/{project_id}/locations/{
      location}/volumes/{volume_id}/snapshots/{snapshot_id}`
    sourceVolume: Volume full name of this backup belongs to. Format:
      `projects/{projects_id}/locations/{location}/volumes/{volume_id}`
    state: Output only. The backup state.
    volumeUsageBytes: Output only. Size of the file system when the backup was
      created. When creating a new volume from the backup, the volume capacity
      will have to be at least as big.
  """

  class BackupTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of backup, manually created or created by a backup
    policy.

    Values:
      TYPE_UNSPECIFIED: Unspecified backup type.
      MANUAL: Manual backup type.
      SCHEDULED: Scheduled backup type.
    """
    TYPE_UNSPECIFIED = 0
    MANUAL = 1
    SCHEDULED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The backup state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: Backup is being created. While in this state, the snapshot for
        the backup point-in-time may not have been created yet, and so the
        point-in-time may not have been fixed.
      UPLOADING: Backup is being uploaded. While in this state, none of the
        writes to the volume will be included in the backup.
      READY: Backup is available for use.
      DELETING: Backup is being deleted.
      ERROR: Backup is not valid and cannot be used for creating new volumes
        or restoring existing volumes.
      UPDATING: Backup is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    UPLOADING = 2
    READY = 3
    DELETING = 4
    ERROR = 5
    UPDATING = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupType = _messages.EnumField('BackupTypeValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  sourceSnapshot = _messages.StringField(6)
  sourceVolume = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  volumeUsageBytes = _messages.IntegerField(9)


class BackupConfig(_messages.Message):
  r"""BackupConfig contains backup related config on a volume.

  Fields:
    backupPolicies: Optional. When specified, schedule backups will be created
      based on the policy configuration.
    backupVault: Optional. Name of backup vault. Format:
      projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id
      }
    scheduledBackupEnabled: Optional. When set to true, scheduled backup is
      enabled on the volume.
  """

  backupPolicies = _messages.StringField(1, repeated=True)
  backupVault = _messages.StringField(2)
  scheduledBackupEnabled = _messages.BooleanField(3)


class BackupPolicy(_messages.Message):
  r"""Backup Policy.

  Enums:
    StateValueValuesEnum: Output only. The backup policy state.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata.

  Fields:
    assignedVolumeCount: Output only. The total number of volumes assigned by
      this backup policy.
    createTime: Output only. The time when the backup policy was created.
    dailyBackupLimit: Number of daily backups to keep. Note that the minimum
      daily backup limit is 2.
    description: Description of the backup policy.
    enabled: If enabled, make backups automatically according to the
      schedules. This will be applied to all volumes that have this policy
      attached and enforced on volume level. If not specified, default is
      true.
    labels: Resource labels to represent user provided metadata.
    monthlyBackupLimit: Number of monthly backups to keep. Note that the sum
      of daily, weekly and monthly backups should be greater than 1.
    name: Output only. The resource name of the backup policy. Format: `projec
      ts/{project_id}/locations/{location}/backupPolicies/{backupPolicy_id}`.
    state: Output only. The backup policy state.
    weeklyBackupLimit: Number of weekly backups to keep. Note that the sum of
      daily, weekly and monthly backups should be greater than 1.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The backup policy state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: BackupPolicy is being created.
      READY: BackupPolicy is available for use.
      DELETING: BackupPolicy is being deleted.
      ERROR: BackupPolicy is not valid and cannot be used.
      UPDATING: BackupPolicy is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    ERROR = 4
    UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assignedVolumeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  createTime = _messages.StringField(2)
  dailyBackupLimit = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  description = _messages.StringField(4)
  enabled = _messages.BooleanField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  monthlyBackupLimit = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  name = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  weeklyBackupLimit = _messages.IntegerField(10, variant=_messages.Variant.INT32)


class BackupVault(_messages.Message):
  r"""A NetApp BackupVault.

  Enums:
    StateValueValuesEnum: Output only. The backup vault state.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata.

  Fields:
    createTime: Output only. Create time of the backup vault.
    description: Description of the backup vault.
    labels: Resource labels to represent user provided metadata.
    name: Output only. The resource name of the backup vault. Format: `project
      s/{project_id}/locations/{location}/backupVaults/{backupVault_id}`.
    state: Output only. The backup vault state.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The backup vault state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: BackupVault is being created.
      READY: BackupVault is available for use.
      DELETING: BackupVault is being deleted.
      ERROR: BackupVault is not valid and cannot be used.
      UPDATING: BackupVault is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    ERROR = 4
    UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class DailySchedule(_messages.Message):
  r"""Make a snapshot every day e.g. at 04:00, 05:20, 23:50

  Fields:
    hour: Set the hour to start the snapshot (0-23), defaults to midnight (0).
    minute: Set the minute of the hour to start the snapshot (0-59), defaults
      to the top of the hour (0).
    snapshotsToKeep: The maximum number of Snapshots to keep for the hourly
      schedule
  """

  hour = _messages.FloatField(1)
  minute = _messages.FloatField(2)
  snapshotsToKeep = _messages.FloatField(3)


class DestinationVolumeParameters(_messages.Message):
  r"""DestinationVolumeParameters specify input parameters used for creating
  destination volume.

  Fields:
    description: Description for the destination volume.
    shareName: Destination volume's share name. If not specified, source
      volume's share name will be used.
    storagePool: Required. Existing destination StoragePool name.
    volumeId: Desired destination volume resource id. If not specified, source
      volume's resource id will be used. This value must start with a
      lowercase letter followed by up to 62 lowercase letters, numbers, or
      hyphens, and cannot end with a hyphen.
  """

  description = _messages.StringField(1)
  shareName = _messages.StringField(2)
  storagePool = _messages.StringField(3)
  volumeId = _messages.StringField(4)


class EncryptVolumesRequest(_messages.Message):
  r"""EncryptVolumesRequest specifies the KMS config to encrypt existing
  volumes.
  """



class ExportPolicy(_messages.Message):
  r"""Defines the export policy for the volume.

  Fields:
    rules: Required. List of export policy rules
  """

  rules = _messages.MessageField('SimpleExportPolicyRule', 1, repeated=True)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class HourlySchedule(_messages.Message):
  r"""Make a snapshot every hour e.g. at 04:00, 05:00, 06:00.

  Fields:
    minute: Set the minute of the hour to start the snapshot (0-59), defaults
      to the top of the hour (0).
    snapshotsToKeep: The maximum number of Snapshots to keep for the hourly
      schedule
  """

  minute = _messages.FloatField(1)
  snapshotsToKeep = _messages.FloatField(2)


class KmsConfig(_messages.Message):
  r"""KmsConfig is the customer managed encryption key(CMEK) configuration.

  Enums:
    StateValueValuesEnum: Output only. State of the KmsConfig.

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    createTime: Output only. Create time of the KmsConfig.
    cryptoKeyName: Required. Customer managed crypto key resource full name.
      Format: projects/{project}/locations/{location}/keyRings/{key_ring}/cryp
      toKeys/{key}.
    description: Description of the KmsConfig.
    instructions: Output only. Instructions to provide the access to the
      customer provided encryption key.
    labels: Labels as key value pairs
    name: Output only. Name of the KmsConfig.
    serviceAccount: Output only. The Service account which will have access to
      the customer provided encryption key.
    state: Output only. State of the KmsConfig.
    stateDetails: Output only. State details of the KmsConfig.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the KmsConfig.

    Values:
      STATE_UNSPECIFIED: Unspecified KmsConfig State
      READY: KmsConfig State is Ready
      CREATING: KmsConfig State is Creating
      DELETING: KmsConfig State is Deleting
      UPDATING: KmsConfig State is Updating
      IN_USE: KmsConfig State is In Use.
      ERROR: KmsConfig State is Error
      KEY_CHECK_PENDING: KmsConfig State is Pending to verify crypto key
        access.
      KEY_NOT_REACHABLE: KmsConfig State is Not accessbile by the SDE service
        account to the crypto key.
      DISABLING: KmsConfig State is Disabling.
      DISABLED: KmsConfig State is Disabled.
      MIGRATING: KmsConfig State is Migrating. The existing volumes are
        migrating from SMEK to CMEK.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    CREATING = 2
    DELETING = 3
    UPDATING = 4
    IN_USE = 5
    ERROR = 6
    KEY_CHECK_PENDING = 7
    KEY_NOT_REACHABLE = 8
    DISABLING = 9
    DISABLED = 10
    MIGRATING = 11

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  cryptoKeyName = _messages.StringField(2)
  description = _messages.StringField(3)
  instructions = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  serviceAccount = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  stateDetails = _messages.StringField(9)


class ListActiveDirectoriesResponse(_messages.Message):
  r"""ListActiveDirectoriesResponse contains all the active directories
  requested.

  Fields:
    activeDirectories: The list of active directories.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  activeDirectories = _messages.MessageField('ActiveDirectory', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupPoliciesResponse(_messages.Message):
  r"""ListBackupPoliciesResponse contains all the backup policies requested.

  Fields:
    backupPolicies: The list of backup policies.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  backupPolicies = _messages.MessageField('BackupPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupVaultsResponse(_messages.Message):
  r"""ListBackupVaultsResponse is the result of ListBackupVaultsRequest.

  Fields:
    backupVaults: A list of backupVaults in the project for the specified
      location.
    nextPageToken: The token you can use to retrieve the next page of results.
      Not returned if there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  backupVaults = _messages.MessageField('BackupVault', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupsResponse(_messages.Message):
  r"""ListBackupsResponse is the result of ListBackupsRequest.

  Fields:
    backups: A list of backups in the project.
    nextPageToken: The token you can use to retrieve the next page of results.
      Not returned if there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListKmsConfigsResponse(_messages.Message):
  r"""ListKmsConfigsResponse is the response to a ListKmsConfigsRequest.

  Fields:
    kmsConfigs: The list of KmsConfigs
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  kmsConfigs = _messages.MessageField('KmsConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReplicationsResponse(_messages.Message):
  r"""ListReplicationsResponse is the result of ListReplicationsRequest.

  Fields:
    nextPageToken: The token you can use to retrieve the next page of results.
      Not returned if there are no more results in the list.
    replications: A list of replications in the project for the specified
      volume.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  replications = _messages.MessageField('Replication', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSnapshotsResponse(_messages.Message):
  r"""ListSnapshotsResponse is the result of ListSnapshotsRequest.

  Fields:
    nextPageToken: The token you can use to retrieve the next page of results.
      Not returned if there are no more results in the list.
    snapshots: A list of snapshots in the project for the specified volume.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  snapshots = _messages.MessageField('Snapshot', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListStoragePoolsResponse(_messages.Message):
  r"""ListStoragePoolsResponse is the response to a ListStoragePoolsRequest.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    storagePools: The list of StoragePools
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  storagePools = _messages.MessageField('StoragePool', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListVolumesResponse(_messages.Message):
  r"""Message for response to listing Volumes

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
    volumes: The list of Volume
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  volumes = _messages.MessageField('Volume', 3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MonthlySchedule(_messages.Message):
  r"""Make a snapshot once a month e.g. at 2nd 04:00, 7th 05:20, 24th 23:50

  Fields:
    daysOfMonth: Set the day or days of the month to make a snapshot (1-31).
      Accepts a comma separated number of days. Defaults to '1'.
    hour: Set the hour to start the snapshot (0-23), defaults to midnight (0).
    minute: Set the minute of the hour to start the snapshot (0-59), defaults
      to the top of the hour (0).
    snapshotsToKeep: The maximum number of Snapshots to keep for the hourly
      schedule
  """

  daysOfMonth = _messages.StringField(1)
  hour = _messages.FloatField(2)
  minute = _messages.FloatField(3)
  snapshotsToKeep = _messages.FloatField(4)


class MountOption(_messages.Message):
  r"""View only mount options for a volume.

  Enums:
    ProtocolValueValuesEnum: Protocol to mount with.

  Fields:
    export: Export string
    exportFull: Full export string
    instructions: Instructions for mounting
    protocol: Protocol to mount with.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Protocol to mount with.

    Values:
      PROTOCOLS_UNSPECIFIED: Unspecified protocol
      NFSV3: NFS V3 protocol
      NFSV4: NFS V4 protocol
      SMB: SMB protocol
    """
    PROTOCOLS_UNSPECIFIED = 0
    NFSV3 = 1
    NFSV4 = 2
    SMB = 3

  export = _messages.StringField(1)
  exportFull = _messages.StringField(2)
  instructions = _messages.StringField(3)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 4)


class NetappProjectsLocationsActiveDirectoriesCreateRequest(_messages.Message):
  r"""A NetappProjectsLocationsActiveDirectoriesCreateRequest object.

  Fields:
    activeDirectory: A ActiveDirectory resource to be passed as the request
      body.
    activeDirectoryId: Required. ID of the active directory to create.
    parent: Required. Value for parent.
  """

  activeDirectory = _messages.MessageField('ActiveDirectory', 1)
  activeDirectoryId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetappProjectsLocationsActiveDirectoriesDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsActiveDirectoriesDeleteRequest object.

  Fields:
    name: Required. Name of the active directory.
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsActiveDirectoriesGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsActiveDirectoriesGetRequest object.

  Fields:
    name: Required. Name of the active directory.
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsActiveDirectoriesListRequest(_messages.Message):
  r"""A NetappProjectsLocationsActiveDirectoriesListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, the server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListActiveDirectoriesRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetappProjectsLocationsActiveDirectoriesPatchRequest(_messages.Message):
  r"""A NetappProjectsLocationsActiveDirectoriesPatchRequest object.

  Fields:
    activeDirectory: A ActiveDirectory resource to be passed as the request
      body.
    name: Output only. The resource name of the active directory. Format: `pro
      jects/{project_number}/locations/{location_id}/activeDirectories/{active
      _directory_id}`.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Active Directory resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  activeDirectory = _messages.MessageField('ActiveDirectory', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetappProjectsLocationsBackupPoliciesCreateRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupPoliciesCreateRequest object.

  Fields:
    backupPolicy: A BackupPolicy resource to be passed as the request body.
    backupPolicyId: Required. The ID to use for the backup policy. The ID must
      be unique within the specified location. This value must start with a
      lowercase letter followed by up to 62 lowercase letters, numbers, or
      hyphens, and cannot end with a hyphen.
    parent: Required. The location to create the backup policies of, in the
      format `projects/{project_id}/locations/{location}`
  """

  backupPolicy = _messages.MessageField('BackupPolicy', 1)
  backupPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetappProjectsLocationsBackupPoliciesDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupPoliciesDeleteRequest object.

  Fields:
    name: Required. The backup policy resource name, in the format `projects/{
      project_id}/locations/{location}/backupPolicies/{backup_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsBackupPoliciesGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupPoliciesGetRequest object.

  Fields:
    name: Required. The backupPolicy resource name, in the format `projects/{p
      roject_id}/locations/{location}/backupPolicies/{backup_policy_id}`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsBackupPoliciesListRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupPoliciesListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, the server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListBackupPoliciesRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetappProjectsLocationsBackupPoliciesPatchRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupPoliciesPatchRequest object.

  Fields:
    backupPolicy: A BackupPolicy resource to be passed as the request body.
    name: Output only. The resource name of the backup policy. Format: `projec
      ts/{project_id}/locations/{location}/backupPolicies/{backupPolicy_id}`.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Backup Policy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  backupPolicy = _messages.MessageField('BackupPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetappProjectsLocationsBackupVaultsBackupsCreateRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsBackupsCreateRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    backupId: Required. The ID to use for the backup. The ID must be unique
      within the specified backupVault. This value must start with a lowercase
      letter followed by up to 62 lowercase letters, numbers, or hyphens, and
      cannot end with a hyphen. Values that do not match this pattern will
      trigger an INVALID_ARGUMENT error.
    parent: Required. The NetApp backupVault to create the backups of, in the
      format `projects/*/locations/*/backupVaults/{backup_vault_id}`
  """

  backup = _messages.MessageField('Backup', 1)
  backupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetappProjectsLocationsBackupVaultsBackupsDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsBackupsDeleteRequest object.

  Fields:
    name: Required. The backup resource name, in the format `projects/{project
      _id}/locations/{location}/backupVaults/{backup_vault_id}/backups/{backup
      _id}`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsBackupVaultsBackupsGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsBackupsGetRequest object.

  Fields:
    name: Required. The backup resource name, in the format `projects/{project
      _id}/locations/{location}/backupVaults/{backup_vault_id}/backups/{backup
      _id}`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsBackupVaultsBackupsListRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsBackupsListRequest object.

  Fields:
    filter: The standard list filter. If specified, backups will be returned
      based on the attribute name that matches the filter expression. If
      empty, then no backups are filtered out. See https://google.aip.dev/160
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return. The service may return
      fewer than this value. The maximum value is 1000; values above 1000 will
      be coerced to 1000.
    pageToken: The next_page_token value to use if there are additional
      results to retrieve for this list request.
    parent: Required. The backupVault for which to retrieve backup
      information, in the format `projects/{project_id}/locations/{location}/b
      ackupVaults/{backup_vault_id}`. To retrieve backup information for all
      locations, use "-" for the `{location}` value. To retrieve backup
      information for all backupVaults, use "-" for the `{backup_vault_id}`
      value. To retrieve backup information for a volume, use "-" for the
      `{backup_vault_id}` value and specify volume full name with the filter.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetappProjectsLocationsBackupVaultsBackupsPatchRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsBackupsPatchRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    name: Output only. The resource name of the backup. Format: `projects/{pro
      ject_id}/locations/{location}/backupVaults/{backup_vault_id}/backups/{ba
      ckup_id}`.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Backup resource to be updated. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  backup = _messages.MessageField('Backup', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetappProjectsLocationsBackupVaultsCreateRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsCreateRequest object.

  Fields:
    backupVault: A BackupVault resource to be passed as the request body.
    backupVaultId: Required. The ID to use for the backupVault. The ID must be
      unique within the specified location. The max supported length is 63
      characters. This value must start with a lowercase letter followed by up
      to 62 lowercase letters, numbers, or hyphens, and cannot end with a
      hyphen. Values that do not match this pattern will trigger an
      INVALID_ARGUMENT error.
    parent: Required. The location to create the backup vaults, in the format
      `projects/{project_id}/locations/{location}`
  """

  backupVault = _messages.MessageField('BackupVault', 1)
  backupVaultId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetappProjectsLocationsBackupVaultsDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsDeleteRequest object.

  Fields:
    name: Required. The backupVault resource name, in the format `projects/{pr
      oject_id}/locations/{location}/backupVaults/{backup_vault_id}`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsBackupVaultsGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsGetRequest object.

  Fields:
    name: Required. The backupVault resource name, in the format
      `projects/{project_id}/locations/{location}/backupVaults/{backupVault_id
      }`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsBackupVaultsListRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value to use if there are additional
      results to retrieve for this list request.
    parent: Required. The location for which to retrieve backupVault
      information, in the format `projects/{project_id}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetappProjectsLocationsBackupVaultsPatchRequest(_messages.Message):
  r"""A NetappProjectsLocationsBackupVaultsPatchRequest object.

  Fields:
    backupVault: A BackupVault resource to be passed as the request body.
    name: Output only. The resource name of the backup vault. Format: `project
      s/{project_id}/locations/{location}/backupVaults/{backupVault_id}`.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Backup resource to be updated. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  backupVault = _messages.MessageField('BackupVault', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetappProjectsLocationsGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsKmsConfigsCreateRequest(_messages.Message):
  r"""A NetappProjectsLocationsKmsConfigsCreateRequest object.

  Fields:
    kmsConfig: A KmsConfig resource to be passed as the request body.
    kmsConfigId: Required. Id of the requesting KmsConfig If auto-generating
      Id server-side, remove this field and id from the method_signature of
      Create RPC
    parent: Required. Value for parent.
  """

  kmsConfig = _messages.MessageField('KmsConfig', 1)
  kmsConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetappProjectsLocationsKmsConfigsDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsKmsConfigsDeleteRequest object.

  Fields:
    name: Required. Name of the KmsConfig.
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsKmsConfigsEncryptRequest(_messages.Message):
  r"""A NetappProjectsLocationsKmsConfigsEncryptRequest object.

  Fields:
    encryptVolumesRequest: A EncryptVolumesRequest resource to be passed as
      the request body.
    name: Required. Name of the KmsConfig.
  """

  encryptVolumesRequest = _messages.MessageField('EncryptVolumesRequest', 1)
  name = _messages.StringField(2, required=True)


class NetappProjectsLocationsKmsConfigsGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsKmsConfigsGetRequest object.

  Fields:
    name: Required. Name of the KmsConfig
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsKmsConfigsListRequest(_messages.Message):
  r"""A NetappProjectsLocationsKmsConfigsListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value to use if there are additional
      results to retrieve for this list request.
    parent: Required. Parent value
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetappProjectsLocationsKmsConfigsPatchRequest(_messages.Message):
  r"""A NetappProjectsLocationsKmsConfigsPatchRequest object.

  Fields:
    kmsConfig: A KmsConfig resource to be passed as the request body.
    name: Output only. Name of the KmsConfig.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the KmsConfig resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  kmsConfig = _messages.MessageField('KmsConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetappProjectsLocationsKmsConfigsVerifyRequest(_messages.Message):
  r"""A NetappProjectsLocationsKmsConfigsVerifyRequest object.

  Fields:
    name: Required. Name of the KMS Config to be verified.
    verifyKmsConfigRequest: A VerifyKmsConfigRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  verifyKmsConfigRequest = _messages.MessageField('VerifyKmsConfigRequest', 2)


class NetappProjectsLocationsListRequest(_messages.Message):
  r"""A NetappProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetappProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NetappProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetappProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NetappProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetappProjectsLocationsStoragePoolsCreateRequest(_messages.Message):
  r"""A NetappProjectsLocationsStoragePoolsCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    storagePool: A StoragePool resource to be passed as the request body.
    storagePoolId: Required. Id of the requesting storage pool If auto-
      generating Id server-side, remove this field and id from the
      method_signature of Create RPC
  """

  parent = _messages.StringField(1, required=True)
  storagePool = _messages.MessageField('StoragePool', 2)
  storagePoolId = _messages.StringField(3)


class NetappProjectsLocationsStoragePoolsDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsStoragePoolsDeleteRequest object.

  Fields:
    name: Required. Name of the storage pool
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsStoragePoolsGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsStoragePoolsGetRequest object.

  Fields:
    name: Required. Name of the storage pool
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsStoragePoolsListRequest(_messages.Message):
  r"""A NetappProjectsLocationsStoragePoolsListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value to use if there are additional
      results to retrieve for this list request.
    parent: Required. Parent value
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetappProjectsLocationsStoragePoolsPatchRequest(_messages.Message):
  r"""A NetappProjectsLocationsStoragePoolsPatchRequest object.

  Fields:
    name: Output only. Name of the storage pool
    storagePool: A StoragePool resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the StoragePool resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  storagePool = _messages.MessageField('StoragePool', 2)
  updateMask = _messages.StringField(3)


class NetappProjectsLocationsVolumesCreateRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    volume: A Volume resource to be passed as the request body.
    volumeId: Required. Id of the requesting volume If auto-generating Id
      server-side, remove this field and Id from the method_signature of
      Create RPC
  """

  parent = _messages.StringField(1, required=True)
  volume = _messages.MessageField('Volume', 2)
  volumeId = _messages.StringField(3)


class NetappProjectsLocationsVolumesDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesDeleteRequest object.

  Fields:
    force: If this field is set as true, CCFE will not block the volume
      resource deletion even if it has any snapshots resource. (Otherwise, the
      request will only work if the volume has no snapshots.)
    name: Required. Name of the volume
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class NetappProjectsLocationsVolumesGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesGetRequest object.

  Fields:
    name: Required. Name of the volume
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsVolumesListRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, the server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListVolumesRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetappProjectsLocationsVolumesPatchRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesPatchRequest object.

  Fields:
    name: Output only. Name of the volume
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Volume resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    volume: A Volume resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  volume = _messages.MessageField('Volume', 3)


class NetappProjectsLocationsVolumesReplicationsCreateRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesReplicationsCreateRequest object.

  Fields:
    parent: Required. The NetApp volume to create the replications of, in the
      format `projects/{project_id}/locations/{location}/volumes/{volume_id}`
    replication: A Replication resource to be passed as the request body.
    replicationId: Required. ID of the replication to create. This value must
      start with a lowercase letter followed by up to 62 lowercase letters,
      numbers, or hyphens, and cannot end with a hyphen.
  """

  parent = _messages.StringField(1, required=True)
  replication = _messages.MessageField('Replication', 2)
  replicationId = _messages.StringField(3)


class NetappProjectsLocationsVolumesReplicationsDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesReplicationsDeleteRequest object.

  Fields:
    name: Required. The replication resource name, in the format
      `projects/*/locations/*/volumes/*/replications/{replication_id}`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsVolumesReplicationsGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesReplicationsGetRequest object.

  Fields:
    name: Required. The replication resource name, in the format `projects/{pr
      oject_id}/locations/{location}/volumes/{volume_id}/replications/{replica
      tion_id}`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsVolumesReplicationsListRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesReplicationsListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value to use if there are additional
      results to retrieve for this list request.
    parent: Required. The volume for which to retrieve replication
      information, in the format
      `projects/{project_id}/locations/{location}/volumes/{volume_id}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetappProjectsLocationsVolumesReplicationsPatchRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesReplicationsPatchRequest object.

  Fields:
    name: Output only. The resource name of the Replication. Format: `projects
      /{project_id}/locations/{location}/volumes/{volume_id}/replications/{rep
      lication_id}`.
    replication: A Replication resource to be passed as the request body.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field.
  """

  name = _messages.StringField(1, required=True)
  replication = _messages.MessageField('Replication', 2)
  updateMask = _messages.StringField(3)


class NetappProjectsLocationsVolumesReplicationsResumeRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesReplicationsResumeRequest object.

  Fields:
    name: Required. The resource name of the replication, in the format of pro
      jects/{project_id}/locations/{location}/volumes/{volume_id}/replications
      /{replication_id}.
    resumeReplicationRequest: A ResumeReplicationRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  resumeReplicationRequest = _messages.MessageField('ResumeReplicationRequest', 2)


class NetappProjectsLocationsVolumesReplicationsReverseDirectionRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesReplicationsReverseDirectionRequest
  object.

  Fields:
    name: Required. The resource name of the replication, in the format of pro
      jects/{project_id}/locations/{location}/volumes/{volume_id}/replications
      /{replication_id}.
    reverseReplicationDirectionRequest: A ReverseReplicationDirectionRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  reverseReplicationDirectionRequest = _messages.MessageField('ReverseReplicationDirectionRequest', 2)


class NetappProjectsLocationsVolumesReplicationsStopRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesReplicationsStopRequest object.

  Fields:
    name: Required. The resource name of the replication, in the format of pro
      jects/{project_id}/locations/{location}/volumes/{volume_id}/replications
      /{replication_id}.
    stopReplicationRequest: A StopReplicationRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  stopReplicationRequest = _messages.MessageField('StopReplicationRequest', 2)


class NetappProjectsLocationsVolumesRevertRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesRevertRequest object.

  Fields:
    name: Required. The resource name of the volume, in the format of
      projects/{project_id}/locations/{location}/volumes/{volume_id}.
    revertVolumeRequest: A RevertVolumeRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  revertVolumeRequest = _messages.MessageField('RevertVolumeRequest', 2)


class NetappProjectsLocationsVolumesSnapshotsCreateRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesSnapshotsCreateRequest object.

  Fields:
    parent: Required. The NetApp volume to create the snapshots of, in the
      format `projects/{project_id}/locations/{location}/volumes/{volume_id}`
    snapshot: A Snapshot resource to be passed as the request body.
    snapshotId: Required. ID of the snapshot to create. This value must start
      with a lowercase letter followed by up to 62 lowercase letters, numbers,
      or hyphens, and cannot end with a hyphen.
  """

  parent = _messages.StringField(1, required=True)
  snapshot = _messages.MessageField('Snapshot', 2)
  snapshotId = _messages.StringField(3)


class NetappProjectsLocationsVolumesSnapshotsDeleteRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesSnapshotsDeleteRequest object.

  Fields:
    name: Required. The snapshot resource name, in the format
      `projects/*/locations/*/volumes/*/snapshots/{snapshot_id}`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsVolumesSnapshotsGetRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesSnapshotsGetRequest object.

  Fields:
    name: Required. The snapshot resource name, in the format `projects/{proje
      ct_id}/locations/{location}/volumes/{volume_id}/snapshots/{snapshot_id}`
  """

  name = _messages.StringField(1, required=True)


class NetappProjectsLocationsVolumesSnapshotsListRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesSnapshotsListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value to use if there are additional
      results to retrieve for this list request.
    parent: Required. The volume for which to retrieve snapshot information,
      in the format
      `projects/{project_id}/locations/{location}/volumes/{volume_id}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetappProjectsLocationsVolumesSnapshotsPatchRequest(_messages.Message):
  r"""A NetappProjectsLocationsVolumesSnapshotsPatchRequest object.

  Fields:
    name: Output only. The resource name of the snapshot. Format: `projects/{p
      roject_id}/locations/{location}/volumes/{volume_id}/snapshots/{snapshot_
      id}`.
    snapshot: A Snapshot resource to be passed as the request body.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field.
  """

  name = _messages.StringField(1, required=True)
  snapshot = _messages.MessageField('Snapshot', 2)
  updateMask = _messages.StringField(3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      canceled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Replication(_messages.Message):
  r"""Replication is a nested resource under Volume, that describes a cross-
  region replication relationship between 2 volumes in different regions.

  Enums:
    MirrorStateValueValuesEnum: Output only. Indicates the state of mirroring.
    ReplicationScheduleValueValuesEnum: Required. Indicates the schedule for
      replication.
    RoleValueValuesEnum: Output only. Indicates whether this points to source
      or destination.
    StateValueValuesEnum: Output only. State of the replication.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata.

  Fields:
    createTime: Output only. Replication create time.
    description: A description about this replication relationship.
    destinationVolume: Output only. Full name of destination volume resource.
      Example : "projects/{project}/locations/{location}/volumes/{volume_id}"
    destinationVolumeParameters: Required. Input only. Destination volume
      parameters
    healthy: Output only. Condition of the relationship. Can be one of the
      following: - true: The replication relationship is healthy. It has not
      missed the most recent scheduled transfer. - false: The replication
      relationship is not healthy. It has missed the most recent scheduled
      transfer.
    labels: Resource labels to represent user provided metadata.
    mirrorState: Output only. Indicates the state of mirroring.
    name: Output only. The resource name of the Replication. Format: `projects
      /{project_id}/locations/{location}/volumes/{volume_id}/replications/{rep
      lication_id}`.
    replicationSchedule: Required. Indicates the schedule for replication.
    role: Output only. Indicates whether this points to source or destination.
    sourceVolume: Output only. Full name of source volume resource. Example :
      "projects/{project}/locations/{location}/volumes/{volume_id}"
    state: Output only. State of the replication.
    stateDetails: Output only. State details of the replication.
    transferStats: Output only. Replication transfer statistics.
  """

  class MirrorStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates the state of mirroring.

    Values:
      MIRROR_STATE_UNSPECIFIED: Unspecified MirrorState
      PREPARING: Destination volume is being prepared.
      MIRRORED: Destination volume has been initialized and is ready to
        receive replication transfers.
      STOPPED: Destination volume is not receiving replication transfers.
      TRANSFERRING: Replication is in progress.
    """
    MIRROR_STATE_UNSPECIFIED = 0
    PREPARING = 1
    MIRRORED = 2
    STOPPED = 3
    TRANSFERRING = 4

  class ReplicationScheduleValueValuesEnum(_messages.Enum):
    r"""Required. Indicates the schedule for replication.

    Values:
      REPLICATION_SCHEDULE_UNSPECIFIED: Unspecified ReplicationSchedule
      EVERY_10_MINUTES: Replication happens once every 10 minutes.
      HOURLY: Replication happens once every hour.
      DAILY: Replication happens once every day.
    """
    REPLICATION_SCHEDULE_UNSPECIFIED = 0
    EVERY_10_MINUTES = 1
    HOURLY = 2
    DAILY = 3

  class RoleValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether this points to source or destination.

    Values:
      REPLICATION_ROLE_UNSPECIFIED: Unspecified replication role
      SOURCE: Indicates Source volume.
      DESTINATION: Indicates Destination volume.
    """
    REPLICATION_ROLE_UNSPECIFIED = 0
    SOURCE = 1
    DESTINATION = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the replication.

    Values:
      STATE_UNSPECIFIED: Unspecified replication State
      CREATING: Replication is creating.
      READY: Replication is ready.
      UPDATING: Replication is updating.
      DELETING: Replication is deleting.
      ERROR: Replication is in error state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    DELETING = 4
    ERROR = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  destinationVolume = _messages.StringField(3)
  destinationVolumeParameters = _messages.MessageField('DestinationVolumeParameters', 4)
  healthy = _messages.BooleanField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  mirrorState = _messages.EnumField('MirrorStateValueValuesEnum', 7)
  name = _messages.StringField(8)
  replicationSchedule = _messages.EnumField('ReplicationScheduleValueValuesEnum', 9)
  role = _messages.EnumField('RoleValueValuesEnum', 10)
  sourceVolume = _messages.StringField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  stateDetails = _messages.StringField(13)
  transferStats = _messages.MessageField('TransferStats', 14)


class RestoreParameters(_messages.Message):
  r"""The RestoreParameters if volume is created from a snapshot or backup.

  Fields:
    sourceBackup: Full name of the backup resource. Format: projects/{project}
      /locations/{location}/backupVaults/{backup_vault_id}/backups/{backup_id}
    sourceSnapshot: Full name of the snapshot resource. Format: projects/{proj
      ect}/locations/{location}/volumes/{volume}/snapshots/{snapshot}
  """

  sourceBackup = _messages.StringField(1)
  sourceSnapshot = _messages.StringField(2)


class ResumeReplicationRequest(_messages.Message):
  r"""ResumeReplicationRequest resumes a stopped replication."""


class ReverseReplicationDirectionRequest(_messages.Message):
  r"""ReverseReplicationDirectionRequest reverses direction of replication.
  Source becomes destination and destination becomes source.
  """



class RevertVolumeRequest(_messages.Message):
  r"""RevertVolumeRequest reverts the given volume to the specified snapshot.

  Fields:
    snapshotId: Required. The snapshot resource ID, in the format 'my-
      snapshot', where the specified ID is the {snapshot_id} of the fully
      qualified name like projects/{project_id}/locations/{location_id}/volume
      s/{volume_id}/snapshots/{snapshot_id}
  """

  snapshotId = _messages.StringField(1)


class SimpleExportPolicyRule(_messages.Message):
  r"""An export policy rule describing various export options.

  Enums:
    AccessTypeValueValuesEnum: Access type (ReadWrite, ReadOnly, None)

  Fields:
    accessType: Access type (ReadWrite, ReadOnly, None)
    allowedClients: Comma separated list of allowed clients IP addresses
    hasRootAccess: Whether Unix root access will be granted.
    kerberos5ReadOnly: If enabled (true) the rule defines a read only access
      for clients matching the 'allowedClients' specification. It enables nfs
      clients to mount using 'authentication' kerberos security mode.
    kerberos5ReadWrite: If enabled (true) the rule defines read and write
      access for clients matching the 'allowedClients' specification. It
      enables nfs clients to mount using 'authentication' kerberos security
      mode. The 'kerberos5ReadOnly' value be ignored if this is enabled.
    kerberos5iReadOnly: If enabled (true) the rule defines a read only access
      for clients matching the 'allowedClients' specification. It enables nfs
      clients to mount using 'integrity' kerberos security mode.
    kerberos5iReadWrite: If enabled (true) the rule defines read and write
      access for clients matching the 'allowedClients' specification. It
      enables nfs clients to mount using 'integrity' kerberos security mode.
      The 'kerberos5iReadOnly' value be ignored if this is enabled.
    kerberos5pReadOnly: If enabled (true) the rule defines a read only access
      for clients matching the 'allowedClients' specification. It enables nfs
      clients to mount using 'privacy' kerberos security mode.
    kerberos5pReadWrite: If enabled (true) the rule defines read and write
      access for clients matching the 'allowedClients' specification. It
      enables nfs clients to mount using 'privacy' kerberos security mode. The
      'kerberos5pReadOnly' value be ignored if this is enabled.
    nfsv3: NFS V3 protocol.
    nfsv4: NFS V4 protocol.
  """

  class AccessTypeValueValuesEnum(_messages.Enum):
    r"""Access type (ReadWrite, ReadOnly, None)

    Values:
      ACCESS_TYPE_UNSPECIFIED: Unspecified Access Type
      READ_ONLY: Read Only
      READ_WRITE: Read Write
      READ_NONE: None
    """
    ACCESS_TYPE_UNSPECIFIED = 0
    READ_ONLY = 1
    READ_WRITE = 2
    READ_NONE = 3

  accessType = _messages.EnumField('AccessTypeValueValuesEnum', 1)
  allowedClients = _messages.StringField(2)
  hasRootAccess = _messages.StringField(3)
  kerberos5ReadOnly = _messages.BooleanField(4)
  kerberos5ReadWrite = _messages.BooleanField(5)
  kerberos5iReadOnly = _messages.BooleanField(6)
  kerberos5iReadWrite = _messages.BooleanField(7)
  kerberos5pReadOnly = _messages.BooleanField(8)
  kerberos5pReadWrite = _messages.BooleanField(9)
  nfsv3 = _messages.BooleanField(10)
  nfsv4 = _messages.BooleanField(11)


class Snapshot(_messages.Message):
  r"""Snapshot is a point-in-time version of a Volume's content.

  Enums:
    StateValueValuesEnum: Output only. The snapshot state.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata.

  Fields:
    createTime: Output only. The time when the snapshot was created.
    description: A description of the snapshot with 2048 characters or less.
      Requests with longer descriptions will be rejected.
    labels: Resource labels to represent user provided metadata.
    name: Output only. The resource name of the snapshot. Format: `projects/{p
      roject_id}/locations/{location}/volumes/{volume_id}/snapshots/{snapshot_
      id}`.
    state: Output only. The snapshot state.
    stateDetails: Output only. State details of the storage pool
    usedBytes: Output only. Current storage usage for the snapshot in bytes.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The snapshot state.

    Values:
      STATE_UNSPECIFIED: Unspecified Snapshot State
      READY: Snapshot State is Ready
      CREATING: Snapshot State is Creating
      DELETING: Snapshot State is Deleting
      UPDATING: Snapshot State is Updating
      DISABLED: Snapshot State is Disabled
      ERROR: Snapshot State is Error
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    CREATING = 2
    DELETING = 3
    UPDATING = 4
    DISABLED = 5
    ERROR = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  stateDetails = _messages.StringField(6)
  usedBytes = _messages.FloatField(7)


class SnapshotPolicy(_messages.Message):
  r"""Snapshot Policy for a volume.

  Fields:
    dailySchedule: Daily schedule policy.
    enabled: If enabled, make snapshots automatically according to the
      schedules. Default is false.
    hourlySchedule: Hourly schedule policy.
    monthlySchedule: Monthly schedule policy.
    weeklySchedule: Weekly schedule policy.
  """

  dailySchedule = _messages.MessageField('DailySchedule', 1)
  enabled = _messages.BooleanField(2)
  hourlySchedule = _messages.MessageField('HourlySchedule', 3)
  monthlySchedule = _messages.MessageField('MonthlySchedule', 4)
  weeklySchedule = _messages.MessageField('WeeklySchedule', 5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopReplicationRequest(_messages.Message):
  r"""StopReplicationRequest stops a replication until resumed.

  Fields:
    force: Indicates whether to stop replication forcefully while data
      transfer is in progress. Warning! if force is true, this will abort any
      current transfers and can lead to data loss due to partial transfer. If
      force is false, stop replication will fail while data transfer is in
      progress and you will need to retry later.
  """

  force = _messages.BooleanField(1)


class StoragePool(_messages.Message):
  r"""StoragePool is a container for volumes with a service level and
  capacity. Volumes can be created in a pool of sufficient available capacity.
  StoragePool capacity is what you are billed for.

  Enums:
    EncryptionTypeValueValuesEnum: Output only. Specifies the current pool
      encryption key source.
    ServiceLevelValueValuesEnum: Required. Service level of the storage pool
    StateValueValuesEnum: Output only. State of the storage pool

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    activeDirectory: Specifies the Active Directory to be used for creating a
      SMB volume.
    capacityGib: Required. Capacity in GIB of the pool
    createTime: Output only. Create time of the storage pool
    description: Description of the storage pool
    encryptionType: Output only. Specifies the current pool encryption key
      source.
    globalAccessAllowed: Optional. Allows SO pool to access AD or DNS server
      from other regions.
    kmsConfig: Specifies the KMS config to be used for volume encryption.
    labels: Labels as key value pairs
    ldapEnabled: Flag indicating if the pool is NFS LDAP enabled or not.
    name: Output only. Name of the storage pool
    network: Required. VPC Network name. Format:
      projects/{project}/global/networks/{network}
    psaRange: Name of the Private Service Access allocated range. If not
      provided, any available range will be chosen.
    serviceLevel: Required. Service level of the storage pool
    state: Output only. State of the storage pool
    stateDetails: Output only. State details of the storage pool
    volumeCapacityGib: Output only. Allocated size of all volumes in GIB in
      the storage pool
    volumeCount: Output only. Volume count of the storage pool
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Specifies the current pool encryption key source.

    Values:
      ENCRYPTION_TYPE_UNSPECIFIED: The source of encryption key is not
        specified.
      SERVICE_MANAGED: Google managed encryption key.
      CLOUD_KMS: Customer managed encryption key, which is stored in KMS.
    """
    ENCRYPTION_TYPE_UNSPECIFIED = 0
    SERVICE_MANAGED = 1
    CLOUD_KMS = 2

  class ServiceLevelValueValuesEnum(_messages.Enum):
    r"""Required. Service level of the storage pool

    Values:
      SERVICE_LEVEL_UNSPECIFIED: <no description>
      PREMIUM: <no description>
      EXTREME: <no description>
      STANDARD: Standard (Software offering)
    """
    SERVICE_LEVEL_UNSPECIFIED = 0
    PREMIUM = 1
    EXTREME = 2
    STANDARD = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the storage pool

    Values:
      STATE_UNSPECIFIED: Unspecified Storage Pool State
      READY: Storage Pool State is Ready
      CREATING: Storage Pool State is Creating
      DELETING: Storage Pool State is Deleting
      UPDATING: Storage Pool State is Updating
      RESTORING: Storage Pool State is Restoring
      DISABLED: Storage Pool State is Disabled
      ERROR: Storage Pool State is Error
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    CREATING = 2
    DELETING = 3
    UPDATING = 4
    RESTORING = 5
    DISABLED = 6
    ERROR = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activeDirectory = _messages.StringField(1)
  capacityGib = _messages.IntegerField(2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 5)
  globalAccessAllowed = _messages.BooleanField(6)
  kmsConfig = _messages.StringField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  ldapEnabled = _messages.BooleanField(9)
  name = _messages.StringField(10)
  network = _messages.StringField(11)
  psaRange = _messages.StringField(12)
  serviceLevel = _messages.EnumField('ServiceLevelValueValuesEnum', 13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  stateDetails = _messages.StringField(15)
  volumeCapacityGib = _messages.IntegerField(16)
  volumeCount = _messages.IntegerField(17, variant=_messages.Variant.INT32)


class TransferStats(_messages.Message):
  r"""TransferStats reports all statistics related to replication transfer.

  Fields:
    lagDuration: Lag duration indicates the duration by which Destination
      region volume content lags behind the primary region volume content.
    lastTransferBytes: Last transfer size in bytes.
    lastTransferDuration: Time taken during last transfer.
    lastTransferEndTime: Time when last transfer completed.
    lastTransferError: A message describing the cause of the last transfer
      failure.
    totalTransferDuration: Total time taken during transfer.
    transferBytes: bytes trasferred so far in current transfer.
    updateTime: Time when progress was updated last.
  """

  lagDuration = _messages.StringField(1)
  lastTransferBytes = _messages.IntegerField(2)
  lastTransferDuration = _messages.StringField(3)
  lastTransferEndTime = _messages.StringField(4)
  lastTransferError = _messages.StringField(5)
  totalTransferDuration = _messages.StringField(6)
  transferBytes = _messages.IntegerField(7)
  updateTime = _messages.StringField(8)


class VerifyKmsConfigRequest(_messages.Message):
  r"""VerifyKmsConfigRequest specifies the KMS config to be validated."""


class VerifyKmsConfigResponse(_messages.Message):
  r"""VerifyKmsConfigResponse contains the information if the config is
  correctly and error message.

  Fields:
    healthError: Output only. Error message if config is not healthy.
    healthy: Output only. If the customer key configured correctly to the
      encrypt volume.
    instructions: Output only. Instructions for the customers to provide the
      access to the encryption key.
  """

  healthError = _messages.StringField(1)
  healthy = _messages.BooleanField(2)
  instructions = _messages.StringField(3)


class Volume(_messages.Message):
  r"""Volume provides a filesystem that you can mount.

  Enums:
    EncryptionTypeValueValuesEnum: Output only. Specified the current volume
      encryption key source.
    ProtocolsValueListEntryValuesEnum:
    RestrictedActionsValueListEntryValuesEnum:
    SecurityStyleValueValuesEnum: Optional. Security Style of the Volume
    ServiceLevelValueValuesEnum: Output only. Service level of the volume
    SmbSettingsValueListEntryValuesEnum:
    StateValueValuesEnum: Output only. State of the volume

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    activeDirectory: Output only. Specifies the ActiveDirectory name of a SMB
      volume.
    backupConfig: BackupConfig of the volume.
    capacityGib: Required. Capacity in GIB of the volume
    createTime: Output only. Create time of the volume
    description: Optional. Description of the volume
    encryptionType: Output only. Specified the current volume encryption key
      source.
    exportPolicy: Optional. Export policy of the volume
    hasReplication: Output only. Indicates whether the volume is part of a
      replication relationship.
    kerberosEnabled: Optional. Flag indicating if the volume is a kerberos
      volume or not, export policy rules control kerberos security modes
      (krb5, krb5i, krb5p).
    kmsConfig: Output only. Specifies the KMS config to be used for volume
      encryption.
    labels: Optional. Labels as key value pairs
    ldapEnabled: Output only. Flag indicating if the volume is NFS LDAP
      enabled or not.
    mountOptions: Output only. Mount options of this volume
    name: Output only. Name of the volume
    network: Output only. VPC Network name. Format:
      projects/{project}/global/networks/{network}
    protocols: Required. Protocols required for the volume
    psaRange: Output only. Name of the Private Service Access allocated range.
      This is optional. If not provided, any available range will be chosen.
    restoreParameters: Optional. Specifies the source of the volume to be
      created from.
    restrictedActions: Optional. List of actions that are restricted on this
      volume.
    securityStyle: Optional. Security Style of the Volume
    serviceLevel: Output only. Service level of the volume
    shareName: Required. Share name of the volume
    smbSettings: Optional. SMB share settings for the volume.
    snapReserve: Optional. Snap_reserve specifies percentage of volume storage
      reserved for snapshot storage. Default is 0 percent.
    snapshotDirectory: Optional. Snapshot_directory if enabled (true) the
      volume will contain a read-only .snapshot directory which provides
      access to each of the volume's snapshots.
    snapshotPolicy: Optional. SnapshotPolicy for a volume.
    state: Output only. State of the volume
    stateDetails: Output only. State details of the volume
    storagePool: Required. StoragePool name of the volume
    unixPermissions: Optional. Default unix style permission (e.g. 777) the
      mount point will be created with. Applicable for NFS protocol types
      only.
    usedGib: Output only. Used capacity in GIB of the volume. This is computed
      periodically and it does not represent the realtime usage.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Specified the current volume encryption key source.

    Values:
      ENCRYPTION_TYPE_UNSPECIFIED: The source of encryption key is not
        specified.
      SERVICE_MANAGED: Google managed encryption key.
      CLOUD_KMS: Customer managed encryption key, which is stored in KMS.
    """
    ENCRYPTION_TYPE_UNSPECIFIED = 0
    SERVICE_MANAGED = 1
    CLOUD_KMS = 2

  class ProtocolsValueListEntryValuesEnum(_messages.Enum):
    r"""ProtocolsValueListEntryValuesEnum enum type.

    Values:
      PROTOCOLS_UNSPECIFIED: Unspecified protocol
      NFSV3: NFS V3 protocol
      NFSV4: NFS V4 protocol
      SMB: SMB protocol
    """
    PROTOCOLS_UNSPECIFIED = 0
    NFSV3 = 1
    NFSV4 = 2
    SMB = 3

  class RestrictedActionsValueListEntryValuesEnum(_messages.Enum):
    r"""RestrictedActionsValueListEntryValuesEnum enum type.

    Values:
      RESTRICTED_ACTION_UNSPECIFIED: Unspecified restricted action
      DELETE: Prevent volume from being deleted when mounted.
    """
    RESTRICTED_ACTION_UNSPECIFIED = 0
    DELETE = 1

  class SecurityStyleValueValuesEnum(_messages.Enum):
    r"""Optional. Security Style of the Volume

    Values:
      SECURITY_STYLE_UNSPECIFIED: SecurityStyle is unspecified
      NTFS: SecurityStyle uses NTFS
      UNIX: SecurityStyle uses NTFS
    """
    SECURITY_STYLE_UNSPECIFIED = 0
    NTFS = 1
    UNIX = 2

  class ServiceLevelValueValuesEnum(_messages.Enum):
    r"""Output only. Service level of the volume

    Values:
      SERVICE_LEVEL_UNSPECIFIED: <no description>
      PREMIUM: <no description>
      EXTREME: <no description>
      STANDARD: Standard (Software offering)
    """
    SERVICE_LEVEL_UNSPECIFIED = 0
    PREMIUM = 1
    EXTREME = 2
    STANDARD = 3

  class SmbSettingsValueListEntryValuesEnum(_messages.Enum):
    r"""SmbSettingsValueListEntryValuesEnum enum type.

    Values:
      SMB_SETTINGS_UNSPECIFIED: Unspecified default option
      ENCRYPT_DATA: SMB setting encrypt data
      BROWSABLE: SMB setting browsable
      CHANGE_NOTIFY: SMB setting notify change
      NON_BROWSABLE: SMB setting not to notify change
      OPLOCKS: SMB setting oplocks
      SHOW_SNAPSHOT: SMB setting to show snapshots
      SHOW_PREVIOUS_VERSIONS: SMB setting to show previous versions
      ACCESS_BASED_ENUMERATION: SMB setting to access volume based on
        enumerartion
      CONTINUOUSLY_AVAILABLE: Continuously available enumeration
    """
    SMB_SETTINGS_UNSPECIFIED = 0
    ENCRYPT_DATA = 1
    BROWSABLE = 2
    CHANGE_NOTIFY = 3
    NON_BROWSABLE = 4
    OPLOCKS = 5
    SHOW_SNAPSHOT = 6
    SHOW_PREVIOUS_VERSIONS = 7
    ACCESS_BASED_ENUMERATION = 8
    CONTINUOUSLY_AVAILABLE = 9

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the volume

    Values:
      STATE_UNSPECIFIED: Unspecified Volume State
      READY: Volume State is Ready
      CREATING: Volume State is Creating
      DELETING: Volume State is Deleting
      UPDATING: Volume State is Updating
      RESTORING: Volume State is Restoring
      DISABLED: Volume State is Disabled
      ERROR: Volume State is Error
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    CREATING = 2
    DELETING = 3
    UPDATING = 4
    RESTORING = 5
    DISABLED = 6
    ERROR = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activeDirectory = _messages.StringField(1)
  backupConfig = _messages.MessageField('BackupConfig', 2)
  capacityGib = _messages.IntegerField(3)
  createTime = _messages.StringField(4)
  description = _messages.StringField(5)
  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 6)
  exportPolicy = _messages.MessageField('ExportPolicy', 7)
  hasReplication = _messages.BooleanField(8)
  kerberosEnabled = _messages.BooleanField(9)
  kmsConfig = _messages.StringField(10)
  labels = _messages.MessageField('LabelsValue', 11)
  ldapEnabled = _messages.BooleanField(12)
  mountOptions = _messages.MessageField('MountOption', 13, repeated=True)
  name = _messages.StringField(14)
  network = _messages.StringField(15)
  protocols = _messages.EnumField('ProtocolsValueListEntryValuesEnum', 16, repeated=True)
  psaRange = _messages.StringField(17)
  restoreParameters = _messages.MessageField('RestoreParameters', 18)
  restrictedActions = _messages.EnumField('RestrictedActionsValueListEntryValuesEnum', 19, repeated=True)
  securityStyle = _messages.EnumField('SecurityStyleValueValuesEnum', 20)
  serviceLevel = _messages.EnumField('ServiceLevelValueValuesEnum', 21)
  shareName = _messages.StringField(22)
  smbSettings = _messages.EnumField('SmbSettingsValueListEntryValuesEnum', 23, repeated=True)
  snapReserve = _messages.FloatField(24)
  snapshotDirectory = _messages.BooleanField(25)
  snapshotPolicy = _messages.MessageField('SnapshotPolicy', 26)
  state = _messages.EnumField('StateValueValuesEnum', 27)
  stateDetails = _messages.StringField(28)
  storagePool = _messages.StringField(29)
  unixPermissions = _messages.StringField(30)
  usedGib = _messages.IntegerField(31)


class WeeklySchedule(_messages.Message):
  r"""Make a snapshot every week e.g. at Monday 04:00, Wednesday 05:20, Sunday
  23:50

  Fields:
    day: Set the day or days of the week to make a snapshot. Accepts a comma
      separated days of the week. Defaults to 'Sunday'.
    hour: Set the hour to start the snapshot (0-23), defaults to midnight (0).
    minute: Set the minute of the hour to start the snapshot (0-59), defaults
      to the top of the hour (0).
    snapshotsToKeep: The maximum number of Snapshots to keep for the hourly
      schedule
  """

  day = _messages.StringField(1)
  hour = _messages.FloatField(2)
  minute = _messages.FloatField(3)
  snapshotsToKeep = _messages.FloatField(4)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

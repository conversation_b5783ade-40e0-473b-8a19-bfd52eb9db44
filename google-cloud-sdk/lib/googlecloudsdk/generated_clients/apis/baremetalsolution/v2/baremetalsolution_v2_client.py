"""Generated client library for baremetalsolution version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.baremetalsolution.v2 import baremetalsolution_v2_messages as messages


class BaremetalsolutionV2(base_api.BaseApiClient):
  """Generated client library for service baremetalsolution version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://baremetalsolution.googleapis.com/'
  MTLS_BASE_URL = 'https://baremetalsolution.mtls.googleapis.com/'

  _PACKAGE = 'baremetalsolution'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'BaremetalsolutionV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new baremetalsolution handle."""
    url = url or self.BASE_URL
    super(BaremetalsolutionV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_instanceQuotas = self.ProjectsLocationsInstanceQuotasService(self)
    self.projects_locations_instances = self.ProjectsLocationsInstancesService(self)
    self.projects_locations_networkQuotas = self.ProjectsLocationsNetworkQuotasService(self)
    self.projects_locations_networks = self.ProjectsLocationsNetworksService(self)
    self.projects_locations_nfsShares = self.ProjectsLocationsNfsSharesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_osImages = self.ProjectsLocationsOsImagesService(self)
    self.projects_locations_provisioningConfigs = self.ProjectsLocationsProvisioningConfigsService(self)
    self.projects_locations_provisioningQuotas = self.ProjectsLocationsProvisioningQuotasService(self)
    self.projects_locations_snapshotSchedulePolicies = self.ProjectsLocationsSnapshotSchedulePoliciesService(self)
    self.projects_locations_sshKeys = self.ProjectsLocationsSshKeysService(self)
    self.projects_locations_storageQuotas = self.ProjectsLocationsStorageQuotasService(self)
    self.projects_locations_volumes_luns = self.ProjectsLocationsVolumesLunsService(self)
    self.projects_locations_volumes_snapshots = self.ProjectsLocationsVolumesSnapshotsService(self)
    self.projects_locations_volumes = self.ProjectsLocationsVolumesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsInstanceQuotasService(base_api.BaseApiService):
    """Service class for the projects_locations_instanceQuotas resource."""

    _NAME = 'projects_locations_instanceQuotas'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsInstanceQuotasService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List Instance provisioning quotas.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstanceQuotasListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInstanceQuotasResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instanceQuotas',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.instanceQuotas.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/instanceQuotas',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsInstanceQuotasListRequest',
        response_type_name='ListInstanceQuotasResponse',
        supports_download=False,
    )

  class ProjectsLocationsInstancesService(base_api.BaseApiService):
    """Service class for the projects_locations_instances resource."""

    _NAME = 'projects_locations_instances'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def AttachVolume(self, request, global_params=None):
      r"""Attach volume to instance.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesAttachVolumeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AttachVolume')
      return self._RunMethod(
          config, request, global_params=global_params)

    AttachVolume.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:attachVolume',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.instances.attachVolume',
        ordered_params=['instance'],
        path_params=['instance'],
        query_params=[],
        relative_path='v2/{+instance}:attachVolume',
        request_field='attachVolumeRequest',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesAttachVolumeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DetachLun(self, request, global_params=None):
      r"""Detach LUN from Instance.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesDetachLunRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('DetachLun')
      return self._RunMethod(
          config, request, global_params=global_params)

    DetachLun.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:detachLun',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.instances.detachLun',
        ordered_params=['instance'],
        path_params=['instance'],
        query_params=[],
        relative_path='v2/{+instance}:detachLun',
        request_field='detachLunRequest',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesDetachLunRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DetachVolume(self, request, global_params=None):
      r"""Detach volume from instance.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesDetachVolumeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('DetachVolume')
      return self._RunMethod(
          config, request, global_params=global_params)

    DetachVolume.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:detachVolume',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.instances.detachVolume',
        ordered_params=['instance'],
        path_params=['instance'],
        query_params=[],
        relative_path='v2/{+instance}:detachVolume',
        request_field='detachVolumeRequest',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesDetachVolumeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DisableInteractiveSerialConsole(self, request, global_params=None):
      r"""Disable the interactive serial console feature on an instance.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesDisableInteractiveSerialConsoleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('DisableInteractiveSerialConsole')
      return self._RunMethod(
          config, request, global_params=global_params)

    DisableInteractiveSerialConsole.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:disableInteractiveSerialConsole',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.instances.disableInteractiveSerialConsole',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:disableInteractiveSerialConsole',
        request_field='disableInteractiveSerialConsoleRequest',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesDisableInteractiveSerialConsoleRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def EnableInteractiveSerialConsole(self, request, global_params=None):
      r"""Enable the interactive serial console feature on an instance.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesEnableInteractiveSerialConsoleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('EnableInteractiveSerialConsole')
      return self._RunMethod(
          config, request, global_params=global_params)

    EnableInteractiveSerialConsole.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:enableInteractiveSerialConsole',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.instances.enableInteractiveSerialConsole',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:enableInteractiveSerialConsole',
        request_field='enableInteractiveSerialConsoleRequest',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesEnableInteractiveSerialConsoleRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details about a single server.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Instance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.instances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesGetRequest',
        response_type_name='Instance',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List servers in a given project and location.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.instances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/instances',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesListRequest',
        response_type_name='ListInstancesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update details of a single server.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}',
        http_method='PATCH',
        method_id='baremetalsolution.projects.locations.instances.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='instance',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Rename(self, request, global_params=None):
      r"""RenameInstance sets a new name for an instance. Use with caution, previous names become immediately invalidated.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesRenameRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Instance) The response message.
      """
      config = self.GetMethodConfig('Rename')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rename.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:rename',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.instances.rename',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:rename',
        request_field='renameInstanceRequest',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesRenameRequest',
        response_type_name='Instance',
        supports_download=False,
    )

    def Reset(self, request, global_params=None):
      r"""Perform an ungraceful, hard reset on a server. Equivalent to shutting the power off and then turning it back on.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesResetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Reset')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reset.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:reset',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.instances.reset',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:reset',
        request_field='resetInstanceRequest',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesResetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Start(self, request, global_params=None):
      r"""Starts a server that was shutdown.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesStartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Start')
      return self._RunMethod(
          config, request, global_params=global_params)

    Start.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:start',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.instances.start',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:start',
        request_field='startInstanceRequest',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesStartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stop a running server.

      Args:
        request: (BaremetalsolutionProjectsLocationsInstancesStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:stop',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.instances.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:stop',
        request_field='stopInstanceRequest',
        request_type_name='BaremetalsolutionProjectsLocationsInstancesStopRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsNetworkQuotasService(base_api.BaseApiService):
    """Service class for the projects_locations_networkQuotas resource."""

    _NAME = 'projects_locations_networkQuotas'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsNetworkQuotasService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List Network provisioning quotas.

      Args:
        request: (BaremetalsolutionProjectsLocationsNetworkQuotasListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNetworkQuotasResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/networkQuotas',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.networkQuotas.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/networkQuotas',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsNetworkQuotasListRequest',
        response_type_name='ListNetworkQuotasResponse',
        supports_download=False,
    )

  class ProjectsLocationsNetworksService(base_api.BaseApiService):
    """Service class for the projects_locations_networks resource."""

    _NAME = 'projects_locations_networks'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsNetworksService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details of a single network.

      Args:
        request: (BaremetalsolutionProjectsLocationsNetworksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Network) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/networks/{networksId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.networks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsNetworksGetRequest',
        response_type_name='Network',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List network in a given project and location.

      Args:
        request: (BaremetalsolutionProjectsLocationsNetworksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNetworksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/networks',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.networks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/networks',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsNetworksListRequest',
        response_type_name='ListNetworksResponse',
        supports_download=False,
    )

    def ListNetworkUsage(self, request, global_params=None):
      r"""List all Networks (and used IPs for each Network) in the vendor account associated with the specified project.

      Args:
        request: (BaremetalsolutionProjectsLocationsNetworksListNetworkUsageRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNetworkUsageResponse) The response message.
      """
      config = self.GetMethodConfig('ListNetworkUsage')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListNetworkUsage.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/networks:listNetworkUsage',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.networks.listNetworkUsage',
        ordered_params=['location'],
        path_params=['location'],
        query_params=[],
        relative_path='v2/{+location}/networks:listNetworkUsage',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsNetworksListNetworkUsageRequest',
        response_type_name='ListNetworkUsageResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update details of a single network.

      Args:
        request: (BaremetalsolutionProjectsLocationsNetworksPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/networks/{networksId}',
        http_method='PATCH',
        method_id='baremetalsolution.projects.locations.networks.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='network',
        request_type_name='BaremetalsolutionProjectsLocationsNetworksPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Rename(self, request, global_params=None):
      r"""RenameNetwork sets a new name for a network. Use with caution, previous names become immediately invalidated.

      Args:
        request: (BaremetalsolutionProjectsLocationsNetworksRenameRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Network) The response message.
      """
      config = self.GetMethodConfig('Rename')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rename.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/networks/{networksId}:rename',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.networks.rename',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:rename',
        request_field='renameNetworkRequest',
        request_type_name='BaremetalsolutionProjectsLocationsNetworksRenameRequest',
        response_type_name='Network',
        supports_download=False,
    )

  class ProjectsLocationsNfsSharesService(base_api.BaseApiService):
    """Service class for the projects_locations_nfsShares resource."""

    _NAME = 'projects_locations_nfsShares'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsNfsSharesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create an NFS share.

      Args:
        request: (BaremetalsolutionProjectsLocationsNfsSharesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/nfsShares',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.nfsShares.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/nfsShares',
        request_field='nfsShare',
        request_type_name='BaremetalsolutionProjectsLocationsNfsSharesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an NFS share. The underlying volume is automatically deleted.

      Args:
        request: (BaremetalsolutionProjectsLocationsNfsSharesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/nfsShares/{nfsSharesId}',
        http_method='DELETE',
        method_id='baremetalsolution.projects.locations.nfsShares.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsNfsSharesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details of a single NFS share.

      Args:
        request: (BaremetalsolutionProjectsLocationsNfsSharesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NfsShare) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/nfsShares/{nfsSharesId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.nfsShares.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsNfsSharesGetRequest',
        response_type_name='NfsShare',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List NFS shares.

      Args:
        request: (BaremetalsolutionProjectsLocationsNfsSharesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNfsSharesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/nfsShares',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.nfsShares.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/nfsShares',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsNfsSharesListRequest',
        response_type_name='ListNfsSharesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update details of a single NFS share.

      Args:
        request: (BaremetalsolutionProjectsLocationsNfsSharesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/nfsShares/{nfsSharesId}',
        http_method='PATCH',
        method_id='baremetalsolution.projects.locations.nfsShares.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='nfsShare',
        request_type_name='BaremetalsolutionProjectsLocationsNfsSharesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Rename(self, request, global_params=None):
      r"""RenameNfsShare sets a new name for an nfsshare. Use with caution, previous names become immediately invalidated.

      Args:
        request: (BaremetalsolutionProjectsLocationsNfsSharesRenameRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NfsShare) The response message.
      """
      config = self.GetMethodConfig('Rename')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rename.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/nfsShares/{nfsSharesId}:rename',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.nfsShares.rename',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:rename',
        request_field='renameNfsShareRequest',
        request_type_name='BaremetalsolutionProjectsLocationsNfsSharesRenameRequest',
        response_type_name='NfsShare',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about an operation.

      Args:
        request: (BaremetalsolutionProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOsImagesService(base_api.BaseApiService):
    """Service class for the projects_locations_osImages resource."""

    _NAME = 'projects_locations_osImages'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsOsImagesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Retrieves the list of OS images which are currently approved.

      Args:
        request: (BaremetalsolutionProjectsLocationsOsImagesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOSImagesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/osImages',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.osImages.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/osImages',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsOsImagesListRequest',
        response_type_name='ListOSImagesResponse',
        supports_download=False,
    )

  class ProjectsLocationsProvisioningConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_provisioningConfigs resource."""

    _NAME = 'projects_locations_provisioningConfigs'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsProvisioningConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create new ProvisioningConfig.

      Args:
        request: (BaremetalsolutionProjectsLocationsProvisioningConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProvisioningConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/provisioningConfigs',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.provisioningConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['email'],
        relative_path='v2/{+parent}/provisioningConfigs',
        request_field='provisioningConfig',
        request_type_name='BaremetalsolutionProjectsLocationsProvisioningConfigsCreateRequest',
        response_type_name='ProvisioningConfig',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get ProvisioningConfig by name.

      Args:
        request: (BaremetalsolutionProjectsLocationsProvisioningConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProvisioningConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/provisioningConfigs/{provisioningConfigsId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.provisioningConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsProvisioningConfigsGetRequest',
        response_type_name='ProvisioningConfig',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update existing ProvisioningConfig.

      Args:
        request: (BaremetalsolutionProjectsLocationsProvisioningConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProvisioningConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/provisioningConfigs/{provisioningConfigsId}',
        http_method='PATCH',
        method_id='baremetalsolution.projects.locations.provisioningConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['email', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='provisioningConfig',
        request_type_name='BaremetalsolutionProjectsLocationsProvisioningConfigsPatchRequest',
        response_type_name='ProvisioningConfig',
        supports_download=False,
    )

    def Submit(self, request, global_params=None):
      r"""Submit a provisiong configuration for a given project.

      Args:
        request: (BaremetalsolutionProjectsLocationsProvisioningConfigsSubmitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SubmitProvisioningConfigResponse) The response message.
      """
      config = self.GetMethodConfig('Submit')
      return self._RunMethod(
          config, request, global_params=global_params)

    Submit.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/provisioningConfigs:submit',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.provisioningConfigs.submit',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/provisioningConfigs:submit',
        request_field='submitProvisioningConfigRequest',
        request_type_name='BaremetalsolutionProjectsLocationsProvisioningConfigsSubmitRequest',
        response_type_name='SubmitProvisioningConfigResponse',
        supports_download=False,
    )

  class ProjectsLocationsProvisioningQuotasService(base_api.BaseApiService):
    """Service class for the projects_locations_provisioningQuotas resource."""

    _NAME = 'projects_locations_provisioningQuotas'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsProvisioningQuotasService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List the budget details to provision resources on a given project.

      Args:
        request: (BaremetalsolutionProjectsLocationsProvisioningQuotasListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListProvisioningQuotasResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/provisioningQuotas',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.provisioningQuotas.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/provisioningQuotas',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsProvisioningQuotasListRequest',
        response_type_name='ListProvisioningQuotasResponse',
        supports_download=False,
    )

  class ProjectsLocationsSnapshotSchedulePoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_snapshotSchedulePolicies resource."""

    _NAME = 'projects_locations_snapshotSchedulePolicies'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsSnapshotSchedulePoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a snapshot schedule policy in the specified project.

      Args:
        request: (BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SnapshotSchedulePolicy) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/snapshotSchedulePolicies',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.snapshotSchedulePolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['snapshotSchedulePolicyId'],
        relative_path='v2/{+parent}/snapshotSchedulePolicies',
        request_field='snapshotSchedulePolicy',
        request_type_name='BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesCreateRequest',
        response_type_name='SnapshotSchedulePolicy',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a named snapshot schedule policy.

      Args:
        request: (BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/snapshotSchedulePolicies/{snapshotSchedulePoliciesId}',
        http_method='DELETE',
        method_id='baremetalsolution.projects.locations.snapshotSchedulePolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details of a single snapshot schedule policy.

      Args:
        request: (BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SnapshotSchedulePolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/snapshotSchedulePolicies/{snapshotSchedulePoliciesId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.snapshotSchedulePolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesGetRequest',
        response_type_name='SnapshotSchedulePolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List snapshot schedule policies in a given project and location.

      Args:
        request: (BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSnapshotSchedulePoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/snapshotSchedulePolicies',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.snapshotSchedulePolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/snapshotSchedulePolicies',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesListRequest',
        response_type_name='ListSnapshotSchedulePoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a snapshot schedule policy in the specified project.

      Args:
        request: (BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SnapshotSchedulePolicy) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/snapshotSchedulePolicies/{snapshotSchedulePoliciesId}',
        http_method='PATCH',
        method_id='baremetalsolution.projects.locations.snapshotSchedulePolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='snapshotSchedulePolicy',
        request_type_name='BaremetalsolutionProjectsLocationsSnapshotSchedulePoliciesPatchRequest',
        response_type_name='SnapshotSchedulePolicy',
        supports_download=False,
    )

  class ProjectsLocationsSshKeysService(base_api.BaseApiService):
    """Service class for the projects_locations_sshKeys resource."""

    _NAME = 'projects_locations_sshKeys'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsSshKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Register a public SSH key in the specified project for use with the interactive serial console feature.

      Args:
        request: (BaremetalsolutionProjectsLocationsSshKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SSHKey) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/sshKeys',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.sshKeys.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['sshKeyId'],
        relative_path='v2/{+parent}/sshKeys',
        request_field='sSHKey',
        request_type_name='BaremetalsolutionProjectsLocationsSshKeysCreateRequest',
        response_type_name='SSHKey',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a public SSH key registered in the specified project.

      Args:
        request: (BaremetalsolutionProjectsLocationsSshKeysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/sshKeys/{sshKeysId}',
        http_method='DELETE',
        method_id='baremetalsolution.projects.locations.sshKeys.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsSshKeysDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the public SSH keys registered for the specified project. These SSH keys are used only for the interactive serial console feature.

      Args:
        request: (BaremetalsolutionProjectsLocationsSshKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSSHKeysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/sshKeys',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.sshKeys.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/sshKeys',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsSshKeysListRequest',
        response_type_name='ListSSHKeysResponse',
        supports_download=False,
    )

  class ProjectsLocationsStorageQuotasService(base_api.BaseApiService):
    """Service class for the projects_locations_storageQuotas resource."""

    _NAME = 'projects_locations_storageQuotas'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsStorageQuotasService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List Storage provisioning quotas.

      Args:
        request: (BaremetalsolutionProjectsLocationsStorageQuotasListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListStorageQuotasResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/storageQuotas',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.storageQuotas.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/storageQuotas',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsStorageQuotasListRequest',
        response_type_name='ListStorageQuotasResponse',
        supports_download=False,
    )

  class ProjectsLocationsVolumesLunsService(base_api.BaseApiService):
    """Service class for the projects_locations_volumes_luns resource."""

    _NAME = 'projects_locations_volumes_luns'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsVolumesLunsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete a Lun. Lun shouldn't be attached to any Instances.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesLunsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/luns/{lunsId}',
        http_method='DELETE',
        method_id='baremetalsolution.projects.locations.volumes.luns.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesLunsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Evict(self, request, global_params=None):
      r"""Skips lun's cooloff and deletes it now. Lun must be in cooloff state.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesLunsEvictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Evict')
      return self._RunMethod(
          config, request, global_params=global_params)

    Evict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/luns/{lunsId}:evict',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.volumes.luns.evict',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:evict',
        request_field='evictLunRequest',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesLunsEvictRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details of a single storage logical unit number(LUN).

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesLunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Lun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/luns/{lunsId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.volumes.luns.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesLunsGetRequest',
        response_type_name='Lun',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List storage volume luns for given storage volume.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesLunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLunsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/luns',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.volumes.luns.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/luns',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesLunsListRequest',
        response_type_name='ListLunsResponse',
        supports_download=False,
    )

  class ProjectsLocationsVolumesSnapshotsService(base_api.BaseApiService):
    """Service class for the projects_locations_volumes_snapshots resource."""

    _NAME = 'projects_locations_volumes_snapshots'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsVolumesSnapshotsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Takes a snapshot of a boot volume. Returns INVALID_ARGUMENT if called for a non-boot volume.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesSnapshotsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VolumeSnapshot) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.volumes.snapshots.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/snapshots',
        request_field='volumeSnapshot',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesSnapshotsCreateRequest',
        response_type_name='VolumeSnapshot',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a volume snapshot. Returns INVALID_ARGUMENT if called for a non-boot volume.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesSnapshotsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots/{snapshotsId}',
        http_method='DELETE',
        method_id='baremetalsolution.projects.locations.volumes.snapshots.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesSnapshotsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the specified snapshot resource. Returns INVALID_ARGUMENT if called for a non-boot volume.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesSnapshotsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VolumeSnapshot) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots/{snapshotsId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.volumes.snapshots.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesSnapshotsGetRequest',
        response_type_name='VolumeSnapshot',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves the list of snapshots for the specified volume. Returns a response with an empty list of snapshots if called for a non-boot volume.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesSnapshotsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVolumeSnapshotsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.volumes.snapshots.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/snapshots',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesSnapshotsListRequest',
        response_type_name='ListVolumeSnapshotsResponse',
        supports_download=False,
    )

    def RestoreVolumeSnapshot(self, request, global_params=None):
      r"""Uses the specified snapshot to restore its parent volume. Returns INVALID_ARGUMENT if called for a non-boot volume.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesSnapshotsRestoreVolumeSnapshotRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RestoreVolumeSnapshot')
      return self._RunMethod(
          config, request, global_params=global_params)

    RestoreVolumeSnapshot.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}/snapshots/{snapshotsId}:restoreVolumeSnapshot',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.volumes.snapshots.restoreVolumeSnapshot',
        ordered_params=['volumeSnapshot'],
        path_params=['volumeSnapshot'],
        query_params=[],
        relative_path='v2/{+volumeSnapshot}:restoreVolumeSnapshot',
        request_field='restoreVolumeSnapshotRequest',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesSnapshotsRestoreVolumeSnapshotRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsVolumesService(base_api.BaseApiService):
    """Service class for the projects_locations_volumes resource."""

    _NAME = 'projects_locations_volumes'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsVolumesService, self).__init__(client)
      self._upload_configs = {
          }

    def AllocateLuns(self, request, global_params=None):
      r"""Allocate Volume's Luns.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesAllocateLunsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AllocateLuns')
      return self._RunMethod(
          config, request, global_params=global_params)

    AllocateLuns.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}:allocateLuns',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.volumes.allocateLuns',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}:allocateLuns',
        request_field='allocateLunsRequest',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesAllocateLunsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Create a volume.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.volumes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/volumes',
        request_field='volume',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CreateAndAttach(self, request, global_params=None):
      r"""Create a volume, allocate Luns and attach them to instances.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesCreateAndAttachRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CreateAndAttach')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateAndAttach.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes:createAndAttach',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.volumes.createAndAttach',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/volumes:createAndAttach',
        request_field='createAndAttachVolumeRequest',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesCreateAndAttachRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a volume. Volume shouldn't have any Luns.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}',
        http_method='DELETE',
        method_id='baremetalsolution.projects.locations.volumes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Evict(self, request, global_params=None):
      r"""Skips volume's cooloff and deletes it now. Volume must be in cooloff state.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesEvictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Evict')
      return self._RunMethod(
          config, request, global_params=global_params)

    Evict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}:evict',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.volumes.evict',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:evict',
        request_field='evictVolumeRequest',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesEvictRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details of a single storage volume.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Volume) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.volumes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesGetRequest',
        response_type_name='Volume',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List storage volumes in a given project and location.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVolumesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.volumes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/volumes',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesListRequest',
        response_type_name='ListVolumesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update details of a single storage volume.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}',
        http_method='PATCH',
        method_id='baremetalsolution.projects.locations.volumes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='volume',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Rename(self, request, global_params=None):
      r"""RenameVolume sets a new name for a volume. Use with caution, previous names become immediately invalidated.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesRenameRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Volume) The response message.
      """
      config = self.GetMethodConfig('Rename')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rename.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}:rename',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.volumes.rename',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:rename',
        request_field='renameVolumeRequest',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesRenameRequest',
        response_type_name='Volume',
        supports_download=False,
    )

    def Resize(self, request, global_params=None):
      r"""Emergency Volume resize.

      Args:
        request: (BaremetalsolutionProjectsLocationsVolumesResizeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Resize')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resize.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/volumes/{volumesId}:resize',
        http_method='POST',
        method_id='baremetalsolution.projects.locations.volumes.resize',
        ordered_params=['volume'],
        path_params=['volume'],
        query_params=[],
        relative_path='v2/{+volume}:resize',
        request_field='resizeVolumeRequest',
        request_type_name='BaremetalsolutionProjectsLocationsVolumesResizeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (BaremetalsolutionProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (BaremetalsolutionProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations',
        http_method='GET',
        method_id='baremetalsolution.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/locations',
        request_field='',
        request_type_name='BaremetalsolutionProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(BaremetalsolutionV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

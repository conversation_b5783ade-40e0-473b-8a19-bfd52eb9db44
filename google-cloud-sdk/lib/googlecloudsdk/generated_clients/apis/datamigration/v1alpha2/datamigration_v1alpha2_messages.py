"""Generated message classes for datamigration version v1alpha2.

Manage Cloud Database Migration Service resources on Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'datamigration'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudSqlConnectionProfile(_messages.Message):
  r"""Specifies required connection parameters, and, optionally, the
  parameters required to create a Cloud SQL destination database instance.

  Fields:
    cloudSqlId: Output only. The Cloud SQL instance ID that this connection
      profile is associated with.
    privateIp: Output only. The Cloud SQL database instance's private IP.
    publicIp: Output only. The Cloud SQL database instance's public IP.
    settings: Immutable. Metadata used to create the destination Cloud SQL
      database.
  """

  cloudSqlId = _messages.StringField(1)
  privateIp = _messages.StringField(2)
  publicIp = _messages.StringField(3)
  settings = _messages.MessageField('CloudSqlSettings', 4)


class CloudSqlSettings(_messages.Message):
  r"""Settings for creating a Cloud SQL database instance.

  Enums:
    ActivationPolicyValueValuesEnum: The activation policy specifies when the
      instance is activated; it is applicable only when the instance state is
      'RUNNABLE'. Valid values: 'ALWAYS': The instance is on, and remains so
      even in the absence of connection requests. `NEVER`: The instance is
      off; it is not activated, even if a connection request arrives.
    DataDiskTypeValueValuesEnum: The type of storage: `PD_SSD` (default) or
      `PD_HDD`.
    DatabaseVersionValueValuesEnum: The database engine type and version.

  Messages:
    DatabaseFlagsValue: The database flags passed to the Cloud SQL instance at
      startup. An object containing a list of "key": value pairs. Example: {
      "name": "wrench", "mass": "1.3kg", "count": "3" }.
    UserLabelsValue: The resource labels for a Cloud SQL instance to use to
      annotate any related underlying resources such as Compute Engine VMs. An
      object containing a list of "key": "value" pairs. Example: `{ "name":
      "wrench", "mass": "18kg", "count": "3" }`.

  Fields:
    activationPolicy: The activation policy specifies when the instance is
      activated; it is applicable only when the instance state is 'RUNNABLE'.
      Valid values: 'ALWAYS': The instance is on, and remains so even in the
      absence of connection requests. `NEVER`: The instance is off; it is not
      activated, even if a connection request arrives.
    autoStorageIncrease: [default: ON] If you enable this setting, Cloud SQL
      checks your available storage every 30 seconds. If the available storage
      falls below a threshold size, Cloud SQL automatically adds additional
      storage capacity. If the available storage repeatedly falls below the
      threshold size, Cloud SQL continues to add storage until it reaches the
      maximum of 30 TB.
    dataDiskSizeGb: The storage capacity available to the database, in GB. The
      minimum (and default) size is 10GB.
    dataDiskType: The type of storage: `PD_SSD` (default) or `PD_HDD`.
    databaseFlags: The database flags passed to the Cloud SQL instance at
      startup. An object containing a list of "key": value pairs. Example: {
      "name": "wrench", "mass": "1.3kg", "count": "3" }.
    databaseVersion: The database engine type and version.
    hasRootPassword: Output only. Indicates If this connection profile root
      password is stored.
    ipConfig: The settings for IP Management. This allows to enable or disable
      the instance IP and manage which external networks can connect to the
      instance. The IPv4 address cannot be disabled.
    rootPassword: Input only. Initial root password.
    sourceId: The Database Migration Service source connection profile ID, in
      the format: `projects/my_project_name/locations/us-
      central1/connectionProfiles/connection_profile_ID`
    storageAutoResizeLimit: The maximum size to which storage capacity can be
      automatically increased. The default value is 0, which specifies that
      there is no limit.
    tier: The tier (or machine type) for this instance, for example:
      `db-n1-standard-1` (MySQL instances). For more information, see [Cloud
      SQL Instance Settings](https://cloud.google.com/sql/docs/mysql/instance-
      settings).
    userLabels: The resource labels for a Cloud SQL instance to use to
      annotate any related underlying resources such as Compute Engine VMs. An
      object containing a list of "key": "value" pairs. Example: `{ "name":
      "wrench", "mass": "18kg", "count": "3" }`.
    zone: The Google Cloud Platform zone where your Cloud SQL database
      instance is located.
  """

  class ActivationPolicyValueValuesEnum(_messages.Enum):
    r"""The activation policy specifies when the instance is activated; it is
    applicable only when the instance state is 'RUNNABLE'. Valid values:
    'ALWAYS': The instance is on, and remains so even in the absence of
    connection requests. `NEVER`: The instance is off; it is not activated,
    even if a connection request arrives.

    Values:
      SQL_ACTIVATION_POLICY_UNSPECIFIED: unspecified policy.
      ALWAYS: The instance is always up and running.
      NEVER: The instance should never spin up.
    """
    SQL_ACTIVATION_POLICY_UNSPECIFIED = 0
    ALWAYS = 1
    NEVER = 2

  class DataDiskTypeValueValuesEnum(_messages.Enum):
    r"""The type of storage: `PD_SSD` (default) or `PD_HDD`.

    Values:
      SQL_DATA_DISK_TYPE_UNSPECIFIED: Unspecified.
      PD_SSD: SSD disk.
      PD_HDD: HDD disk.
    """
    SQL_DATA_DISK_TYPE_UNSPECIFIED = 0
    PD_SSD = 1
    PD_HDD = 2

  class DatabaseVersionValueValuesEnum(_messages.Enum):
    r"""The database engine type and version.

    Values:
      SQL_DATABASE_VERSION_UNSPECIFIED: Unspecified version.
      MYSQL_5_6: MySQL 5.6.
      MYSQL_5_7: MySQL 5.7.
      MYSQL_8_0: MySQL 8.0.
    """
    SQL_DATABASE_VERSION_UNSPECIFIED = 0
    MYSQL_5_6 = 1
    MYSQL_5_7 = 2
    MYSQL_8_0 = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DatabaseFlagsValue(_messages.Message):
    r"""The database flags passed to the Cloud SQL instance at startup. An
    object containing a list of "key": value pairs. Example: { "name":
    "wrench", "mass": "1.3kg", "count": "3" }.

    Messages:
      AdditionalProperty: An additional property for a DatabaseFlagsValue
        object.

    Fields:
      additionalProperties: Additional properties of type DatabaseFlagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DatabaseFlagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UserLabelsValue(_messages.Message):
    r"""The resource labels for a Cloud SQL instance to use to annotate any
    related underlying resources such as Compute Engine VMs. An object
    containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
    "mass": "18kg", "count": "3" }`.

    Messages:
      AdditionalProperty: An additional property for a UserLabelsValue object.

    Fields:
      additionalProperties: Additional properties of type UserLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UserLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activationPolicy = _messages.EnumField('ActivationPolicyValueValuesEnum', 1)
  autoStorageIncrease = _messages.BooleanField(2)
  dataDiskSizeGb = _messages.IntegerField(3)
  dataDiskType = _messages.EnumField('DataDiskTypeValueValuesEnum', 4)
  databaseFlags = _messages.MessageField('DatabaseFlagsValue', 5)
  databaseVersion = _messages.EnumField('DatabaseVersionValueValuesEnum', 6)
  hasRootPassword = _messages.BooleanField(7)
  ipConfig = _messages.MessageField('SqlIpConfig', 8)
  rootPassword = _messages.StringField(9)
  sourceId = _messages.StringField(10)
  storageAutoResizeLimit = _messages.IntegerField(11)
  tier = _messages.StringField(12)
  userLabels = _messages.MessageField('UserLabelsValue', 13)
  zone = _messages.StringField(14)


class ConnectionProfile(_messages.Message):
  r"""A connection profile definition.

  Enums:
    ProviderValueValuesEnum: The database provider.
    StateValueValuesEnum: The current connection profile state (e.g. DRAFT,
      READY, or FAILED).

  Messages:
    LabelsValue: The resource labels for connection profile to use to annotate
      any related underlying resources such as Compute Engine VMs. An object
      containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
      "mass": "1.3kg", "count": "3" }`.

  Fields:
    cloudsql: A CloudSQL database connection profile.
    createTime: Output only. The timestamp when the resource was created. A
      timestamp in RFC3339 UTC "Zulu" format, accurate to nanoseconds.
      Example: "2014-10-02T15:01:23.045123456Z".
    displayName: The connection profile display name.
    error: Output only. The error details in case of state FAILED.
    labels: The resource labels for connection profile to use to annotate any
      related underlying resources such as Compute Engine VMs. An object
      containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
      "mass": "1.3kg", "count": "3" }`.
    mysql: A MySQL database connection profile.
    name: The name of this connection profile resource in the form of projects
      /{project}/locations/{location}/connectionProfiles/{connectionProfile}.
    provider: The database provider.
    state: The current connection profile state (e.g. DRAFT, READY, or
      FAILED).
    updateTime: Output only. The timestamp when the resource was last updated.
      A timestamp in RFC3339 UTC "Zulu" format, accurate to nanoseconds.
      Example: "2014-10-02T15:01:23.045123456Z".
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""The database provider.

    Values:
      DATABASE_PROVIDER_UNSPECIFIED: The database provider is unknown.
      CLOUDSQL: CloudSQL runs the database.
      RDS: RDS runs the database.
    """
    DATABASE_PROVIDER_UNSPECIFIED = 0
    CLOUDSQL = 1
    RDS = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""The current connection profile state (e.g. DRAFT, READY, or FAILED).

    Values:
      STATE_UNSPECIFIED: The state of the connection profile is unknown.
      DRAFT: The connection profile is in draft mode and fully editable.
      CREATING: The connection profile is being created.
      READY: The connection profile is ready.
      UPDATING: The connection profile is being updated.
      DELETING: The connection profile is being deleted.
      DELETED: The connection profile has been deleted.
      FAILED: The last action on the connection profile failed.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    CREATING = 2
    READY = 3
    UPDATING = 4
    DELETING = 5
    DELETED = 6
    FAILED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The resource labels for connection profile to use to annotate any
    related underlying resources such as Compute Engine VMs. An object
    containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
    "mass": "1.3kg", "count": "3" }`.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cloudsql = _messages.MessageField('CloudSqlConnectionProfile', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  error = _messages.MessageField('Status', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  mysql = _messages.MessageField('MySqlConnectionProfile', 6)
  name = _messages.StringField(7)
  provider = _messages.EnumField('ProviderValueValuesEnum', 8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  updateTime = _messages.StringField(10)


class DatabaseType(_messages.Message):
  r"""A message defining the database engine and provider.

  Enums:
    EngineValueValuesEnum: The database engine.
    ProviderValueValuesEnum: The database provider.

  Fields:
    engine: The database engine.
    provider: The database provider.
  """

  class EngineValueValuesEnum(_messages.Enum):
    r"""The database engine.

    Values:
      DATABASE_ENGINE_UNSPECIFIED: The source database engine of the migration
        job is unknown.
      MYSQL: The source engine is MySQL.
    """
    DATABASE_ENGINE_UNSPECIFIED = 0
    MYSQL = 1

  class ProviderValueValuesEnum(_messages.Enum):
    r"""The database provider.

    Values:
      DATABASE_PROVIDER_UNSPECIFIED: The database provider is unknown.
      CLOUDSQL: CloudSQL runs the database.
      RDS: RDS runs the database.
    """
    DATABASE_PROVIDER_UNSPECIFIED = 0
    CLOUDSQL = 1
    RDS = 2

  engine = _messages.EnumField('EngineValueValuesEnum', 1)
  provider = _messages.EnumField('ProviderValueValuesEnum', 2)


class DatamigrationProjectsLocationsConnectionProfilesCreateRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesCreateRequest object.

  Fields:
    connectionProfile: A ConnectionProfile resource to be passed as the
      request body.
    connectionProfileId: Required. The connection profile identifier.
    parent: Required. The parent, which owns this collection of connection
      profiles.
    requestId: A unique id used to identify the request. If the server
      receives two requests with the same id, then the second request will be
      ignored. It is recommended to always set this value to a UUID. The id
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  connectionProfile = _messages.MessageField('ConnectionProfile', 1)
  connectionProfileId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class DatamigrationProjectsLocationsConnectionProfilesDeleteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesDeleteRequest object.

  Fields:
    force: In case of force delete, the CloudSQL replica database is also
      deleted (only for CloudSQL connection profile).
    name: Required. Name of the connection profile resource to delete.
    requestId: A unique id used to identify the request. If the server
      receives two requests with the same id, then the second request will be
      ignored. It is recommended to always set this value to a UUID. The id
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class DatamigrationProjectsLocationsConnectionProfilesGetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsConnectionProfilesGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesGetRequest object.

  Fields:
    name: Required. Name of the connection profile resource to get.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsConnectionProfilesListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesListRequest object.

  Fields:
    filter: A filter expression that filters connection profiles listed in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be either =, !=, >, or <. For example, list connection profiles created
      this year by specifying **createTime %gt;
      2020-01-01T00:00:00.000000000Z**. You can also filter nested fields. For
      example, you could specify **mySql.username = %lt;my_username%gt;** to
      list all connection profiles configured to connect with a specific
      username.
    orderBy: the order by fields for the result.
    pageSize: The maximum number of connection profiles to return. The service
      may return fewer than this value. If unspecified, at most 50 connection
      profiles will be returned. The maximum value is 1000; values above 1000
      will be coerced to 1000.
    pageToken: A page token, received from a previous `ListConnectionProfiles`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListConnectionProfiles` must match the
      call that provided the page token.
    parent: Required. The parent, which owns this collection of connection
      profiles.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DatamigrationProjectsLocationsConnectionProfilesPatchRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesPatchRequest object.

  Fields:
    connectionProfile: A ConnectionProfile resource to be passed as the
      request body.
    name: The name of this connection profile resource in the form of projects
      /{project}/locations/{location}/connectionProfiles/{connectionProfile}.
    requestId: A unique id used to identify the request. If the server
      receives two requests with the same id, then the second request will be
      ignored. It is recommended to always set this value to a UUID. The id
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the connection profile resource by the update.
  """

  connectionProfile = _messages.MessageField('ConnectionProfile', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class DatamigrationProjectsLocationsConnectionProfilesSetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatamigrationProjectsLocationsConnectionProfilesTestIamPermissionsRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsConnectionProfilesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DatamigrationProjectsLocationsGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class DatamigrationProjectsLocationsMigrationJobsCreateRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsCreateRequest object.

  Fields:
    migrationJob: A MigrationJob resource to be passed as the request body.
    migrationJobId: Required. The ID of the instance to create.
    parent: Required. The parent, which owns this collection of migration
      jobs.
    requestId: A unique id used to identify the request. If the server
      receives two requests with the same id, then the second request will be
      ignored. It is recommended to always set this value to a UUID. The id
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  migrationJob = _messages.MessageField('MigrationJob', 1)
  migrationJobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class DatamigrationProjectsLocationsMigrationJobsDeleteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsDeleteRequest object.

  Fields:
    force: The destination CloudSQL connection profile is always deleted with
      the migration job. In case of force delete, the destination CloudSQL
      replica database is also deleted.
    name: Required. Name of the migration job resource to delete.
    requestId: A unique id used to identify the request. If the server
      receives two requests with the same id, then the second request will be
      ignored. It is recommended to always set this value to a UUID. The id
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class DatamigrationProjectsLocationsMigrationJobsGenerateSshScriptRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsGenerateSshScriptRequest
  object.

  Fields:
    generateSshScriptRequest: A GenerateSshScriptRequest resource to be passed
      as the request body.
    name: Name of the migration job resource to generate the SSH script.
  """

  generateSshScriptRequest = _messages.MessageField('GenerateSshScriptRequest', 1)
  name = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsMigrationJobsGetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsMigrationJobsGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsGetRequest object.

  Fields:
    name: Required. Name of the migration job resource to get.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsMigrationJobsListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsListRequest object.

  Fields:
    filter: A filter expression that filters migration jobs listed in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be either =, !=, >, or <. For example, list migration jobs created this
      year by specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z.**
      You can also filter nested fields. For example, you could specify
      **reverseSshConnectivity.vmIp = "*******"** to select all migration jobs
      connecting through the specific SSH tunnel bastion.
    orderBy: Sort the results based on the migration job name. Valid values
      are: "name", "name asc", and "name desc".
    pageSize: The maximum number of migration jobs to return. The service may
      return fewer than this value. If unspecified, at most 50 migration jobs
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: The nextPageToken value received in the previous call to
      migrationJobs.list, used in the subsequent request to retrieve the next
      page of results. On first call this should be left blank. When
      paginating, all other parameters provided to migrationJobs.list must
      match the call that provided the page token.
    parent: Required. The parent, which owns this collection of migrationJobs.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DatamigrationProjectsLocationsMigrationJobsPatchRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsPatchRequest object.

  Fields:
    migrationJob: A MigrationJob resource to be passed as the request body.
    name: The name (URI) of this migration job resource, in the form of:
      projects/{project}/locations/{location}/migrationJobs/{migrationJob}.
    requestId: A unique id used to identify the request. If the server
      receives two requests with the same id, then the second request will be
      ignored. It is recommended to always set this value to a UUID. The id
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the migration job resource by the update.
  """

  migrationJob = _messages.MessageField('MigrationJob', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class DatamigrationProjectsLocationsMigrationJobsPromoteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsPromoteRequest object.

  Fields:
    name: Name of the migration job resource to promote.
    promoteMigrationJobRequest: A PromoteMigrationJobRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  promoteMigrationJobRequest = _messages.MessageField('PromoteMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsRestartRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsRestartRequest object.

  Fields:
    name: Name of the migration job resource to restart.
    restartMigrationJobRequest: A RestartMigrationJobRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  restartMigrationJobRequest = _messages.MessageField('RestartMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsResumeRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsResumeRequest object.

  Fields:
    name: Name of the migration job resource to resume.
    resumeMigrationJobRequest: A ResumeMigrationJobRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  resumeMigrationJobRequest = _messages.MessageField('ResumeMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsSetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsStartRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsStartRequest object.

  Fields:
    name: Name of the migration job resource to start.
    startMigrationJobRequest: A StartMigrationJobRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  startMigrationJobRequest = _messages.MessageField('StartMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsStopRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsStopRequest object.

  Fields:
    name: Name of the migration job resource to stop.
    stopMigrationJobRequest: A StopMigrationJobRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  stopMigrationJobRequest = _messages.MessageField('StopMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsTestIamPermissionsRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsVerifyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsVerifyRequest object.

  Fields:
    name: Name of the migration job resource to verify.
    verifyMigrationJobRequest: A VerifyMigrationJobRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  verifyMigrationJobRequest = _messages.MessageField('VerifyMigrationJobRequest', 2)


class DatamigrationProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GenerateSshScriptRequest(_messages.Message):
  r"""Request message for 'GenerateSshScript' request.

  Fields:
    vm: Required. Bastion VM Instance name to use or to create.
    vmCreationConfig: The VM creation configuration.
    vmPort: The port that will be open on the bastion host
    vmSelectionConfig: The VM selection configuration.
  """

  vm = _messages.StringField(1)
  vmCreationConfig = _messages.MessageField('VmCreationConfig', 2)
  vmPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  vmSelectionConfig = _messages.MessageField('VmSelectionConfig', 4)


class GoogleCloudClouddmsV1alpha2OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ListConnectionProfilesResponse(_messages.Message):
  r"""Response message for 'ListConnectionProfiles' request.

  Fields:
    connectionProfiles: The response list of connection profiles.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  connectionProfiles = _messages.MessageField('ConnectionProfile', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMigrationJobsResponse(_messages.Message):
  r"""Response message for 'ListMigrationJobs' request.

  Fields:
    migrationJobs: The list of migration jobs objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  migrationJobs = _messages.MessageField('MigrationJob', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MigrationJob(_messages.Message):
  r"""Represents a Database Migration Service migration job object.

  Enums:
    PhaseValueValuesEnum: Output only. The current migration job phase.
    StateValueValuesEnum: The current migration job state.
    TypeValueValuesEnum: Required. The migration job type.

  Messages:
    LabelsValue: The resource labels for migration job to use to annotate any
      related underlying resources such as Compute Engine VMs. An object
      containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
      "mass": "1.3kg", "count": "3" }`.

  Fields:
    createTime: Output only. The timestamp when the migration job resource was
      created. A timestamp in RFC3339 UTC "Zulu" format, accurate to
      nanoseconds. Example: "2014-10-02T15:01:23.045123456Z".
    destination: Required. The resource name (URI) of the destination
      connection profile.
    destinationDatabase: The database engine type and provider of the
      destination.
    displayName: The migration job display name.
    dumpPath: The path to the dump file in Google Cloud Storage, in the
      format: (gs://[BUCKET_NAME]/[OBJECT_NAME]).
    duration: Output only. The duration of the migration job (in seconds). A
      duration in seconds with up to nine fractional digits, terminated by
      's'. Example: "3.5s".
    endTime: Output only. If the migration job is completed, the time when it
      was completed.
    error: Output only. The error details in case of state FAILED.
    labels: The resource labels for migration job to use to annotate any
      related underlying resources such as Compute Engine VMs. An object
      containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
      "mass": "1.3kg", "count": "3" }`.
    name: The name (URI) of this migration job resource, in the form of:
      projects/{project}/locations/{location}/migrationJobs/{migrationJob}.
    phase: Output only. The current migration job phase.
    reverseSshConnectivity: The details needed to communicate to the source
      over Reverse SSH tunnel connectivity.
    source: Required. The resource name (URI) of the source connection
      profile.
    sourceDatabase: The database engine type and provider of the source.
    state: The current migration job state.
    staticIpConnectivity: static ip connectivity data (default, no additional
      details needed).
    type: Required. The migration job type.
    updateTime: Output only. The timestamp when the migration job resource was
      last updated. A timestamp in RFC3339 UTC "Zulu" format, accurate to
      nanoseconds. Example: "2014-10-02T15:01:23.045123456Z".
    vpcPeeringConnectivity: The details of the VPC network that the source
      database is located in.
  """

  class PhaseValueValuesEnum(_messages.Enum):
    r"""Output only. The current migration job phase.

    Values:
      PHASE_UNSPECIFIED: The phase of the migration job is unknown.
      FULL_DUMP: The migration job is in the full dump phase.
      CDC: The migration job is CDC phase.
      PROMOTE_IN_PROGRESS: The migration job is running the promote phase.
      WAITING_FOR_SOURCE_WRITES_TO_STOP: Only RDS flow - waiting for source
        writes to stop
      PREPARING_THE_DUMP: Only RDS flow - the sources writes stopped, waiting
        for dump to begin
    """
    PHASE_UNSPECIFIED = 0
    FULL_DUMP = 1
    CDC = 2
    PROMOTE_IN_PROGRESS = 3
    WAITING_FOR_SOURCE_WRITES_TO_STOP = 4
    PREPARING_THE_DUMP = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""The current migration job state.

    Values:
      STATE_UNSPECIFIED: The state of the migration job is unknown.
      MAINTENANCE: The migration job is down for maintenance.
      DRAFT: The migration job is in draft mode and no resources are created.
      CREATING: The migration job is being created.
      NOT_STARTED: The migration job is created and not started.
      RUNNING: The migration job is running.
      FAILED: The migration job failed.
      COMPLETED: The migration job has been completed.
      DELETING: The migration job is being deleted.
      STOPPING: The migration job is being stopped.
      STOPPED: The migration job is currently stopped.
      DELETED: The migration job has been deleted.
      UPDATING: The migration job is being updated.
      STARTING: The migration job is starting.
      RESTARTING: The migration job is restarting.
      RESUMING: The migration job is resuming.
    """
    STATE_UNSPECIFIED = 0
    MAINTENANCE = 1
    DRAFT = 2
    CREATING = 3
    NOT_STARTED = 4
    RUNNING = 5
    FAILED = 6
    COMPLETED = 7
    DELETING = 8
    STOPPING = 9
    STOPPED = 10
    DELETED = 11
    UPDATING = 12
    STARTING = 13
    RESTARTING = 14
    RESUMING = 15

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The migration job type.

    Values:
      TYPE_UNSPECIFIED: The type of the migration job is unknown.
      ONE_TIME: The migration job is a one time migration.
      CONTINUOUS: The migration job is a continuous migration.
    """
    TYPE_UNSPECIFIED = 0
    ONE_TIME = 1
    CONTINUOUS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The resource labels for migration job to use to annotate any related
    underlying resources such as Compute Engine VMs. An object containing a
    list of "key": "value" pairs. Example: `{ "name": "wrench", "mass":
    "1.3kg", "count": "3" }`.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  destination = _messages.StringField(2)
  destinationDatabase = _messages.MessageField('DatabaseType', 3)
  displayName = _messages.StringField(4)
  dumpPath = _messages.StringField(5)
  duration = _messages.StringField(6)
  endTime = _messages.StringField(7)
  error = _messages.MessageField('Status', 8)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  phase = _messages.EnumField('PhaseValueValuesEnum', 11)
  reverseSshConnectivity = _messages.MessageField('ReverseSshConnectivity', 12)
  source = _messages.StringField(13)
  sourceDatabase = _messages.MessageField('DatabaseType', 14)
  state = _messages.EnumField('StateValueValuesEnum', 15)
  staticIpConnectivity = _messages.MessageField('StaticIpConnectivity', 16)
  type = _messages.EnumField('TypeValueValuesEnum', 17)
  updateTime = _messages.StringField(18)
  vpcPeeringConnectivity = _messages.MessageField('VpcPeeringConnectivity', 19)


class MigrationJobVerificationError(_messages.Message):
  r"""Error message of a verification Migration job.

  Enums:
    ErrorCodeValueValuesEnum: Output only. An instance of ErrorCode specifying
      the error that occurred.

  Fields:
    errorCode: Output only. An instance of ErrorCode specifying the error that
      occurred.
    errorDetailMessage: Output only. A specific detailed error message, if
      supplied by the engine.
    errorMessage: Output only. A formatted message with further details about
      the error and a CTA.
  """

  class ErrorCodeValueValuesEnum(_messages.Enum):
    r"""Output only. An instance of ErrorCode specifying the error that
    occurred.

    Values:
      ERROR_CODE_UNSPECIFIED: An unknown error occurred
      CONNECTION_FAILURE: We failed to connect to one of the connection
        profile.
      AUTHENTICATION_FAILURE: We failed to authenticate to one of the
        connection profile.
      INVALID_CONNECTION_PROFILE_CONFIG: One of the involved connection
        profiles has an invalid configuration.
      VERSION_INCOMPATIBILITY: The versions of the source and the destination
        are incompatible.
      CONNECTION_PROFILE_TYPES_INCOMPATIBILITY: The types of the source and
        the destination are incompatible.
      UNSUPPORTED_GTID_MODE: The gtid_mode is not supported, applicable for
        MySQL.
      UNSUPPORTED_DEFINER: The definer is not supported.
      CANT_RESTART_RUNNING_MIGRATION: Migration is already running at the time
        of restart request.
      TABLES_WITH_LIMITED_SUPPORT: The source has tables with limited support.
        E.g. PostgreSQL tables without primary keys.
      UNSUPPORTED_DATABASE_LOCALE: The source uses an unsupported locale.
      UNSUPPORTED_DATABASE_FDW_CONFIG: The source uses an unsupported Foreign
        Data Wrapper configuration.
      ERROR_RDBMS: There was an underlying RDBMS error.
      SOURCE_SIZE_EXCEEDS_THRESHOLD: The source DB size in Bytes exceeds a
        certain threshold. The migration might require an increase of quota,
        or might not be supported.
      EXISTING_CONFLICTING_DATABASES: The destination DB contains existing
        databases that are conflicting with those in the source DB.
      PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE: Insufficient privilege to enable
        the parallelism configuration.
    """
    ERROR_CODE_UNSPECIFIED = 0
    CONNECTION_FAILURE = 1
    AUTHENTICATION_FAILURE = 2
    INVALID_CONNECTION_PROFILE_CONFIG = 3
    VERSION_INCOMPATIBILITY = 4
    CONNECTION_PROFILE_TYPES_INCOMPATIBILITY = 5
    UNSUPPORTED_GTID_MODE = 6
    UNSUPPORTED_DEFINER = 7
    CANT_RESTART_RUNNING_MIGRATION = 8
    TABLES_WITH_LIMITED_SUPPORT = 9
    UNSUPPORTED_DATABASE_LOCALE = 10
    UNSUPPORTED_DATABASE_FDW_CONFIG = 11
    ERROR_RDBMS = 12
    SOURCE_SIZE_EXCEEDS_THRESHOLD = 13
    EXISTING_CONFLICTING_DATABASES = 14
    PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE = 15

  errorCode = _messages.EnumField('ErrorCodeValueValuesEnum', 1)
  errorDetailMessage = _messages.StringField(2)
  errorMessage = _messages.StringField(3)


class MySqlConnectionProfile(_messages.Message):
  r"""Specifies connection parameters required specifically for MySQL
  databases.

  Fields:
    cloudSqlId: If the source is a Cloud SQL database, use this field to
      provide the Cloud SQL instance ID of the source.
    hasPassword: Output only. Indicates If this connection profile password is
      stored.
    host: Required. The IP or hostname of the source MySQL database.
    password: Required. Input only. The password for the user that Database
      Migration Service will be using to connect to the database. This field
      is not returned on request, and the value is encrypted when stored in
      Database Migration Service.
    port: Required. The network port of the source MySQL database.
    ssl: SSL configuration for the destination to connect to the source
      database.
    username: Required. The username that Database Migration Service will use
      to connect to the database. The value is encrypted when stored in
      Database Migration Service.
  """

  cloudSqlId = _messages.StringField(1)
  hasPassword = _messages.BooleanField(2)
  host = _messages.StringField(3)
  password = _messages.StringField(4)
  port = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  ssl = _messages.MessageField('SslConfig', 6)
  username = _messages.StringField(7)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PromoteMigrationJobRequest(_messages.Message):
  r"""Request message for 'PromoteMigrationJob' request."""


class RestartMigrationJobRequest(_messages.Message):
  r"""Request message for 'RestartMigrationJob' request."""


class ResumeMigrationJobRequest(_messages.Message):
  r"""Request message for 'ResumeMigrationJob' request."""


class ReverseSshConnectivity(_messages.Message):
  r"""The details needed to configure a reverse SSH tunnel between the source
  and destination databases. These details will be used when calling the
  generateSshScript method (see https://cloud.google.com/database-migration/do
  cs/reference/rest/v1alpha2/projects.locations.migrationJobs/generateSshScrip
  t) to produce the script that will help set up the reverse SSH tunnel, and
  to set up the VPC peering between the Cloud SQL private network and the VPC.

  Fields:
    vm: The name of the virtual machine (Compute Engine) used as the bastion
      server for the SSH tunnel.
    vmIp: Required. The IP of the virtual machine (Compute Engine) used as the
      bastion server for the SSH tunnel.
    vmPort: Required. The forwarding port of the virtual machine (Compute
      Engine) used as the bastion server for the SSH tunnel.
    vpc: The name of the VPC to peer with the Cloud SQL private network.
  """

  vm = _messages.StringField(1)
  vmIp = _messages.StringField(2)
  vmPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  vpc = _messages.StringField(4)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SqlAclEntry(_messages.Message):
  r"""An entry for an Access Control list.

  Fields:
    expireTime: The time when this access control entry expires in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example:
      `2012-11-15T16:19:00.094Z`.
    label: A label to identify this entry.
    value: The allowlisted value for the access control list.
  """

  expireTime = _messages.StringField(1)
  label = _messages.StringField(2)
  value = _messages.StringField(3)


class SqlIpConfig(_messages.Message):
  r"""IP Management configuration.

  Fields:
    authorizedNetworks: The list of external networks that are allowed to
      connect to the instance using the IP. See
      https://en.wikipedia.org/wiki/CIDR_notation#CIDR_notation, also known as
      'slash' notation (e.g. `*************/24`).
    enableIpv4: Whether the instance is assigned a public IP address or not.
    privateNetwork: The resource link for the VPC network from which the Cloud
      SQL instance is accessible for private IP. For example,
      `/projects/myProject/global/networks/default`. This setting can be
      updated, but it cannot be removed after it is set.
    requireSsl: Whether SSL connections over IP should be enforced or not.
  """

  authorizedNetworks = _messages.MessageField('SqlAclEntry', 1, repeated=True)
  enableIpv4 = _messages.BooleanField(2)
  privateNetwork = _messages.StringField(3)
  requireSsl = _messages.BooleanField(4)


class SshScript(_messages.Message):
  r"""Response message for 'GenerateSshScript' request.

  Fields:
    script: The ssh configuration script.
  """

  script = _messages.StringField(1)


class SslConfig(_messages.Message):
  r"""SSL configuration information.

  Enums:
    TypeValueValuesEnum: Output only. The ssl config type according to
      'client_key', 'client_certificate' and 'ca_certificate'.

  Fields:
    caCertificate: Required. Input only. The x509 PEM-encoded certificate of
      the CA that signed the source database server's certificate. The replica
      will use this certificate to verify it's connecting to the right host.
    clientCertificate: Input only. The x509 PEM-encoded certificate that will
      be used by the replica to authenticate against the source database
      server.If this field is used then the 'client_key' field is mandatory.
    clientKey: Input only. The unencrypted PKCS#1 or PKCS#8 PEM-encoded
      private key associated with the Client Certificate. If this field is
      used then the 'client_certificate' field is mandatory.
    type: Output only. The ssl config type according to 'client_key',
      'client_certificate' and 'ca_certificate'.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The ssl config type according to 'client_key',
    'client_certificate' and 'ca_certificate'.

    Values:
      SSL_TYPE_UNSPECIFIED: Unspecified.
      SERVER_ONLY: Only 'ca_certificate' specified.
      SERVER_CLIENT: Both server ('ca_certificate'), and client ('client_key',
        'client_certificate') specified.
    """
    SSL_TYPE_UNSPECIFIED = 0
    SERVER_ONLY = 1
    SERVER_CLIENT = 2

  caCertificate = _messages.StringField(1)
  clientCertificate = _messages.StringField(2)
  clientKey = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartMigrationJobRequest(_messages.Message):
  r"""Request message for 'StartMigrationJob' request."""


class StaticIpConnectivity(_messages.Message):
  r"""The source database will allow incoming connections from the destination
  database's public IP. You can retrieve the Cloud SQL instance's public IP
  from the Cloud SQL console or using Cloud SQL APIs. No additional
  configuration is required.
  """



class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopMigrationJobRequest(_messages.Message):
  r"""Request message for 'StopMigrationJob' request."""


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class VerifyMigrationJobRequest(_messages.Message):
  r"""Request message for 'VerifyMigrationJob' request."""


class VmCreationConfig(_messages.Message):
  r"""VM creation configuration message.

  Fields:
    subnet: The subnet name the vm needs to be created in.
    vmMachineType: Required. VM instance machine type to create.
    vmZone: The Google Cloud Platform zone to create the VM in.
    vpc: The VPC name the vm needs to be created in.
  """

  subnet = _messages.StringField(1)
  vmMachineType = _messages.StringField(2)
  vmZone = _messages.StringField(3)
  vpc = _messages.StringField(4)


class VmSelectionConfig(_messages.Message):
  r"""VM selection configuration message

  Fields:
    vmZone: Required. The Google Cloud Platform zone the VM is located.
  """

  vmZone = _messages.StringField(1)


class VpcPeeringConnectivity(_messages.Message):
  r"""The details of the VPC where the source database is located in Google
  Cloud. We will use this information to set up the VPC peering connection
  between Cloud SQL and this VPC.

  Fields:
    vpc: The name of the VPC network to peer with the Cloud SQL private
      network.
  """

  vpc = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

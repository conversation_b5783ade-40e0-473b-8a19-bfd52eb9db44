"""Generated message classes for datamigration version v1.

Manage Cloud Database Migration Service resources on Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'datamigration'


class AlloyDbConnectionProfile(_messages.Message):
  r"""Specifies required connection parameters, and the parameters required to
  create an AlloyDB destination cluster.

  Fields:
    clusterId: Required. The AlloyDB cluster ID that this connection profile
      is associated with.
    settings: Immutable. Metadata used to create the destination AlloyDB
      cluster.
  """

  clusterId = _messages.StringField(1)
  settings = _messages.MessageField('AlloyDbSettings', 2)


class AlloyDbSettings(_messages.Message):
  r"""Settings for creating an AlloyDB cluster.

  Messages:
    LabelsValue: Labels for the AlloyDB cluster created by DMS. An object
      containing a list of 'key', 'value' pairs.

  Fields:
    encryptionConfig: Optional. The encryption config can be specified to
      encrypt the data disks and other persistent data resources of a cluster
      with a customer-managed encryption key (CMEK). When this field is not
      specified, the cluster will then use default encryption scheme to
      protect the user data.
    initialUser: Required. Input only. Initial user to setup during cluster
      creation. Required.
    labels: Labels for the AlloyDB cluster created by DMS. An object
      containing a list of 'key', 'value' pairs.
    primaryInstanceSettings: A PrimaryInstanceSettings attribute.
    vpcNetwork: Required. The resource link for the VPC network in which
      cluster resources are created and from which they are accessible via
      Private IP. The network must belong to the same project as the cluster.
      It is specified in the form:
      "projects/{project_number}/global/networks/{network_id}". This is
      required to create a cluster.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for the AlloyDB cluster created by DMS. An object containing a
    list of 'key', 'value' pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  encryptionConfig = _messages.MessageField('EncryptionConfig', 1)
  initialUser = _messages.MessageField('UserPassword', 2)
  labels = _messages.MessageField('LabelsValue', 3)
  primaryInstanceSettings = _messages.MessageField('PrimaryInstanceSettings', 4)
  vpcNetwork = _messages.StringField(5)


class ApplyConversionWorkspaceRequest(_messages.Message):
  r"""Request message for 'ApplyConversionWorkspace' request.

  Fields:
    autoCommit: Optional. Specifies whether the conversion workspace is to be
      committed automatically after the apply.
    connectionProfile: Optional. Fully qualified (Uri) name of the destination
      connection profile.
    dryRun: Optional. Only validates the apply process, but doesn't change the
      destination database. Only works for PostgreSQL destination connection
      profile.
    filter: Filter which entities to apply. Leaving this field empty will
      apply all of the entities. Supports Google AIP 160 based filtering.
  """

  autoCommit = _messages.BooleanField(1)
  connectionProfile = _messages.StringField(2)
  dryRun = _messages.BooleanField(3)
  filter = _messages.StringField(4)


class ApplyHash(_messages.Message):
  r"""Apply a hash function on the value.

  Fields:
    uuidFromBytes: Optional. Generate UUID from the data's byte array
  """

  uuidFromBytes = _messages.MessageField('Empty', 1)


class ApplyJobDetails(_messages.Message):
  r"""Details regarding an Apply background job.

  Fields:
    connectionProfile: Output only. The connection profile which was used for
      the apply job.
    filter: Output only. AIP-160 based filter used to specify the entities to
      apply
  """

  connectionProfile = _messages.StringField(1)
  filter = _messages.StringField(2)


class AssignSpecificValue(_messages.Message):
  r"""Set to a specific value (value is converted to fit the target data type)

  Fields:
    value: Required. Specific value to be assigned
  """

  value = _messages.StringField(1)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class BackgroundJobLogEntry(_messages.Message):
  r"""Execution log of a background job.

  Enums:
    CompletionStateValueValuesEnum: Output only. Job completion state, i.e.
      the final state after the job completed.
    JobTypeValueValuesEnum: The type of job that was executed.

  Fields:
    applyJobDetails: Output only. Apply job details.
    completionComment: Output only. Job completion comment, such as how many
      entities were seeded, how many warnings were found during conversion,
      and similar information.
    completionState: Output only. Job completion state, i.e. the final state
      after the job completed.
    convertJobDetails: Output only. Convert job details.
    finishTime: The timestamp when the background job was finished.
    id: The background job log entry ID.
    importRulesJobDetails: Output only. Import rules job details.
    jobType: The type of job that was executed.
    requestAutocommit: Output only. Whether the client requested the
      conversion workspace to be committed after a successful completion of
      the job.
    seedJobDetails: Output only. Seed job details.
    startTime: The timestamp when the background job was started.
  """

  class CompletionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Job completion state, i.e. the final state after the job
    completed.

    Values:
      JOB_COMPLETION_STATE_UNSPECIFIED: The status is not specified. This
        state is used when job is not yet finished.
      SUCCEEDED: Success.
      FAILED: Error.
    """
    JOB_COMPLETION_STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2

  class JobTypeValueValuesEnum(_messages.Enum):
    r"""The type of job that was executed.

    Values:
      BACKGROUND_JOB_TYPE_UNSPECIFIED: Unspecified background job type.
      BACKGROUND_JOB_TYPE_SOURCE_SEED: Job to seed from the source database.
      BACKGROUND_JOB_TYPE_CONVERT: Job to convert the source database into a
        draft of the destination database.
      BACKGROUND_JOB_TYPE_APPLY_DESTINATION: Job to apply the draft tree onto
        the destination.
      BACKGROUND_JOB_TYPE_IMPORT_RULES_FILE: Job to import and convert mapping
        rules from an external source such as an ora2pg config file.
    """
    BACKGROUND_JOB_TYPE_UNSPECIFIED = 0
    BACKGROUND_JOB_TYPE_SOURCE_SEED = 1
    BACKGROUND_JOB_TYPE_CONVERT = 2
    BACKGROUND_JOB_TYPE_APPLY_DESTINATION = 3
    BACKGROUND_JOB_TYPE_IMPORT_RULES_FILE = 4

  applyJobDetails = _messages.MessageField('ApplyJobDetails', 1)
  completionComment = _messages.StringField(2)
  completionState = _messages.EnumField('CompletionStateValueValuesEnum', 3)
  convertJobDetails = _messages.MessageField('ConvertJobDetails', 4)
  finishTime = _messages.StringField(5)
  id = _messages.StringField(6)
  importRulesJobDetails = _messages.MessageField('ImportRulesJobDetails', 7)
  jobType = _messages.EnumField('JobTypeValueValuesEnum', 8)
  requestAutocommit = _messages.BooleanField(9)
  seedJobDetails = _messages.MessageField('SeedJobDetails', 10)
  startTime = _messages.StringField(11)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudSqlConnectionProfile(_messages.Message):
  r"""Specifies required connection parameters, and, optionally, the
  parameters required to create a Cloud SQL destination database instance.

  Fields:
    additionalPublicIp: Output only. The Cloud SQL database instance's
      additional (outgoing) public IP. Used when the Cloud SQL database
      availability type is REGIONAL (i.e. multiple zones / highly available).
    cloudSqlId: Output only. The Cloud SQL instance ID that this connection
      profile is associated with.
    privateIp: Output only. The Cloud SQL database instance's private IP.
    publicIp: Output only. The Cloud SQL database instance's public IP.
    settings: Immutable. Metadata used to create the destination Cloud SQL
      database.
  """

  additionalPublicIp = _messages.StringField(1)
  cloudSqlId = _messages.StringField(2)
  privateIp = _messages.StringField(3)
  publicIp = _messages.StringField(4)
  settings = _messages.MessageField('CloudSqlSettings', 5)


class CloudSqlSettings(_messages.Message):
  r"""Settings for creating a Cloud SQL database instance.

  Enums:
    ActivationPolicyValueValuesEnum: The activation policy specifies when the
      instance is activated; it is applicable only when the instance state is
      'RUNNABLE'. Valid values: 'ALWAYS': The instance is on, and remains so
      even in the absence of connection requests. `NEVER`: The instance is
      off; it is not activated, even if a connection request arrives.
    AvailabilityTypeValueValuesEnum: Optional. Availability type. Potential
      values: * `ZONAL`: The instance serves data from only one zone. Outages
      in that zone affect data availability. * `REGIONAL`: The instance can
      serve data from more than one zone in a region (it is highly available).
    DataDiskTypeValueValuesEnum: The type of storage: `PD_SSD` (default) or
      `PD_HDD`.
    DatabaseVersionValueValuesEnum: The database engine type and version.
    EditionValueValuesEnum: Optional. The edition of the given Cloud SQL
      instance.

  Messages:
    DatabaseFlagsValue: The database flags passed to the Cloud SQL instance at
      startup. An object containing a list of "key": value pairs. Example: {
      "name": "wrench", "mass": "1.3kg", "count": "3" }.
    UserLabelsValue: The resource labels for a Cloud SQL instance to use to
      annotate any related underlying resources such as Compute Engine VMs. An
      object containing a list of "key": "value" pairs. Example: `{ "name":
      "wrench", "mass": "18kg", "count": "3" }`.

  Fields:
    activationPolicy: The activation policy specifies when the instance is
      activated; it is applicable only when the instance state is 'RUNNABLE'.
      Valid values: 'ALWAYS': The instance is on, and remains so even in the
      absence of connection requests. `NEVER`: The instance is off; it is not
      activated, even if a connection request arrives.
    autoStorageIncrease: [default: ON] If you enable this setting, Cloud SQL
      checks your available storage every 30 seconds. If the available storage
      falls below a threshold size, Cloud SQL automatically adds additional
      storage capacity. If the available storage repeatedly falls below the
      threshold size, Cloud SQL continues to add storage until it reaches the
      maximum of 30 TB.
    availabilityType: Optional. Availability type. Potential values: *
      `ZONAL`: The instance serves data from only one zone. Outages in that
      zone affect data availability. * `REGIONAL`: The instance can serve data
      from more than one zone in a region (it is highly available).
    cmekKeyName: The KMS key name used for the csql instance.
    collation: The Cloud SQL default instance level collation.
    dataDiskSizeGb: The storage capacity available to the database, in GB. The
      minimum (and default) size is 10GB.
    dataDiskType: The type of storage: `PD_SSD` (default) or `PD_HDD`.
    databaseFlags: The database flags passed to the Cloud SQL instance at
      startup. An object containing a list of "key": value pairs. Example: {
      "name": "wrench", "mass": "1.3kg", "count": "3" }.
    databaseVersion: The database engine type and version.
    edition: Optional. The edition of the given Cloud SQL instance.
    ipConfig: The settings for IP Management. This allows to enable or disable
      the instance IP and manage which external networks can connect to the
      instance. The IPv4 address cannot be disabled.
    rootPassword: Input only. Initial root password.
    rootPasswordSet: Output only. Indicates If this connection profile root
      password is stored.
    secondaryZone: Optional. The Google Cloud Platform zone where the failover
      Cloud SQL database instance is located. Used when the Cloud SQL database
      availability type is REGIONAL (i.e. multiple zones / highly available).
    sourceId: The Database Migration Service source connection profile ID, in
      the format: `projects/my_project_name/locations/us-
      central1/connectionProfiles/connection_profile_ID`
    storageAutoResizeLimit: The maximum size to which storage capacity can be
      automatically increased. The default value is 0, which specifies that
      there is no limit.
    tier: The tier (or machine type) for this instance, for example:
      `db-n1-standard-1` (MySQL instances) or `db-custom-1-3840` (PostgreSQL
      instances). For more information, see [Cloud SQL Instance
      Settings](https://cloud.google.com/sql/docs/mysql/instance-settings).
    userLabels: The resource labels for a Cloud SQL instance to use to
      annotate any related underlying resources such as Compute Engine VMs. An
      object containing a list of "key": "value" pairs. Example: `{ "name":
      "wrench", "mass": "18kg", "count": "3" }`.
    zone: The Google Cloud Platform zone where your Cloud SQL database
      instance is located.
  """

  class ActivationPolicyValueValuesEnum(_messages.Enum):
    r"""The activation policy specifies when the instance is activated; it is
    applicable only when the instance state is 'RUNNABLE'. Valid values:
    'ALWAYS': The instance is on, and remains so even in the absence of
    connection requests. `NEVER`: The instance is off; it is not activated,
    even if a connection request arrives.

    Values:
      SQL_ACTIVATION_POLICY_UNSPECIFIED: unspecified policy.
      ALWAYS: The instance is always up and running.
      NEVER: The instance should never spin up.
    """
    SQL_ACTIVATION_POLICY_UNSPECIFIED = 0
    ALWAYS = 1
    NEVER = 2

  class AvailabilityTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Availability type. Potential values: * `ZONAL`: The instance
    serves data from only one zone. Outages in that zone affect data
    availability. * `REGIONAL`: The instance can serve data from more than one
    zone in a region (it is highly available).

    Values:
      SQL_AVAILABILITY_TYPE_UNSPECIFIED: This is an unknown Availability type.
      ZONAL: Zonal availablility instance.
      REGIONAL: Regional availability instance.
    """
    SQL_AVAILABILITY_TYPE_UNSPECIFIED = 0
    ZONAL = 1
    REGIONAL = 2

  class DataDiskTypeValueValuesEnum(_messages.Enum):
    r"""The type of storage: `PD_SSD` (default) or `PD_HDD`.

    Values:
      SQL_DATA_DISK_TYPE_UNSPECIFIED: Unspecified.
      PD_SSD: SSD disk.
      PD_HDD: HDD disk.
    """
    SQL_DATA_DISK_TYPE_UNSPECIFIED = 0
    PD_SSD = 1
    PD_HDD = 2

  class DatabaseVersionValueValuesEnum(_messages.Enum):
    r"""The database engine type and version.

    Values:
      SQL_DATABASE_VERSION_UNSPECIFIED: Unspecified version.
      MYSQL_5_6: MySQL 5.6.
      MYSQL_5_7: MySQL 5.7.
      POSTGRES_9_6: PostgreSQL 9.6.
      POSTGRES_11: PostgreSQL 11.
      POSTGRES_10: PostgreSQL 10.
      MYSQL_8_0: MySQL 8.0.
      POSTGRES_12: PostgreSQL 12.
      POSTGRES_13: PostgreSQL 13.
      POSTGRES_14: PostgreSQL 14.
      POSTGRES_15: PostgreSQL 15.
    """
    SQL_DATABASE_VERSION_UNSPECIFIED = 0
    MYSQL_5_6 = 1
    MYSQL_5_7 = 2
    POSTGRES_9_6 = 3
    POSTGRES_11 = 4
    POSTGRES_10 = 5
    MYSQL_8_0 = 6
    POSTGRES_12 = 7
    POSTGRES_13 = 8
    POSTGRES_14 = 9
    POSTGRES_15 = 10

  class EditionValueValuesEnum(_messages.Enum):
    r"""Optional. The edition of the given Cloud SQL instance.

    Values:
      EDITION_UNSPECIFIED: The instance did not specify the edition.
      ENTERPRISE: The instance is an enterprise edition.
      ENTERPRISE_PLUS: The instance is an enterprise plus edition.
    """
    EDITION_UNSPECIFIED = 0
    ENTERPRISE = 1
    ENTERPRISE_PLUS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DatabaseFlagsValue(_messages.Message):
    r"""The database flags passed to the Cloud SQL instance at startup. An
    object containing a list of "key": value pairs. Example: { "name":
    "wrench", "mass": "1.3kg", "count": "3" }.

    Messages:
      AdditionalProperty: An additional property for a DatabaseFlagsValue
        object.

    Fields:
      additionalProperties: Additional properties of type DatabaseFlagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DatabaseFlagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UserLabelsValue(_messages.Message):
    r"""The resource labels for a Cloud SQL instance to use to annotate any
    related underlying resources such as Compute Engine VMs. An object
    containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
    "mass": "18kg", "count": "3" }`.

    Messages:
      AdditionalProperty: An additional property for a UserLabelsValue object.

    Fields:
      additionalProperties: Additional properties of type UserLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UserLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activationPolicy = _messages.EnumField('ActivationPolicyValueValuesEnum', 1)
  autoStorageIncrease = _messages.BooleanField(2)
  availabilityType = _messages.EnumField('AvailabilityTypeValueValuesEnum', 3)
  cmekKeyName = _messages.StringField(4)
  collation = _messages.StringField(5)
  dataDiskSizeGb = _messages.IntegerField(6)
  dataDiskType = _messages.EnumField('DataDiskTypeValueValuesEnum', 7)
  databaseFlags = _messages.MessageField('DatabaseFlagsValue', 8)
  databaseVersion = _messages.EnumField('DatabaseVersionValueValuesEnum', 9)
  edition = _messages.EnumField('EditionValueValuesEnum', 10)
  ipConfig = _messages.MessageField('SqlIpConfig', 11)
  rootPassword = _messages.StringField(12)
  rootPasswordSet = _messages.BooleanField(13)
  secondaryZone = _messages.StringField(14)
  sourceId = _messages.StringField(15)
  storageAutoResizeLimit = _messages.IntegerField(16)
  tier = _messages.StringField(17)
  userLabels = _messages.MessageField('UserLabelsValue', 18)
  zone = _messages.StringField(19)


class ColumnEntity(_messages.Message):
  r"""Column is not used as an independent entity, it is retrieved as part of
  a Table entity.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    array: Is the column of array type.
    arrayLength: If the column is array, of which length.
    autoGenerated: Is the column auto-generated/identity.
    charset: Charset override - instead of table level charset.
    collation: Collation override - instead of table level collation.
    comment: Comment associated with the column.
    customFeatures: Custom engine specific features.
    dataType: Column data type.
    defaultValue: Default value of the column.
    fractionalSecondsPrecision: Column fractional second precision - used for
      timestamp based datatypes.
    length: Column length - e.g. varchar (50).
    name: Column name.
    nullable: Is the column nullable.
    ordinalPosition: Column order in the table.
    precision: Column precision - when relevant.
    scale: Column scale - when relevant.
    setValues: Specifies the list of values allowed in the column. Only used
      for set data type.
    udt: Is the column a UDT.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  array = _messages.BooleanField(1)
  arrayLength = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  autoGenerated = _messages.BooleanField(3)
  charset = _messages.StringField(4)
  collation = _messages.StringField(5)
  comment = _messages.StringField(6)
  customFeatures = _messages.MessageField('CustomFeaturesValue', 7)
  dataType = _messages.StringField(8)
  defaultValue = _messages.StringField(9)
  fractionalSecondsPrecision = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  length = _messages.IntegerField(11)
  name = _messages.StringField(12)
  nullable = _messages.BooleanField(13)
  ordinalPosition = _messages.IntegerField(14, variant=_messages.Variant.INT32)
  precision = _messages.IntegerField(15, variant=_messages.Variant.INT32)
  scale = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  setValues = _messages.StringField(17, repeated=True)
  udt = _messages.BooleanField(18)


class CommitConversionWorkspaceRequest(_messages.Message):
  r"""Request message for 'CommitConversionWorkspace' request.

  Fields:
    commitName: Optional. Optional name of the commit.
  """

  commitName = _messages.StringField(1)


class ConditionalColumnSetValue(_messages.Message):
  r"""Options to configure rule type ConditionalColumnSetValue. The rule is
  used to transform the data which is being replicated/migrated. The rule
  filter field can refer to one or more entities. The rule scope can be one
  of: Column.

  Messages:
    CustomFeaturesValue: Optional. Custom engine specific features.

  Fields:
    customFeatures: Optional. Custom engine specific features.
    sourceNumericFilter: Optional. Optional filter on source column precision
      and scale. Used for fixed point numbers such as NUMERIC/NUMBER data
      types.
    sourceTextFilter: Optional. Optional filter on source column length. Used
      for text based data types like varchar.
    valueTransformation: Required. Description of data transformation during
      migration.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Optional. Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  sourceNumericFilter = _messages.MessageField('SourceNumericFilter', 2)
  sourceTextFilter = _messages.MessageField('SourceTextFilter', 3)
  valueTransformation = _messages.MessageField('ValueTransformation', 4)


class ConnectionProfile(_messages.Message):
  r"""A connection profile definition.

  Enums:
    ProviderValueValuesEnum: The database provider.
    StateValueValuesEnum: The current connection profile state (e.g. DRAFT,
      READY, or FAILED).

  Messages:
    LabelsValue: The resource labels for connection profile to use to annotate
      any related underlying resources such as Compute Engine VMs. An object
      containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
      "mass": "1.3kg", "count": "3" }`.

  Fields:
    alloydb: An AlloyDB cluster connection profile.
    cloudsql: A CloudSQL database connection profile.
    createTime: Output only. The timestamp when the resource was created. A
      timestamp in RFC3339 UTC "Zulu" format, accurate to nanoseconds.
      Example: "2014-10-02T15:01:23.045123456Z".
    displayName: The connection profile display name.
    error: Output only. The error details in case of state FAILED.
    labels: The resource labels for connection profile to use to annotate any
      related underlying resources such as Compute Engine VMs. An object
      containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
      "mass": "1.3kg", "count": "3" }`.
    mysql: A MySQL database connection profile.
    name: The name of this connection profile resource in the form of projects
      /{project}/locations/{location}/connectionProfiles/{connectionProfile}.
    oracle: An Oracle database connection profile.
    postgresql: A PostgreSQL database connection profile.
    provider: The database provider.
    spanner: A Spanner database connection profile.
    state: The current connection profile state (e.g. DRAFT, READY, or
      FAILED).
    updateTime: Output only. The timestamp when the resource was last updated.
      A timestamp in RFC3339 UTC "Zulu" format, accurate to nanoseconds.
      Example: "2014-10-02T15:01:23.045123456Z".
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""The database provider.

    Values:
      DATABASE_PROVIDER_UNSPECIFIED: The database provider is unknown.
      CLOUDSQL: CloudSQL runs the database.
      RDS: RDS runs the database.
      AURORA: Amazon Aurora.
      ALLOYDB: AlloyDB.
    """
    DATABASE_PROVIDER_UNSPECIFIED = 0
    CLOUDSQL = 1
    RDS = 2
    AURORA = 3
    ALLOYDB = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The current connection profile state (e.g. DRAFT, READY, or FAILED).

    Values:
      STATE_UNSPECIFIED: The state of the connection profile is unknown.
      DRAFT: The connection profile is in draft mode and fully editable.
      CREATING: The connection profile is being created.
      READY: The connection profile is ready.
      UPDATING: The connection profile is being updated.
      DELETING: The connection profile is being deleted.
      DELETED: The connection profile has been deleted.
      FAILED: The last action on the connection profile failed.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    CREATING = 2
    READY = 3
    UPDATING = 4
    DELETING = 5
    DELETED = 6
    FAILED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The resource labels for connection profile to use to annotate any
    related underlying resources such as Compute Engine VMs. An object
    containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
    "mass": "1.3kg", "count": "3" }`.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  alloydb = _messages.MessageField('AlloyDbConnectionProfile', 1)
  cloudsql = _messages.MessageField('CloudSqlConnectionProfile', 2)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  error = _messages.MessageField('Status', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  mysql = _messages.MessageField('MySqlConnectionProfile', 7)
  name = _messages.StringField(8)
  oracle = _messages.MessageField('OracleConnectionProfile', 9)
  postgresql = _messages.MessageField('PostgreSqlConnectionProfile', 10)
  provider = _messages.EnumField('ProviderValueValuesEnum', 11)
  spanner = _messages.MessageField('SpannerConnectionProfile', 12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  updateTime = _messages.StringField(14)


class ConstraintEntity(_messages.Message):
  r"""Constraint is not used as an independent entity, it is retrieved as part
  of another entity such as Table or View.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
    name: The name of the table constraint.
    referenceColumns: Reference columns which may be associated with the
      constraint. For example, if the constraint is a FOREIGN_KEY, this
      represents the list of full names of referenced columns by the foreign
      key.
    referenceTable: Reference table which may be associated with the
      constraint. For example, if the constraint is a FOREIGN_KEY, this
      represents the list of full name of the referenced table by the foreign
      key.
    tableColumns: Table columns used as part of the Constraint, for example
      primary key constraint should list the columns which constitutes the
      key.
    tableName: Table which is associated with the constraint. In case the
      constraint is defined on a table, this field is left empty as this
      information is stored in parent_name. However, if constraint is defined
      on a view, this field stores the table name on which the view is
      defined.
    type: Type of constraint, for example unique, primary key, foreign key
      (currently only primary key is supported).
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  name = _messages.StringField(2)
  referenceColumns = _messages.StringField(3, repeated=True)
  referenceTable = _messages.StringField(4)
  tableColumns = _messages.StringField(5, repeated=True)
  tableName = _messages.StringField(6)
  type = _messages.StringField(7)


class ConversionWorkspace(_messages.Message):
  r"""The main conversion workspace resource entity.

  Messages:
    GlobalSettingsValue: Optional. A generic list of settings for the
      workspace. The settings are database pair dependant and can indicate
      default behavior for the mapping rules engine or turn on or off specific
      features. Such examples can be: convert_foreign_key_to_interleave=true,
      skip_triggers=false, ignore_non_table_synonyms=true

  Fields:
    createTime: Output only. The timestamp when the workspace resource was
      created.
    destination: Required. The destination engine details.
    displayName: Optional. The display name for the workspace.
    globalSettings: Optional. A generic list of settings for the workspace.
      The settings are database pair dependant and can indicate default
      behavior for the mapping rules engine or turn on or off specific
      features. Such examples can be: convert_foreign_key_to_interleave=true,
      skip_triggers=false, ignore_non_table_synonyms=true
    hasUncommittedChanges: Output only. Whether the workspace has uncommitted
      changes (changes which were made after the workspace was committed).
    latestCommitId: Output only. The latest commit ID.
    latestCommitTime: Output only. The timestamp when the workspace was
      committed.
    name: Full name of the workspace resource, in the form of: projects/{proje
      ct}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
    source: Required. The source engine details.
    updateTime: Output only. The timestamp when the workspace resource was
      last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class GlobalSettingsValue(_messages.Message):
    r"""Optional. A generic list of settings for the workspace. The settings
    are database pair dependant and can indicate default behavior for the
    mapping rules engine or turn on or off specific features. Such examples
    can be: convert_foreign_key_to_interleave=true, skip_triggers=false,
    ignore_non_table_synonyms=true

    Messages:
      AdditionalProperty: An additional property for a GlobalSettingsValue
        object.

    Fields:
      additionalProperties: Additional properties of type GlobalSettingsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a GlobalSettingsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  destination = _messages.MessageField('DatabaseEngineInfo', 2)
  displayName = _messages.StringField(3)
  globalSettings = _messages.MessageField('GlobalSettingsValue', 4)
  hasUncommittedChanges = _messages.BooleanField(5)
  latestCommitId = _messages.StringField(6)
  latestCommitTime = _messages.StringField(7)
  name = _messages.StringField(8)
  source = _messages.MessageField('DatabaseEngineInfo', 9)
  updateTime = _messages.StringField(10)


class ConversionWorkspaceInfo(_messages.Message):
  r"""A conversion workspace's version.

  Fields:
    commitId: The commit ID of the conversion workspace.
    name: The resource name (URI) of the conversion workspace.
  """

  commitId = _messages.StringField(1)
  name = _messages.StringField(2)


class ConvertConversionWorkspaceRequest(_messages.Message):
  r"""Request message for 'ConvertConversionWorkspace' request.

  Fields:
    autoCommit: Optional. Specifies whether the conversion workspace is to be
      committed automatically after the conversion.
    convertFullPath: Optional. Automatically convert the full entity path for
      each entity specified by the filter. For example, if the filter
      specifies a table, that table schema (and database if there is one) will
      also be converted.
    filter: Optional. Filter the entities to convert. Leaving this field empty
      will convert all of the entities. Supports Google AIP-160 style
      filtering.
  """

  autoCommit = _messages.BooleanField(1)
  convertFullPath = _messages.BooleanField(2)
  filter = _messages.StringField(3)


class ConvertJobDetails(_messages.Message):
  r"""Details regarding a Convert background job.

  Fields:
    filter: Output only. AIP-160 based filter used to specify the entities to
      convert
  """

  filter = _messages.StringField(1)


class ConvertRowIdToColumn(_messages.Message):
  r"""Options to configure rule type ConvertROWIDToColumn. The rule is used to
  add column rowid to destination tables based on an Oracle rowid
  function/property. The rule filter field can refer to one or more entities.
  The rule scope can be one of: Table. This rule requires additional filter to
  be specified beyond the basic rule filter field, which is whether or not to
  work on tables which already have a primary key defined.

  Fields:
    onlyIfNoPrimaryKey: Required. Only work on tables without primary key
      defined
  """

  onlyIfNoPrimaryKey = _messages.BooleanField(1)


class DatabaseEngineInfo(_messages.Message):
  r"""The type and version of a source or destination database.

  Enums:
    EngineValueValuesEnum: Required. Engine type.

  Fields:
    engine: Required. Engine type.
    version: Required. Engine named version, for example 12.c.1.
  """

  class EngineValueValuesEnum(_messages.Enum):
    r"""Required. Engine type.

    Values:
      DATABASE_ENGINE_UNSPECIFIED: The source database engine of the migration
        job is unknown.
      MYSQL: The source engine is MySQL.
      POSTGRESQL: The source engine is PostgreSQL.
      ORACLE: The source engine is Oracle.
    """
    DATABASE_ENGINE_UNSPECIFIED = 0
    MYSQL = 1
    POSTGRESQL = 2
    ORACLE = 3

  engine = _messages.EnumField('EngineValueValuesEnum', 1)
  version = _messages.StringField(2)


class DatabaseEntity(_messages.Message):
  r"""The base entity type for all the database related entities. The message
  contains the entity name, the name of its parent, the entity type, and the
  specific details per entity type.

  Enums:
    EntityTypeValueValuesEnum: The type of the database entity (table, view,
      index, ...).
    TreeValueValuesEnum: The type of tree the entity belongs to.

  Fields:
    database: Database.
    databaseFunction: Function.
    databasePackage: Package.
    entityDdl: Details about the entity DDL script. Multiple DDL scripts are
      provided for child entities such as a table entity will have one DDL for
      the table with additional DDLs for each index, constraint and such.
    entityType: The type of the database entity (table, view, index, ...).
    issues: Details about the various issues found for the entity.
    mappings: Details about entity mappings. For source tree entities, this
      holds the draft entities which were generated by the mapping rules. For
      draft tree entities, this holds the source entities which were converted
      to form the draft entity. Destination entities will have no mapping
      details.
    materializedView: Materialized view.
    parentEntity: The full name of the parent entity (e.g. schema name).
    schema: Schema.
    sequence: Sequence.
    shortName: The short name (e.g. table name) of the entity.
    storedProcedure: Stored procedure.
    synonym: Synonym.
    table: Table.
    tree: The type of tree the entity belongs to.
    udt: UDT.
    view: View.
  """

  class EntityTypeValueValuesEnum(_messages.Enum):
    r"""The type of the database entity (table, view, index, ...).

    Values:
      DATABASE_ENTITY_TYPE_UNSPECIFIED: Unspecified database entity type.
      DATABASE_ENTITY_TYPE_SCHEMA: Schema.
      DATABASE_ENTITY_TYPE_TABLE: Table.
      DATABASE_ENTITY_TYPE_COLUMN: Column.
      DATABASE_ENTITY_TYPE_CONSTRAINT: Constraint.
      DATABASE_ENTITY_TYPE_INDEX: Index.
      DATABASE_ENTITY_TYPE_TRIGGER: Trigger.
      DATABASE_ENTITY_TYPE_VIEW: View.
      DATABASE_ENTITY_TYPE_SEQUENCE: Sequence.
      DATABASE_ENTITY_TYPE_STORED_PROCEDURE: Stored Procedure.
      DATABASE_ENTITY_TYPE_FUNCTION: Function.
      DATABASE_ENTITY_TYPE_SYNONYM: Synonym.
      DATABASE_ENTITY_TYPE_DATABASE_PACKAGE: Package.
      DATABASE_ENTITY_TYPE_UDT: UDT.
      DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW: Materialized View.
      DATABASE_ENTITY_TYPE_DATABASE: Database.
    """
    DATABASE_ENTITY_TYPE_UNSPECIFIED = 0
    DATABASE_ENTITY_TYPE_SCHEMA = 1
    DATABASE_ENTITY_TYPE_TABLE = 2
    DATABASE_ENTITY_TYPE_COLUMN = 3
    DATABASE_ENTITY_TYPE_CONSTRAINT = 4
    DATABASE_ENTITY_TYPE_INDEX = 5
    DATABASE_ENTITY_TYPE_TRIGGER = 6
    DATABASE_ENTITY_TYPE_VIEW = 7
    DATABASE_ENTITY_TYPE_SEQUENCE = 8
    DATABASE_ENTITY_TYPE_STORED_PROCEDURE = 9
    DATABASE_ENTITY_TYPE_FUNCTION = 10
    DATABASE_ENTITY_TYPE_SYNONYM = 11
    DATABASE_ENTITY_TYPE_DATABASE_PACKAGE = 12
    DATABASE_ENTITY_TYPE_UDT = 13
    DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW = 14
    DATABASE_ENTITY_TYPE_DATABASE = 15

  class TreeValueValuesEnum(_messages.Enum):
    r"""The type of tree the entity belongs to.

    Values:
      TREE_TYPE_UNSPECIFIED: Tree type unspecified.
      SOURCE: Tree of entities loaded from a source database.
      DRAFT: Tree of entities converted from the source tree using the mapping
        rules.
      DESTINATION: Tree of entities observed on the destination database.
    """
    TREE_TYPE_UNSPECIFIED = 0
    SOURCE = 1
    DRAFT = 2
    DESTINATION = 3

  database = _messages.MessageField('DatabaseInstanceEntity', 1)
  databaseFunction = _messages.MessageField('FunctionEntity', 2)
  databasePackage = _messages.MessageField('PackageEntity', 3)
  entityDdl = _messages.MessageField('EntityDdl', 4, repeated=True)
  entityType = _messages.EnumField('EntityTypeValueValuesEnum', 5)
  issues = _messages.MessageField('EntityIssue', 6, repeated=True)
  mappings = _messages.MessageField('EntityMapping', 7, repeated=True)
  materializedView = _messages.MessageField('MaterializedViewEntity', 8)
  parentEntity = _messages.StringField(9)
  schema = _messages.MessageField('SchemaEntity', 10)
  sequence = _messages.MessageField('SequenceEntity', 11)
  shortName = _messages.StringField(12)
  storedProcedure = _messages.MessageField('StoredProcedureEntity', 13)
  synonym = _messages.MessageField('SynonymEntity', 14)
  table = _messages.MessageField('TableEntity', 15)
  tree = _messages.EnumField('TreeValueValuesEnum', 16)
  udt = _messages.MessageField('UDTEntity', 17)
  view = _messages.MessageField('ViewEntity', 18)


class DatabaseInstanceEntity(_messages.Message):
  r"""DatabaseInstance acts as a parent entity to other database entities.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)


class DatabaseType(_messages.Message):
  r"""A message defining the database engine and provider.

  Enums:
    EngineValueValuesEnum: The database engine.
    ProviderValueValuesEnum: The database provider.

  Fields:
    engine: The database engine.
    provider: The database provider.
  """

  class EngineValueValuesEnum(_messages.Enum):
    r"""The database engine.

    Values:
      DATABASE_ENGINE_UNSPECIFIED: The source database engine of the migration
        job is unknown.
      MYSQL: The source engine is MySQL.
      POSTGRESQL: The source engine is PostgreSQL.
      ORACLE: The source engine is Oracle.
    """
    DATABASE_ENGINE_UNSPECIFIED = 0
    MYSQL = 1
    POSTGRESQL = 2
    ORACLE = 3

  class ProviderValueValuesEnum(_messages.Enum):
    r"""The database provider.

    Values:
      DATABASE_PROVIDER_UNSPECIFIED: The database provider is unknown.
      CLOUDSQL: CloudSQL runs the database.
      RDS: RDS runs the database.
      AURORA: Amazon Aurora.
      ALLOYDB: AlloyDB.
    """
    DATABASE_PROVIDER_UNSPECIFIED = 0
    CLOUDSQL = 1
    RDS = 2
    AURORA = 3
    ALLOYDB = 4

  engine = _messages.EnumField('EngineValueValuesEnum', 1)
  provider = _messages.EnumField('ProviderValueValuesEnum', 2)


class DatamigrationProjectsLocationsConnectionProfilesCreateRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesCreateRequest object.

  Fields:
    connectionProfile: A ConnectionProfile resource to be passed as the
      request body.
    connectionProfileId: Required. The connection profile identifier.
    parent: Required. The parent which owns this collection of connection
      profiles.
    requestId: Optional. A unique ID used to identify the request. If the
      server receives two requests with the same ID, then the second request
      is ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
    skipValidation: Optional. Create the connection profile without validating
      it. The default is false. Only supported for Oracle connection profiles.
    validateOnly: Optional. Only validate the connection profile, but don't
      create any resources. The default is false. Only supported for Oracle
      connection profiles.
  """

  connectionProfile = _messages.MessageField('ConnectionProfile', 1)
  connectionProfileId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  skipValidation = _messages.BooleanField(5)
  validateOnly = _messages.BooleanField(6)


class DatamigrationProjectsLocationsConnectionProfilesDeleteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesDeleteRequest object.

  Fields:
    force: In case of force delete, the CloudSQL replica database is also
      deleted (only for CloudSQL connection profile).
    name: Required. Name of the connection profile resource to delete.
    requestId: A unique ID used to identify the request. If the server
      receives two requests with the same ID, then the second request is
      ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class DatamigrationProjectsLocationsConnectionProfilesGetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsConnectionProfilesGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesGetRequest object.

  Fields:
    name: Required. Name of the connection profile resource to get.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsConnectionProfilesListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesListRequest object.

  Fields:
    filter: A filter expression that filters connection profiles listed in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be either =, !=, >, or <. For example, list connection profiles created
      this year by specifying **createTime %gt;
      2020-01-01T00:00:00.000000000Z**. You can also filter nested fields. For
      example, you could specify **mySql.username = %lt;my_username%gt;** to
      list all connection profiles configured to connect with a specific
      username.
    orderBy: A comma-separated list of fields to order results according to.
    pageSize: The maximum number of connection profiles to return. The service
      may return fewer than this value. If unspecified, at most 50 connection
      profiles will be returned. The maximum value is 1000; values above 1000
      are coerced to 1000.
    pageToken: A page token, received from a previous `ListConnectionProfiles`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListConnectionProfiles` must match the
      call that provided the page token.
    parent: Required. The parent which owns this collection of connection
      profiles.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DatamigrationProjectsLocationsConnectionProfilesPatchRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesPatchRequest object.

  Fields:
    connectionProfile: A ConnectionProfile resource to be passed as the
      request body.
    name: The name of this connection profile resource in the form of projects
      /{project}/locations/{location}/connectionProfiles/{connectionProfile}.
    requestId: Optional. A unique ID used to identify the request. If the
      server receives two requests with the same ID, then the second request
      is ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
    skipValidation: Optional. Update the connection profile without validating
      it. The default is false. Only supported for Oracle connection profiles.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten by the update in the conversion workspace resource.
    validateOnly: Optional. Only validate the connection profile, but don't
      update any resources. The default is false. Only supported for Oracle
      connection profiles.
  """

  connectionProfile = _messages.MessageField('ConnectionProfile', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  skipValidation = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class DatamigrationProjectsLocationsConnectionProfilesSetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConnectionProfilesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatamigrationProjectsLocationsConnectionProfilesTestIamPermissionsRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsConnectionProfilesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DatamigrationProjectsLocationsConversionWorkspacesApplyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesApplyRequest object.

  Fields:
    applyConversionWorkspaceRequest: A ApplyConversionWorkspaceRequest
      resource to be passed as the request body.
    name: Required. The name of the conversion workspace resource for which to
      apply the draft tree. Must be in the form of: projects/{project}/locatio
      ns/{location}/conversionWorkspaces/{conversion_workspace}.
  """

  applyConversionWorkspaceRequest = _messages.MessageField('ApplyConversionWorkspaceRequest', 1)
  name = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesCommitRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesCommitRequest
  object.

  Fields:
    commitConversionWorkspaceRequest: A CommitConversionWorkspaceRequest
      resource to be passed as the request body.
    name: Required. Name of the conversion workspace resource to commit.
  """

  commitConversionWorkspaceRequest = _messages.MessageField('CommitConversionWorkspaceRequest', 1)
  name = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesConvertRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesConvertRequest
  object.

  Fields:
    convertConversionWorkspaceRequest: A ConvertConversionWorkspaceRequest
      resource to be passed as the request body.
    name: Name of the conversion workspace resource to convert in the form of:
      projects/{project}/locations/{location}/conversionWorkspaces/{conversion
      _workspace}.
  """

  convertConversionWorkspaceRequest = _messages.MessageField('ConvertConversionWorkspaceRequest', 1)
  name = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesCreateRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesCreateRequest
  object.

  Fields:
    conversionWorkspace: A ConversionWorkspace resource to be passed as the
      request body.
    conversionWorkspaceId: Required. The ID of the conversion workspace to
      create.
    parent: Required. The parent which owns this collection of conversion
      workspaces.
    requestId: A unique ID used to identify the request. If the server
      receives two requests with the same ID, then the second request is
      ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  conversionWorkspace = _messages.MessageField('ConversionWorkspace', 1)
  conversionWorkspaceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class DatamigrationProjectsLocationsConversionWorkspacesDeleteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesDeleteRequest
  object.

  Fields:
    force: Force delete the conversion workspace, even if there's a running
      migration that is using the workspace.
    name: Required. Name of the conversion workspace resource to delete.
    requestId: A unique ID used to identify the request. If the server
      receives two requests with the same ID, then the second request is
      ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class DatamigrationProjectsLocationsConversionWorkspacesDescribeConversionWorkspaceRevisionsRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesDescribeConversionWo
  rkspaceRevisionsRequest object.

  Fields:
    commitId: Optional. Optional filter to request a specific commit ID.
    conversionWorkspace: Required. Name of the conversion workspace resource
      whose revisions are listed. Must be in the form of: projects/{project}/l
      ocations/{location}/conversionWorkspaces/{conversion_workspace}.
  """

  commitId = _messages.StringField(1)
  conversionWorkspace = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesDescribeDatabaseEntitiesRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesDescribeDatabaseEnti
  tiesRequest object.

  Enums:
    TreeValueValuesEnum: Required. The tree to fetch.
    ViewValueValuesEnum: Optional. Results view based on AIP-157

  Fields:
    commitId: Optional. Request a specific commit ID. If not specified, the
      entities from the latest commit are returned.
    conversionWorkspace: Required. Name of the conversion workspace resource
      whose database entities are described. Must be in the form of: projects/
      {project}/locations/{location}/conversionWorkspaces/{conversion_workspac
      e}.
    filter: Optional. Filter the returned entities based on AIP-160 standard.
    pageSize: Optional. The maximum number of entities to return. The service
      may return fewer entities than the value specifies.
    pageToken: Optional. The nextPageToken value received in the previous call
      to conversionWorkspace.describeDatabaseEntities, used in the subsequent
      request to retrieve the next page of results. On first call this should
      be left blank. When paginating, all other parameters provided to
      conversionWorkspace.describeDatabaseEntities must match the call that
      provided the page token.
    tree: Required. The tree to fetch.
    uncommitted: Optional. Whether to retrieve the latest committed version of
      the entities or the latest version. This field is ignored if a specific
      commit_id is specified.
    view: Optional. Results view based on AIP-157
  """

  class TreeValueValuesEnum(_messages.Enum):
    r"""Required. The tree to fetch.

    Values:
      DB_TREE_TYPE_UNSPECIFIED: Unspecified tree type.
      SOURCE_TREE: The source database tree.
      DRAFT_TREE: The draft database tree.
      DESTINATION_TREE: The destination database tree.
    """
    DB_TREE_TYPE_UNSPECIFIED = 0
    SOURCE_TREE = 1
    DRAFT_TREE = 2
    DESTINATION_TREE = 3

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Results view based on AIP-157

    Values:
      DATABASE_ENTITY_VIEW_UNSPECIFIED: Unspecified view. Defaults to basic
        view.
      DATABASE_ENTITY_VIEW_BASIC: Default view. Does not return DDLs or
        Issues.
      DATABASE_ENTITY_VIEW_FULL: Return full entity details including
        mappings, ddl and issues.
      DATABASE_ENTITY_VIEW_ROOT_SUMMARY: Top-most (Database, Schema) nodes
        which are returned contains summary details for their decendents such
        as the number of entities per type and issues rollups. When this view
        is used, only a single page of result is returned and the page_size
        property of the request is ignored. The returned page will only
        include the top-most node types.
    """
    DATABASE_ENTITY_VIEW_UNSPECIFIED = 0
    DATABASE_ENTITY_VIEW_BASIC = 1
    DATABASE_ENTITY_VIEW_FULL = 2
    DATABASE_ENTITY_VIEW_ROOT_SUMMARY = 3

  commitId = _messages.StringField(1)
  conversionWorkspace = _messages.StringField(2, required=True)
  filter = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  tree = _messages.EnumField('TreeValueValuesEnum', 6)
  uncommitted = _messages.BooleanField(7)
  view = _messages.EnumField('ViewValueValuesEnum', 8)


class DatamigrationProjectsLocationsConversionWorkspacesGetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesGetRequest object.

  Fields:
    name: Required. Name of the conversion workspace resource to get.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesListRequest object.

  Fields:
    filter: A filter expression that filters conversion workspaces listed in
      the response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be either =, !=, >, or <. For example, list conversion workspaces
      created this year by specifying **createTime %gt;
      2020-01-01T00:00:00.000000000Z.** You can also filter nested fields. For
      example, you could specify **source.version = "12.c.1"** to select all
      conversion workspaces with source database version equal to 12.c.1.
    pageSize: The maximum number of conversion workspaces to return. The
      service may return fewer than this value. If unspecified, at most 50
      sets are returned.
    pageToken: The nextPageToken value received in the previous call to
      conversionWorkspaces.list, used in the subsequent request to retrieve
      the next page of results. On first call this should be left blank. When
      paginating, all other parameters provided to conversionWorkspaces.list
      must match the call that provided the page token.
    parent: Required. The parent which owns this collection of conversion
      workspaces.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesMappingRulesCreateRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsConversionWorkspacesMappingRulesCreateRequest
  object.

  Fields:
    mappingRule: A MappingRule resource to be passed as the request body.
    mappingRuleId: Required. The ID of the rule to create.
    parent: Required. The parent which owns this collection of mapping rules.
    requestId: A unique ID used to identify the request. If the server
      receives two requests with the same ID, then the second request is
      ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  mappingRule = _messages.MessageField('MappingRule', 1)
  mappingRuleId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class DatamigrationProjectsLocationsConversionWorkspacesMappingRulesDeleteRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsConversionWorkspacesMappingRulesDeleteRequest
  object.

  Fields:
    name: Required. Name of the mapping rule resource to delete.
    requestId: Optional. A unique ID used to identify the request. If the
      server receives two requests with the same ID, then the second request
      is ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class DatamigrationProjectsLocationsConversionWorkspacesMappingRulesGetRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsConversionWorkspacesMappingRulesGetRequest
  object.

  Fields:
    name: Required. Name of the mapping rule resource to get. Example:
      conversionWorkspaces/123/mappingRules/rule123 In order to retrieve a
      previous revision of the mapping rule, also provide the revision ID.
      Example: conversionWorkspace/123/mappingRules/rule123@c7cfa2a8c7cfa2a8c7
      cfa2a8c7cfa2a8
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesMappingRulesImportRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsConversionWorkspacesMappingRulesImportRequest
  object.

  Fields:
    importMappingRulesRequest: A ImportMappingRulesRequest resource to be
      passed as the request body.
    parent: Required. Name of the conversion workspace resource to import the
      rules to in the form of: projects/{project}/locations/{location}/convers
      ionWorkspaces/{conversion_workspace}.
  """

  importMappingRulesRequest = _messages.MessageField('ImportMappingRulesRequest', 1)
  parent = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesMappingRulesListRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsConversionWorkspacesMappingRulesListRequest
  object.

  Fields:
    pageSize: The maximum number of rules to return. The service may return
      fewer than this value.
    pageToken: The nextPageToken value received in the previous call to
      mappingRules.list, used in the subsequent request to retrieve the next
      page of results. On first call this should be left blank. When
      paginating, all other parameters provided to mappingRules.list must
      match the call that provided the page token.
    parent: Required. Name of the conversion workspace resource whose mapping
      rules are listed in the form of: projects/{project}/locations/{location}
      /conversionWorkspaces/{conversion_workspace}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DatamigrationProjectsLocationsConversionWorkspacesPatchRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesPatchRequest object.

  Fields:
    conversionWorkspace: A ConversionWorkspace resource to be passed as the
      request body.
    name: Full name of the workspace resource, in the form of: projects/{proje
      ct}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
    requestId: A unique ID used to identify the request. If the server
      receives two requests with the same ID, then the second request is
      ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten by the update in the conversion workspace resource.
  """

  conversionWorkspace = _messages.MessageField('ConversionWorkspace', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class DatamigrationProjectsLocationsConversionWorkspacesRollbackRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesRollbackRequest
  object.

  Fields:
    name: Required. Name of the conversion workspace resource to roll back to.
    rollbackConversionWorkspaceRequest: A RollbackConversionWorkspaceRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  rollbackConversionWorkspaceRequest = _messages.MessageField('RollbackConversionWorkspaceRequest', 2)


class DatamigrationProjectsLocationsConversionWorkspacesSearchBackgroundJobsRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesSearchBackgroundJobs
  Request object.

  Fields:
    completedUntilTime: Optional. If provided, only returns jobs that
      completed until (not including) the given timestamp.
    conversionWorkspace: Required. Name of the conversion workspace resource
      whose jobs are listed, in the form of: projects/{project}/locations/{loc
      ation}/conversionWorkspaces/{conversion_workspace}.
    maxSize: Optional. The maximum number of jobs to return. The service may
      return fewer than this value. If unspecified, at most 100 jobs are
      returned. The maximum value is 100; values above 100 are coerced to 100.
    returnMostRecentPerJobType: Optional. Whether or not to return just the
      most recent job per job type,
  """

  completedUntilTime = _messages.StringField(1)
  conversionWorkspace = _messages.StringField(2, required=True)
  maxSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  returnMostRecentPerJobType = _messages.BooleanField(4)


class DatamigrationProjectsLocationsConversionWorkspacesSeedRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesSeedRequest object.

  Fields:
    name: Name of the conversion workspace resource to seed with new database
      structure, in the form of: projects/{project}/locations/{location}/conve
      rsionWorkspaces/{conversion_workspace}.
    seedConversionWorkspaceRequest: A SeedConversionWorkspaceRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  seedConversionWorkspaceRequest = _messages.MessageField('SeedConversionWorkspaceRequest', 2)


class DatamigrationProjectsLocationsConversionWorkspacesSetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsConversionWorkspacesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatamigrationProjectsLocationsConversionWorkspacesTestIamPermissionsRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsConversionWorkspacesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DatamigrationProjectsLocationsFetchStaticIpsRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsFetchStaticIpsRequest object.

  Fields:
    name: Required. The resource name for the location for which static IPs
      should be returned. Must be in the format `projects/*/locations/*`.
    pageSize: Maximum number of IPs to return.
    pageToken: A page token, received from a previous `FetchStaticIps` call.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class DatamigrationProjectsLocationsGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class DatamigrationProjectsLocationsMigrationJobsCreateRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsCreateRequest object.

  Fields:
    migrationJob: A MigrationJob resource to be passed as the request body.
    migrationJobId: Required. The ID of the instance to create.
    parent: Required. The parent which owns this collection of migration jobs.
    requestId: Optional. A unique ID used to identify the request. If the
      server receives two requests with the same ID, then the second request
      is ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  migrationJob = _messages.MessageField('MigrationJob', 1)
  migrationJobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class DatamigrationProjectsLocationsMigrationJobsDeleteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsDeleteRequest object.

  Fields:
    force: The destination CloudSQL connection profile is always deleted with
      the migration job. In case of force delete, the destination CloudSQL
      replica database is also deleted.
    name: Required. Name of the migration job resource to delete.
    requestId: A unique ID used to identify the request. If the server
      receives two requests with the same ID, then the second request is
      ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class DatamigrationProjectsLocationsMigrationJobsDemoteDestinationRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsDemoteDestinationRequest
  object.

  Fields:
    demoteDestinationRequest: A DemoteDestinationRequest resource to be passed
      as the request body.
    name: Name of the migration job resource to demote its destination.
  """

  demoteDestinationRequest = _messages.MessageField('DemoteDestinationRequest', 1)
  name = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsMigrationJobsGenerateSshScriptRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsGenerateSshScriptRequest
  object.

  Fields:
    generateSshScriptRequest: A GenerateSshScriptRequest resource to be passed
      as the request body.
    migrationJob: Name of the migration job resource to generate the SSH
      script.
  """

  generateSshScriptRequest = _messages.MessageField('GenerateSshScriptRequest', 1)
  migrationJob = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsMigrationJobsGenerateTcpProxyScriptRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsMigrationJobsGenerateTcpProxyScriptRequest
  object.

  Fields:
    generateTcpProxyScriptRequest: A GenerateTcpProxyScriptRequest resource to
      be passed as the request body.
    migrationJob: Name of the migration job resource to generate the TCP Proxy
      script.
  """

  generateTcpProxyScriptRequest = _messages.MessageField('GenerateTcpProxyScriptRequest', 1)
  migrationJob = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsMigrationJobsGetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsMigrationJobsGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsGetRequest object.

  Fields:
    name: Required. Name of the migration job resource to get.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsMigrationJobsListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsListRequest object.

  Fields:
    filter: A filter expression that filters migration jobs listed in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be either =, !=, >, or <. For example, list migration jobs created this
      year by specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z.**
      You can also filter nested fields. For example, you could specify
      **reverseSshConnectivity.vmIp = "*******"** to select all migration jobs
      connecting through the specific SSH tunnel bastion.
    orderBy: Sort the results based on the migration job name. Valid values
      are: "name", "name asc", and "name desc".
    pageSize: The maximum number of migration jobs to return. The service may
      return fewer than this value. If unspecified, at most 50 migration jobs
      will be returned. The maximum value is 1000; values above 1000 are
      coerced to 1000.
    pageToken: The nextPageToken value received in the previous call to
      migrationJobs.list, used in the subsequent request to retrieve the next
      page of results. On first call this should be left blank. When
      paginating, all other parameters provided to migrationJobs.list must
      match the call that provided the page token.
    parent: Required. The parent which owns this collection of migrationJobs.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DatamigrationProjectsLocationsMigrationJobsPatchRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsPatchRequest object.

  Fields:
    migrationJob: A MigrationJob resource to be passed as the request body.
    name: The name (URI) of this migration job resource, in the form of:
      projects/{project}/locations/{location}/migrationJobs/{migrationJob}.
    requestId: A unique ID used to identify the request. If the server
      receives two requests with the same ID, then the second request is
      ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten by the update in the conversion workspace resource.
  """

  migrationJob = _messages.MessageField('MigrationJob', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class DatamigrationProjectsLocationsMigrationJobsPromoteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsPromoteRequest object.

  Fields:
    name: Name of the migration job resource to promote.
    promoteMigrationJobRequest: A PromoteMigrationJobRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  promoteMigrationJobRequest = _messages.MessageField('PromoteMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsRestartRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsRestartRequest object.

  Fields:
    name: Name of the migration job resource to restart.
    restartMigrationJobRequest: A RestartMigrationJobRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  restartMigrationJobRequest = _messages.MessageField('RestartMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsResumeRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsResumeRequest object.

  Fields:
    name: Name of the migration job resource to resume.
    resumeMigrationJobRequest: A ResumeMigrationJobRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  resumeMigrationJobRequest = _messages.MessageField('ResumeMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsSetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsStartRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsStartRequest object.

  Fields:
    name: Name of the migration job resource to start.
    startMigrationJobRequest: A StartMigrationJobRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  startMigrationJobRequest = _messages.MessageField('StartMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsStopRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsStopRequest object.

  Fields:
    name: Name of the migration job resource to stop.
    stopMigrationJobRequest: A StopMigrationJobRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  stopMigrationJobRequest = _messages.MessageField('StopMigrationJobRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsTestIamPermissionsRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DatamigrationProjectsLocationsMigrationJobsVerifyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsMigrationJobsVerifyRequest object.

  Fields:
    name: Name of the migration job resource to verify.
    verifyMigrationJobRequest: A VerifyMigrationJobRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  verifyMigrationJobRequest = _messages.MessageField('VerifyMigrationJobRequest', 2)


class DatamigrationProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class DatamigrationProjectsLocationsPrivateConnectionsCreateRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsPrivateConnectionsCreateRequest object.

  Fields:
    parent: Required. The parent that owns the collection of
      PrivateConnections.
    privateConnection: A PrivateConnection resource to be passed as the
      request body.
    privateConnectionId: Required. The private connection identifier.
    requestId: Optional. A unique ID used to identify the request. If the
      server receives two requests with the same ID, then the second request
      is ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
    skipValidation: Optional. If set to true, will skip validations.
  """

  parent = _messages.StringField(1, required=True)
  privateConnection = _messages.MessageField('PrivateConnection', 2)
  privateConnectionId = _messages.StringField(3)
  requestId = _messages.StringField(4)
  skipValidation = _messages.BooleanField(5)


class DatamigrationProjectsLocationsPrivateConnectionsDeleteRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsPrivateConnectionsDeleteRequest object.

  Fields:
    name: Required. The name of the private connection to delete.
    requestId: Optional. A unique ID used to identify the request. If the
      server receives two requests with the same ID, then the second request
      is ignored. It is recommended to always set this value to a UUID. The ID
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      and hyphens (-). The maximum length is 40 characters.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class DatamigrationProjectsLocationsPrivateConnectionsGetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsPrivateConnectionsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DatamigrationProjectsLocationsPrivateConnectionsGetRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsPrivateConnectionsGetRequest object.

  Fields:
    name: Required. The name of the private connection to get.
  """

  name = _messages.StringField(1, required=True)


class DatamigrationProjectsLocationsPrivateConnectionsListRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsPrivateConnectionsListRequest object.

  Fields:
    filter: A filter expression that filters private connections listed in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be either =, !=, >, or <. For example, list private connections created
      this year by specifying **createTime %gt;
      2021-01-01T00:00:00.000000000Z**.
    orderBy: Order by fields for the result.
    pageSize: Maximum number of private connections to return. If unspecified,
      at most 50 private connections that are returned. The maximum value is
      1000; values above 1000 are coerced to 1000.
    pageToken: Page token received from a previous `ListPrivateConnections`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListPrivateConnections` must match the
      call that provided the page token.
    parent: Required. The parent that owns the collection of private
      connections.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DatamigrationProjectsLocationsPrivateConnectionsSetIamPolicyRequest(_messages.Message):
  r"""A DatamigrationProjectsLocationsPrivateConnectionsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatamigrationProjectsLocationsPrivateConnectionsTestIamPermissionsRequest(_messages.Message):
  r"""A
  DatamigrationProjectsLocationsPrivateConnectionsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DemoteDestinationRequest(_messages.Message):
  r"""Request message for 'DemoteDestination' request."""


class DescribeConversionWorkspaceRevisionsResponse(_messages.Message):
  r"""Response message for 'DescribeConversionWorkspaceRevisions' request.

  Fields:
    revisions: The list of conversion workspace revisions.
  """

  revisions = _messages.MessageField('ConversionWorkspace', 1, repeated=True)


class DescribeDatabaseEntitiesResponse(_messages.Message):
  r"""Response message for 'DescribeDatabaseEntities' request.

  Fields:
    databaseEntities: The list of database entities for the conversion
      workspace.
    nextPageToken: A token which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  databaseEntities = _messages.MessageField('DatabaseEntity', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class DoubleComparisonFilter(_messages.Message):
  r"""Filter based on relation between source value and compare value of type
  double in ConditionalColumnSetValue

  Enums:
    ValueComparisonValueValuesEnum: Required. Relation between source value
      and compare value

  Fields:
    value: Required. Double compare value to be used
    valueComparison: Required. Relation between source value and compare value
  """

  class ValueComparisonValueValuesEnum(_messages.Enum):
    r"""Required. Relation between source value and compare value

    Values:
      VALUE_COMPARISON_UNSPECIFIED: Value comparison unspecified.
      VALUE_COMPARISON_IF_VALUE_SMALLER_THAN: Value is smaller than the
        Compare value.
      VALUE_COMPARISON_IF_VALUE_SMALLER_EQUAL_THAN: Value is smaller or equal
        than the Compare value.
      VALUE_COMPARISON_IF_VALUE_LARGER_THAN: Value is larger than the Compare
        value.
      VALUE_COMPARISON_IF_VALUE_LARGER_EQUAL_THAN: Value is larger or equal
        than the Compare value.
    """
    VALUE_COMPARISON_UNSPECIFIED = 0
    VALUE_COMPARISON_IF_VALUE_SMALLER_THAN = 1
    VALUE_COMPARISON_IF_VALUE_SMALLER_EQUAL_THAN = 2
    VALUE_COMPARISON_IF_VALUE_LARGER_THAN = 3
    VALUE_COMPARISON_IF_VALUE_LARGER_EQUAL_THAN = 4

  value = _messages.FloatField(1)
  valueComparison = _messages.EnumField('ValueComparisonValueValuesEnum', 2)


class DumpFlag(_messages.Message):
  r"""Dump flag definition.

  Fields:
    name: The name of the flag
    value: The value of the flag.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class DumpFlags(_messages.Message):
  r"""Dump flags definition.

  Fields:
    dumpFlags: The flags for the initial dump.
  """

  dumpFlags = _messages.MessageField('DumpFlag', 1, repeated=True)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfig(_messages.Message):
  r"""EncryptionConfig describes the encryption config of a cluster that is
  encrypted with a CMEK (customer-managed encryption key).

  Fields:
    kmsKeyName: The fully-qualified resource name of the KMS key. Each Cloud
      KMS key is regionalized and has the following format: projects/[PROJECT]
      /locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]
  """

  kmsKeyName = _messages.StringField(1)


class EntityDdl(_messages.Message):
  r"""A single DDL statement for a specific entity

  Enums:
    EntityTypeValueValuesEnum: The entity type (if the DDL is for a sub
      entity).

  Fields:
    ddl: The actual ddl code.
    ddlType: Type of DDL (Create, Alter).
    entity: The name of the database entity the ddl refers to.
    entityType: The entity type (if the DDL is for a sub entity).
    issueId: EntityIssues found for this ddl.
  """

  class EntityTypeValueValuesEnum(_messages.Enum):
    r"""The entity type (if the DDL is for a sub entity).

    Values:
      DATABASE_ENTITY_TYPE_UNSPECIFIED: Unspecified database entity type.
      DATABASE_ENTITY_TYPE_SCHEMA: Schema.
      DATABASE_ENTITY_TYPE_TABLE: Table.
      DATABASE_ENTITY_TYPE_COLUMN: Column.
      DATABASE_ENTITY_TYPE_CONSTRAINT: Constraint.
      DATABASE_ENTITY_TYPE_INDEX: Index.
      DATABASE_ENTITY_TYPE_TRIGGER: Trigger.
      DATABASE_ENTITY_TYPE_VIEW: View.
      DATABASE_ENTITY_TYPE_SEQUENCE: Sequence.
      DATABASE_ENTITY_TYPE_STORED_PROCEDURE: Stored Procedure.
      DATABASE_ENTITY_TYPE_FUNCTION: Function.
      DATABASE_ENTITY_TYPE_SYNONYM: Synonym.
      DATABASE_ENTITY_TYPE_DATABASE_PACKAGE: Package.
      DATABASE_ENTITY_TYPE_UDT: UDT.
      DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW: Materialized View.
      DATABASE_ENTITY_TYPE_DATABASE: Database.
    """
    DATABASE_ENTITY_TYPE_UNSPECIFIED = 0
    DATABASE_ENTITY_TYPE_SCHEMA = 1
    DATABASE_ENTITY_TYPE_TABLE = 2
    DATABASE_ENTITY_TYPE_COLUMN = 3
    DATABASE_ENTITY_TYPE_CONSTRAINT = 4
    DATABASE_ENTITY_TYPE_INDEX = 5
    DATABASE_ENTITY_TYPE_TRIGGER = 6
    DATABASE_ENTITY_TYPE_VIEW = 7
    DATABASE_ENTITY_TYPE_SEQUENCE = 8
    DATABASE_ENTITY_TYPE_STORED_PROCEDURE = 9
    DATABASE_ENTITY_TYPE_FUNCTION = 10
    DATABASE_ENTITY_TYPE_SYNONYM = 11
    DATABASE_ENTITY_TYPE_DATABASE_PACKAGE = 12
    DATABASE_ENTITY_TYPE_UDT = 13
    DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW = 14
    DATABASE_ENTITY_TYPE_DATABASE = 15

  ddl = _messages.StringField(1)
  ddlType = _messages.StringField(2)
  entity = _messages.StringField(3)
  entityType = _messages.EnumField('EntityTypeValueValuesEnum', 4)
  issueId = _messages.StringField(5, repeated=True)


class EntityIssue(_messages.Message):
  r"""Issue related to the entity.

  Enums:
    EntityTypeValueValuesEnum: The entity type (if the DDL is for a sub
      entity).
    SeverityValueValuesEnum: Severity of the issue
    TypeValueValuesEnum: The type of the issue.

  Fields:
    code: Error/Warning code
    ddl: The ddl which caused the issue, if relevant.
    entityType: The entity type (if the DDL is for a sub entity).
    id: Unique Issue ID.
    message: Issue detailed message
    position: The position of the issue found, if relevant.
    severity: Severity of the issue
    type: The type of the issue.
  """

  class EntityTypeValueValuesEnum(_messages.Enum):
    r"""The entity type (if the DDL is for a sub entity).

    Values:
      DATABASE_ENTITY_TYPE_UNSPECIFIED: Unspecified database entity type.
      DATABASE_ENTITY_TYPE_SCHEMA: Schema.
      DATABASE_ENTITY_TYPE_TABLE: Table.
      DATABASE_ENTITY_TYPE_COLUMN: Column.
      DATABASE_ENTITY_TYPE_CONSTRAINT: Constraint.
      DATABASE_ENTITY_TYPE_INDEX: Index.
      DATABASE_ENTITY_TYPE_TRIGGER: Trigger.
      DATABASE_ENTITY_TYPE_VIEW: View.
      DATABASE_ENTITY_TYPE_SEQUENCE: Sequence.
      DATABASE_ENTITY_TYPE_STORED_PROCEDURE: Stored Procedure.
      DATABASE_ENTITY_TYPE_FUNCTION: Function.
      DATABASE_ENTITY_TYPE_SYNONYM: Synonym.
      DATABASE_ENTITY_TYPE_DATABASE_PACKAGE: Package.
      DATABASE_ENTITY_TYPE_UDT: UDT.
      DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW: Materialized View.
      DATABASE_ENTITY_TYPE_DATABASE: Database.
    """
    DATABASE_ENTITY_TYPE_UNSPECIFIED = 0
    DATABASE_ENTITY_TYPE_SCHEMA = 1
    DATABASE_ENTITY_TYPE_TABLE = 2
    DATABASE_ENTITY_TYPE_COLUMN = 3
    DATABASE_ENTITY_TYPE_CONSTRAINT = 4
    DATABASE_ENTITY_TYPE_INDEX = 5
    DATABASE_ENTITY_TYPE_TRIGGER = 6
    DATABASE_ENTITY_TYPE_VIEW = 7
    DATABASE_ENTITY_TYPE_SEQUENCE = 8
    DATABASE_ENTITY_TYPE_STORED_PROCEDURE = 9
    DATABASE_ENTITY_TYPE_FUNCTION = 10
    DATABASE_ENTITY_TYPE_SYNONYM = 11
    DATABASE_ENTITY_TYPE_DATABASE_PACKAGE = 12
    DATABASE_ENTITY_TYPE_UDT = 13
    DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW = 14
    DATABASE_ENTITY_TYPE_DATABASE = 15

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Severity of the issue

    Values:
      ISSUE_SEVERITY_UNSPECIFIED: Unspecified issue severity
      ISSUE_SEVERITY_INFO: Info
      ISSUE_SEVERITY_WARNING: Warning
      ISSUE_SEVERITY_ERROR: Error
    """
    ISSUE_SEVERITY_UNSPECIFIED = 0
    ISSUE_SEVERITY_INFO = 1
    ISSUE_SEVERITY_WARNING = 2
    ISSUE_SEVERITY_ERROR = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the issue.

    Values:
      ISSUE_TYPE_UNSPECIFIED: Unspecified issue type.
      ISSUE_TYPE_DDL: Issue originated from the DDL
      ISSUE_TYPE_APPLY: Issue originated during the apply process
      ISSUE_TYPE_CONVERT: Issue originated during the convert process
    """
    ISSUE_TYPE_UNSPECIFIED = 0
    ISSUE_TYPE_DDL = 1
    ISSUE_TYPE_APPLY = 2
    ISSUE_TYPE_CONVERT = 3

  code = _messages.StringField(1)
  ddl = _messages.StringField(2)
  entityType = _messages.EnumField('EntityTypeValueValuesEnum', 3)
  id = _messages.StringField(4)
  message = _messages.StringField(5)
  position = _messages.MessageField('Position', 6)
  severity = _messages.EnumField('SeverityValueValuesEnum', 7)
  type = _messages.EnumField('TypeValueValuesEnum', 8)


class EntityMapping(_messages.Message):
  r"""Details of the mappings of a database entity.

  Enums:
    DraftTypeValueValuesEnum: Type of draft entity.
    SourceTypeValueValuesEnum: Type of source entity.

  Fields:
    draftEntity: Target entity full name. The draft entity can also include a
      column, index or constraint using the same naming notation
      schema.table.column.
    draftType: Type of draft entity.
    mappingLog: Entity mapping log entries. Multiple rules can be effective
      and contribute changes to a converted entity, such as a rule can handle
      the entity name, another rule can handle an entity type. In addition,
      rules which did not change the entity are also logged along with the
      reason preventing them to do so.
    sourceEntity: Source entity full name. The source entity can also be a
      column, index or constraint using the same naming notation
      schema.table.column.
    sourceType: Type of source entity.
  """

  class DraftTypeValueValuesEnum(_messages.Enum):
    r"""Type of draft entity.

    Values:
      DATABASE_ENTITY_TYPE_UNSPECIFIED: Unspecified database entity type.
      DATABASE_ENTITY_TYPE_SCHEMA: Schema.
      DATABASE_ENTITY_TYPE_TABLE: Table.
      DATABASE_ENTITY_TYPE_COLUMN: Column.
      DATABASE_ENTITY_TYPE_CONSTRAINT: Constraint.
      DATABASE_ENTITY_TYPE_INDEX: Index.
      DATABASE_ENTITY_TYPE_TRIGGER: Trigger.
      DATABASE_ENTITY_TYPE_VIEW: View.
      DATABASE_ENTITY_TYPE_SEQUENCE: Sequence.
      DATABASE_ENTITY_TYPE_STORED_PROCEDURE: Stored Procedure.
      DATABASE_ENTITY_TYPE_FUNCTION: Function.
      DATABASE_ENTITY_TYPE_SYNONYM: Synonym.
      DATABASE_ENTITY_TYPE_DATABASE_PACKAGE: Package.
      DATABASE_ENTITY_TYPE_UDT: UDT.
      DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW: Materialized View.
      DATABASE_ENTITY_TYPE_DATABASE: Database.
    """
    DATABASE_ENTITY_TYPE_UNSPECIFIED = 0
    DATABASE_ENTITY_TYPE_SCHEMA = 1
    DATABASE_ENTITY_TYPE_TABLE = 2
    DATABASE_ENTITY_TYPE_COLUMN = 3
    DATABASE_ENTITY_TYPE_CONSTRAINT = 4
    DATABASE_ENTITY_TYPE_INDEX = 5
    DATABASE_ENTITY_TYPE_TRIGGER = 6
    DATABASE_ENTITY_TYPE_VIEW = 7
    DATABASE_ENTITY_TYPE_SEQUENCE = 8
    DATABASE_ENTITY_TYPE_STORED_PROCEDURE = 9
    DATABASE_ENTITY_TYPE_FUNCTION = 10
    DATABASE_ENTITY_TYPE_SYNONYM = 11
    DATABASE_ENTITY_TYPE_DATABASE_PACKAGE = 12
    DATABASE_ENTITY_TYPE_UDT = 13
    DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW = 14
    DATABASE_ENTITY_TYPE_DATABASE = 15

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""Type of source entity.

    Values:
      DATABASE_ENTITY_TYPE_UNSPECIFIED: Unspecified database entity type.
      DATABASE_ENTITY_TYPE_SCHEMA: Schema.
      DATABASE_ENTITY_TYPE_TABLE: Table.
      DATABASE_ENTITY_TYPE_COLUMN: Column.
      DATABASE_ENTITY_TYPE_CONSTRAINT: Constraint.
      DATABASE_ENTITY_TYPE_INDEX: Index.
      DATABASE_ENTITY_TYPE_TRIGGER: Trigger.
      DATABASE_ENTITY_TYPE_VIEW: View.
      DATABASE_ENTITY_TYPE_SEQUENCE: Sequence.
      DATABASE_ENTITY_TYPE_STORED_PROCEDURE: Stored Procedure.
      DATABASE_ENTITY_TYPE_FUNCTION: Function.
      DATABASE_ENTITY_TYPE_SYNONYM: Synonym.
      DATABASE_ENTITY_TYPE_DATABASE_PACKAGE: Package.
      DATABASE_ENTITY_TYPE_UDT: UDT.
      DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW: Materialized View.
      DATABASE_ENTITY_TYPE_DATABASE: Database.
    """
    DATABASE_ENTITY_TYPE_UNSPECIFIED = 0
    DATABASE_ENTITY_TYPE_SCHEMA = 1
    DATABASE_ENTITY_TYPE_TABLE = 2
    DATABASE_ENTITY_TYPE_COLUMN = 3
    DATABASE_ENTITY_TYPE_CONSTRAINT = 4
    DATABASE_ENTITY_TYPE_INDEX = 5
    DATABASE_ENTITY_TYPE_TRIGGER = 6
    DATABASE_ENTITY_TYPE_VIEW = 7
    DATABASE_ENTITY_TYPE_SEQUENCE = 8
    DATABASE_ENTITY_TYPE_STORED_PROCEDURE = 9
    DATABASE_ENTITY_TYPE_FUNCTION = 10
    DATABASE_ENTITY_TYPE_SYNONYM = 11
    DATABASE_ENTITY_TYPE_DATABASE_PACKAGE = 12
    DATABASE_ENTITY_TYPE_UDT = 13
    DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW = 14
    DATABASE_ENTITY_TYPE_DATABASE = 15

  draftEntity = _messages.StringField(1)
  draftType = _messages.EnumField('DraftTypeValueValuesEnum', 2)
  mappingLog = _messages.MessageField('EntityMappingLogEntry', 3, repeated=True)
  sourceEntity = _messages.StringField(4)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 5)


class EntityMappingLogEntry(_messages.Message):
  r"""A single record of a rule which was used for a mapping.

  Fields:
    mappingComment: Comment.
    ruleId: Which rule caused this log entry.
    ruleRevisionId: Rule revision ID.
  """

  mappingComment = _messages.StringField(1)
  ruleId = _messages.StringField(2)
  ruleRevisionId = _messages.StringField(3)


class EntityMove(_messages.Message):
  r"""Options to configure rule type EntityMove. The rule is used to move an
  entity to a new schema. The rule filter field can refer to one or more
  entities. The rule scope can be one of: Table, Column, Constraint, Index,
  View, Function, Stored Procedure, Materialized View, Sequence, UDT

  Fields:
    newSchema: Required. The new schema
  """

  newSchema = _messages.StringField(1)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FetchStaticIpsResponse(_messages.Message):
  r"""Response message for a 'FetchStaticIps' request.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    staticIps: List of static IPs.
  """

  nextPageToken = _messages.StringField(1)
  staticIps = _messages.StringField(2, repeated=True)


class FilterTableColumns(_messages.Message):
  r"""Options to configure rule type FilterTableColumns. The rule is used to
  filter the list of columns to include or exclude from a table. The rule
  filter field can refer to one entity. The rule scope can be: Table Only one
  of the two lists can be specified for the rule.

  Fields:
    excludeColumns: Optional. List of columns to be excluded for a particular
      table.
    includeColumns: Optional. List of columns to be included for a particular
      table.
  """

  excludeColumns = _messages.StringField(1, repeated=True)
  includeColumns = _messages.StringField(2, repeated=True)


class ForwardSshTunnelConnectivity(_messages.Message):
  r"""Forward SSH Tunnel connectivity.

  Fields:
    hostname: Required. Hostname for the SSH tunnel.
    password: Input only. SSH password.
    port: Port for the SSH tunnel, default value is 22.
    privateKey: Input only. SSH private key.
    username: Required. Username for the SSH tunnel.
  """

  hostname = _messages.StringField(1)
  password = _messages.StringField(2)
  port = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  privateKey = _messages.StringField(4)
  username = _messages.StringField(5)


class FunctionEntity(_messages.Message):
  r"""Function's parent is a schema.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
    sqlCode: The SQL code which creates the function.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  sqlCode = _messages.StringField(2)


class GenerateSshScriptRequest(_messages.Message):
  r"""Request message for 'GenerateSshScript' request.

  Fields:
    vm: Required. Bastion VM Instance name to use or to create.
    vmCreationConfig: The VM creation configuration
    vmPort: The port that will be open on the bastion host.
    vmSelectionConfig: The VM selection configuration
  """

  vm = _messages.StringField(1)
  vmCreationConfig = _messages.MessageField('VmCreationConfig', 2)
  vmPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  vmSelectionConfig = _messages.MessageField('VmSelectionConfig', 4)


class GenerateTcpProxyScriptRequest(_messages.Message):
  r"""Request message for 'GenerateTcpProxyScript' request.

  Fields:
    vmMachineType: Required. The type of the Compute instance that will host
      the proxy.
    vmName: Required. The name of the Compute instance that will host the
      proxy.
    vmSubnet: Required. The name of the subnet the Compute instance will use
      for private connectivity. Must be supplied in the form of
      projects/{project}/regions/{region}/subnetworks/{subnetwork}. Note: the
      region for the subnet must match the Compute instance region.
    vmZone: Optional. The Google Cloud Platform zone to create the VM in. The
      fully qualified name of the zone must be specified, including the region
      name, for example "us-central1-b". If not specified, uses the "-b" zone
      of the destination Connection Profile's region.
  """

  vmMachineType = _messages.StringField(1)
  vmName = _messages.StringField(2)
  vmSubnet = _messages.StringField(3)
  vmZone = _messages.StringField(4)


class GoogleCloudClouddmsV1OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ImportMappingRulesRequest(_messages.Message):
  r"""Request message for 'ImportMappingRules' request.

  Enums:
    RulesFormatValueValuesEnum: Required. The format of the rules content
      file.

  Fields:
    autoCommit: Required. Should the conversion workspace be committed
      automatically after the import operation.
    rulesFiles: Required. One or more rules files.
    rulesFormat: Required. The format of the rules content file.
  """

  class RulesFormatValueValuesEnum(_messages.Enum):
    r"""Required. The format of the rules content file.

    Values:
      IMPORT_RULES_FILE_FORMAT_UNSPECIFIED: Unspecified rules format.
      IMPORT_RULES_FILE_FORMAT_HARBOUR_BRIDGE_SESSION_FILE: HarbourBridge
        session file.
      IMPORT_RULES_FILE_FORMAT_ORATOPG_CONFIG_FILE: Ora2Pg configuration file.
    """
    IMPORT_RULES_FILE_FORMAT_UNSPECIFIED = 0
    IMPORT_RULES_FILE_FORMAT_HARBOUR_BRIDGE_SESSION_FILE = 1
    IMPORT_RULES_FILE_FORMAT_ORATOPG_CONFIG_FILE = 2

  autoCommit = _messages.BooleanField(1)
  rulesFiles = _messages.MessageField('RulesFile', 2, repeated=True)
  rulesFormat = _messages.EnumField('RulesFormatValueValuesEnum', 3)


class ImportRulesJobDetails(_messages.Message):
  r"""Details regarding an Import Rules background job.

  Enums:
    FileFormatValueValuesEnum: Output only. The requested file format.

  Fields:
    fileFormat: Output only. The requested file format.
    files: Output only. File names used for the import rules job.
  """

  class FileFormatValueValuesEnum(_messages.Enum):
    r"""Output only. The requested file format.

    Values:
      IMPORT_RULES_FILE_FORMAT_UNSPECIFIED: Unspecified rules format.
      IMPORT_RULES_FILE_FORMAT_HARBOUR_BRIDGE_SESSION_FILE: HarbourBridge
        session file.
      IMPORT_RULES_FILE_FORMAT_ORATOPG_CONFIG_FILE: Ora2Pg configuration file.
    """
    IMPORT_RULES_FILE_FORMAT_UNSPECIFIED = 0
    IMPORT_RULES_FILE_FORMAT_HARBOUR_BRIDGE_SESSION_FILE = 1
    IMPORT_RULES_FILE_FORMAT_ORATOPG_CONFIG_FILE = 2

  fileFormat = _messages.EnumField('FileFormatValueValuesEnum', 1)
  files = _messages.StringField(2, repeated=True)


class IndexEntity(_messages.Message):
  r"""Index is not used as an independent entity, it is retrieved as part of a
  Table entity.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
    name: The name of the index.
    tableColumns: Table columns used as part of the Index, for example B-TREE
      index should list the columns which constitutes the index.
    type: Type of index, for example B-TREE.
    unique: Boolean value indicating whether the index is unique.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  name = _messages.StringField(2)
  tableColumns = _messages.StringField(3, repeated=True)
  type = _messages.StringField(4)
  unique = _messages.BooleanField(5)


class IntComparisonFilter(_messages.Message):
  r"""Filter based on relation between source value and compare value of type
  integer in ConditionalColumnSetValue

  Enums:
    ValueComparisonValueValuesEnum: Required. Relation between source value
      and compare value

  Fields:
    value: Required. Integer compare value to be used
    valueComparison: Required. Relation between source value and compare value
  """

  class ValueComparisonValueValuesEnum(_messages.Enum):
    r"""Required. Relation between source value and compare value

    Values:
      VALUE_COMPARISON_UNSPECIFIED: Value comparison unspecified.
      VALUE_COMPARISON_IF_VALUE_SMALLER_THAN: Value is smaller than the
        Compare value.
      VALUE_COMPARISON_IF_VALUE_SMALLER_EQUAL_THAN: Value is smaller or equal
        than the Compare value.
      VALUE_COMPARISON_IF_VALUE_LARGER_THAN: Value is larger than the Compare
        value.
      VALUE_COMPARISON_IF_VALUE_LARGER_EQUAL_THAN: Value is larger or equal
        than the Compare value.
    """
    VALUE_COMPARISON_UNSPECIFIED = 0
    VALUE_COMPARISON_IF_VALUE_SMALLER_THAN = 1
    VALUE_COMPARISON_IF_VALUE_SMALLER_EQUAL_THAN = 2
    VALUE_COMPARISON_IF_VALUE_LARGER_THAN = 3
    VALUE_COMPARISON_IF_VALUE_LARGER_EQUAL_THAN = 4

  value = _messages.IntegerField(1)
  valueComparison = _messages.EnumField('ValueComparisonValueValuesEnum', 2)


class ListConnectionProfilesResponse(_messages.Message):
  r"""Response message for 'ListConnectionProfiles' request.

  Fields:
    connectionProfiles: The response list of connection profiles.
    nextPageToken: A token which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  connectionProfiles = _messages.MessageField('ConnectionProfile', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListConversionWorkspacesResponse(_messages.Message):
  r"""Response message for 'ListConversionWorkspaces' request.

  Fields:
    conversionWorkspaces: The list of conversion workspace objects.
    nextPageToken: A token which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  conversionWorkspaces = _messages.MessageField('ConversionWorkspace', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMappingRulesResponse(_messages.Message):
  r"""Response message for 'ListMappingRulesRequest' request.

  Fields:
    mappingRules: The list of conversion workspace mapping rules.
    nextPageToken: A token which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  mappingRules = _messages.MessageField('MappingRule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMigrationJobsResponse(_messages.Message):
  r"""Response message for 'ListMigrationJobs' request.

  Fields:
    migrationJobs: The list of migration jobs objects.
    nextPageToken: A token which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  migrationJobs = _messages.MessageField('MigrationJob', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPrivateConnectionsResponse(_messages.Message):
  r"""Response message for 'ListPrivateConnections' request.

  Fields:
    nextPageToken: A token which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    privateConnections: List of private connections.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  privateConnections = _messages.MessageField('PrivateConnection', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MachineConfig(_messages.Message):
  r"""MachineConfig describes the configuration of a machine.

  Fields:
    cpuCount: The number of CPU's in the VM instance.
  """

  cpuCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class MappingRule(_messages.Message):
  r"""Definition of a transformation that is to be applied to a group of
  entities in the source schema. Several such transformations can be applied
  to an entity sequentially to define the corresponding entity in the target
  schema.

  Enums:
    RuleScopeValueValuesEnum: Required. The rule scope
    StateValueValuesEnum: Optional. The mapping rule state

  Fields:
    conditionalColumnSetValue: Optional. Rule to specify how the data
      contained in a column should be transformed (such as trimmed, rounded,
      etc) provided that the data meets certain criteria.
    convertRowidColumn: Optional. Rule to specify how multiple tables should
      be converted with an additional rowid column.
    displayName: Optional. A human readable name
    entityMove: Optional. Rule to specify how multiple entities should be
      relocated into a different schema.
    filter: Required. The rule filter
    filterTableColumns: Optional. Rule to specify the list of columns to
      include or exclude from a table.
    multiColumnDataTypeChange: Optional. Rule to specify how multiple columns
      should be converted to a different data type.
    multiEntityRename: Optional. Rule to specify how multiple entities should
      be renamed.
    name: Full name of the mapping rule resource, in the form of: projects/{pr
      oject}/locations/{location}/conversionWorkspaces/{set}/mappingRule/{rule
      }.
    revisionCreateTime: Output only. The timestamp that the revision was
      created.
    revisionId: Output only. The revision ID of the mapping rule. A new
      revision is committed whenever the mapping rule is changed in any way.
      The format is an 8-character hexadecimal string.
    ruleOrder: Required. The order in which the rule is applied. Lower order
      rules are applied before higher value rules so they may end up being
      overridden.
    ruleScope: Required. The rule scope
    setTablePrimaryKey: Optional. Rule to specify the primary key for a table
    singleColumnChange: Optional. Rule to specify how a single column is
      converted.
    singleEntityRename: Optional. Rule to specify how a single entity should
      be renamed.
    singlePackageChange: Optional. Rule to specify how a single package is
      converted.
    sourceSqlChange: Optional. Rule to change the sql code for an entity, for
      example, function, procedure.
    state: Optional. The mapping rule state
  """

  class RuleScopeValueValuesEnum(_messages.Enum):
    r"""Required. The rule scope

    Values:
      DATABASE_ENTITY_TYPE_UNSPECIFIED: Unspecified database entity type.
      DATABASE_ENTITY_TYPE_SCHEMA: Schema.
      DATABASE_ENTITY_TYPE_TABLE: Table.
      DATABASE_ENTITY_TYPE_COLUMN: Column.
      DATABASE_ENTITY_TYPE_CONSTRAINT: Constraint.
      DATABASE_ENTITY_TYPE_INDEX: Index.
      DATABASE_ENTITY_TYPE_TRIGGER: Trigger.
      DATABASE_ENTITY_TYPE_VIEW: View.
      DATABASE_ENTITY_TYPE_SEQUENCE: Sequence.
      DATABASE_ENTITY_TYPE_STORED_PROCEDURE: Stored Procedure.
      DATABASE_ENTITY_TYPE_FUNCTION: Function.
      DATABASE_ENTITY_TYPE_SYNONYM: Synonym.
      DATABASE_ENTITY_TYPE_DATABASE_PACKAGE: Package.
      DATABASE_ENTITY_TYPE_UDT: UDT.
      DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW: Materialized View.
      DATABASE_ENTITY_TYPE_DATABASE: Database.
    """
    DATABASE_ENTITY_TYPE_UNSPECIFIED = 0
    DATABASE_ENTITY_TYPE_SCHEMA = 1
    DATABASE_ENTITY_TYPE_TABLE = 2
    DATABASE_ENTITY_TYPE_COLUMN = 3
    DATABASE_ENTITY_TYPE_CONSTRAINT = 4
    DATABASE_ENTITY_TYPE_INDEX = 5
    DATABASE_ENTITY_TYPE_TRIGGER = 6
    DATABASE_ENTITY_TYPE_VIEW = 7
    DATABASE_ENTITY_TYPE_SEQUENCE = 8
    DATABASE_ENTITY_TYPE_STORED_PROCEDURE = 9
    DATABASE_ENTITY_TYPE_FUNCTION = 10
    DATABASE_ENTITY_TYPE_SYNONYM = 11
    DATABASE_ENTITY_TYPE_DATABASE_PACKAGE = 12
    DATABASE_ENTITY_TYPE_UDT = 13
    DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW = 14
    DATABASE_ENTITY_TYPE_DATABASE = 15

  class StateValueValuesEnum(_messages.Enum):
    r"""Optional. The mapping rule state

    Values:
      STATE_UNSPECIFIED: The state of the mapping rule is unknown.
      ENABLED: The rule is enabled.
      DISABLED: The rule is disabled.
      DELETED: The rule is logically deleted.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    DELETED = 3

  conditionalColumnSetValue = _messages.MessageField('ConditionalColumnSetValue', 1)
  convertRowidColumn = _messages.MessageField('ConvertRowIdToColumn', 2)
  displayName = _messages.StringField(3)
  entityMove = _messages.MessageField('EntityMove', 4)
  filter = _messages.MessageField('MappingRuleFilter', 5)
  filterTableColumns = _messages.MessageField('FilterTableColumns', 6)
  multiColumnDataTypeChange = _messages.MessageField('MultiColumnDatatypeChange', 7)
  multiEntityRename = _messages.MessageField('MultiEntityRename', 8)
  name = _messages.StringField(9)
  revisionCreateTime = _messages.StringField(10)
  revisionId = _messages.StringField(11)
  ruleOrder = _messages.IntegerField(12)
  ruleScope = _messages.EnumField('RuleScopeValueValuesEnum', 13)
  setTablePrimaryKey = _messages.MessageField('SetTablePrimaryKey', 14)
  singleColumnChange = _messages.MessageField('SingleColumnChange', 15)
  singleEntityRename = _messages.MessageField('SingleEntityRename', 16)
  singlePackageChange = _messages.MessageField('SinglePackageChange', 17)
  sourceSqlChange = _messages.MessageField('SourceSqlChange', 18)
  state = _messages.EnumField('StateValueValuesEnum', 19)


class MappingRuleFilter(_messages.Message):
  r"""A filter defining the entities that a mapping rule should be applied to.
  When more than one field is specified, the rule is applied only to entities
  which match all the fields.

  Fields:
    entities: Optional. The rule should be applied to specific entities
      defined by their fully qualified names.
    entityNameContains: Optional. The rule should be applied to entities whose
      non-qualified name contains the given string.
    entityNamePrefix: Optional. The rule should be applied to entities whose
      non-qualified name starts with the given prefix.
    entityNameSuffix: Optional. The rule should be applied to entities whose
      non-qualified name ends with the given suffix.
    parentEntity: Optional. The rule should be applied to entities whose
      parent entity (fully qualified name) matches the given value. For
      example, if the rule applies to a table entity, the expected value
      should be a schema (schema). If the rule applies to a column or index
      entity, the expected value can be either a schema (schema) or a table
      (schema.table)
  """

  entities = _messages.StringField(1, repeated=True)
  entityNameContains = _messages.StringField(2)
  entityNamePrefix = _messages.StringField(3)
  entityNameSuffix = _messages.StringField(4)
  parentEntity = _messages.StringField(5)


class MaterializedViewEntity(_messages.Message):
  r"""MaterializedView's parent is a schema.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
    sqlCode: The SQL code which creates the view.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  sqlCode = _messages.StringField(2)


class MigrationJob(_messages.Message):
  r"""Represents a Database Migration Service migration job object.

  Enums:
    PhaseValueValuesEnum: Output only. The current migration job phase.
    StateValueValuesEnum: The current migration job state.
    TypeValueValuesEnum: Required. The migration job type.

  Messages:
    LabelsValue: The resource labels for migration job to use to annotate any
      related underlying resources such as Compute Engine VMs. An object
      containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
      "mass": "1.3kg", "count": "3" }`.

  Fields:
    cmekKeyName: The CMEK (customer-managed encryption key) fully qualified
      key name used for the migration job. This field supports all migration
      jobs types except for: * Mysql to Mysql (use the cmek field in the
      cloudsql connection profile instead). * PostrgeSQL to PostgreSQL (use
      the cmek field in the cloudsql connection profile instead). * PostgreSQL
      to AlloyDB (use the kms_key_name field in the alloydb connection profile
      instead). Each Cloud CMEK key has the following format: projects/[PROJEC
      T]/locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]
    conversionWorkspace: The conversion workspace used by the migration.
    createTime: Output only. The timestamp when the migration job resource was
      created. A timestamp in RFC3339 UTC "Zulu" format, accurate to
      nanoseconds. Example: "2014-10-02T15:01:23.045123456Z".
    destination: Required. The resource name (URI) of the destination
      connection profile.
    destinationDatabase: The database engine type and provider of the
      destination.
    displayName: The migration job display name.
    dumpFlags: The initial dump flags. This field and the "dump_path" field
      are mutually exclusive.
    dumpPath: The path to the dump file in Google Cloud Storage, in the
      format: (gs://[BUCKET_NAME]/[OBJECT_NAME]). This field and the
      "dump_flags" field are mutually exclusive.
    duration: Output only. The duration of the migration job (in seconds). A
      duration in seconds with up to nine fractional digits, terminated by
      's'. Example: "3.5s".
    endTime: Output only. If the migration job is completed, the time when it
      was completed.
    error: Output only. The error details in case of state FAILED.
    filter: This field can be used to select the entities to migrate as part
      of the migration job. It uses AIP-160 notation to select a subset of the
      entities configured on the associated conversion-workspace. This field
      should not be set on migration-jobs that are not associated with a
      conversion workspace.
    labels: The resource labels for migration job to use to annotate any
      related underlying resources such as Compute Engine VMs. An object
      containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
      "mass": "1.3kg", "count": "3" }`.
    name: The name (URI) of this migration job resource, in the form of:
      projects/{project}/locations/{location}/migrationJobs/{migrationJob}.
    performanceConfig: Optional. Data dump parallelism settings used by the
      migration. Currently applicable only for MySQL to Cloud SQL for MySQL
      migrations only.
    phase: Output only. The current migration job phase.
    reverseSshConnectivity: The details needed to communicate to the source
      over Reverse SSH tunnel connectivity.
    source: Required. The resource name (URI) of the source connection
      profile.
    sourceDatabase: The database engine type and provider of the source.
    state: The current migration job state.
    staticIpConnectivity: static ip connectivity data (default, no additional
      details needed).
    type: Required. The migration job type.
    updateTime: Output only. The timestamp when the migration job resource was
      last updated. A timestamp in RFC3339 UTC "Zulu" format, accurate to
      nanoseconds. Example: "2014-10-02T15:01:23.045123456Z".
    vpcPeeringConnectivity: The details of the VPC network that the source
      database is located in.
  """

  class PhaseValueValuesEnum(_messages.Enum):
    r"""Output only. The current migration job phase.

    Values:
      PHASE_UNSPECIFIED: The phase of the migration job is unknown.
      FULL_DUMP: The migration job is in the full dump phase.
      CDC: The migration job is CDC phase.
      PROMOTE_IN_PROGRESS: The migration job is running the promote phase.
      WAITING_FOR_SOURCE_WRITES_TO_STOP: Only RDS flow - waiting for source
        writes to stop
      PREPARING_THE_DUMP: Only RDS flow - the sources writes stopped, waiting
        for dump to begin
    """
    PHASE_UNSPECIFIED = 0
    FULL_DUMP = 1
    CDC = 2
    PROMOTE_IN_PROGRESS = 3
    WAITING_FOR_SOURCE_WRITES_TO_STOP = 4
    PREPARING_THE_DUMP = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""The current migration job state.

    Values:
      STATE_UNSPECIFIED: The state of the migration job is unknown.
      MAINTENANCE: The migration job is down for maintenance.
      DRAFT: The migration job is in draft mode and no resources are created.
      CREATING: The migration job is being created.
      NOT_STARTED: The migration job is created and not started.
      RUNNING: The migration job is running.
      FAILED: The migration job failed.
      COMPLETED: The migration job has been completed.
      DELETING: The migration job is being deleted.
      STOPPING: The migration job is being stopped.
      STOPPED: The migration job is currently stopped.
      DELETED: The migration job has been deleted.
      UPDATING: The migration job is being updated.
      STARTING: The migration job is starting.
      RESTARTING: The migration job is restarting.
      RESUMING: The migration job is resuming.
    """
    STATE_UNSPECIFIED = 0
    MAINTENANCE = 1
    DRAFT = 2
    CREATING = 3
    NOT_STARTED = 4
    RUNNING = 5
    FAILED = 6
    COMPLETED = 7
    DELETING = 8
    STOPPING = 9
    STOPPED = 10
    DELETED = 11
    UPDATING = 12
    STARTING = 13
    RESTARTING = 14
    RESUMING = 15

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The migration job type.

    Values:
      TYPE_UNSPECIFIED: The type of the migration job is unknown.
      ONE_TIME: The migration job is a one time migration.
      CONTINUOUS: The migration job is a continuous migration.
    """
    TYPE_UNSPECIFIED = 0
    ONE_TIME = 1
    CONTINUOUS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The resource labels for migration job to use to annotate any related
    underlying resources such as Compute Engine VMs. An object containing a
    list of "key": "value" pairs. Example: `{ "name": "wrench", "mass":
    "1.3kg", "count": "3" }`.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cmekKeyName = _messages.StringField(1)
  conversionWorkspace = _messages.MessageField('ConversionWorkspaceInfo', 2)
  createTime = _messages.StringField(3)
  destination = _messages.StringField(4)
  destinationDatabase = _messages.MessageField('DatabaseType', 5)
  displayName = _messages.StringField(6)
  dumpFlags = _messages.MessageField('DumpFlags', 7)
  dumpPath = _messages.StringField(8)
  duration = _messages.StringField(9)
  endTime = _messages.StringField(10)
  error = _messages.MessageField('Status', 11)
  filter = _messages.StringField(12)
  labels = _messages.MessageField('LabelsValue', 13)
  name = _messages.StringField(14)
  performanceConfig = _messages.MessageField('PerformanceConfig', 15)
  phase = _messages.EnumField('PhaseValueValuesEnum', 16)
  reverseSshConnectivity = _messages.MessageField('ReverseSshConnectivity', 17)
  source = _messages.StringField(18)
  sourceDatabase = _messages.MessageField('DatabaseType', 19)
  state = _messages.EnumField('StateValueValuesEnum', 20)
  staticIpConnectivity = _messages.MessageField('StaticIpConnectivity', 21)
  type = _messages.EnumField('TypeValueValuesEnum', 22)
  updateTime = _messages.StringField(23)
  vpcPeeringConnectivity = _messages.MessageField('VpcPeeringConnectivity', 24)


class MigrationJobVerificationError(_messages.Message):
  r"""Error message of a verification Migration job.

  Enums:
    ErrorCodeValueValuesEnum: Output only. An instance of ErrorCode specifying
      the error that occurred.

  Fields:
    errorCode: Output only. An instance of ErrorCode specifying the error that
      occurred.
    errorDetailMessage: Output only. A specific detailed error message, if
      supplied by the engine.
    errorMessage: Output only. A formatted message with further details about
      the error and a CTA.
  """

  class ErrorCodeValueValuesEnum(_messages.Enum):
    r"""Output only. An instance of ErrorCode specifying the error that
    occurred.

    Values:
      ERROR_CODE_UNSPECIFIED: An unknown error occurred
      CONNECTION_FAILURE: We failed to connect to one of the connection
        profile.
      AUTHENTICATION_FAILURE: We failed to authenticate to one of the
        connection profile.
      INVALID_CONNECTION_PROFILE_CONFIG: One of the involved connection
        profiles has an invalid configuration.
      VERSION_INCOMPATIBILITY: The versions of the source and the destination
        are incompatible.
      CONNECTION_PROFILE_TYPES_INCOMPATIBILITY: The types of the source and
        the destination are incompatible.
      NO_PGLOGICAL_INSTALLED: No pglogical extension installed on databases,
        applicable for postgres.
      PGLOGICAL_NODE_ALREADY_EXISTS: pglogical node already exists on
        databases, applicable for postgres.
      INVALID_WAL_LEVEL: The value of parameter wal_level is not set to
        logical.
      INVALID_SHARED_PRELOAD_LIBRARY: The value of parameter
        shared_preload_libraries does not include pglogical.
      INSUFFICIENT_MAX_REPLICATION_SLOTS: The value of parameter
        max_replication_slots is not sufficient.
      INSUFFICIENT_MAX_WAL_SENDERS: The value of parameter max_wal_senders is
        not sufficient.
      INSUFFICIENT_MAX_WORKER_PROCESSES: The value of parameter
        max_worker_processes is not sufficient.
      UNSUPPORTED_EXTENSIONS: Extensions installed are either not supported or
        having unsupported versions.
      UNSUPPORTED_MIGRATION_TYPE: Unsupported migration type.
      INVALID_RDS_LOGICAL_REPLICATION: Invalid RDS logical replication.
      UNSUPPORTED_GTID_MODE: The gtid_mode is not supported, applicable for
        MySQL.
      UNSUPPORTED_TABLE_DEFINITION: The table definition is not support due to
        missing primary key or replica identity.
      UNSUPPORTED_DEFINER: The definer is not supported.
      CANT_RESTART_RUNNING_MIGRATION: Migration is already running at the time
        of restart request.
      SOURCE_ALREADY_SETUP: The source already has a replication setup.
      TABLES_WITH_LIMITED_SUPPORT: The source has tables with limited support.
        E.g. PostgreSQL tables without primary keys.
      UNSUPPORTED_DATABASE_LOCALE: The source uses an unsupported locale.
      UNSUPPORTED_DATABASE_FDW_CONFIG: The source uses an unsupported Foreign
        Data Wrapper configuration.
      ERROR_RDBMS: There was an underlying RDBMS error.
      SOURCE_SIZE_EXCEEDS_THRESHOLD: The source DB size in Bytes exceeds a
        certain threshold. The migration might require an increase of quota,
        or might not be supported.
      EXISTING_CONFLICTING_DATABASES: The destination DB contains existing
        databases that are conflicting with those in the source DB.
      PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE: Insufficient privilege to enable
        the parallelism configuration.
    """
    ERROR_CODE_UNSPECIFIED = 0
    CONNECTION_FAILURE = 1
    AUTHENTICATION_FAILURE = 2
    INVALID_CONNECTION_PROFILE_CONFIG = 3
    VERSION_INCOMPATIBILITY = 4
    CONNECTION_PROFILE_TYPES_INCOMPATIBILITY = 5
    NO_PGLOGICAL_INSTALLED = 6
    PGLOGICAL_NODE_ALREADY_EXISTS = 7
    INVALID_WAL_LEVEL = 8
    INVALID_SHARED_PRELOAD_LIBRARY = 9
    INSUFFICIENT_MAX_REPLICATION_SLOTS = 10
    INSUFFICIENT_MAX_WAL_SENDERS = 11
    INSUFFICIENT_MAX_WORKER_PROCESSES = 12
    UNSUPPORTED_EXTENSIONS = 13
    UNSUPPORTED_MIGRATION_TYPE = 14
    INVALID_RDS_LOGICAL_REPLICATION = 15
    UNSUPPORTED_GTID_MODE = 16
    UNSUPPORTED_TABLE_DEFINITION = 17
    UNSUPPORTED_DEFINER = 18
    CANT_RESTART_RUNNING_MIGRATION = 19
    SOURCE_ALREADY_SETUP = 20
    TABLES_WITH_LIMITED_SUPPORT = 21
    UNSUPPORTED_DATABASE_LOCALE = 22
    UNSUPPORTED_DATABASE_FDW_CONFIG = 23
    ERROR_RDBMS = 24
    SOURCE_SIZE_EXCEEDS_THRESHOLD = 25
    EXISTING_CONFLICTING_DATABASES = 26
    PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE = 27

  errorCode = _messages.EnumField('ErrorCodeValueValuesEnum', 1)
  errorDetailMessage = _messages.StringField(2)
  errorMessage = _messages.StringField(3)


class MultiColumnDatatypeChange(_messages.Message):
  r"""Options to configure rule type MultiColumnDatatypeChange. The rule is
  used to change the data type and associated properties of multiple columns
  at once. The rule filter field can refer to one or more entities. The rule
  scope can be one of:Column. This rule requires additional filters to be
  specified beyond the basic rule filter field, which is the source data type,
  but the rule supports additional filtering capabilities such as the minimum
  and maximum field length. All additional filters which are specified are
  required to be met in order for the rule to be applied (logical AND between
  the fields).

  Messages:
    CustomFeaturesValue: Optional. Custom engine specific features.

  Fields:
    customFeatures: Optional. Custom engine specific features.
    newDataType: Required. New data type.
    overrideFractionalSecondsPrecision: Optional. Column fractional seconds
      precision - used only for timestamp based datatypes - if not specified
      and relevant uses the source column fractional seconds precision.
    overrideLength: Optional. Column length - e.g. varchar (50) - if not
      specified and relevant uses the source column length.
    overridePrecision: Optional. Column precision - when relevant - if not
      specified and relevant uses the source column precision.
    overrideScale: Optional. Column scale - when relevant - if not specified
      and relevant uses the source column scale.
    sourceDataTypeFilter: Required. Filter on source data type.
    sourceNumericFilter: Optional. Filter for fixed point number data types
      such as NUMERIC/NUMBER.
    sourceTextFilter: Optional. Filter for text-based data types like varchar.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Optional. Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  newDataType = _messages.StringField(2)
  overrideFractionalSecondsPrecision = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  overrideLength = _messages.IntegerField(4)
  overridePrecision = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  overrideScale = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  sourceDataTypeFilter = _messages.StringField(7)
  sourceNumericFilter = _messages.MessageField('SourceNumericFilter', 8)
  sourceTextFilter = _messages.MessageField('SourceTextFilter', 9)


class MultiEntityRename(_messages.Message):
  r"""Options to configure rule type MultiEntityRename. The rule is used to
  rename multiple entities. The rule filter field can refer to one or more
  entities. The rule scope can be one of: Database, Schema, Table, Column,
  Constraint, Index, View, Function, Stored Procedure, Materialized View,
  Sequence, UDT

  Enums:
    SourceNameTransformationValueValuesEnum: Optional. Additional
      transformation that can be done on the source entity name before it is
      being used by the new_name_pattern, for example lower case. If no
      transformation is desired, use NO_TRANSFORMATION

  Fields:
    newNamePattern: Optional. The pattern used to generate the new entity's
      name. This pattern must include the characters '{name}', which will be
      replaced with the name of the original entity. For example, the pattern
      't_{name}' for an entity name jobs would be converted to 't_jobs'. If
      unspecified, the default value for this field is '{name}'
    sourceNameTransformation: Optional. Additional transformation that can be
      done on the source entity name before it is being used by the
      new_name_pattern, for example lower case. If no transformation is
      desired, use NO_TRANSFORMATION
  """

  class SourceNameTransformationValueValuesEnum(_messages.Enum):
    r"""Optional. Additional transformation that can be done on the source
    entity name before it is being used by the new_name_pattern, for example
    lower case. If no transformation is desired, use NO_TRANSFORMATION

    Values:
      ENTITY_NAME_TRANSFORMATION_UNSPECIFIED: Entity name transformation
        unspecified.
      ENTITY_NAME_TRANSFORMATION_NO_TRANSFORMATION: No transformation.
      ENTITY_NAME_TRANSFORMATION_LOWER_CASE: Transform to lower case.
      ENTITY_NAME_TRANSFORMATION_UPPER_CASE: Transform to upper case.
      ENTITY_NAME_TRANSFORMATION_CAPITALIZED_CASE: Transform to capitalized
        case.
    """
    ENTITY_NAME_TRANSFORMATION_UNSPECIFIED = 0
    ENTITY_NAME_TRANSFORMATION_NO_TRANSFORMATION = 1
    ENTITY_NAME_TRANSFORMATION_LOWER_CASE = 2
    ENTITY_NAME_TRANSFORMATION_UPPER_CASE = 3
    ENTITY_NAME_TRANSFORMATION_CAPITALIZED_CASE = 4

  newNamePattern = _messages.StringField(1)
  sourceNameTransformation = _messages.EnumField('SourceNameTransformationValueValuesEnum', 2)


class MySqlConnectionProfile(_messages.Message):
  r"""Specifies connection parameters required specifically for MySQL
  databases.

  Fields:
    cloudSqlId: If the source is a Cloud SQL database, use this field to
      provide the Cloud SQL instance ID of the source.
    forwardSshConnectivity: Forward SSH tunnel connectivity.
    host: Required. The IP or hostname of the source MySQL database.
    password: Required. Input only. The password for the user that Database
      Migration Service will be using to connect to the database. This field
      is not returned on request, and the value is encrypted when stored in
      Database Migration Service.
    passwordSet: Output only. Indicates If this connection profile password is
      stored.
    port: Required. The network port of the source MySQL database.
    privateConnectivity: Private connectivity.
    ssl: SSL configuration for the destination to connect to the source
      database.
    staticServiceIpConnectivity: Static Service IP connectivity.
    username: Required. The username that Database Migration Service will use
      to connect to the database. The value is encrypted when stored in
      Database Migration Service.
  """

  cloudSqlId = _messages.StringField(1)
  forwardSshConnectivity = _messages.MessageField('ForwardSshTunnelConnectivity', 2)
  host = _messages.StringField(3)
  password = _messages.StringField(4)
  passwordSet = _messages.BooleanField(5)
  port = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  privateConnectivity = _messages.MessageField('PrivateConnectivity', 7)
  ssl = _messages.MessageField('SslConfig', 8)
  staticServiceIpConnectivity = _messages.MessageField('StaticServiceIpConnectivity', 9)
  username = _messages.StringField(10)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OracleConnectionProfile(_messages.Message):
  r"""Specifies connection parameters required specifically for Oracle
  databases.

  Fields:
    databaseService: Required. Database service for the Oracle connection.
    forwardSshConnectivity: Forward SSH tunnel connectivity.
    host: Required. The IP or hostname of the source Oracle database.
    password: Required. Input only. The password for the user that Database
      Migration Service will be using to connect to the database. This field
      is not returned on request, and the value is encrypted when stored in
      Database Migration Service.
    passwordSet: Output only. Indicates whether a new password is included in
      the request.
    port: Required. The network port of the source Oracle database.
    privateConnectivity: Private connectivity.
    ssl: SSL configuration for the connection to the source Oracle database. *
      Only `SERVER_ONLY` configuration is supported for Oracle SSL. * SSL is
      supported for Oracle versions 12 and above.
    staticServiceIpConnectivity: Static Service IP connectivity.
    username: Required. The username that Database Migration Service will use
      to connect to the database. The value is encrypted when stored in
      Database Migration Service.
  """

  databaseService = _messages.StringField(1)
  forwardSshConnectivity = _messages.MessageField('ForwardSshTunnelConnectivity', 2)
  host = _messages.StringField(3)
  password = _messages.StringField(4)
  passwordSet = _messages.BooleanField(5)
  port = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  privateConnectivity = _messages.MessageField('PrivateConnectivity', 7)
  ssl = _messages.MessageField('SslConfig', 8)
  staticServiceIpConnectivity = _messages.MessageField('StaticServiceIpConnectivity', 9)
  username = _messages.StringField(10)


class PackageEntity(_messages.Message):
  r"""Package's parent is a schema.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
    packageBody: The SQL code which creates the package body. If the package
      specification has cursors or subprograms, then the package body is
      mandatory.
    packageSqlCode: The SQL code which creates the package.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  packageBody = _messages.StringField(2)
  packageSqlCode = _messages.StringField(3)


class PerformanceConfig(_messages.Message):
  r"""Performance configuration definition.

  Enums:
    DumpParallelLevelValueValuesEnum: Initial dump parallelism level.

  Fields:
    dumpParallelLevel: Initial dump parallelism level.
  """

  class DumpParallelLevelValueValuesEnum(_messages.Enum):
    r"""Initial dump parallelism level.

    Values:
      DUMP_PARALLEL_LEVEL_UNSPECIFIED: Unknown dump parallel level. Will be
        defaulted to OPTIMAL.
      MIN: Minimal parallel level.
      OPTIMAL: Optimal parallel level.
      MAX: Maximum parallel level.
    """
    DUMP_PARALLEL_LEVEL_UNSPECIFIED = 0
    MIN = 1
    OPTIMAL = 2
    MAX = 3

  dumpParallelLevel = _messages.EnumField('DumpParallelLevelValueValuesEnum', 1)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Position(_messages.Message):
  r"""Issue position.

  Fields:
    column: Issue column number
    length: Issue length
    line: Issue line number
    offset: Issue offset
  """

  column = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  length = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  line = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  offset = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PostgreSqlConnectionProfile(_messages.Message):
  r"""Specifies connection parameters required specifically for PostgreSQL
  databases.

  Enums:
    NetworkArchitectureValueValuesEnum: Output only. If the source is a Cloud
      SQL database, this field indicates the network architecture it's
      associated with.

  Fields:
    alloydbClusterId: Optional. If the destination is an AlloyDB database, use
      this field to provide the AlloyDB cluster ID.
    cloudSqlId: If the source is a Cloud SQL database, use this field to
      provide the Cloud SQL instance ID of the source.
    host: Required. The IP or hostname of the source PostgreSQL database.
    networkArchitecture: Output only. If the source is a Cloud SQL database,
      this field indicates the network architecture it's associated with.
    password: Required. Input only. The password for the user that Database
      Migration Service will be using to connect to the database. This field
      is not returned on request, and the value is encrypted when stored in
      Database Migration Service.
    passwordSet: Output only. Indicates If this connection profile password is
      stored.
    port: Required. The network port of the source PostgreSQL database.
    privateServiceConnectConnectivity: Private service connect connectivity.
    ssl: SSL configuration for the destination to connect to the source
      database.
    staticIpConnectivity: Static ip connectivity data (default, no additional
      details needed).
    username: Required. The username that Database Migration Service will use
      to connect to the database. The value is encrypted when stored in
      Database Migration Service.
  """

  class NetworkArchitectureValueValuesEnum(_messages.Enum):
    r"""Output only. If the source is a Cloud SQL database, this field
    indicates the network architecture it's associated with.

    Values:
      NETWORK_ARCHITECTURE_UNSPECIFIED: <no description>
      NETWORK_ARCHITECTURE_OLD_CSQL_PRODUCER: Instance is in Cloud SQL's old
        producer network architecture.
      NETWORK_ARCHITECTURE_NEW_CSQL_PRODUCER: Instance is in Cloud SQL's new
        producer network architecture.
    """
    NETWORK_ARCHITECTURE_UNSPECIFIED = 0
    NETWORK_ARCHITECTURE_OLD_CSQL_PRODUCER = 1
    NETWORK_ARCHITECTURE_NEW_CSQL_PRODUCER = 2

  alloydbClusterId = _messages.StringField(1)
  cloudSqlId = _messages.StringField(2)
  host = _messages.StringField(3)
  networkArchitecture = _messages.EnumField('NetworkArchitectureValueValuesEnum', 4)
  password = _messages.StringField(5)
  passwordSet = _messages.BooleanField(6)
  port = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  privateServiceConnectConnectivity = _messages.MessageField('PrivateServiceConnectConnectivity', 8)
  ssl = _messages.MessageField('SslConfig', 9)
  staticIpConnectivity = _messages.MessageField('StaticIpConnectivity', 10)
  username = _messages.StringField(11)


class PrimaryInstanceSettings(_messages.Message):
  r"""Settings for the cluster's primary instance

  Messages:
    DatabaseFlagsValue: Database flags to pass to AlloyDB when DMS is creating
      the AlloyDB cluster and instances. See the AlloyDB documentation for how
      these can be used.
    LabelsValue: Labels for the AlloyDB primary instance created by DMS. An
      object containing a list of 'key', 'value' pairs.

  Fields:
    databaseFlags: Database flags to pass to AlloyDB when DMS is creating the
      AlloyDB cluster and instances. See the AlloyDB documentation for how
      these can be used.
    id: Required. The ID of the AlloyDB primary instance. The ID must satisfy
      the regex expression "[a-z0-9-]+".
    labels: Labels for the AlloyDB primary instance created by DMS. An object
      containing a list of 'key', 'value' pairs.
    machineConfig: Configuration for the machines that host the underlying
      database engine.
    privateIp: Output only. The private IP address for the Instance. This is
      the connection endpoint for an end-user application.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DatabaseFlagsValue(_messages.Message):
    r"""Database flags to pass to AlloyDB when DMS is creating the AlloyDB
    cluster and instances. See the AlloyDB documentation for how these can be
    used.

    Messages:
      AdditionalProperty: An additional property for a DatabaseFlagsValue
        object.

    Fields:
      additionalProperties: Additional properties of type DatabaseFlagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DatabaseFlagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for the AlloyDB primary instance created by DMS. An object
    containing a list of 'key', 'value' pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  databaseFlags = _messages.MessageField('DatabaseFlagsValue', 1)
  id = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  machineConfig = _messages.MessageField('MachineConfig', 4)
  privateIp = _messages.StringField(5)


class PrivateConnection(_messages.Message):
  r"""The PrivateConnection resource is used to establish private connectivity
  with the customer's network.

  Enums:
    StateValueValuesEnum: Output only. The state of the private connection.

  Messages:
    LabelsValue: The resource labels for private connections to use to
      annotate any related underlying resources such as Compute Engine VMs. An
      object containing a list of "key": "value" pairs. Example: `{ "name":
      "wrench", "mass": "1.3kg", "count": "3" }`.

  Fields:
    createTime: Output only. The create time of the resource.
    displayName: The private connection display name.
    error: Output only. The error details in case of state FAILED.
    labels: The resource labels for private connections to use to annotate any
      related underlying resources such as Compute Engine VMs. An object
      containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
      "mass": "1.3kg", "count": "3" }`.
    name: The name of the resource.
    state: Output only. The state of the private connection.
    updateTime: Output only. The last update time of the resource.
    vpcPeeringConfig: VPC peering configuration.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the private connection.

    Values:
      STATE_UNSPECIFIED: <no description>
      CREATING: The private connection is in creation state - creating
        resources.
      CREATED: The private connection has been created with all of its
        resources.
      FAILED: The private connection creation has failed.
      DELETING: The private connection is being deleted.
      FAILED_TO_DELETE: Delete request has failed, resource is in invalid
        state.
      DELETED: The private connection has been deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    CREATED = 2
    FAILED = 3
    DELETING = 4
    FAILED_TO_DELETE = 5
    DELETED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The resource labels for private connections to use to annotate any
    related underlying resources such as Compute Engine VMs. An object
    containing a list of "key": "value" pairs. Example: `{ "name": "wrench",
    "mass": "1.3kg", "count": "3" }`.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  error = _messages.MessageField('Status', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)
  vpcPeeringConfig = _messages.MessageField('VpcPeeringConfig', 8)


class PrivateConnectivity(_messages.Message):
  r"""Private Connectivity.

  Fields:
    privateConnection: Required. The resource name (URI) of the private
      connection.
  """

  privateConnection = _messages.StringField(1)


class PrivateServiceConnectConnectivity(_messages.Message):
  r"""[Private Service Connect
  connectivity](https://cloud.google.com/vpc/docs/private-service-
  connect#service-attachments)

  Fields:
    serviceAttachment: Required. A service attachment that exposes a database,
      and has the following format: projects/{project}/regions/{region}/servic
      eAttachments/{service_attachment_name}
  """

  serviceAttachment = _messages.StringField(1)


class PromoteMigrationJobRequest(_messages.Message):
  r"""Request message for 'PromoteMigrationJob' request."""


class RestartMigrationJobRequest(_messages.Message):
  r"""Request message for 'RestartMigrationJob' request.

  Fields:
    skipValidation: Optional. Restart the migration job without running prior
      configuration verification. Defaults to `false`.
  """

  skipValidation = _messages.BooleanField(1)


class ResumeMigrationJobRequest(_messages.Message):
  r"""Request message for 'ResumeMigrationJob' request."""


class ReverseSshConnectivity(_messages.Message):
  r"""The details needed to configure a reverse SSH tunnel between the source
  and destination databases. These details will be used when calling the
  generateSshScript method (see https://cloud.google.com/database-migration/do
  cs/reference/rest/v1/projects.locations.migrationJobs/generateSshScript) to
  produce the script that will help set up the reverse SSH tunnel, and to set
  up the VPC peering between the Cloud SQL private network and the VPC.

  Fields:
    vm: The name of the virtual machine (Compute Engine) used as the bastion
      server for the SSH tunnel.
    vmIp: Required. The IP of the virtual machine (Compute Engine) used as the
      bastion server for the SSH tunnel.
    vmPort: Required. The forwarding port of the virtual machine (Compute
      Engine) used as the bastion server for the SSH tunnel.
    vpc: The name of the VPC to peer with the Cloud SQL private network.
  """

  vm = _messages.StringField(1)
  vmIp = _messages.StringField(2)
  vmPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  vpc = _messages.StringField(4)


class RollbackConversionWorkspaceRequest(_messages.Message):
  r"""Request message for 'RollbackConversionWorkspace' request."""


class RoundToScale(_messages.Message):
  r"""This allows the data to change scale, for example if the source is 2
  digits after the decimal point, specify round to scale value = 2. If for
  example the value needs to be converted to an integer, use round to scale
  value = 0.

  Fields:
    scale: Required. Scale value to be used
  """

  scale = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class RulesFile(_messages.Message):
  r"""Details of a single rules file.

  Fields:
    rulesContent: Required. The text content of the rules that needs to be
      converted.
    rulesSourceFilename: Required. The filename of the rules that needs to be
      converted. The filename is used mainly so that future logs of the import
      rules job contain it, and can therefore be searched by it.
  """

  rulesContent = _messages.StringField(1)
  rulesSourceFilename = _messages.StringField(2)


class SchemaEntity(_messages.Message):
  r"""Schema typically has no parent entity, but can have a parent entity
  DatabaseInstance (for database engines which support it). For some database
  engines, the terms schema and user can be used interchangeably when they
  refer to a namespace or a collection of other database entities. Can store
  additional information which is schema specific.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)


class SearchBackgroundJobsResponse(_messages.Message):
  r"""Response message for 'SearchBackgroundJobs' request.

  Fields:
    jobs: The list of conversion workspace mapping rules.
  """

  jobs = _messages.MessageField('BackgroundJobLogEntry', 1, repeated=True)


class SeedConversionWorkspaceRequest(_messages.Message):
  r"""Request message for 'SeedConversionWorkspace' request.

  Fields:
    autoCommit: Should the conversion workspace be committed automatically
      after the seed operation.
    destinationConnectionProfile: Optional. Fully qualified (Uri) name of the
      destination connection profile.
    sourceConnectionProfile: Optional. Fully qualified (Uri) name of the
      source connection profile.
  """

  autoCommit = _messages.BooleanField(1)
  destinationConnectionProfile = _messages.StringField(2)
  sourceConnectionProfile = _messages.StringField(3)


class SeedJobDetails(_messages.Message):
  r"""Details regarding a Seed background job.

  Fields:
    connectionProfile: Output only. The connection profile which was used for
      the seed job.
  """

  connectionProfile = _messages.StringField(1)


class SequenceEntity(_messages.Message):
  r"""Sequence's parent is a schema.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    cache: Indicates number of entries to cache / precreate.
    customFeatures: Custom engine specific features.
    cycle: Indicates whether the sequence value should cycle through.
    increment: Increment value for the sequence.
    maxValue: Maximum number for the sequence represented as bytes to
      accommodate large. numbers
    minValue: Minimum number for the sequence represented as bytes to
      accommodate large. numbers
    startValue: Start number for the sequence represented as bytes to
      accommodate large. numbers
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cache = _messages.IntegerField(1)
  customFeatures = _messages.MessageField('CustomFeaturesValue', 2)
  cycle = _messages.BooleanField(3)
  increment = _messages.IntegerField(4)
  maxValue = _messages.BytesField(5)
  minValue = _messages.BytesField(6)
  startValue = _messages.BytesField(7)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SetTablePrimaryKey(_messages.Message):
  r"""Options to configure rule type SetTablePrimaryKey. The rule is used to
  specify the columns and name to configure/alter the primary key of a table.
  The rule filter field can refer to one entity. The rule scope can be one of:
  Table.

  Fields:
    primaryKey: Optional. Name for the primary key
    primaryKeyColumns: Required. List of column names for the primary key
  """

  primaryKey = _messages.StringField(1)
  primaryKeyColumns = _messages.StringField(2, repeated=True)


class SingleColumnChange(_messages.Message):
  r"""Options to configure rule type SingleColumnChange. The rule is used to
  change the properties of a column. The rule filter field can refer to one
  entity. The rule scope can be one of: Column. When using this rule, if a
  field is not specified than the destination column's configuration will be
  the same as the one in the source column..

  Messages:
    CustomFeaturesValue: Optional. Custom engine specific features.

  Fields:
    array: Optional. Is the column of array type.
    arrayLength: Optional. The length of the array, only relevant if the
      column type is an array.
    autoGenerated: Optional. Is the column auto-generated/identity.
    charset: Optional. Charset override - instead of table level charset.
    collation: Optional. Collation override - instead of table level
      collation.
    comment: Optional. Comment associated with the column.
    customFeatures: Optional. Custom engine specific features.
    dataType: Optional. Column data type name.
    fractionalSecondsPrecision: Optional. Column fractional seconds precision
      - e.g. 2 as in timestamp (2) - when relevant.
    length: Optional. Column length - e.g. 50 as in varchar (50) - when
      relevant.
    nullable: Optional. Is the column nullable.
    precision: Optional. Column precision - e.g. 8 as in double (8,2) - when
      relevant.
    scale: Optional. Column scale - e.g. 2 as in double (8,2) - when relevant.
    setValues: Optional. Specifies the list of values allowed in the column.
    udt: Optional. Is the column a UDT (User-defined Type).
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Optional. Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  array = _messages.BooleanField(1)
  arrayLength = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  autoGenerated = _messages.BooleanField(3)
  charset = _messages.StringField(4)
  collation = _messages.StringField(5)
  comment = _messages.StringField(6)
  customFeatures = _messages.MessageField('CustomFeaturesValue', 7)
  dataType = _messages.StringField(8)
  fractionalSecondsPrecision = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  length = _messages.IntegerField(10)
  nullable = _messages.BooleanField(11)
  precision = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  scale = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  setValues = _messages.StringField(14, repeated=True)
  udt = _messages.BooleanField(15)


class SingleEntityRename(_messages.Message):
  r"""Options to configure rule type SingleEntityRename. The rule is used to
  rename an entity. The rule filter field can refer to only one entity. The
  rule scope can be one of: Database, Schema, Table, Column, Constraint,
  Index, View, Function, Stored Procedure, Materialized View, Sequence, UDT,
  Synonym

  Fields:
    newName: Required. The new name of the destination entity
  """

  newName = _messages.StringField(1)


class SinglePackageChange(_messages.Message):
  r"""Options to configure rule type SinglePackageChange. The rule is used to
  alter the sql code for a package entities. The rule filter field can refer
  to one entity. The rule scope can be: Package

  Fields:
    packageBody: Optional. Sql code for package body
    packageDescription: Optional. Sql code for package description
  """

  packageBody = _messages.StringField(1)
  packageDescription = _messages.StringField(2)


class SourceNumericFilter(_messages.Message):
  r"""Filter for fixed point number data types such as NUMERIC/NUMBER

  Enums:
    NumericFilterOptionValueValuesEnum: Required. Enum to set the option
      defining the datatypes numeric filter has to be applied to

  Fields:
    numericFilterOption: Required. Enum to set the option defining the
      datatypes numeric filter has to be applied to
    sourceMaxPrecisionFilter: Optional. The filter will match columns with
      precision smaller than or equal to this number.
    sourceMaxScaleFilter: Optional. The filter will match columns with scale
      smaller than or equal to this number.
    sourceMinPrecisionFilter: Optional. The filter will match columns with
      precision greater than or equal to this number.
    sourceMinScaleFilter: Optional. The filter will match columns with scale
      greater than or equal to this number.
  """

  class NumericFilterOptionValueValuesEnum(_messages.Enum):
    r"""Required. Enum to set the option defining the datatypes numeric filter
    has to be applied to

    Values:
      NUMERIC_FILTER_OPTION_UNSPECIFIED: Numeric filter option unspecified
      NUMERIC_FILTER_OPTION_ALL: Numeric filter option that matches all
        numeric columns.
      NUMERIC_FILTER_OPTION_LIMIT: Numeric filter option that matches columns
        having numeric datatypes with specified precision and scale within the
        limited range of filter.
      NUMERIC_FILTER_OPTION_LIMITLESS: Numeric filter option that matches only
        the numeric columns with no precision and scale specified.
    """
    NUMERIC_FILTER_OPTION_UNSPECIFIED = 0
    NUMERIC_FILTER_OPTION_ALL = 1
    NUMERIC_FILTER_OPTION_LIMIT = 2
    NUMERIC_FILTER_OPTION_LIMITLESS = 3

  numericFilterOption = _messages.EnumField('NumericFilterOptionValueValuesEnum', 1)
  sourceMaxPrecisionFilter = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  sourceMaxScaleFilter = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  sourceMinPrecisionFilter = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  sourceMinScaleFilter = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class SourceSqlChange(_messages.Message):
  r"""Options to configure rule type SourceSqlChange. The rule is used to
  alter the sql code for database entities. The rule filter field can refer to
  one entity. The rule scope can be: StoredProcedure, Function, Trigger, View

  Fields:
    sqlCode: Required. Sql code for source (stored procedure, function,
      trigger or view)
  """

  sqlCode = _messages.StringField(1)


class SourceTextFilter(_messages.Message):
  r"""Filter for text-based data types like varchar.

  Fields:
    sourceMaxLengthFilter: Optional. The filter will match columns with length
      smaller than or equal to this number.
    sourceMinLengthFilter: Optional. The filter will match columns with length
      greater than or equal to this number.
  """

  sourceMaxLengthFilter = _messages.IntegerField(1)
  sourceMinLengthFilter = _messages.IntegerField(2)


class SpannerConnectionProfile(_messages.Message):
  r"""Specifies connection parameters required specifically for Spanner
  databases.

  Fields:
    database: Required. The database in the spanner instance to connect to, in
      the form: "projects/my-project/instances/my-instance/databases/my-
      database"
  """

  database = _messages.StringField(1)


class SqlAclEntry(_messages.Message):
  r"""An entry for an Access Control list.

  Fields:
    expireTime: The time when this access control entry expires in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example:
      `2012-11-15T16:19:00.094Z`.
    label: A label to identify this entry.
    ttl: Input only. The time-to-leave of this access control entry.
    value: The allowlisted value for the access control list.
  """

  expireTime = _messages.StringField(1)
  label = _messages.StringField(2)
  ttl = _messages.StringField(3)
  value = _messages.StringField(4)


class SqlIpConfig(_messages.Message):
  r"""IP Management configuration.

  Fields:
    allocatedIpRange: Optional. The name of the allocated IP address range for
      the private IP Cloud SQL instance. This name refers to an already
      allocated IP range address. If set, the instance IP address will be
      created in the allocated range. Note that this IP address range can't be
      modified after the instance is created. If you change the VPC when
      configuring connectivity settings for the migration job, this field is
      not relevant.
    authorizedNetworks: The list of external networks that are allowed to
      connect to the instance using the IP. See
      https://en.wikipedia.org/wiki/CIDR_notation#CIDR_notation, also known as
      'slash' notation (e.g. `*************/24`).
    enableIpv4: Whether the instance should be assigned an IPv4 address or
      not.
    privateNetwork: The resource link for the VPC network from which the Cloud
      SQL instance is accessible for private IP. For example,
      `projects/myProject/global/networks/default`. This setting can be
      updated, but it cannot be removed after it is set.
    requireSsl: Whether SSL connections over IP should be enforced or not.
  """

  allocatedIpRange = _messages.StringField(1)
  authorizedNetworks = _messages.MessageField('SqlAclEntry', 2, repeated=True)
  enableIpv4 = _messages.BooleanField(3)
  privateNetwork = _messages.StringField(4)
  requireSsl = _messages.BooleanField(5)


class SshScript(_messages.Message):
  r"""Response message for 'GenerateSshScript' request.

  Fields:
    script: The ssh configuration script.
  """

  script = _messages.StringField(1)


class SslConfig(_messages.Message):
  r"""SSL configuration information.

  Enums:
    TypeValueValuesEnum: Output only. The ssl config type according to
      'client_key', 'client_certificate' and 'ca_certificate'.

  Fields:
    caCertificate: Required. Input only. The x509 PEM-encoded certificate of
      the CA that signed the source database server's certificate. The replica
      will use this certificate to verify it's connecting to the right host.
    clientCertificate: Input only. The x509 PEM-encoded certificate that will
      be used by the replica to authenticate against the source database
      server.If this field is used then the 'client_key' field is mandatory.
    clientKey: Input only. The unencrypted PKCS#1 or PKCS#8 PEM-encoded
      private key associated with the Client Certificate. If this field is
      used then the 'client_certificate' field is mandatory.
    type: Output only. The ssl config type according to 'client_key',
      'client_certificate' and 'ca_certificate'.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The ssl config type according to 'client_key',
    'client_certificate' and 'ca_certificate'.

    Values:
      SSL_TYPE_UNSPECIFIED: Unspecified.
      SERVER_ONLY: Only 'ca_certificate' specified.
      SERVER_CLIENT: Both server ('ca_certificate'), and client ('client_key',
        'client_certificate') specified.
    """
    SSL_TYPE_UNSPECIFIED = 0
    SERVER_ONLY = 1
    SERVER_CLIENT = 2

  caCertificate = _messages.StringField(1)
  clientCertificate = _messages.StringField(2)
  clientKey = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartMigrationJobRequest(_messages.Message):
  r"""Request message for 'StartMigrationJob' request.

  Fields:
    skipValidation: Optional. Start the migration job without running prior
      configuration verification. Defaults to `false`.
  """

  skipValidation = _messages.BooleanField(1)


class StaticIpConnectivity(_messages.Message):
  r"""The source database will allow incoming connections from the public IP
  of the destination database. You can retrieve the public IP of the Cloud SQL
  instance from the Cloud SQL console or using Cloud SQL APIs. No additional
  configuration is required.
  """



class StaticServiceIpConnectivity(_messages.Message):
  r"""Static IP address connectivity configured on service project."""


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopMigrationJobRequest(_messages.Message):
  r"""Request message for 'StopMigrationJob' request."""


class StoredProcedureEntity(_messages.Message):
  r"""Stored procedure's parent is a schema.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
    sqlCode: The SQL code which creates the stored procedure.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  sqlCode = _messages.StringField(2)


class SynonymEntity(_messages.Message):
  r"""Synonym's parent is a schema.

  Enums:
    SourceTypeValueValuesEnum: The type of the entity for which the synonym is
      being created (usually a table or a sequence).

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
    sourceEntity: The name of the entity for which the synonym is being
      created (the source).
    sourceType: The type of the entity for which the synonym is being created
      (usually a table or a sequence).
  """

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""The type of the entity for which the synonym is being created (usually
    a table or a sequence).

    Values:
      DATABASE_ENTITY_TYPE_UNSPECIFIED: Unspecified database entity type.
      DATABASE_ENTITY_TYPE_SCHEMA: Schema.
      DATABASE_ENTITY_TYPE_TABLE: Table.
      DATABASE_ENTITY_TYPE_COLUMN: Column.
      DATABASE_ENTITY_TYPE_CONSTRAINT: Constraint.
      DATABASE_ENTITY_TYPE_INDEX: Index.
      DATABASE_ENTITY_TYPE_TRIGGER: Trigger.
      DATABASE_ENTITY_TYPE_VIEW: View.
      DATABASE_ENTITY_TYPE_SEQUENCE: Sequence.
      DATABASE_ENTITY_TYPE_STORED_PROCEDURE: Stored Procedure.
      DATABASE_ENTITY_TYPE_FUNCTION: Function.
      DATABASE_ENTITY_TYPE_SYNONYM: Synonym.
      DATABASE_ENTITY_TYPE_DATABASE_PACKAGE: Package.
      DATABASE_ENTITY_TYPE_UDT: UDT.
      DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW: Materialized View.
      DATABASE_ENTITY_TYPE_DATABASE: Database.
    """
    DATABASE_ENTITY_TYPE_UNSPECIFIED = 0
    DATABASE_ENTITY_TYPE_SCHEMA = 1
    DATABASE_ENTITY_TYPE_TABLE = 2
    DATABASE_ENTITY_TYPE_COLUMN = 3
    DATABASE_ENTITY_TYPE_CONSTRAINT = 4
    DATABASE_ENTITY_TYPE_INDEX = 5
    DATABASE_ENTITY_TYPE_TRIGGER = 6
    DATABASE_ENTITY_TYPE_VIEW = 7
    DATABASE_ENTITY_TYPE_SEQUENCE = 8
    DATABASE_ENTITY_TYPE_STORED_PROCEDURE = 9
    DATABASE_ENTITY_TYPE_FUNCTION = 10
    DATABASE_ENTITY_TYPE_SYNONYM = 11
    DATABASE_ENTITY_TYPE_DATABASE_PACKAGE = 12
    DATABASE_ENTITY_TYPE_UDT = 13
    DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW = 14
    DATABASE_ENTITY_TYPE_DATABASE = 15

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  sourceEntity = _messages.StringField(2)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 3)


class TableEntity(_messages.Message):
  r"""Table's parent is a schema.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    columns: Table columns.
    comment: Comment associated with the table.
    constraints: Table constraints.
    customFeatures: Custom engine specific features.
    indices: Table indices.
    triggers: Table triggers.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  columns = _messages.MessageField('ColumnEntity', 1, repeated=True)
  comment = _messages.StringField(2)
  constraints = _messages.MessageField('ConstraintEntity', 3, repeated=True)
  customFeatures = _messages.MessageField('CustomFeaturesValue', 4)
  indices = _messages.MessageField('IndexEntity', 5, repeated=True)
  triggers = _messages.MessageField('TriggerEntity', 6, repeated=True)


class TcpProxyScript(_messages.Message):
  r"""Response message for 'GenerateTcpProxyScript' request.

  Fields:
    script: The TCP Proxy configuration script.
  """

  script = _messages.StringField(1)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TriggerEntity(_messages.Message):
  r"""Trigger is not used as an independent entity, it is retrieved as part of
  a Table entity.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
    name: The name of the trigger.
    sqlCode: The SQL code which creates the trigger.
    triggerType: Indicates when the trigger fires, for example BEFORE
      STATEMENT, AFTER EACH ROW.
    triggeringEvents: The DML, DDL, or database events that fire the trigger,
      for example INSERT, UPDATE.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  name = _messages.StringField(2)
  sqlCode = _messages.StringField(3)
  triggerType = _messages.StringField(4)
  triggeringEvents = _messages.StringField(5, repeated=True)


class UDTEntity(_messages.Message):
  r"""UDT's parent is a schema.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    customFeatures: Custom engine specific features.
    udtBody: The SQL code which creates the udt body.
    udtSqlCode: The SQL code which creates the udt.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customFeatures = _messages.MessageField('CustomFeaturesValue', 1)
  udtBody = _messages.StringField(2)
  udtSqlCode = _messages.StringField(3)


class UserPassword(_messages.Message):
  r"""The username/password for a database user. Used for specifying initial
  users at cluster creation time.

  Fields:
    password: The initial password for the user.
    passwordSet: Output only. Indicates if the initial_user.password field has
      been set.
    user: The database username.
  """

  password = _messages.StringField(1)
  passwordSet = _messages.BooleanField(2)
  user = _messages.StringField(3)


class ValueListFilter(_messages.Message):
  r"""A list of values to filter by in ConditionalColumnSetValue

  Enums:
    ValuePresentListValueValuesEnum: Required. Indicates whether the filter
      matches rows with values that are present in the list or those with
      values not present in it.

  Fields:
    ignoreCase: Required. Whether to ignore case when filtering by values.
      Defaults to false
    valuePresentList: Required. Indicates whether the filter matches rows with
      values that are present in the list or those with values not present in
      it.
    values: Required. The list to be used to filter by
  """

  class ValuePresentListValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether the filter matches rows with values that
    are present in the list or those with values not present in it.

    Values:
      VALUE_PRESENT_IN_LIST_UNSPECIFIED: Value present in list unspecified
      VALUE_PRESENT_IN_LIST_IF_VALUE_LIST: If the source value is in the
        supplied list at value_list
      VALUE_PRESENT_IN_LIST_IF_VALUE_NOT_LIST: If the source value is not in
        the supplied list at value_list
    """
    VALUE_PRESENT_IN_LIST_UNSPECIFIED = 0
    VALUE_PRESENT_IN_LIST_IF_VALUE_LIST = 1
    VALUE_PRESENT_IN_LIST_IF_VALUE_NOT_LIST = 2

  ignoreCase = _messages.BooleanField(1)
  valuePresentList = _messages.EnumField('ValuePresentListValueValuesEnum', 2)
  values = _messages.StringField(3, repeated=True)


class ValueTransformation(_messages.Message):
  r"""Description of data transformation during migration as part of the
  ConditionalColumnSetValue.

  Fields:
    applyHash: Optional. Applies a hash function on the data
    assignMaxValue: Optional. Set to max_value - if integer or numeric, will
      use int.maxvalue, etc
    assignMinValue: Optional. Set to min_value - if integer or numeric, will
      use int.minvalue, etc
    assignNull: Optional. Set to null
    assignSpecificValue: Optional. Set to a specific value (value is converted
      to fit the target data type)
    doubleComparison: Optional. Filter on relation between source value and
      compare value of type double.
    intComparison: Optional. Filter on relation between source value and
      compare value of type integer.
    isNull: Optional. Value is null
    roundScale: Optional. Allows the data to change scale
    valueList: Optional. Value is found in the specified list.
  """

  applyHash = _messages.MessageField('ApplyHash', 1)
  assignMaxValue = _messages.MessageField('Empty', 2)
  assignMinValue = _messages.MessageField('Empty', 3)
  assignNull = _messages.MessageField('Empty', 4)
  assignSpecificValue = _messages.MessageField('AssignSpecificValue', 5)
  doubleComparison = _messages.MessageField('DoubleComparisonFilter', 6)
  intComparison = _messages.MessageField('IntComparisonFilter', 7)
  isNull = _messages.MessageField('Empty', 8)
  roundScale = _messages.MessageField('RoundToScale', 9)
  valueList = _messages.MessageField('ValueListFilter', 10)


class VerifyMigrationJobRequest(_messages.Message):
  r"""Request message for 'VerifyMigrationJob' request.

  Fields:
    migrationJob: Optional. The changed migration job parameters to verify. It
      will not update the migration job.
    updateMask: Optional. Field mask is used to specify the changed fields to
      be verified. It will not update the migration job.
  """

  migrationJob = _messages.MessageField('MigrationJob', 1)
  updateMask = _messages.StringField(2)


class ViewEntity(_messages.Message):
  r"""View's parent is a schema.

  Messages:
    CustomFeaturesValue: Custom engine specific features.

  Fields:
    constraints: View constraints.
    customFeatures: Custom engine specific features.
    sqlCode: The SQL code which creates the view.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomFeaturesValue(_messages.Message):
    r"""Custom engine specific features.

    Messages:
      AdditionalProperty: An additional property for a CustomFeaturesValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomFeaturesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  constraints = _messages.MessageField('ConstraintEntity', 1, repeated=True)
  customFeatures = _messages.MessageField('CustomFeaturesValue', 2)
  sqlCode = _messages.StringField(3)


class VmCreationConfig(_messages.Message):
  r"""VM creation configuration message

  Fields:
    subnet: The subnet name the vm needs to be created in.
    vmMachineType: Required. VM instance machine type to create.
    vmZone: The Google Cloud Platform zone to create the VM in.
  """

  subnet = _messages.StringField(1)
  vmMachineType = _messages.StringField(2)
  vmZone = _messages.StringField(3)


class VmSelectionConfig(_messages.Message):
  r"""VM selection configuration message

  Fields:
    vmZone: Required. The Google Cloud Platform zone the VM is located.
  """

  vmZone = _messages.StringField(1)


class VpcPeeringConfig(_messages.Message):
  r"""The VPC peering configuration is used to create VPC peering with the
  consumer's VPC.

  Fields:
    subnet: Required. A free subnet for peering. (CIDR of /29)
    vpcName: Required. Fully qualified name of the VPC that Database Migration
      Service will peer to.
  """

  subnet = _messages.StringField(1)
  vpcName = _messages.StringField(2)


class VpcPeeringConnectivity(_messages.Message):
  r"""The details of the VPC where the source database is located in Google
  Cloud. We will use this information to set up the VPC peering connection
  between Cloud SQL and this VPC.

  Fields:
    vpc: The name of the VPC network to peer with the Cloud SQL private
      network.
  """

  vpc = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

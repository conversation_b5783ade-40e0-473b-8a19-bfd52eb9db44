"""Generated client library for beyondcorp version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.beyondcorp.v1alpha import beyondcorp_v1alpha_messages as messages


class BeyondcorpV1alpha(base_api.BaseApiClient):
  """Generated client library for service beyondcorp version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://beyondcorp.googleapis.com/'
  MTLS_BASE_URL = 'https://beyondcorp.mtls.googleapis.com/'

  _PACKAGE = 'beyondcorp'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'BeyondcorpV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new beyondcorp handle."""
    url = url or self.BASE_URL
    super(BeyondcorpV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.organizations_locations_global_partnerTenants_browserDlpRules = self.OrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesService(self)
    self.organizations_locations_global_partnerTenants_proxyConfigs = self.OrganizationsLocationsGlobalPartnerTenantsProxyConfigsService(self)
    self.organizations_locations_global_partnerTenants = self.OrganizationsLocationsGlobalPartnerTenantsService(self)
    self.organizations_locations_global = self.OrganizationsLocationsGlobalService(self)
    self.organizations_locations_insights = self.OrganizationsLocationsInsightsService(self)
    self.organizations_locations_operations = self.OrganizationsLocationsOperationsService(self)
    self.organizations_locations_subscriptions = self.OrganizationsLocationsSubscriptionsService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_appConnections = self.ProjectsLocationsAppConnectionsService(self)
    self.projects_locations_appConnectors = self.ProjectsLocationsAppConnectorsService(self)
    self.projects_locations_appGateways = self.ProjectsLocationsAppGatewaysService(self)
    self.projects_locations_applicationDomains = self.ProjectsLocationsApplicationDomainsService(self)
    self.projects_locations_applications = self.ProjectsLocationsApplicationsService(self)
    self.projects_locations_clientConnectorServices = self.ProjectsLocationsClientConnectorServicesService(self)
    self.projects_locations_clientGateways = self.ProjectsLocationsClientGatewaysService(self)
    self.projects_locations_connections = self.ProjectsLocationsConnectionsService(self)
    self.projects_locations_connectors = self.ProjectsLocationsConnectorsService(self)
    self.projects_locations_insights = self.ProjectsLocationsInsightsService(self)
    self.projects_locations_netConnections = self.ProjectsLocationsNetConnectionsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class OrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesService(base_api.BaseApiService):
    """Service class for the organizations_locations_global_partnerTenants_browserDlpRules resource."""

    _NAME = 'organizations_locations_global_partnerTenants_browserDlpRules'

    def __init__(self, client):
      super(BeyondcorpV1alpha.OrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new BrowserDlpRule in a given organization and PartnerTenant.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/browserDlpRules',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.browserDlpRules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId'],
        relative_path='v1alpha/{+parent}/browserDlpRules',
        request_field='googleCloudBeyondcorpPartnerservicesV1alphaBrowserDlpRule',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing BrowserDlpRule.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/browserDlpRules/{browserDlpRulesId}',
        http_method='DELETE',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.browserDlpRules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single BrowserDlpRule.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpPartnerservicesV1alphaBrowserDlpRule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/browserDlpRules/{browserDlpRulesId}',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.browserDlpRules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesGetRequest',
        response_type_name='GoogleCloudBeyondcorpPartnerservicesV1alphaBrowserDlpRule',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/browserDlpRules/{browserDlpRulesId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.browserDlpRules.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BrowserDlpRules for PartnerTenant in a given organization.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpPartnerservicesV1alphaListBrowserDlpRulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/browserDlpRules',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.browserDlpRules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/browserDlpRules',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesListRequest',
        response_type_name='GoogleCloudBeyondcorpPartnerservicesV1alphaListBrowserDlpRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update an existing BrowserDlpRule in a given organization and PartnerTenant.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/browserDlpRules/{browserDlpRulesId}',
        http_method='PATCH',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.browserDlpRules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='googleCloudBeyondcorpPartnerservicesV1alphaBrowserDlpRule',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/browserDlpRules/{browserDlpRulesId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.browserDlpRules.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/browserDlpRules/{browserDlpRulesId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.browserDlpRules.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsBrowserDlpRulesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsGlobalPartnerTenantsProxyConfigsService(base_api.BaseApiService):
    """Service class for the organizations_locations_global_partnerTenants_proxyConfigs resource."""

    _NAME = 'organizations_locations_global_partnerTenants_proxyConfigs'

    def __init__(self, client):
      super(BeyondcorpV1alpha.OrganizationsLocationsGlobalPartnerTenantsProxyConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new BeyondCorp Enterprise ProxyConfig in a given organization and PartnerTenant. Can only be called by on onboarded Beyondcorp Enterprise partner.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/proxyConfigs',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.proxyConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId'],
        relative_path='v1alpha/{+parent}/proxyConfigs',
        request_field='googleCloudBeyondcorpPartnerservicesV1alphaProxyConfig',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ProxyConfig.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/proxyConfigs/{proxyConfigsId}',
        http_method='DELETE',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.proxyConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ProxyConfig.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpPartnerservicesV1alphaProxyConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/proxyConfigs/{proxyConfigsId}',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.proxyConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsGetRequest',
        response_type_name='GoogleCloudBeyondcorpPartnerservicesV1alphaProxyConfig',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/proxyConfigs/{proxyConfigsId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.proxyConfigs.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ProxyConfigs for PartnerTenant in a given organization.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpPartnerservicesV1alphaListProxyConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/proxyConfigs',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.proxyConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/proxyConfigs',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsListRequest',
        response_type_name='GoogleCloudBeyondcorpPartnerservicesV1alphaListProxyConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a single proxy config.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/proxyConfigs/{proxyConfigsId}',
        http_method='PATCH',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.proxyConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='googleCloudBeyondcorpPartnerservicesV1alphaProxyConfig',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/proxyConfigs/{proxyConfigsId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.proxyConfigs.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}/proxyConfigs/{proxyConfigsId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.proxyConfigs.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsProxyConfigsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsGlobalPartnerTenantsService(base_api.BaseApiService):
    """Service class for the organizations_locations_global_partnerTenants resource."""

    _NAME = 'organizations_locations_global_partnerTenants'

    def __init__(self, client):
      super(BeyondcorpV1alpha.OrganizationsLocationsGlobalPartnerTenantsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new BeyondCorp Enterprise partnerTenant in a given organization and can only be called by onboarded BeyondCorp Enterprise partner.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId'],
        relative_path='v1alpha/{+parent}/partnerTenants',
        request_field='googleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single PartnerTenant.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}',
        http_method='DELETE',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single PartnerTenant.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsGetRequest',
        response_type_name='GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PartnerTenants in a given organization.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/partnerTenants',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsListRequest',
        response_type_name='GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a single PartnerTenant.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}',
        http_method='PATCH',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='googleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpOrganizationsLocationsGlobalPartnerTenantsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.global.partnerTenants.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpOrganizationsLocationsGlobalPartnerTenantsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsGlobalService(base_api.BaseApiService):
    """Service class for the organizations_locations_global resource."""

    _NAME = 'organizations_locations_global'

    def __init__(self, client):
      super(BeyondcorpV1alpha.OrganizationsLocationsGlobalService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsInsightsService(base_api.BaseApiService):
    """Service class for the organizations_locations_insights resource."""

    _NAME = 'organizations_locations_insights'

    def __init__(self, client):
      super(BeyondcorpV1alpha.OrganizationsLocationsInsightsService, self).__init__(client)
      self._upload_configs = {
          }

    def ConfiguredInsight(self, request, global_params=None):
      r"""Gets the value for a selected particular insight based on the provided filters. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project.

      Args:
        request: (BeyondcorpOrganizationsLocationsInsightsConfiguredInsightRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse) The response message.
      """
      config = self.GetMethodConfig('ConfiguredInsight')
      return self._RunMethod(
          config, request, global_params=global_params)

    ConfiguredInsight.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/insights/{insightsId}:configuredInsight',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.insights.configuredInsight',
        ordered_params=['insight'],
        path_params=['insight'],
        query_params=['aggregation', 'customGrouping_fieldFilter', 'customGrouping_groupFields', 'endTime', 'fieldFilter', 'group', 'pageSize', 'pageToken', 'startTime'],
        relative_path='v1alpha/{+insight}:configuredInsight',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsInsightsConfiguredInsightRequest',
        response_type_name='GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the value for a selected particular insight with default configuration. The default aggregation level is 'DAILY' and no grouping will be applied or default grouping if applicable. The data will be returned for recent 7 days starting the day before. The insight data size will be limited to 50 rows. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project. Setting the `view` to `BASIC` will only return the metadata for the insight.

      Args:
        request: (BeyondcorpOrganizationsLocationsInsightsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/insights/{insightsId}',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.insights.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsInsightsGetRequest',
        response_type_name='GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists for all the available insights that could be fetched from the system. Allows to filter using category. Setting the `view` to `BASIC` will let you iterate over the list of insight metadatas.

      Args:
        request: (BeyondcorpOrganizationsLocationsInsightsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/insights',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.insights.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'view'],
        relative_path='v1alpha/{+parent}/insights',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsInsightsListRequest',
        response_type_name='GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_operations resource."""

    _NAME = 'organizations_locations_operations'

    def __init__(self, client):
      super(BeyondcorpV1alpha.OrganizationsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (BeyondcorpOrganizationsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='BeyondcorpOrganizationsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (BeyondcorpOrganizationsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='beyondcorp.organizations.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (BeyondcorpOrganizationsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (BeyondcorpOrganizationsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/operations',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsSubscriptionsService(base_api.BaseApiService):
    """Service class for the organizations_locations_subscriptions resource."""

    _NAME = 'organizations_locations_subscriptions'

    def __init__(self, client):
      super(BeyondcorpV1alpha.OrganizationsLocationsSubscriptionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new BeyondCorp Enterprise Subscription in a given organization. Location will always be global as BeyondCorp subscriptions are per organization.

      Args:
        request: (BeyondcorpOrganizationsLocationsSubscriptionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/subscriptions',
        http_method='POST',
        method_id='beyondcorp.organizations.locations.subscriptions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}/subscriptions',
        request_field='googleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription',
        request_type_name='BeyondcorpOrganizationsLocationsSubscriptionsCreateRequest',
        response_type_name='GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Subscription.

      Args:
        request: (BeyondcorpOrganizationsLocationsSubscriptionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/subscriptions/{subscriptionsId}',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.subscriptions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsSubscriptionsGetRequest',
        response_type_name='GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Subscriptions in a given organization and location.

      Args:
        request: (BeyondcorpOrganizationsLocationsSubscriptionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/subscriptions',
        http_method='GET',
        method_id='beyondcorp.organizations.locations.subscriptions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/subscriptions',
        request_field='',
        request_type_name='BeyondcorpOrganizationsLocationsSubscriptionsListRequest',
        response_type_name='GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(BeyondcorpV1alpha.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(BeyondcorpV1alpha.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsAppConnectionsService(base_api.BaseApiService):
    """Service class for the projects_locations_appConnections resource."""

    _NAME = 'projects_locations_appConnections'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsAppConnectionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AppConnection in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appConnections.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['appConnectionId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/appConnections',
        request_field='googleCloudBeyondcorpAppconnectionsV1alphaAppConnection',
        request_type_name='BeyondcorpProjectsLocationsAppConnectionsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single AppConnection.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}',
        http_method='DELETE',
        method_id='beyondcorp.projects.locations.appConnections.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single AppConnection.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appConnections.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectionsGetRequest',
        response_type_name='GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectionsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appConnections.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectionsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists AppConnections in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appConnections.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/appConnections',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectionsListRequest',
        response_type_name='GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single AppConnection.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}',
        http_method='PATCH',
        method_id='beyondcorp.projects.locations.appConnections.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='googleCloudBeyondcorpAppconnectionsV1alphaAppConnection',
        request_type_name='BeyondcorpProjectsLocationsAppConnectionsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Resolve(self, request, global_params=None):
      r"""Resolves AppConnections details for a given AppConnector. An internal method called by a connector to find AppConnections to connect to.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectionsResolveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse) The response message.
      """
      config = self.GetMethodConfig('Resolve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resolve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections:resolve',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appConnections.resolve',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['appConnectorId', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/appConnections:resolve',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectionsResolveRequest',
        response_type_name='GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectionsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appConnections.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsAppConnectionsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectionsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appConnections.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsAppConnectionsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAppConnectorsService(base_api.BaseApiService):
    """Service class for the projects_locations_appConnectors resource."""

    _NAME = 'projects_locations_appConnectors'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsAppConnectorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AppConnector in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appConnectors.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['appConnectorId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/appConnectors',
        request_field='googleCloudBeyondcorpAppconnectorsV1alphaAppConnector',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single AppConnector.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}',
        http_method='DELETE',
        method_id='beyondcorp.projects.locations.appConnectors.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single AppConnector.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appConnectors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsGetRequest',
        response_type_name='GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appConnectors.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists AppConnectors in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appConnectors.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/appConnectors',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsListRequest',
        response_type_name='GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single AppConnector.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}',
        http_method='PATCH',
        method_id='beyondcorp.projects.locations.appConnectors.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='googleCloudBeyondcorpAppconnectorsV1alphaAppConnector',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ReportStatus(self, request, global_params=None):
      r"""Report status for a given connector.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsReportStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ReportStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReportStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:reportStatus',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appConnectors.reportStatus',
        ordered_params=['appConnector'],
        path_params=['appConnector'],
        query_params=[],
        relative_path='v1alpha/{+appConnector}:reportStatus',
        request_field='googleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsReportStatusRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ResolveInstanceConfig(self, request, global_params=None):
      r"""Gets instance configuration for a given AppConnector. An internal method called by a AppConnector to get its container config.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsResolveInstanceConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse) The response message.
      """
      config = self.GetMethodConfig('ResolveInstanceConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    ResolveInstanceConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:resolveInstanceConfig',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appConnectors.resolveInstanceConfig',
        ordered_params=['appConnector'],
        path_params=['appConnector'],
        query_params=[],
        relative_path='v1alpha/{+appConnector}:resolveInstanceConfig',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsResolveInstanceConfigRequest',
        response_type_name='GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appConnectors.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsAppConnectorsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appConnectors.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsAppConnectorsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAppGatewaysService(base_api.BaseApiService):
    """Service class for the projects_locations_appGateways resource."""

    _NAME = 'projects_locations_appGateways'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsAppGatewaysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AppGateway in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsAppGatewaysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appGateways.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['appGatewayId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/appGateways',
        request_field='appGateway',
        request_type_name='BeyondcorpProjectsLocationsAppGatewaysCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single AppGateway.

      Args:
        request: (BeyondcorpProjectsLocationsAppGatewaysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}',
        http_method='DELETE',
        method_id='beyondcorp.projects.locations.appGateways.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppGatewaysDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single AppGateway.

      Args:
        request: (BeyondcorpProjectsLocationsAppGatewaysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AppGateway) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appGateways.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppGatewaysGetRequest',
        response_type_name='AppGateway',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsAppGatewaysGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appGateways.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppGatewaysGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists AppGateways in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsAppGatewaysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAppGatewaysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways',
        http_method='GET',
        method_id='beyondcorp.projects.locations.appGateways.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/appGateways',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsAppGatewaysListRequest',
        response_type_name='ListAppGatewaysResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsAppGatewaysSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appGateways.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsAppGatewaysSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsAppGatewaysTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.appGateways.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsAppGatewaysTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsApplicationDomainsService(base_api.BaseApiService):
    """Service class for the projects_locations_applicationDomains resource."""

    _NAME = 'projects_locations_applicationDomains'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsApplicationDomainsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsApplicationDomainsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applicationDomains/{applicationDomainsId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.applicationDomains.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsApplicationDomainsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsApplicationDomainsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applicationDomains/{applicationDomainsId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.applicationDomains.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsApplicationDomainsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsApplicationDomainsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applicationDomains/{applicationDomainsId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.applicationDomains.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsApplicationDomainsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsApplicationsService(base_api.BaseApiService):
    """Service class for the projects_locations_applications resource."""

    _NAME = 'projects_locations_applications'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsApplicationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsApplicationsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.applications.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsApplicationsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsApplicationsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.applications.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsApplicationsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsApplicationsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.applications.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsApplicationsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsClientConnectorServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_clientConnectorServices resource."""

    _NAME = 'projects_locations_clientConnectorServices'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsClientConnectorServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsClientConnectorServicesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clientConnectorServices/{clientConnectorServicesId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.clientConnectorServices.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsClientConnectorServicesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsClientConnectorServicesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clientConnectorServices/{clientConnectorServicesId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.clientConnectorServices.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsClientConnectorServicesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsClientConnectorServicesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clientConnectorServices/{clientConnectorServicesId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.clientConnectorServices.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsClientConnectorServicesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsClientGatewaysService(base_api.BaseApiService):
    """Service class for the projects_locations_clientGateways resource."""

    _NAME = 'projects_locations_clientGateways'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsClientGatewaysService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsClientGatewaysGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clientGateways/{clientGatewaysId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.clientGateways.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsClientGatewaysGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsClientGatewaysSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clientGateways/{clientGatewaysId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.clientGateways.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsClientGatewaysSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsClientGatewaysTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/clientGateways/{clientGatewaysId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.clientGateways.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsClientGatewaysTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsConnectionsService(base_api.BaseApiService):
    """Service class for the projects_locations_connections resource."""

    _NAME = 'projects_locations_connections'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsConnectionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Connection in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsConnectionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connections',
        http_method='POST',
        method_id='beyondcorp.projects.locations.connections.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['connectionId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/connections',
        request_field='connection',
        request_type_name='BeyondcorpProjectsLocationsConnectionsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Connection.

      Args:
        request: (BeyondcorpProjectsLocationsConnectionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}',
        http_method='DELETE',
        method_id='beyondcorp.projects.locations.connections.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Connection.

      Args:
        request: (BeyondcorpProjectsLocationsConnectionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Connection) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}',
        http_method='GET',
        method_id='beyondcorp.projects.locations.connections.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectionsGetRequest',
        response_type_name='Connection',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsConnectionsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.connections.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectionsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Connections in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsConnectionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConnectionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connections',
        http_method='GET',
        method_id='beyondcorp.projects.locations.connections.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/connections',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectionsListRequest',
        response_type_name='ListConnectionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Connection.

      Args:
        request: (BeyondcorpProjectsLocationsConnectionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}',
        http_method='PATCH',
        method_id='beyondcorp.projects.locations.connections.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='connection',
        request_type_name='BeyondcorpProjectsLocationsConnectionsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Resolve(self, request, global_params=None):
      r"""Resolves connections details for a given connector. An internal method called by a connector to find connections to connect to.

      Args:
        request: (BeyondcorpProjectsLocationsConnectionsResolveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResolveConnectionsResponse) The response message.
      """
      config = self.GetMethodConfig('Resolve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resolve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connections:resolve',
        http_method='GET',
        method_id='beyondcorp.projects.locations.connections.resolve',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['connectorId', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/connections:resolve',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectionsResolveRequest',
        response_type_name='ResolveConnectionsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsConnectionsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.connections.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsConnectionsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsConnectionsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.connections.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsConnectionsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsConnectorsService(base_api.BaseApiService):
    """Service class for the projects_locations_connectors resource."""

    _NAME = 'projects_locations_connectors'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsConnectorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Connector in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors',
        http_method='POST',
        method_id='beyondcorp.projects.locations.connectors.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['connectorId', 'requestId', 'validateOnly'],
        relative_path='v1alpha/{+parent}/connectors',
        request_field='connector',
        request_type_name='BeyondcorpProjectsLocationsConnectorsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Connector.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}',
        http_method='DELETE',
        method_id='beyondcorp.projects.locations.connectors.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectorsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Connector.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Connector) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}',
        http_method='GET',
        method_id='beyondcorp.projects.locations.connectors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectorsGetRequest',
        response_type_name='Connector',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.connectors.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectorsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Connectors in a given project and location.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConnectorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors',
        http_method='GET',
        method_id='beyondcorp.projects.locations.connectors.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/connectors',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectorsListRequest',
        response_type_name='ListConnectorsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Connector.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}',
        http_method='PATCH',
        method_id='beyondcorp.projects.locations.connectors.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha/{+name}',
        request_field='connector',
        request_type_name='BeyondcorpProjectsLocationsConnectorsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ReportStatus(self, request, global_params=None):
      r"""Report status for a given connector.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsReportStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ReportStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReportStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}:reportStatus',
        http_method='POST',
        method_id='beyondcorp.projects.locations.connectors.reportStatus',
        ordered_params=['connector'],
        path_params=['connector'],
        query_params=[],
        relative_path='v1alpha/{+connector}:reportStatus',
        request_field='reportStatusRequest',
        request_type_name='BeyondcorpProjectsLocationsConnectorsReportStatusRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ResolveInstanceConfig(self, request, global_params=None):
      r"""Gets instance configuration for a given connector. An internal method called by a connector to get its container config.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsResolveInstanceConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResolveInstanceConfigResponse) The response message.
      """
      config = self.GetMethodConfig('ResolveInstanceConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    ResolveInstanceConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}:resolveInstanceConfig',
        http_method='GET',
        method_id='beyondcorp.projects.locations.connectors.resolveInstanceConfig',
        ordered_params=['connector'],
        path_params=['connector'],
        query_params=[],
        relative_path='v1alpha/{+connector}:resolveInstanceConfig',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsConnectorsResolveInstanceConfigRequest',
        response_type_name='ResolveInstanceConfigResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.connectors.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsConnectorsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsConnectorsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.connectors.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsConnectorsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsInsightsService(base_api.BaseApiService):
    """Service class for the projects_locations_insights resource."""

    _NAME = 'projects_locations_insights'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsInsightsService, self).__init__(client)
      self._upload_configs = {
          }

    def ConfiguredInsight(self, request, global_params=None):
      r"""Gets the value for a selected particular insight based on the provided filters. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project.

      Args:
        request: (BeyondcorpProjectsLocationsInsightsConfiguredInsightRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse) The response message.
      """
      config = self.GetMethodConfig('ConfiguredInsight')
      return self._RunMethod(
          config, request, global_params=global_params)

    ConfiguredInsight.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/insights/{insightsId}:configuredInsight',
        http_method='GET',
        method_id='beyondcorp.projects.locations.insights.configuredInsight',
        ordered_params=['insight'],
        path_params=['insight'],
        query_params=['aggregation', 'customGrouping_fieldFilter', 'customGrouping_groupFields', 'endTime', 'fieldFilter', 'group', 'pageSize', 'pageToken', 'startTime'],
        relative_path='v1alpha/{+insight}:configuredInsight',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsInsightsConfiguredInsightRequest',
        response_type_name='GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the value for a selected particular insight with default configuration. The default aggregation level is 'DAILY' and no grouping will be applied or default grouping if applicable. The data will be returned for recent 7 days starting the day before. The insight data size will be limited to 50 rows. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project. Setting the `view` to `BASIC` will only return the metadata for the insight.

      Args:
        request: (BeyondcorpProjectsLocationsInsightsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/insights/{insightsId}',
        http_method='GET',
        method_id='beyondcorp.projects.locations.insights.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsInsightsGetRequest',
        response_type_name='GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists for all the available insights that could be fetched from the system. Allows to filter using category. Setting the `view` to `BASIC` will let you iterate over the list of insight metadatas.

      Args:
        request: (BeyondcorpProjectsLocationsInsightsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/insights',
        http_method='GET',
        method_id='beyondcorp.projects.locations.insights.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'view'],
        relative_path='v1alpha/{+parent}/insights',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsInsightsListRequest',
        response_type_name='GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse',
        supports_download=False,
    )

  class ProjectsLocationsNetConnectionsService(base_api.BaseApiService):
    """Service class for the projects_locations_netConnections resource."""

    _NAME = 'projects_locations_netConnections'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsNetConnectionsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (BeyondcorpProjectsLocationsNetConnectionsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/netConnections/{netConnectionsId}:getIamPolicy',
        http_method='GET',
        method_id='beyondcorp.projects.locations.netConnections.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsNetConnectionsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (BeyondcorpProjectsLocationsNetConnectionsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/netConnections/{netConnectionsId}:setIamPolicy',
        http_method='POST',
        method_id='beyondcorp.projects.locations.netConnections.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='BeyondcorpProjectsLocationsNetConnectionsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (BeyondcorpProjectsLocationsNetConnectionsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/netConnections/{netConnectionsId}:testIamPermissions',
        http_method='POST',
        method_id='beyondcorp.projects.locations.netConnections.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='BeyondcorpProjectsLocationsNetConnectionsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (BeyondcorpProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='beyondcorp.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='BeyondcorpProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (BeyondcorpProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='beyondcorp.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (BeyondcorpProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='beyondcorp.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (BeyondcorpProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='beyondcorp.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/operations',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (BeyondcorpProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='beyondcorp.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsGetRequest',
        response_type_name='GoogleCloudLocationLocation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (BeyondcorpProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations',
        http_method='GET',
        method_id='beyondcorp.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='BeyondcorpProjectsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(BeyondcorpV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

"""Generated client library for dialogflow version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.dialogflow.v2 import dialogflow_v2_messages as messages


class DialogflowV2(base_api.BaseApiClient):
  """Generated client library for service dialogflow version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://dialogflow.googleapis.com/'
  MTLS_BASE_URL = 'https://dialogflow.mtls.googleapis.com/'

  _PACKAGE = 'dialogflow'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/dialogflow']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'DialogflowV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new dialogflow handle."""
    url = url or self.BASE_URL
    super(DialogflowV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_agent_entityTypes_entities = self.ProjectsAgentEntityTypesEntitiesService(self)
    self.projects_agent_entityTypes = self.ProjectsAgentEntityTypesService(self)
    self.projects_agent_environments_intents = self.ProjectsAgentEnvironmentsIntentsService(self)
    self.projects_agent_environments_users_sessions_contexts = self.ProjectsAgentEnvironmentsUsersSessionsContextsService(self)
    self.projects_agent_environments_users_sessions_entityTypes = self.ProjectsAgentEnvironmentsUsersSessionsEntityTypesService(self)
    self.projects_agent_environments_users_sessions = self.ProjectsAgentEnvironmentsUsersSessionsService(self)
    self.projects_agent_environments_users = self.ProjectsAgentEnvironmentsUsersService(self)
    self.projects_agent_environments = self.ProjectsAgentEnvironmentsService(self)
    self.projects_agent_intents = self.ProjectsAgentIntentsService(self)
    self.projects_agent_knowledgeBases_documents = self.ProjectsAgentKnowledgeBasesDocumentsService(self)
    self.projects_agent_knowledgeBases = self.ProjectsAgentKnowledgeBasesService(self)
    self.projects_agent_sessions_contexts = self.ProjectsAgentSessionsContextsService(self)
    self.projects_agent_sessions_entityTypes = self.ProjectsAgentSessionsEntityTypesService(self)
    self.projects_agent_sessions = self.ProjectsAgentSessionsService(self)
    self.projects_agent_versions = self.ProjectsAgentVersionsService(self)
    self.projects_agent = self.ProjectsAgentService(self)
    self.projects_answerRecords = self.ProjectsAnswerRecordsService(self)
    self.projects_conversationDatasets = self.ProjectsConversationDatasetsService(self)
    self.projects_conversationModels_evaluations = self.ProjectsConversationModelsEvaluationsService(self)
    self.projects_conversationModels = self.ProjectsConversationModelsService(self)
    self.projects_conversationProfiles = self.ProjectsConversationProfilesService(self)
    self.projects_conversations_messages = self.ProjectsConversationsMessagesService(self)
    self.projects_conversations_participants_suggestions = self.ProjectsConversationsParticipantsSuggestionsService(self)
    self.projects_conversations_participants = self.ProjectsConversationsParticipantsService(self)
    self.projects_conversations_suggestions = self.ProjectsConversationsSuggestionsService(self)
    self.projects_conversations = self.ProjectsConversationsService(self)
    self.projects_knowledgeBases_documents = self.ProjectsKnowledgeBasesDocumentsService(self)
    self.projects_knowledgeBases = self.ProjectsKnowledgeBasesService(self)
    self.projects_locations_agent_entityTypes_entities = self.ProjectsLocationsAgentEntityTypesEntitiesService(self)
    self.projects_locations_agent_entityTypes = self.ProjectsLocationsAgentEntityTypesService(self)
    self.projects_locations_agent_environments_intents = self.ProjectsLocationsAgentEnvironmentsIntentsService(self)
    self.projects_locations_agent_environments_users_sessions_contexts = self.ProjectsLocationsAgentEnvironmentsUsersSessionsContextsService(self)
    self.projects_locations_agent_environments_users_sessions_entityTypes = self.ProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesService(self)
    self.projects_locations_agent_environments_users_sessions = self.ProjectsLocationsAgentEnvironmentsUsersSessionsService(self)
    self.projects_locations_agent_environments_users = self.ProjectsLocationsAgentEnvironmentsUsersService(self)
    self.projects_locations_agent_environments = self.ProjectsLocationsAgentEnvironmentsService(self)
    self.projects_locations_agent_intents = self.ProjectsLocationsAgentIntentsService(self)
    self.projects_locations_agent_sessions_contexts = self.ProjectsLocationsAgentSessionsContextsService(self)
    self.projects_locations_agent_sessions_entityTypes = self.ProjectsLocationsAgentSessionsEntityTypesService(self)
    self.projects_locations_agent_sessions = self.ProjectsLocationsAgentSessionsService(self)
    self.projects_locations_agent_versions = self.ProjectsLocationsAgentVersionsService(self)
    self.projects_locations_agent = self.ProjectsLocationsAgentService(self)
    self.projects_locations_answerRecords = self.ProjectsLocationsAnswerRecordsService(self)
    self.projects_locations_conversationDatasets = self.ProjectsLocationsConversationDatasetsService(self)
    self.projects_locations_conversationModels_evaluations = self.ProjectsLocationsConversationModelsEvaluationsService(self)
    self.projects_locations_conversationModels = self.ProjectsLocationsConversationModelsService(self)
    self.projects_locations_conversationProfiles = self.ProjectsLocationsConversationProfilesService(self)
    self.projects_locations_conversations_messages = self.ProjectsLocationsConversationsMessagesService(self)
    self.projects_locations_conversations_participants_suggestions = self.ProjectsLocationsConversationsParticipantsSuggestionsService(self)
    self.projects_locations_conversations_participants = self.ProjectsLocationsConversationsParticipantsService(self)
    self.projects_locations_conversations_suggestions = self.ProjectsLocationsConversationsSuggestionsService(self)
    self.projects_locations_conversations = self.ProjectsLocationsConversationsService(self)
    self.projects_locations_knowledgeBases_documents = self.ProjectsLocationsKnowledgeBasesDocumentsService(self)
    self.projects_locations_knowledgeBases = self.ProjectsLocationsKnowledgeBasesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_suggestions = self.ProjectsLocationsSuggestionsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_operations = self.ProjectsOperationsService(self)
    self.projects_suggestions = self.ProjectsSuggestionsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsAgentEntityTypesEntitiesService(base_api.BaseApiService):
    """Service class for the projects_agent_entityTypes_entities resource."""

    _NAME = 'projects_agent_entityTypes_entities'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentEntityTypesEntitiesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Creates multiple new entities in the specified entity type. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentEntityTypesEntitiesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes/{entityTypesId}/entities:batchCreate',
        http_method='POST',
        method_id='dialogflow.projects.agent.entityTypes.entities.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entities:batchCreate',
        request_field='googleCloudDialogflowV2BatchCreateEntitiesRequest',
        request_type_name='DialogflowProjectsAgentEntityTypesEntitiesBatchCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def BatchDelete(self, request, global_params=None):
      r"""Deletes entities in the specified entity type. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentEntityTypesEntitiesBatchDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes/{entityTypesId}/entities:batchDelete',
        http_method='POST',
        method_id='dialogflow.projects.agent.entityTypes.entities.batchDelete',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entities:batchDelete',
        request_field='googleCloudDialogflowV2BatchDeleteEntitiesRequest',
        request_type_name='DialogflowProjectsAgentEntityTypesEntitiesBatchDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def BatchUpdate(self, request, global_params=None):
      r"""Updates or creates multiple entities in the specified entity type. This method does not affect entities in the entity type that aren't explicitly specified in the request. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training). .

      Args:
        request: (DialogflowProjectsAgentEntityTypesEntitiesBatchUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes/{entityTypesId}/entities:batchUpdate',
        http_method='POST',
        method_id='dialogflow.projects.agent.entityTypes.entities.batchUpdate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entities:batchUpdate',
        request_field='googleCloudDialogflowV2BatchUpdateEntitiesRequest',
        request_type_name='DialogflowProjectsAgentEntityTypesEntitiesBatchUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsAgentEntityTypesService(base_api.BaseApiService):
    """Service class for the projects_agent_entityTypes resource."""

    _NAME = 'projects_agent_entityTypes'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentEntityTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchDelete(self, request, global_params=None):
      r"""Deletes entity types in the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentEntityTypesBatchDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes:batchDelete',
        http_method='POST',
        method_id='dialogflow.projects.agent.entityTypes.batchDelete',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entityTypes:batchDelete',
        request_field='googleCloudDialogflowV2BatchDeleteEntityTypesRequest',
        request_type_name='DialogflowProjectsAgentEntityTypesBatchDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def BatchUpdate(self, request, global_params=None):
      r"""Updates/Creates multiple entity types in the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: BatchUpdateEntityTypesResponse Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentEntityTypesBatchUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes:batchUpdate',
        http_method='POST',
        method_id='dialogflow.projects.agent.entityTypes.batchUpdate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entityTypes:batchUpdate',
        request_field='googleCloudDialogflowV2BatchUpdateEntityTypesRequest',
        request_type_name='DialogflowProjectsAgentEntityTypesBatchUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an entity type in the specified agent. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentEntityTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2EntityType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes',
        http_method='POST',
        method_id='dialogflow.projects.agent.entityTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['languageCode'],
        relative_path='v2/{+parent}/entityTypes',
        request_field='googleCloudDialogflowV2EntityType',
        request_type_name='DialogflowProjectsAgentEntityTypesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2EntityType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified entity type. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentEntityTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes/{entityTypesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.entityTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentEntityTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified entity type.

      Args:
        request: (DialogflowProjectsAgentEntityTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2EntityType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes/{entityTypesId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.entityTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['languageCode'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentEntityTypesGetRequest',
        response_type_name='GoogleCloudDialogflowV2EntityType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all entity types in the specified agent.

      Args:
        request: (DialogflowProjectsAgentEntityTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListEntityTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes',
        http_method='GET',
        method_id='dialogflow.projects.agent.entityTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['languageCode', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/entityTypes',
        request_field='',
        request_type_name='DialogflowProjectsAgentEntityTypesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListEntityTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified entity type. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentEntityTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2EntityType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/entityTypes/{entityTypesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.entityTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['languageCode', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2EntityType',
        request_type_name='DialogflowProjectsAgentEntityTypesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2EntityType',
        supports_download=False,
    )

  class ProjectsAgentEnvironmentsIntentsService(base_api.BaseApiService):
    """Service class for the projects_agent_environments_intents resource."""

    _NAME = 'projects_agent_environments_intents'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentEnvironmentsIntentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns the list of all intents in the specified agent.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsIntentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListIntentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/intents',
        http_method='GET',
        method_id='dialogflow.projects.agent.environments.intents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['intentView', 'languageCode', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/intents',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsIntentsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListIntentsResponse',
        supports_download=False,
    )

  class ProjectsAgentEnvironmentsUsersSessionsContextsService(base_api.BaseApiService):
    """Service class for the projects_agent_environments_users_sessions_contexts resource."""

    _NAME = 'projects_agent_environments_users_sessions_contexts'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentEnvironmentsUsersSessionsContextsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a context. If the specified context already exists, overrides the context.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsContextsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts',
        http_method='POST',
        method_id='dialogflow.projects.agent.environments.users.sessions.contexts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/contexts',
        request_field='googleCloudDialogflowV2Context',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsContextsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified context.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsContextsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.environments.users.sessions.contexts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsContextsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified context.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsContextsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.environments.users.sessions.contexts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsContextsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all contexts in the specified session.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsContextsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListContextsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts',
        http_method='GET',
        method_id='dialogflow.projects.agent.environments.users.sessions.contexts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/contexts',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsContextsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListContextsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified context.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsContextsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.environments.users.sessions.contexts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Context',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsContextsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

  class ProjectsAgentEnvironmentsUsersSessionsEntityTypesService(base_api.BaseApiService):
    """Service class for the projects_agent_environments_users_sessions_entityTypes resource."""

    _NAME = 'projects_agent_environments_users_sessions_entityTypes'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentEnvironmentsUsersSessionsEntityTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a session entity type. If the specified session entity type already exists, overrides the session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes',
        http_method='POST',
        method_id='dialogflow.projects.agent.environments.users.sessions.entityTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entityTypes',
        request_field='googleCloudDialogflowV2SessionEntityType',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.environments.users.sessions.entityTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.environments.users.sessions.entityTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesGetRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all session entity types in the specified session. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListSessionEntityTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes',
        http_method='GET',
        method_id='dialogflow.projects.agent.environments.users.sessions.entityTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/entityTypes',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListSessionEntityTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.environments.users.sessions.entityTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2SessionEntityType',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsEntityTypesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

  class ProjectsAgentEnvironmentsUsersSessionsService(base_api.BaseApiService):
    """Service class for the projects_agent_environments_users_sessions resource."""

    _NAME = 'projects_agent_environments_users_sessions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentEnvironmentsUsersSessionsService, self).__init__(client)
      self._upload_configs = {
          }

    def DeleteContexts(self, request, global_params=None):
      r"""Deletes all active contexts in the specified session.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsDeleteContextsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('DeleteContexts')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteContexts.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.environments.users.sessions.deleteContexts',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/contexts',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsDeleteContextsRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def DetectIntent(self, request, global_params=None):
      r"""Processes a natural language query and returns structured, actionable data as a result. This method is not idempotent, because it may cause contexts and session entity types to be updated, which in turn might affect results of future queries. If you might use [Agent Assist](https://cloud.google.com/dialogflow/docs/#aa) or other CCAI products now or in the future, consider using AnalyzeContent instead of `DetectIntent`. `AnalyzeContent` has additional functionality for Agent Assist and other CCAI products. Note: Always use agent versions for production traffic. See [Versions and environments](https://cloud.google.com/dialogflow/es/docs/agents-versions).

      Args:
        request: (DialogflowProjectsAgentEnvironmentsUsersSessionsDetectIntentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2DetectIntentResponse) The response message.
      """
      config = self.GetMethodConfig('DetectIntent')
      return self._RunMethod(
          config, request, global_params=global_params)

    DetectIntent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}:detectIntent',
        http_method='POST',
        method_id='dialogflow.projects.agent.environments.users.sessions.detectIntent',
        ordered_params=['session'],
        path_params=['session'],
        query_params=[],
        relative_path='v2/{+session}:detectIntent',
        request_field='googleCloudDialogflowV2DetectIntentRequest',
        request_type_name='DialogflowProjectsAgentEnvironmentsUsersSessionsDetectIntentRequest',
        response_type_name='GoogleCloudDialogflowV2DetectIntentResponse',
        supports_download=False,
    )

  class ProjectsAgentEnvironmentsUsersService(base_api.BaseApiService):
    """Service class for the projects_agent_environments_users resource."""

    _NAME = 'projects_agent_environments_users'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentEnvironmentsUsersService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsAgentEnvironmentsService(base_api.BaseApiService):
    """Service class for the projects_agent_environments resource."""

    _NAME = 'projects_agent_environments'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentEnvironmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an agent environment.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Environment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments',
        http_method='POST',
        method_id='dialogflow.projects.agent.environments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['environmentId'],
        relative_path='v2/{+parent}/environments',
        request_field='googleCloudDialogflowV2Environment',
        request_type_name='DialogflowProjectsAgentEnvironmentsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Environment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified agent environment.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.environments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified agent environment.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Environment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.environments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Environment',
        supports_download=False,
    )

    def GetHistory(self, request, global_params=None):
      r"""Gets the history of the specified environment.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsGetHistoryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2EnvironmentHistory) The response message.
      """
      config = self.GetMethodConfig('GetHistory')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetHistory.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}/history',
        http_method='GET',
        method_id='dialogflow.projects.agent.environments.getHistory',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/history',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsGetHistoryRequest',
        response_type_name='GoogleCloudDialogflowV2EnvironmentHistory',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all non-default environments of the specified agent.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListEnvironmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments',
        http_method='GET',
        method_id='dialogflow.projects.agent.environments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/environments',
        request_field='',
        request_type_name='DialogflowProjectsAgentEnvironmentsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListEnvironmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified agent environment. This method allows you to deploy new agent versions into the environment. When an environment is pointed to a new agent version by setting `environment.agent_version`, the environment is temporarily set to the `LOADING` state. During that time, the environment continues serving the previous version of the agent. After the new agent version is done loading, the environment is set back to the `RUNNING` state. You can use "-" as Environment ID in environment name to update an agent version in the default environment. WARNING: this will negate all recent changes to the draft agent and can't be undone. You may want to save the draft agent to a version before calling this method.

      Args:
        request: (DialogflowProjectsAgentEnvironmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Environment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/environments/{environmentsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.environments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowLoadToDraftAndDiscardChanges', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Environment',
        request_type_name='DialogflowProjectsAgentEnvironmentsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Environment',
        supports_download=False,
    )

  class ProjectsAgentIntentsService(base_api.BaseApiService):
    """Service class for the projects_agent_intents resource."""

    _NAME = 'projects_agent_intents'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentIntentsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchDelete(self, request, global_params=None):
      r"""Deletes intents in the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentIntentsBatchDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/intents:batchDelete',
        http_method='POST',
        method_id='dialogflow.projects.agent.intents.batchDelete',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/intents:batchDelete',
        request_field='googleCloudDialogflowV2BatchDeleteIntentsRequest',
        request_type_name='DialogflowProjectsAgentIntentsBatchDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def BatchUpdate(self, request, global_params=None):
      r"""Updates/Creates multiple intents in the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: BatchUpdateIntentsResponse Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentIntentsBatchUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/intents:batchUpdate',
        http_method='POST',
        method_id='dialogflow.projects.agent.intents.batchUpdate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/intents:batchUpdate',
        request_field='googleCloudDialogflowV2BatchUpdateIntentsRequest',
        request_type_name='DialogflowProjectsAgentIntentsBatchUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an intent in the specified agent. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentIntentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Intent) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/intents',
        http_method='POST',
        method_id='dialogflow.projects.agent.intents.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['intentView', 'languageCode'],
        relative_path='v2/{+parent}/intents',
        request_field='googleCloudDialogflowV2Intent',
        request_type_name='DialogflowProjectsAgentIntentsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Intent',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified intent and its direct or indirect followup intents. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentIntentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/intents/{intentsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.intents.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentIntentsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified intent.

      Args:
        request: (DialogflowProjectsAgentIntentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Intent) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/intents/{intentsId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.intents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['intentView', 'languageCode'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentIntentsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Intent',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all intents in the specified agent.

      Args:
        request: (DialogflowProjectsAgentIntentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListIntentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/intents',
        http_method='GET',
        method_id='dialogflow.projects.agent.intents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['intentView', 'languageCode', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/intents',
        request_field='',
        request_type_name='DialogflowProjectsAgentIntentsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListIntentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified intent. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentIntentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Intent) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/intents/{intentsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.intents.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['intentView', 'languageCode', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Intent',
        request_type_name='DialogflowProjectsAgentIntentsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Intent',
        supports_download=False,
    )

  class ProjectsAgentKnowledgeBasesDocumentsService(base_api.BaseApiService):
    """Service class for the projects_agent_knowledgeBases_documents resource."""

    _NAME = 'projects_agent_knowledgeBases_documents'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentKnowledgeBasesDocumentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new document. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesDocumentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases/{knowledgeBasesId}/documents',
        http_method='POST',
        method_id='dialogflow.projects.agent.knowledgeBases.documents.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/documents',
        request_field='googleCloudDialogflowV2Document',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesDocumentsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified document. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesDocumentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.knowledgeBases.documents.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesDocumentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified document.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesDocumentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Document) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.knowledgeBases.documents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesDocumentsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Document',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all documents of the knowledge base.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesDocumentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListDocumentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases/{knowledgeBasesId}/documents',
        http_method='GET',
        method_id='dialogflow.projects.agent.knowledgeBases.documents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/documents',
        request_field='',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesDocumentsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListDocumentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified document. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesDocumentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.knowledgeBases.documents.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Document',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesDocumentsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Reload(self, request, global_params=None):
      r"""Reloads the specified document from its specified source, content_uri or content. The previously loaded content of the document will be deleted. Note: Even when the content of the document has not changed, there still may be side effects because of internal implementation changes. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document Note: The `projects.agent.knowledgeBases.documents` resource is deprecated; only use `projects.knowledgeBases.documents`.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesDocumentsReloadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Reload')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}:reload',
        http_method='POST',
        method_id='dialogflow.projects.agent.knowledgeBases.documents.reload',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:reload',
        request_field='googleCloudDialogflowV2ReloadDocumentRequest',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesDocumentsReloadRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsAgentKnowledgeBasesService(base_api.BaseApiService):
    """Service class for the projects_agent_knowledgeBases resource."""

    _NAME = 'projects_agent_knowledgeBases'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentKnowledgeBasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a knowledge base.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2KnowledgeBase) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases',
        http_method='POST',
        method_id='dialogflow.projects.agent.knowledgeBases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/knowledgeBases',
        request_field='googleCloudDialogflowV2KnowledgeBase',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2KnowledgeBase',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified knowledge base.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases/{knowledgeBasesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.knowledgeBases.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified knowledge base.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2KnowledgeBase) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases/{knowledgeBasesId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.knowledgeBases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesGetRequest',
        response_type_name='GoogleCloudDialogflowV2KnowledgeBase',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all knowledge bases of the specified agent.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListKnowledgeBasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases',
        http_method='GET',
        method_id='dialogflow.projects.agent.knowledgeBases.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/knowledgeBases',
        request_field='',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListKnowledgeBasesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified knowledge base.

      Args:
        request: (DialogflowProjectsAgentKnowledgeBasesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2KnowledgeBase) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/knowledgeBases/{knowledgeBasesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.knowledgeBases.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2KnowledgeBase',
        request_type_name='DialogflowProjectsAgentKnowledgeBasesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2KnowledgeBase',
        supports_download=False,
    )

  class ProjectsAgentSessionsContextsService(base_api.BaseApiService):
    """Service class for the projects_agent_sessions_contexts resource."""

    _NAME = 'projects_agent_sessions_contexts'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentSessionsContextsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a context. If the specified context already exists, overrides the context.

      Args:
        request: (DialogflowProjectsAgentSessionsContextsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/contexts',
        http_method='POST',
        method_id='dialogflow.projects.agent.sessions.contexts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/contexts',
        request_field='googleCloudDialogflowV2Context',
        request_type_name='DialogflowProjectsAgentSessionsContextsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified context.

      Args:
        request: (DialogflowProjectsAgentSessionsContextsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.sessions.contexts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentSessionsContextsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified context.

      Args:
        request: (DialogflowProjectsAgentSessionsContextsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.sessions.contexts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentSessionsContextsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all contexts in the specified session.

      Args:
        request: (DialogflowProjectsAgentSessionsContextsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListContextsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/contexts',
        http_method='GET',
        method_id='dialogflow.projects.agent.sessions.contexts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/contexts',
        request_field='',
        request_type_name='DialogflowProjectsAgentSessionsContextsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListContextsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified context.

      Args:
        request: (DialogflowProjectsAgentSessionsContextsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.sessions.contexts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Context',
        request_type_name='DialogflowProjectsAgentSessionsContextsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

  class ProjectsAgentSessionsEntityTypesService(base_api.BaseApiService):
    """Service class for the projects_agent_sessions_entityTypes resource."""

    _NAME = 'projects_agent_sessions_entityTypes'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentSessionsEntityTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a session entity type. If the specified session entity type already exists, overrides the session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentSessionsEntityTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/entityTypes',
        http_method='POST',
        method_id='dialogflow.projects.agent.sessions.entityTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entityTypes',
        request_field='googleCloudDialogflowV2SessionEntityType',
        request_type_name='DialogflowProjectsAgentSessionsEntityTypesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentSessionsEntityTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.sessions.entityTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentSessionsEntityTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentSessionsEntityTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.sessions.entityTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentSessionsEntityTypesGetRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all session entity types in the specified session. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentSessionsEntityTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListSessionEntityTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/entityTypes',
        http_method='GET',
        method_id='dialogflow.projects.agent.sessions.entityTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/entityTypes',
        request_field='',
        request_type_name='DialogflowProjectsAgentSessionsEntityTypesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListSessionEntityTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsAgentSessionsEntityTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.sessions.entityTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2SessionEntityType',
        request_type_name='DialogflowProjectsAgentSessionsEntityTypesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

  class ProjectsAgentSessionsService(base_api.BaseApiService):
    """Service class for the projects_agent_sessions resource."""

    _NAME = 'projects_agent_sessions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentSessionsService, self).__init__(client)
      self._upload_configs = {
          }

    def DeleteContexts(self, request, global_params=None):
      r"""Deletes all active contexts in the specified session.

      Args:
        request: (DialogflowProjectsAgentSessionsDeleteContextsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('DeleteContexts')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteContexts.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}/contexts',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.sessions.deleteContexts',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/contexts',
        request_field='',
        request_type_name='DialogflowProjectsAgentSessionsDeleteContextsRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def DetectIntent(self, request, global_params=None):
      r"""Processes a natural language query and returns structured, actionable data as a result. This method is not idempotent, because it may cause contexts and session entity types to be updated, which in turn might affect results of future queries. If you might use [Agent Assist](https://cloud.google.com/dialogflow/docs/#aa) or other CCAI products now or in the future, consider using AnalyzeContent instead of `DetectIntent`. `AnalyzeContent` has additional functionality for Agent Assist and other CCAI products. Note: Always use agent versions for production traffic. See [Versions and environments](https://cloud.google.com/dialogflow/es/docs/agents-versions).

      Args:
        request: (DialogflowProjectsAgentSessionsDetectIntentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2DetectIntentResponse) The response message.
      """
      config = self.GetMethodConfig('DetectIntent')
      return self._RunMethod(
          config, request, global_params=global_params)

    DetectIntent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/sessions/{sessionsId}:detectIntent',
        http_method='POST',
        method_id='dialogflow.projects.agent.sessions.detectIntent',
        ordered_params=['session'],
        path_params=['session'],
        query_params=[],
        relative_path='v2/{+session}:detectIntent',
        request_field='googleCloudDialogflowV2DetectIntentRequest',
        request_type_name='DialogflowProjectsAgentSessionsDetectIntentRequest',
        response_type_name='GoogleCloudDialogflowV2DetectIntentResponse',
        supports_download=False,
    )

  class ProjectsAgentVersionsService(base_api.BaseApiService):
    """Service class for the projects_agent_versions resource."""

    _NAME = 'projects_agent_versions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an agent version. The new version points to the agent instance in the "default" environment.

      Args:
        request: (DialogflowProjectsAgentVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Version) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/versions',
        http_method='POST',
        method_id='dialogflow.projects.agent.versions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/versions',
        request_field='googleCloudDialogflowV2Version',
        request_type_name='DialogflowProjectsAgentVersionsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Version',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete the specified agent version.

      Args:
        request: (DialogflowProjectsAgentVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/versions/{versionsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.agent.versions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentVersionsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified agent version.

      Args:
        request: (DialogflowProjectsAgentVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Version) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/versions/{versionsId}',
        http_method='GET',
        method_id='dialogflow.projects.agent.versions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentVersionsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Version',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all versions of the specified agent.

      Args:
        request: (DialogflowProjectsAgentVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/versions',
        http_method='GET',
        method_id='dialogflow.projects.agent.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/versions',
        request_field='',
        request_type_name='DialogflowProjectsAgentVersionsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListVersionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified agent version. Note that this method does not allow you to update the state of the agent the given version points to. It allows you to update only mutable properties of the version resource.

      Args:
        request: (DialogflowProjectsAgentVersionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Version) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/versions/{versionsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.versions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Version',
        request_type_name='DialogflowProjectsAgentVersionsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Version',
        supports_download=False,
    )

  class ProjectsAgentService(base_api.BaseApiService):
    """Service class for the projects_agent resource."""

    _NAME = 'projects_agent'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAgentService, self).__init__(client)
      self._upload_configs = {
          }

    def Export(self, request, global_params=None):
      r"""Exports the specified agent to a ZIP file. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: ExportAgentResponse.

      Args:
        request: (DialogflowProjectsAgentExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent:export',
        http_method='POST',
        method_id='dialogflow.projects.agent.export',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent:export',
        request_field='googleCloudDialogflowV2ExportAgentRequest',
        request_type_name='DialogflowProjectsAgentExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def GetFulfillment(self, request, global_params=None):
      r"""Retrieves the fulfillment.

      Args:
        request: (DialogflowProjectsAgentGetFulfillmentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Fulfillment) The response message.
      """
      config = self.GetMethodConfig('GetFulfillment')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetFulfillment.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/fulfillment',
        http_method='GET',
        method_id='dialogflow.projects.agent.getFulfillment',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsAgentGetFulfillmentRequest',
        response_type_name='GoogleCloudDialogflowV2Fulfillment',
        supports_download=False,
    )

    def GetValidationResult(self, request, global_params=None):
      r"""Gets agent validation result. Agent validation is performed during training time and is updated automatically when training is completed.

      Args:
        request: (DialogflowProjectsAgentGetValidationResultRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ValidationResult) The response message.
      """
      config = self.GetMethodConfig('GetValidationResult')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetValidationResult.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/validationResult',
        http_method='GET',
        method_id='dialogflow.projects.agent.getValidationResult',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['languageCode'],
        relative_path='v2/{+parent}/agent/validationResult',
        request_field='',
        request_type_name='DialogflowProjectsAgentGetValidationResultRequest',
        response_type_name='GoogleCloudDialogflowV2ValidationResult',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports the specified agent from a ZIP file. Uploads new intents and entity types without deleting the existing ones. Intents and entity types with the same name are replaced with the new versions from ImportAgentRequest. After the import, the imported draft agent will be trained automatically (unless disabled in agent settings). However, once the import is done, training may not be completed yet. Please call TrainAgent and wait for the operation it returns in order to train explicitly. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) The operation only tracks when importing is complete, not when it is done training. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent:import',
        http_method='POST',
        method_id='dialogflow.projects.agent.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent:import',
        request_field='googleCloudDialogflowV2ImportAgentRequest',
        request_type_name='DialogflowProjectsAgentImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restores the specified agent from a ZIP file. Replaces the current agent version with a new one. All the intents and entity types in the older version are deleted. After the restore, the restored draft agent will be trained automatically (unless disabled in agent settings). However, once the restore is done, training may not be completed yet. Please call TrainAgent and wait for the operation it returns in order to train explicitly. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) The operation only tracks when restoring is complete, not when it is done training. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent:restore',
        http_method='POST',
        method_id='dialogflow.projects.agent.restore',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent:restore',
        request_field='googleCloudDialogflowV2RestoreAgentRequest',
        request_type_name='DialogflowProjectsAgentRestoreRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Returns the list of agents. Since there is at most one conversational agent per project, this method is useful primarily for listing all agents across projects the caller has access to. One can achieve that with a wildcard project collection id "-". Refer to [List Sub-Collections](https://cloud.google.com/apis/design/design_patterns#list_sub-collections).

      Args:
        request: (DialogflowProjectsAgentSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SearchAgentsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent:search',
        http_method='GET',
        method_id='dialogflow.projects.agent.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/agent:search',
        request_field='',
        request_type_name='DialogflowProjectsAgentSearchRequest',
        response_type_name='GoogleCloudDialogflowV2SearchAgentsResponse',
        supports_download=False,
    )

    def Train(self, request, global_params=None):
      r"""Trains the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsAgentTrainRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Train')
      return self._RunMethod(
          config, request, global_params=global_params)

    Train.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent:train',
        http_method='POST',
        method_id='dialogflow.projects.agent.train',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent:train',
        request_field='googleCloudDialogflowV2TrainAgentRequest',
        request_type_name='DialogflowProjectsAgentTrainRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def UpdateFulfillment(self, request, global_params=None):
      r"""Updates the fulfillment.

      Args:
        request: (DialogflowProjectsAgentUpdateFulfillmentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Fulfillment) The response message.
      """
      config = self.GetMethodConfig('UpdateFulfillment')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateFulfillment.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent/fulfillment',
        http_method='PATCH',
        method_id='dialogflow.projects.agent.updateFulfillment',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Fulfillment',
        request_type_name='DialogflowProjectsAgentUpdateFulfillmentRequest',
        response_type_name='GoogleCloudDialogflowV2Fulfillment',
        supports_download=False,
    )

  class ProjectsAnswerRecordsService(base_api.BaseApiService):
    """Service class for the projects_answerRecords resource."""

    _NAME = 'projects_answerRecords'

    def __init__(self, client):
      super(DialogflowV2.ProjectsAnswerRecordsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns the list of all answer records in the specified project in reverse chronological order.

      Args:
        request: (DialogflowProjectsAnswerRecordsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListAnswerRecordsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/answerRecords',
        http_method='GET',
        method_id='dialogflow.projects.answerRecords.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/answerRecords',
        request_field='',
        request_type_name='DialogflowProjectsAnswerRecordsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListAnswerRecordsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified answer record.

      Args:
        request: (DialogflowProjectsAnswerRecordsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2AnswerRecord) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/answerRecords/{answerRecordsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.answerRecords.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2AnswerRecord',
        request_type_name='DialogflowProjectsAnswerRecordsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2AnswerRecord',
        supports_download=False,
    )

  class ProjectsConversationDatasetsService(base_api.BaseApiService):
    """Service class for the projects_conversationDatasets resource."""

    _NAME = 'projects_conversationDatasets'

    def __init__(self, client):
      super(DialogflowV2.ProjectsConversationDatasetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves the specified conversation dataset.

      Args:
        request: (DialogflowProjectsConversationDatasetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationDataset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationDatasets/{conversationDatasetsId}',
        http_method='GET',
        method_id='dialogflow.projects.conversationDatasets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsConversationDatasetsGetRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationDataset',
        supports_download=False,
    )

    def ImportConversationData(self, request, global_params=None):
      r"""Import data into the specified conversation dataset. Note that it is not allowed to import data to a conversation dataset that already has data in it. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: ImportConversationDataOperationMetadata - `response`: ImportConversationDataOperationResponse.

      Args:
        request: (DialogflowProjectsConversationDatasetsImportConversationDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ImportConversationData')
      return self._RunMethod(
          config, request, global_params=global_params)

    ImportConversationData.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationDatasets/{conversationDatasetsId}:importConversationData',
        http_method='POST',
        method_id='dialogflow.projects.conversationDatasets.importConversationData',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:importConversationData',
        request_field='googleCloudDialogflowV2ImportConversationDataRequest',
        request_type_name='DialogflowProjectsConversationDatasetsImportConversationDataRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all conversation datasets in the specified project and location.

      Args:
        request: (DialogflowProjectsConversationDatasetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationDatasetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationDatasets',
        http_method='GET',
        method_id='dialogflow.projects.conversationDatasets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/conversationDatasets',
        request_field='',
        request_type_name='DialogflowProjectsConversationDatasetsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationDatasetsResponse',
        supports_download=False,
    )

  class ProjectsConversationModelsEvaluationsService(base_api.BaseApiService):
    """Service class for the projects_conversationModels_evaluations resource."""

    _NAME = 'projects_conversationModels_evaluations'

    def __init__(self, client):
      super(DialogflowV2.ProjectsConversationModelsEvaluationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets an evaluation of conversation model.

      Args:
        request: (DialogflowProjectsConversationModelsEvaluationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationModelEvaluation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationModels/{conversationModelsId}/evaluations/{evaluationsId}',
        http_method='GET',
        method_id='dialogflow.projects.conversationModels.evaluations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsConversationModelsEvaluationsGetRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationModelEvaluation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists evaluations of a conversation model.

      Args:
        request: (DialogflowProjectsConversationModelsEvaluationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationModelEvaluationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationModels/{conversationModelsId}/evaluations',
        http_method='GET',
        method_id='dialogflow.projects.conversationModels.evaluations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/evaluations',
        request_field='',
        request_type_name='DialogflowProjectsConversationModelsEvaluationsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationModelEvaluationsResponse',
        supports_download=False,
    )

  class ProjectsConversationModelsService(base_api.BaseApiService):
    """Service class for the projects_conversationModels resource."""

    _NAME = 'projects_conversationModels'

    def __init__(self, client):
      super(DialogflowV2.ProjectsConversationModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a model. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: CreateConversationModelOperationMetadata - `response`: ConversationModel.

      Args:
        request: (DialogflowProjectsConversationModelsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationModels',
        http_method='POST',
        method_id='dialogflow.projects.conversationModels.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/conversationModels',
        request_field='googleCloudDialogflowV2ConversationModel',
        request_type_name='DialogflowProjectsConversationModelsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a model. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: DeleteConversationModelOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsConversationModelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationModels/{conversationModelsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.conversationModels.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsConversationModelsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Deploy(self, request, global_params=None):
      r"""Deploys a model. If a model is already deployed, deploying it has no effect. A model can only serve prediction requests after it gets deployed. For article suggestion, custom model will not be used unless it is deployed. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: DeployConversationModelOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsConversationModelsDeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Deploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationModels/{conversationModelsId}:deploy',
        http_method='POST',
        method_id='dialogflow.projects.conversationModels.deploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:deploy',
        request_field='googleCloudDialogflowV2DeployConversationModelRequest',
        request_type_name='DialogflowProjectsConversationModelsDeployRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets conversation model.

      Args:
        request: (DialogflowProjectsConversationModelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationModel) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationModels/{conversationModelsId}',
        http_method='GET',
        method_id='dialogflow.projects.conversationModels.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsConversationModelsGetRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationModel',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists conversation models.

      Args:
        request: (DialogflowProjectsConversationModelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationModelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationModels',
        http_method='GET',
        method_id='dialogflow.projects.conversationModels.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/conversationModels',
        request_field='',
        request_type_name='DialogflowProjectsConversationModelsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationModelsResponse',
        supports_download=False,
    )

    def Undeploy(self, request, global_params=None):
      r"""Undeploys a model. If the model is not deployed this method has no effect. If the model is currently being used: - For article suggestion, article suggestion will fallback to the default model if model is undeployed. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: UndeployConversationModelOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsConversationModelsUndeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Undeploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undeploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationModels/{conversationModelsId}:undeploy',
        http_method='POST',
        method_id='dialogflow.projects.conversationModels.undeploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:undeploy',
        request_field='googleCloudDialogflowV2UndeployConversationModelRequest',
        request_type_name='DialogflowProjectsConversationModelsUndeployRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsConversationProfilesService(base_api.BaseApiService):
    """Service class for the projects_conversationProfiles resource."""

    _NAME = 'projects_conversationProfiles'

    def __init__(self, client):
      super(DialogflowV2.ProjectsConversationProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def ClearSuggestionFeatureConfig(self, request, global_params=None):
      r"""Clears a suggestion feature from a conversation profile for the given participant role. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: ClearSuggestionFeatureConfigOperationMetadata - `response`: ConversationProfile.

      Args:
        request: (DialogflowProjectsConversationProfilesClearSuggestionFeatureConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ClearSuggestionFeatureConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    ClearSuggestionFeatureConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationProfiles/{conversationProfilesId}:clearSuggestionFeatureConfig',
        http_method='POST',
        method_id='dialogflow.projects.conversationProfiles.clearSuggestionFeatureConfig',
        ordered_params=['conversationProfile'],
        path_params=['conversationProfile'],
        query_params=[],
        relative_path='v2/{+conversationProfile}:clearSuggestionFeatureConfig',
        request_field='googleCloudDialogflowV2ClearSuggestionFeatureConfigRequest',
        request_type_name='DialogflowProjectsConversationProfilesClearSuggestionFeatureConfigRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a conversation profile in the specified project. ConversationProfile.CreateTime and ConversationProfile.UpdateTime aren't populated in the response. You can retrieve them via GetConversationProfile API.

      Args:
        request: (DialogflowProjectsConversationProfilesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationProfile) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationProfiles',
        http_method='POST',
        method_id='dialogflow.projects.conversationProfiles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/conversationProfiles',
        request_field='googleCloudDialogflowV2ConversationProfile',
        request_type_name='DialogflowProjectsConversationProfilesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationProfile',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified conversation profile.

      Args:
        request: (DialogflowProjectsConversationProfilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationProfiles/{conversationProfilesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.conversationProfiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsConversationProfilesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified conversation profile.

      Args:
        request: (DialogflowProjectsConversationProfilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationProfile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationProfiles/{conversationProfilesId}',
        http_method='GET',
        method_id='dialogflow.projects.conversationProfiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsConversationProfilesGetRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationProfile',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all conversation profiles in the specified project.

      Args:
        request: (DialogflowProjectsConversationProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationProfiles',
        http_method='GET',
        method_id='dialogflow.projects.conversationProfiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/conversationProfiles',
        request_field='',
        request_type_name='DialogflowProjectsConversationProfilesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationProfilesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified conversation profile. ConversationProfile.CreateTime and ConversationProfile.UpdateTime aren't populated in the response. You can retrieve them via GetConversationProfile API.

      Args:
        request: (DialogflowProjectsConversationProfilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationProfile) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationProfiles/{conversationProfilesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.conversationProfiles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2ConversationProfile',
        request_type_name='DialogflowProjectsConversationProfilesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationProfile',
        supports_download=False,
    )

    def SetSuggestionFeatureConfig(self, request, global_params=None):
      r"""Adds or updates a suggestion feature in a conversation profile. If the conversation profile contains the type of suggestion feature for the participant role, it will update it. Otherwise it will insert the suggestion feature. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: SetSuggestionFeatureConfigOperationMetadata - `response`: ConversationProfile If a long running operation to add or update suggestion feature config for the same conversation profile, participant role and suggestion feature type exists, please cancel the existing long running operation before sending such request, otherwise the request will be rejected.

      Args:
        request: (DialogflowProjectsConversationProfilesSetSuggestionFeatureConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('SetSuggestionFeatureConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetSuggestionFeatureConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversationProfiles/{conversationProfilesId}:setSuggestionFeatureConfig',
        http_method='POST',
        method_id='dialogflow.projects.conversationProfiles.setSuggestionFeatureConfig',
        ordered_params=['conversationProfile'],
        path_params=['conversationProfile'],
        query_params=[],
        relative_path='v2/{+conversationProfile}:setSuggestionFeatureConfig',
        request_field='googleCloudDialogflowV2SetSuggestionFeatureConfigRequest',
        request_type_name='DialogflowProjectsConversationProfilesSetSuggestionFeatureConfigRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsConversationsMessagesService(base_api.BaseApiService):
    """Service class for the projects_conversations_messages resource."""

    _NAME = 'projects_conversations_messages'

    def __init__(self, client):
      super(DialogflowV2.ProjectsConversationsMessagesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists messages that belong to a given conversation. `messages` are ordered by `create_time` in descending order. To fetch updates without duplication, send request with filter `create_time_epoch_microseconds > [first item's create_time of previous request]` and empty page_token.

      Args:
        request: (DialogflowProjectsConversationsMessagesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListMessagesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/messages',
        http_method='GET',
        method_id='dialogflow.projects.conversations.messages.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/messages',
        request_field='',
        request_type_name='DialogflowProjectsConversationsMessagesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListMessagesResponse',
        supports_download=False,
    )

  class ProjectsConversationsParticipantsSuggestionsService(base_api.BaseApiService):
    """Service class for the projects_conversations_participants_suggestions resource."""

    _NAME = 'projects_conversations_participants_suggestions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsConversationsParticipantsSuggestionsService, self).__init__(client)
      self._upload_configs = {
          }

    def SuggestArticles(self, request, global_params=None):
      r"""Gets suggested articles for a participant based on specific historical messages.

      Args:
        request: (DialogflowProjectsConversationsParticipantsSuggestionsSuggestArticlesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SuggestArticlesResponse) The response message.
      """
      config = self.GetMethodConfig('SuggestArticles')
      return self._RunMethod(
          config, request, global_params=global_params)

    SuggestArticles.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/participants/{participantsId}/suggestions:suggestArticles',
        http_method='POST',
        method_id='dialogflow.projects.conversations.participants.suggestions.suggestArticles',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/suggestions:suggestArticles',
        request_field='googleCloudDialogflowV2SuggestArticlesRequest',
        request_type_name='DialogflowProjectsConversationsParticipantsSuggestionsSuggestArticlesRequest',
        response_type_name='GoogleCloudDialogflowV2SuggestArticlesResponse',
        supports_download=False,
    )

    def SuggestFaqAnswers(self, request, global_params=None):
      r"""Gets suggested faq answers for a participant based on specific historical messages.

      Args:
        request: (DialogflowProjectsConversationsParticipantsSuggestionsSuggestFaqAnswersRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SuggestFaqAnswersResponse) The response message.
      """
      config = self.GetMethodConfig('SuggestFaqAnswers')
      return self._RunMethod(
          config, request, global_params=global_params)

    SuggestFaqAnswers.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/participants/{participantsId}/suggestions:suggestFaqAnswers',
        http_method='POST',
        method_id='dialogflow.projects.conversations.participants.suggestions.suggestFaqAnswers',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/suggestions:suggestFaqAnswers',
        request_field='googleCloudDialogflowV2SuggestFaqAnswersRequest',
        request_type_name='DialogflowProjectsConversationsParticipantsSuggestionsSuggestFaqAnswersRequest',
        response_type_name='GoogleCloudDialogflowV2SuggestFaqAnswersResponse',
        supports_download=False,
    )

    def SuggestSmartReplies(self, request, global_params=None):
      r"""Gets smart replies for a participant based on specific historical messages.

      Args:
        request: (DialogflowProjectsConversationsParticipantsSuggestionsSuggestSmartRepliesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SuggestSmartRepliesResponse) The response message.
      """
      config = self.GetMethodConfig('SuggestSmartReplies')
      return self._RunMethod(
          config, request, global_params=global_params)

    SuggestSmartReplies.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/participants/{participantsId}/suggestions:suggestSmartReplies',
        http_method='POST',
        method_id='dialogflow.projects.conversations.participants.suggestions.suggestSmartReplies',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/suggestions:suggestSmartReplies',
        request_field='googleCloudDialogflowV2SuggestSmartRepliesRequest',
        request_type_name='DialogflowProjectsConversationsParticipantsSuggestionsSuggestSmartRepliesRequest',
        response_type_name='GoogleCloudDialogflowV2SuggestSmartRepliesResponse',
        supports_download=False,
    )

  class ProjectsConversationsParticipantsService(base_api.BaseApiService):
    """Service class for the projects_conversations_participants resource."""

    _NAME = 'projects_conversations_participants'

    def __init__(self, client):
      super(DialogflowV2.ProjectsConversationsParticipantsService, self).__init__(client)
      self._upload_configs = {
          }

    def AnalyzeContent(self, request, global_params=None):
      r"""Adds a text (chat, for example), or audio (phone recording, for example) message from a participant into the conversation. Note: Always use agent versions for production traffic sent to virtual agents. See [Versions and environments](https://cloud.google.com/dialogflow/es/docs/agents-versions).

      Args:
        request: (DialogflowProjectsConversationsParticipantsAnalyzeContentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2AnalyzeContentResponse) The response message.
      """
      config = self.GetMethodConfig('AnalyzeContent')
      return self._RunMethod(
          config, request, global_params=global_params)

    AnalyzeContent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/participants/{participantsId}:analyzeContent',
        http_method='POST',
        method_id='dialogflow.projects.conversations.participants.analyzeContent',
        ordered_params=['participant'],
        path_params=['participant'],
        query_params=[],
        relative_path='v2/{+participant}:analyzeContent',
        request_field='googleCloudDialogflowV2AnalyzeContentRequest',
        request_type_name='DialogflowProjectsConversationsParticipantsAnalyzeContentRequest',
        response_type_name='GoogleCloudDialogflowV2AnalyzeContentResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new participant in a conversation.

      Args:
        request: (DialogflowProjectsConversationsParticipantsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Participant) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/participants',
        http_method='POST',
        method_id='dialogflow.projects.conversations.participants.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/participants',
        request_field='googleCloudDialogflowV2Participant',
        request_type_name='DialogflowProjectsConversationsParticipantsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Participant',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a conversation participant.

      Args:
        request: (DialogflowProjectsConversationsParticipantsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Participant) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/participants/{participantsId}',
        http_method='GET',
        method_id='dialogflow.projects.conversations.participants.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsConversationsParticipantsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Participant',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all participants in the specified conversation.

      Args:
        request: (DialogflowProjectsConversationsParticipantsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListParticipantsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/participants',
        http_method='GET',
        method_id='dialogflow.projects.conversations.participants.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/participants',
        request_field='',
        request_type_name='DialogflowProjectsConversationsParticipantsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListParticipantsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified participant.

      Args:
        request: (DialogflowProjectsConversationsParticipantsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Participant) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/participants/{participantsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.conversations.participants.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Participant',
        request_type_name='DialogflowProjectsConversationsParticipantsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Participant',
        supports_download=False,
    )

  class ProjectsConversationsSuggestionsService(base_api.BaseApiService):
    """Service class for the projects_conversations_suggestions resource."""

    _NAME = 'projects_conversations_suggestions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsConversationsSuggestionsService, self).__init__(client)
      self._upload_configs = {
          }

    def SuggestConversationSummary(self, request, global_params=None):
      r"""Suggests summary for a conversation based on specific historical messages. The range of the messages to be used for summary can be specified in the request.

      Args:
        request: (DialogflowProjectsConversationsSuggestionsSuggestConversationSummaryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SuggestConversationSummaryResponse) The response message.
      """
      config = self.GetMethodConfig('SuggestConversationSummary')
      return self._RunMethod(
          config, request, global_params=global_params)

    SuggestConversationSummary.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}/suggestions:suggestConversationSummary',
        http_method='POST',
        method_id='dialogflow.projects.conversations.suggestions.suggestConversationSummary',
        ordered_params=['conversation'],
        path_params=['conversation'],
        query_params=[],
        relative_path='v2/{+conversation}/suggestions:suggestConversationSummary',
        request_field='googleCloudDialogflowV2SuggestConversationSummaryRequest',
        request_type_name='DialogflowProjectsConversationsSuggestionsSuggestConversationSummaryRequest',
        response_type_name='GoogleCloudDialogflowV2SuggestConversationSummaryResponse',
        supports_download=False,
    )

  class ProjectsConversationsService(base_api.BaseApiService):
    """Service class for the projects_conversations resource."""

    _NAME = 'projects_conversations'

    def __init__(self, client):
      super(DialogflowV2.ProjectsConversationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Complete(self, request, global_params=None):
      r"""Completes the specified conversation. Finished conversations are purged from the database after 30 days.

      Args:
        request: (DialogflowProjectsConversationsCompleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Conversation) The response message.
      """
      config = self.GetMethodConfig('Complete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Complete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}:complete',
        http_method='POST',
        method_id='dialogflow.projects.conversations.complete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:complete',
        request_field='googleCloudDialogflowV2CompleteConversationRequest',
        request_type_name='DialogflowProjectsConversationsCompleteRequest',
        response_type_name='GoogleCloudDialogflowV2Conversation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new conversation. Conversations are auto-completed after 24 hours. Conversation Lifecycle: There are two stages during a conversation: Automated Agent Stage and Assist Stage. For Automated Agent Stage, there will be a dialogflow agent responding to user queries. For Assist Stage, there's no dialogflow agent responding to user queries. But we will provide suggestions which are generated from conversation. If Conversation.conversation_profile is configured for a dialogflow agent, conversation will start from `Automated Agent Stage`, otherwise, it will start from `Assist Stage`. And during `Automated Agent Stage`, once an Intent with Intent.live_agent_handoff is triggered, conversation will transfer to Assist Stage.

      Args:
        request: (DialogflowProjectsConversationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Conversation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations',
        http_method='POST',
        method_id='dialogflow.projects.conversations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['conversationId'],
        relative_path='v2/{+parent}/conversations',
        request_field='googleCloudDialogflowV2Conversation',
        request_type_name='DialogflowProjectsConversationsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Conversation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specific conversation.

      Args:
        request: (DialogflowProjectsConversationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Conversation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations/{conversationsId}',
        http_method='GET',
        method_id='dialogflow.projects.conversations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsConversationsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Conversation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all conversations in the specified project.

      Args:
        request: (DialogflowProjectsConversationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/conversations',
        http_method='GET',
        method_id='dialogflow.projects.conversations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/conversations',
        request_field='',
        request_type_name='DialogflowProjectsConversationsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationsResponse',
        supports_download=False,
    )

  class ProjectsKnowledgeBasesDocumentsService(base_api.BaseApiService):
    """Service class for the projects_knowledgeBases_documents resource."""

    _NAME = 'projects_knowledgeBases_documents'

    def __init__(self, client):
      super(DialogflowV2.ProjectsKnowledgeBasesDocumentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new document. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document.

      Args:
        request: (DialogflowProjectsKnowledgeBasesDocumentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}/documents',
        http_method='POST',
        method_id='dialogflow.projects.knowledgeBases.documents.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/documents',
        request_field='googleCloudDialogflowV2Document',
        request_type_name='DialogflowProjectsKnowledgeBasesDocumentsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified document. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsKnowledgeBasesDocumentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.knowledgeBases.documents.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsKnowledgeBasesDocumentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports a smart messaging candidate document into the specified destination. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document.

      Args:
        request: (DialogflowProjectsKnowledgeBasesDocumentsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}:export',
        http_method='POST',
        method_id='dialogflow.projects.knowledgeBases.documents.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:export',
        request_field='googleCloudDialogflowV2ExportDocumentRequest',
        request_type_name='DialogflowProjectsKnowledgeBasesDocumentsExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified document.

      Args:
        request: (DialogflowProjectsKnowledgeBasesDocumentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Document) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}',
        http_method='GET',
        method_id='dialogflow.projects.knowledgeBases.documents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsKnowledgeBasesDocumentsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Document',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Creates documents by importing data from external sources. Dialogflow supports up to 350 documents in each request. If you try to import more, Dialogflow will return an error. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: ImportDocumentsResponse.

      Args:
        request: (DialogflowProjectsKnowledgeBasesDocumentsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}/documents:import',
        http_method='POST',
        method_id='dialogflow.projects.knowledgeBases.documents.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/documents:import',
        request_field='googleCloudDialogflowV2ImportDocumentsRequest',
        request_type_name='DialogflowProjectsKnowledgeBasesDocumentsImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all documents of the knowledge base.

      Args:
        request: (DialogflowProjectsKnowledgeBasesDocumentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListDocumentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}/documents',
        http_method='GET',
        method_id='dialogflow.projects.knowledgeBases.documents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/documents',
        request_field='',
        request_type_name='DialogflowProjectsKnowledgeBasesDocumentsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListDocumentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified document. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document.

      Args:
        request: (DialogflowProjectsKnowledgeBasesDocumentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.knowledgeBases.documents.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Document',
        request_type_name='DialogflowProjectsKnowledgeBasesDocumentsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Reload(self, request, global_params=None):
      r"""Reloads the specified document from its specified source, content_uri or content. The previously loaded content of the document will be deleted. Note: Even when the content of the document has not changed, there still may be side effects because of internal implementation changes. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document Note: The `projects.agent.knowledgeBases.documents` resource is deprecated; only use `projects.knowledgeBases.documents`.

      Args:
        request: (DialogflowProjectsKnowledgeBasesDocumentsReloadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Reload')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}:reload',
        http_method='POST',
        method_id='dialogflow.projects.knowledgeBases.documents.reload',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:reload',
        request_field='googleCloudDialogflowV2ReloadDocumentRequest',
        request_type_name='DialogflowProjectsKnowledgeBasesDocumentsReloadRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsKnowledgeBasesService(base_api.BaseApiService):
    """Service class for the projects_knowledgeBases resource."""

    _NAME = 'projects_knowledgeBases'

    def __init__(self, client):
      super(DialogflowV2.ProjectsKnowledgeBasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a knowledge base.

      Args:
        request: (DialogflowProjectsKnowledgeBasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2KnowledgeBase) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases',
        http_method='POST',
        method_id='dialogflow.projects.knowledgeBases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/knowledgeBases',
        request_field='googleCloudDialogflowV2KnowledgeBase',
        request_type_name='DialogflowProjectsKnowledgeBasesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2KnowledgeBase',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified knowledge base.

      Args:
        request: (DialogflowProjectsKnowledgeBasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.knowledgeBases.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsKnowledgeBasesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified knowledge base.

      Args:
        request: (DialogflowProjectsKnowledgeBasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2KnowledgeBase) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}',
        http_method='GET',
        method_id='dialogflow.projects.knowledgeBases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsKnowledgeBasesGetRequest',
        response_type_name='GoogleCloudDialogflowV2KnowledgeBase',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all knowledge bases of the specified agent.

      Args:
        request: (DialogflowProjectsKnowledgeBasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListKnowledgeBasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases',
        http_method='GET',
        method_id='dialogflow.projects.knowledgeBases.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/knowledgeBases',
        request_field='',
        request_type_name='DialogflowProjectsKnowledgeBasesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListKnowledgeBasesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified knowledge base.

      Args:
        request: (DialogflowProjectsKnowledgeBasesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2KnowledgeBase) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/knowledgeBases/{knowledgeBasesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.knowledgeBases.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2KnowledgeBase',
        request_type_name='DialogflowProjectsKnowledgeBasesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2KnowledgeBase',
        supports_download=False,
    )

  class ProjectsLocationsAgentEntityTypesEntitiesService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_entityTypes_entities resource."""

    _NAME = 'projects_locations_agent_entityTypes_entities'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentEntityTypesEntitiesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Creates multiple new entities in the specified entity type. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesEntitiesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes/{entityTypesId}/entities:batchCreate',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.entityTypes.entities.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entities:batchCreate',
        request_field='googleCloudDialogflowV2BatchCreateEntitiesRequest',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesEntitiesBatchCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def BatchDelete(self, request, global_params=None):
      r"""Deletes entities in the specified entity type. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesEntitiesBatchDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes/{entityTypesId}/entities:batchDelete',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.entityTypes.entities.batchDelete',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entities:batchDelete',
        request_field='googleCloudDialogflowV2BatchDeleteEntitiesRequest',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesEntitiesBatchDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def BatchUpdate(self, request, global_params=None):
      r"""Updates or creates multiple entities in the specified entity type. This method does not affect entities in the entity type that aren't explicitly specified in the request. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training). .

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesEntitiesBatchUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes/{entityTypesId}/entities:batchUpdate',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.entityTypes.entities.batchUpdate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entities:batchUpdate',
        request_field='googleCloudDialogflowV2BatchUpdateEntitiesRequest',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesEntitiesBatchUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsAgentEntityTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_entityTypes resource."""

    _NAME = 'projects_locations_agent_entityTypes'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentEntityTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchDelete(self, request, global_params=None):
      r"""Deletes entity types in the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesBatchDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes:batchDelete',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.entityTypes.batchDelete',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entityTypes:batchDelete',
        request_field='googleCloudDialogflowV2BatchDeleteEntityTypesRequest',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesBatchDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def BatchUpdate(self, request, global_params=None):
      r"""Updates/Creates multiple entity types in the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: BatchUpdateEntityTypesResponse Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesBatchUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes:batchUpdate',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.entityTypes.batchUpdate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entityTypes:batchUpdate',
        request_field='googleCloudDialogflowV2BatchUpdateEntityTypesRequest',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesBatchUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an entity type in the specified agent. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2EntityType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.entityTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['languageCode'],
        relative_path='v2/{+parent}/entityTypes',
        request_field='googleCloudDialogflowV2EntityType',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2EntityType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified entity type. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes/{entityTypesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.entityTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified entity type.

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2EntityType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes/{entityTypesId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.entityTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['languageCode'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesGetRequest',
        response_type_name='GoogleCloudDialogflowV2EntityType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all entity types in the specified agent.

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListEntityTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.entityTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['languageCode', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/entityTypes',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListEntityTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified entity type. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentEntityTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2EntityType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/entityTypes/{entityTypesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.agent.entityTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['languageCode', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2EntityType',
        request_type_name='DialogflowProjectsLocationsAgentEntityTypesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2EntityType',
        supports_download=False,
    )

  class ProjectsLocationsAgentEnvironmentsIntentsService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_environments_intents resource."""

    _NAME = 'projects_locations_agent_environments_intents'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentEnvironmentsIntentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns the list of all intents in the specified agent.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsIntentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListIntentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/intents',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.environments.intents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['intentView', 'languageCode', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/intents',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsIntentsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListIntentsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAgentEnvironmentsUsersSessionsContextsService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_environments_users_sessions_contexts resource."""

    _NAME = 'projects_locations_agent_environments_users_sessions_contexts'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentEnvironmentsUsersSessionsContextsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a context. If the specified context already exists, overrides the context.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.contexts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/contexts',
        request_field='googleCloudDialogflowV2Context',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified context.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.contexts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified context.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.contexts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all contexts in the specified session.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListContextsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.contexts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/contexts',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListContextsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified context.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.contexts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Context',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsContextsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

  class ProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_environments_users_sessions_entityTypes resource."""

    _NAME = 'projects_locations_agent_environments_users_sessions_entityTypes'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a session entity type. If the specified session entity type already exists, overrides the session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.entityTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entityTypes',
        request_field='googleCloudDialogflowV2SessionEntityType',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.entityTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.entityTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesGetRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all session entity types in the specified session. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListSessionEntityTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.entityTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/entityTypes',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListSessionEntityTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.entityTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2SessionEntityType',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsEntityTypesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

  class ProjectsLocationsAgentEnvironmentsUsersSessionsService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_environments_users_sessions resource."""

    _NAME = 'projects_locations_agent_environments_users_sessions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentEnvironmentsUsersSessionsService, self).__init__(client)
      self._upload_configs = {
          }

    def DeleteContexts(self, request, global_params=None):
      r"""Deletes all active contexts in the specified session.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsDeleteContextsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('DeleteContexts')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteContexts.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}/contexts',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.deleteContexts',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/contexts',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsDeleteContextsRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def DetectIntent(self, request, global_params=None):
      r"""Processes a natural language query and returns structured, actionable data as a result. This method is not idempotent, because it may cause contexts and session entity types to be updated, which in turn might affect results of future queries. If you might use [Agent Assist](https://cloud.google.com/dialogflow/docs/#aa) or other CCAI products now or in the future, consider using AnalyzeContent instead of `DetectIntent`. `AnalyzeContent` has additional functionality for Agent Assist and other CCAI products. Note: Always use agent versions for production traffic. See [Versions and environments](https://cloud.google.com/dialogflow/es/docs/agents-versions).

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsDetectIntentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2DetectIntentResponse) The response message.
      """
      config = self.GetMethodConfig('DetectIntent')
      return self._RunMethod(
          config, request, global_params=global_params)

    DetectIntent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/users/{usersId}/sessions/{sessionsId}:detectIntent',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.environments.users.sessions.detectIntent',
        ordered_params=['session'],
        path_params=['session'],
        query_params=[],
        relative_path='v2/{+session}:detectIntent',
        request_field='googleCloudDialogflowV2DetectIntentRequest',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsUsersSessionsDetectIntentRequest',
        response_type_name='GoogleCloudDialogflowV2DetectIntentResponse',
        supports_download=False,
    )

  class ProjectsLocationsAgentEnvironmentsUsersService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_environments_users resource."""

    _NAME = 'projects_locations_agent_environments_users'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentEnvironmentsUsersService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsAgentEnvironmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_environments resource."""

    _NAME = 'projects_locations_agent_environments'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentEnvironmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an agent environment.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Environment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.environments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['environmentId'],
        relative_path='v2/{+parent}/environments',
        request_field='googleCloudDialogflowV2Environment',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Environment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified agent environment.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.environments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified agent environment.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Environment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.environments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Environment',
        supports_download=False,
    )

    def GetHistory(self, request, global_params=None):
      r"""Gets the history of the specified environment.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsGetHistoryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2EnvironmentHistory) The response message.
      """
      config = self.GetMethodConfig('GetHistory')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetHistory.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}/history',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.environments.getHistory',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/history',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsGetHistoryRequest',
        response_type_name='GoogleCloudDialogflowV2EnvironmentHistory',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all non-default environments of the specified agent.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListEnvironmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.environments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/environments',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListEnvironmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified agent environment. This method allows you to deploy new agent versions into the environment. When an environment is pointed to a new agent version by setting `environment.agent_version`, the environment is temporarily set to the `LOADING` state. During that time, the environment continues serving the previous version of the agent. After the new agent version is done loading, the environment is set back to the `RUNNING` state. You can use "-" as Environment ID in environment name to update an agent version in the default environment. WARNING: this will negate all recent changes to the draft agent and can't be undone. You may want to save the draft agent to a version before calling this method.

      Args:
        request: (DialogflowProjectsLocationsAgentEnvironmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Environment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/environments/{environmentsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.agent.environments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowLoadToDraftAndDiscardChanges', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Environment',
        request_type_name='DialogflowProjectsLocationsAgentEnvironmentsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Environment',
        supports_download=False,
    )

  class ProjectsLocationsAgentIntentsService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_intents resource."""

    _NAME = 'projects_locations_agent_intents'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentIntentsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchDelete(self, request, global_params=None):
      r"""Deletes intents in the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentIntentsBatchDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/intents:batchDelete',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.intents.batchDelete',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/intents:batchDelete',
        request_field='googleCloudDialogflowV2BatchDeleteIntentsRequest',
        request_type_name='DialogflowProjectsLocationsAgentIntentsBatchDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def BatchUpdate(self, request, global_params=None):
      r"""Updates/Creates multiple intents in the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: BatchUpdateIntentsResponse Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentIntentsBatchUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/intents:batchUpdate',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.intents.batchUpdate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/intents:batchUpdate',
        request_field='googleCloudDialogflowV2BatchUpdateIntentsRequest',
        request_type_name='DialogflowProjectsLocationsAgentIntentsBatchUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an intent in the specified agent. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentIntentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Intent) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/intents',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.intents.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['intentView', 'languageCode'],
        relative_path='v2/{+parent}/intents',
        request_field='googleCloudDialogflowV2Intent',
        request_type_name='DialogflowProjectsLocationsAgentIntentsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Intent',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified intent and its direct or indirect followup intents. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentIntentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/intents/{intentsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.intents.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentIntentsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified intent.

      Args:
        request: (DialogflowProjectsLocationsAgentIntentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Intent) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/intents/{intentsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.intents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['intentView', 'languageCode'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentIntentsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Intent',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all intents in the specified agent.

      Args:
        request: (DialogflowProjectsLocationsAgentIntentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListIntentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/intents',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.intents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['intentView', 'languageCode', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/intents',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentIntentsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListIntentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified intent. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentIntentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Intent) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/intents/{intentsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.agent.intents.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['intentView', 'languageCode', 'updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Intent',
        request_type_name='DialogflowProjectsLocationsAgentIntentsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Intent',
        supports_download=False,
    )

  class ProjectsLocationsAgentSessionsContextsService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_sessions_contexts resource."""

    _NAME = 'projects_locations_agent_sessions_contexts'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentSessionsContextsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a context. If the specified context already exists, overrides the context.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsContextsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/contexts',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.sessions.contexts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/contexts',
        request_field='googleCloudDialogflowV2Context',
        request_type_name='DialogflowProjectsLocationsAgentSessionsContextsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified context.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsContextsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.sessions.contexts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentSessionsContextsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified context.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsContextsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.sessions.contexts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentSessionsContextsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all contexts in the specified session.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsContextsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListContextsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/contexts',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.sessions.contexts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/contexts',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentSessionsContextsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListContextsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified context.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsContextsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Context) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/contexts/{contextsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.agent.sessions.contexts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Context',
        request_type_name='DialogflowProjectsLocationsAgentSessionsContextsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Context',
        supports_download=False,
    )

  class ProjectsLocationsAgentSessionsEntityTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_sessions_entityTypes resource."""

    _NAME = 'projects_locations_agent_sessions_entityTypes'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentSessionsEntityTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a session entity type. If the specified session entity type already exists, overrides the session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsEntityTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/entityTypes',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.sessions.entityTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/entityTypes',
        request_field='googleCloudDialogflowV2SessionEntityType',
        request_type_name='DialogflowProjectsLocationsAgentSessionsEntityTypesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsEntityTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.sessions.entityTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentSessionsEntityTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsEntityTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.sessions.entityTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentSessionsEntityTypesGetRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all session entity types in the specified session. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsEntityTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListSessionEntityTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/entityTypes',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.sessions.entityTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/entityTypes',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentSessionsEntityTypesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListSessionEntityTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified session entity type. This method doesn't work with Google Assistant integration. Contact Dialogflow support if you need to use session entities with Google Assistant integration.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsEntityTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SessionEntityType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/entityTypes/{entityTypesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.agent.sessions.entityTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2SessionEntityType',
        request_type_name='DialogflowProjectsLocationsAgentSessionsEntityTypesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2SessionEntityType',
        supports_download=False,
    )

  class ProjectsLocationsAgentSessionsService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_sessions resource."""

    _NAME = 'projects_locations_agent_sessions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentSessionsService, self).__init__(client)
      self._upload_configs = {
          }

    def DeleteContexts(self, request, global_params=None):
      r"""Deletes all active contexts in the specified session.

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsDeleteContextsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('DeleteContexts')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteContexts.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}/contexts',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.sessions.deleteContexts',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/contexts',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentSessionsDeleteContextsRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def DetectIntent(self, request, global_params=None):
      r"""Processes a natural language query and returns structured, actionable data as a result. This method is not idempotent, because it may cause contexts and session entity types to be updated, which in turn might affect results of future queries. If you might use [Agent Assist](https://cloud.google.com/dialogflow/docs/#aa) or other CCAI products now or in the future, consider using AnalyzeContent instead of `DetectIntent`. `AnalyzeContent` has additional functionality for Agent Assist and other CCAI products. Note: Always use agent versions for production traffic. See [Versions and environments](https://cloud.google.com/dialogflow/es/docs/agents-versions).

      Args:
        request: (DialogflowProjectsLocationsAgentSessionsDetectIntentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2DetectIntentResponse) The response message.
      """
      config = self.GetMethodConfig('DetectIntent')
      return self._RunMethod(
          config, request, global_params=global_params)

    DetectIntent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/sessions/{sessionsId}:detectIntent',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.sessions.detectIntent',
        ordered_params=['session'],
        path_params=['session'],
        query_params=[],
        relative_path='v2/{+session}:detectIntent',
        request_field='googleCloudDialogflowV2DetectIntentRequest',
        request_type_name='DialogflowProjectsLocationsAgentSessionsDetectIntentRequest',
        response_type_name='GoogleCloudDialogflowV2DetectIntentResponse',
        supports_download=False,
    )

  class ProjectsLocationsAgentVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_agent_versions resource."""

    _NAME = 'projects_locations_agent_versions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an agent version. The new version points to the agent instance in the "default" environment.

      Args:
        request: (DialogflowProjectsLocationsAgentVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Version) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/versions',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.versions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/versions',
        request_field='googleCloudDialogflowV2Version',
        request_type_name='DialogflowProjectsLocationsAgentVersionsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Version',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete the specified agent version.

      Args:
        request: (DialogflowProjectsLocationsAgentVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/versions/{versionsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.agent.versions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentVersionsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified agent version.

      Args:
        request: (DialogflowProjectsLocationsAgentVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Version) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/versions/{versionsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.versions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentVersionsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Version',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all versions of the specified agent.

      Args:
        request: (DialogflowProjectsLocationsAgentVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/versions',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/versions',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentVersionsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListVersionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified agent version. Note that this method does not allow you to update the state of the agent the given version points to. It allows you to update only mutable properties of the version resource.

      Args:
        request: (DialogflowProjectsLocationsAgentVersionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Version) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/versions/{versionsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.agent.versions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Version',
        request_type_name='DialogflowProjectsLocationsAgentVersionsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Version',
        supports_download=False,
    )

  class ProjectsLocationsAgentService(base_api.BaseApiService):
    """Service class for the projects_locations_agent resource."""

    _NAME = 'projects_locations_agent'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAgentService, self).__init__(client)
      self._upload_configs = {
          }

    def Export(self, request, global_params=None):
      r"""Exports the specified agent to a ZIP file. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: ExportAgentResponse.

      Args:
        request: (DialogflowProjectsLocationsAgentExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent:export',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.export',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent:export',
        request_field='googleCloudDialogflowV2ExportAgentRequest',
        request_type_name='DialogflowProjectsLocationsAgentExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def GetFulfillment(self, request, global_params=None):
      r"""Retrieves the fulfillment.

      Args:
        request: (DialogflowProjectsLocationsAgentGetFulfillmentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Fulfillment) The response message.
      """
      config = self.GetMethodConfig('GetFulfillment')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetFulfillment.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/fulfillment',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.getFulfillment',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentGetFulfillmentRequest',
        response_type_name='GoogleCloudDialogflowV2Fulfillment',
        supports_download=False,
    )

    def GetValidationResult(self, request, global_params=None):
      r"""Gets agent validation result. Agent validation is performed during training time and is updated automatically when training is completed.

      Args:
        request: (DialogflowProjectsLocationsAgentGetValidationResultRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ValidationResult) The response message.
      """
      config = self.GetMethodConfig('GetValidationResult')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetValidationResult.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/validationResult',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.getValidationResult',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['languageCode'],
        relative_path='v2/{+parent}/agent/validationResult',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentGetValidationResultRequest',
        response_type_name='GoogleCloudDialogflowV2ValidationResult',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports the specified agent from a ZIP file. Uploads new intents and entity types without deleting the existing ones. Intents and entity types with the same name are replaced with the new versions from ImportAgentRequest. After the import, the imported draft agent will be trained automatically (unless disabled in agent settings). However, once the import is done, training may not be completed yet. Please call TrainAgent and wait for the operation it returns in order to train explicitly. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) The operation only tracks when importing is complete, not when it is done training. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent:import',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent:import',
        request_field='googleCloudDialogflowV2ImportAgentRequest',
        request_type_name='DialogflowProjectsLocationsAgentImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restores the specified agent from a ZIP file. Replaces the current agent version with a new one. All the intents and entity types in the older version are deleted. After the restore, the restored draft agent will be trained automatically (unless disabled in agent settings). However, once the restore is done, training may not be completed yet. Please call TrainAgent and wait for the operation it returns in order to train explicitly. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) The operation only tracks when restoring is complete, not when it is done training. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent:restore',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.restore',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent:restore',
        request_field='googleCloudDialogflowV2RestoreAgentRequest',
        request_type_name='DialogflowProjectsLocationsAgentRestoreRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Returns the list of agents. Since there is at most one conversational agent per project, this method is useful primarily for listing all agents across projects the caller has access to. One can achieve that with a wildcard project collection id "-". Refer to [List Sub-Collections](https://cloud.google.com/apis/design/design_patterns#list_sub-collections).

      Args:
        request: (DialogflowProjectsLocationsAgentSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SearchAgentsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent:search',
        http_method='GET',
        method_id='dialogflow.projects.locations.agent.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/agent:search',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAgentSearchRequest',
        response_type_name='GoogleCloudDialogflowV2SearchAgentsResponse',
        supports_download=False,
    )

    def Train(self, request, global_params=None):
      r"""Trains the specified agent. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: An empty [Struct message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct) - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty) Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsAgentTrainRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Train')
      return self._RunMethod(
          config, request, global_params=global_params)

    Train.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent:train',
        http_method='POST',
        method_id='dialogflow.projects.locations.agent.train',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent:train',
        request_field='googleCloudDialogflowV2TrainAgentRequest',
        request_type_name='DialogflowProjectsLocationsAgentTrainRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def UpdateFulfillment(self, request, global_params=None):
      r"""Updates the fulfillment.

      Args:
        request: (DialogflowProjectsLocationsAgentUpdateFulfillmentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Fulfillment) The response message.
      """
      config = self.GetMethodConfig('UpdateFulfillment')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateFulfillment.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent/fulfillment',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.agent.updateFulfillment',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Fulfillment',
        request_type_name='DialogflowProjectsLocationsAgentUpdateFulfillmentRequest',
        response_type_name='GoogleCloudDialogflowV2Fulfillment',
        supports_download=False,
    )

  class ProjectsLocationsAnswerRecordsService(base_api.BaseApiService):
    """Service class for the projects_locations_answerRecords resource."""

    _NAME = 'projects_locations_answerRecords'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsAnswerRecordsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns the list of all answer records in the specified project in reverse chronological order.

      Args:
        request: (DialogflowProjectsLocationsAnswerRecordsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListAnswerRecordsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/answerRecords',
        http_method='GET',
        method_id='dialogflow.projects.locations.answerRecords.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/answerRecords',
        request_field='',
        request_type_name='DialogflowProjectsLocationsAnswerRecordsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListAnswerRecordsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified answer record.

      Args:
        request: (DialogflowProjectsLocationsAnswerRecordsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2AnswerRecord) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/answerRecords/{answerRecordsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.answerRecords.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2AnswerRecord',
        request_type_name='DialogflowProjectsLocationsAnswerRecordsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2AnswerRecord',
        supports_download=False,
    )

  class ProjectsLocationsConversationDatasetsService(base_api.BaseApiService):
    """Service class for the projects_locations_conversationDatasets resource."""

    _NAME = 'projects_locations_conversationDatasets'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsConversationDatasetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new conversation dataset. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: CreateConversationDatasetOperationMetadata - `response`: ConversationDataset.

      Args:
        request: (DialogflowProjectsLocationsConversationDatasetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationDatasets',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversationDatasets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/conversationDatasets',
        request_field='googleCloudDialogflowV2ConversationDataset',
        request_type_name='DialogflowProjectsLocationsConversationDatasetsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified conversation dataset. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: DeleteConversationDatasetOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsLocationsConversationDatasetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationDatasets/{conversationDatasetsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.conversationDatasets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationDatasetsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified conversation dataset.

      Args:
        request: (DialogflowProjectsLocationsConversationDatasetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationDataset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationDatasets/{conversationDatasetsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversationDatasets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationDatasetsGetRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationDataset',
        supports_download=False,
    )

    def ImportConversationData(self, request, global_params=None):
      r"""Import data into the specified conversation dataset. Note that it is not allowed to import data to a conversation dataset that already has data in it. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: ImportConversationDataOperationMetadata - `response`: ImportConversationDataOperationResponse.

      Args:
        request: (DialogflowProjectsLocationsConversationDatasetsImportConversationDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ImportConversationData')
      return self._RunMethod(
          config, request, global_params=global_params)

    ImportConversationData.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationDatasets/{conversationDatasetsId}:importConversationData',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversationDatasets.importConversationData',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:importConversationData',
        request_field='googleCloudDialogflowV2ImportConversationDataRequest',
        request_type_name='DialogflowProjectsLocationsConversationDatasetsImportConversationDataRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all conversation datasets in the specified project and location.

      Args:
        request: (DialogflowProjectsLocationsConversationDatasetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationDatasetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationDatasets',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversationDatasets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/conversationDatasets',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationDatasetsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationDatasetsResponse',
        supports_download=False,
    )

  class ProjectsLocationsConversationModelsEvaluationsService(base_api.BaseApiService):
    """Service class for the projects_locations_conversationModels_evaluations resource."""

    _NAME = 'projects_locations_conversationModels_evaluations'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsConversationModelsEvaluationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates evaluation of a conversation model.

      Args:
        request: (DialogflowProjectsLocationsConversationModelsEvaluationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationModels/{conversationModelsId}/evaluations',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversationModels.evaluations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/evaluations',
        request_field='googleCloudDialogflowV2CreateConversationModelEvaluationRequest',
        request_type_name='DialogflowProjectsLocationsConversationModelsEvaluationsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an evaluation of conversation model.

      Args:
        request: (DialogflowProjectsLocationsConversationModelsEvaluationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationModelEvaluation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationModels/{conversationModelsId}/evaluations/{evaluationsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversationModels.evaluations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationModelsEvaluationsGetRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationModelEvaluation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists evaluations of a conversation model.

      Args:
        request: (DialogflowProjectsLocationsConversationModelsEvaluationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationModelEvaluationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationModels/{conversationModelsId}/evaluations',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversationModels.evaluations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/evaluations',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationModelsEvaluationsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationModelEvaluationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsConversationModelsService(base_api.BaseApiService):
    """Service class for the projects_locations_conversationModels resource."""

    _NAME = 'projects_locations_conversationModels'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsConversationModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a model. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: CreateConversationModelOperationMetadata - `response`: ConversationModel.

      Args:
        request: (DialogflowProjectsLocationsConversationModelsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationModels',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversationModels.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/conversationModels',
        request_field='googleCloudDialogflowV2ConversationModel',
        request_type_name='DialogflowProjectsLocationsConversationModelsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a model. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: DeleteConversationModelOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsLocationsConversationModelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationModels/{conversationModelsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.conversationModels.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationModelsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Deploy(self, request, global_params=None):
      r"""Deploys a model. If a model is already deployed, deploying it has no effect. A model can only serve prediction requests after it gets deployed. For article suggestion, custom model will not be used unless it is deployed. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: DeployConversationModelOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsLocationsConversationModelsDeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Deploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationModels/{conversationModelsId}:deploy',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversationModels.deploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:deploy',
        request_field='googleCloudDialogflowV2DeployConversationModelRequest',
        request_type_name='DialogflowProjectsLocationsConversationModelsDeployRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets conversation model.

      Args:
        request: (DialogflowProjectsLocationsConversationModelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationModel) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationModels/{conversationModelsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversationModels.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationModelsGetRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationModel',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists conversation models.

      Args:
        request: (DialogflowProjectsLocationsConversationModelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationModelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationModels',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversationModels.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/conversationModels',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationModelsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationModelsResponse',
        supports_download=False,
    )

    def Undeploy(self, request, global_params=None):
      r"""Undeploys a model. If the model is not deployed this method has no effect. If the model is currently being used: - For article suggestion, article suggestion will fallback to the default model if model is undeployed. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: UndeployConversationModelOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsLocationsConversationModelsUndeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Undeploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undeploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationModels/{conversationModelsId}:undeploy',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversationModels.undeploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:undeploy',
        request_field='googleCloudDialogflowV2UndeployConversationModelRequest',
        request_type_name='DialogflowProjectsLocationsConversationModelsUndeployRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsConversationProfilesService(base_api.BaseApiService):
    """Service class for the projects_locations_conversationProfiles resource."""

    _NAME = 'projects_locations_conversationProfiles'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsConversationProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def ClearSuggestionFeatureConfig(self, request, global_params=None):
      r"""Clears a suggestion feature from a conversation profile for the given participant role. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: ClearSuggestionFeatureConfigOperationMetadata - `response`: ConversationProfile.

      Args:
        request: (DialogflowProjectsLocationsConversationProfilesClearSuggestionFeatureConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ClearSuggestionFeatureConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    ClearSuggestionFeatureConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationProfiles/{conversationProfilesId}:clearSuggestionFeatureConfig',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversationProfiles.clearSuggestionFeatureConfig',
        ordered_params=['conversationProfile'],
        path_params=['conversationProfile'],
        query_params=[],
        relative_path='v2/{+conversationProfile}:clearSuggestionFeatureConfig',
        request_field='googleCloudDialogflowV2ClearSuggestionFeatureConfigRequest',
        request_type_name='DialogflowProjectsLocationsConversationProfilesClearSuggestionFeatureConfigRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a conversation profile in the specified project. ConversationProfile.CreateTime and ConversationProfile.UpdateTime aren't populated in the response. You can retrieve them via GetConversationProfile API.

      Args:
        request: (DialogflowProjectsLocationsConversationProfilesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationProfile) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationProfiles',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversationProfiles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/conversationProfiles',
        request_field='googleCloudDialogflowV2ConversationProfile',
        request_type_name='DialogflowProjectsLocationsConversationProfilesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationProfile',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified conversation profile.

      Args:
        request: (DialogflowProjectsLocationsConversationProfilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationProfiles/{conversationProfilesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.conversationProfiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationProfilesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified conversation profile.

      Args:
        request: (DialogflowProjectsLocationsConversationProfilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationProfile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationProfiles/{conversationProfilesId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversationProfiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationProfilesGetRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationProfile',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all conversation profiles in the specified project.

      Args:
        request: (DialogflowProjectsLocationsConversationProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationProfiles',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversationProfiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/conversationProfiles',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationProfilesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationProfilesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified conversation profile. ConversationProfile.CreateTime and ConversationProfile.UpdateTime aren't populated in the response. You can retrieve them via GetConversationProfile API.

      Args:
        request: (DialogflowProjectsLocationsConversationProfilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ConversationProfile) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationProfiles/{conversationProfilesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.conversationProfiles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2ConversationProfile',
        request_type_name='DialogflowProjectsLocationsConversationProfilesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2ConversationProfile',
        supports_download=False,
    )

    def SetSuggestionFeatureConfig(self, request, global_params=None):
      r"""Adds or updates a suggestion feature in a conversation profile. If the conversation profile contains the type of suggestion feature for the participant role, it will update it. Otherwise it will insert the suggestion feature. This method is a [long-running operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations). The returned `Operation` type has the following method-specific fields: - `metadata`: SetSuggestionFeatureConfigOperationMetadata - `response`: ConversationProfile If a long running operation to add or update suggestion feature config for the same conversation profile, participant role and suggestion feature type exists, please cancel the existing long running operation before sending such request, otherwise the request will be rejected.

      Args:
        request: (DialogflowProjectsLocationsConversationProfilesSetSuggestionFeatureConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('SetSuggestionFeatureConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetSuggestionFeatureConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversationProfiles/{conversationProfilesId}:setSuggestionFeatureConfig',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversationProfiles.setSuggestionFeatureConfig',
        ordered_params=['conversationProfile'],
        path_params=['conversationProfile'],
        query_params=[],
        relative_path='v2/{+conversationProfile}:setSuggestionFeatureConfig',
        request_field='googleCloudDialogflowV2SetSuggestionFeatureConfigRequest',
        request_type_name='DialogflowProjectsLocationsConversationProfilesSetSuggestionFeatureConfigRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsConversationsMessagesService(base_api.BaseApiService):
    """Service class for the projects_locations_conversations_messages resource."""

    _NAME = 'projects_locations_conversations_messages'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsConversationsMessagesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists messages that belong to a given conversation. `messages` are ordered by `create_time` in descending order. To fetch updates without duplication, send request with filter `create_time_epoch_microseconds > [first item's create_time of previous request]` and empty page_token.

      Args:
        request: (DialogflowProjectsLocationsConversationsMessagesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListMessagesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/messages',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversations.messages.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/messages',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationsMessagesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListMessagesResponse',
        supports_download=False,
    )

  class ProjectsLocationsConversationsParticipantsSuggestionsService(base_api.BaseApiService):
    """Service class for the projects_locations_conversations_participants_suggestions resource."""

    _NAME = 'projects_locations_conversations_participants_suggestions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsConversationsParticipantsSuggestionsService, self).__init__(client)
      self._upload_configs = {
          }

    def SuggestArticles(self, request, global_params=None):
      r"""Gets suggested articles for a participant based on specific historical messages.

      Args:
        request: (DialogflowProjectsLocationsConversationsParticipantsSuggestionsSuggestArticlesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SuggestArticlesResponse) The response message.
      """
      config = self.GetMethodConfig('SuggestArticles')
      return self._RunMethod(
          config, request, global_params=global_params)

    SuggestArticles.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/participants/{participantsId}/suggestions:suggestArticles',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversations.participants.suggestions.suggestArticles',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/suggestions:suggestArticles',
        request_field='googleCloudDialogflowV2SuggestArticlesRequest',
        request_type_name='DialogflowProjectsLocationsConversationsParticipantsSuggestionsSuggestArticlesRequest',
        response_type_name='GoogleCloudDialogflowV2SuggestArticlesResponse',
        supports_download=False,
    )

    def SuggestFaqAnswers(self, request, global_params=None):
      r"""Gets suggested faq answers for a participant based on specific historical messages.

      Args:
        request: (DialogflowProjectsLocationsConversationsParticipantsSuggestionsSuggestFaqAnswersRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SuggestFaqAnswersResponse) The response message.
      """
      config = self.GetMethodConfig('SuggestFaqAnswers')
      return self._RunMethod(
          config, request, global_params=global_params)

    SuggestFaqAnswers.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/participants/{participantsId}/suggestions:suggestFaqAnswers',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversations.participants.suggestions.suggestFaqAnswers',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/suggestions:suggestFaqAnswers',
        request_field='googleCloudDialogflowV2SuggestFaqAnswersRequest',
        request_type_name='DialogflowProjectsLocationsConversationsParticipantsSuggestionsSuggestFaqAnswersRequest',
        response_type_name='GoogleCloudDialogflowV2SuggestFaqAnswersResponse',
        supports_download=False,
    )

    def SuggestSmartReplies(self, request, global_params=None):
      r"""Gets smart replies for a participant based on specific historical messages.

      Args:
        request: (DialogflowProjectsLocationsConversationsParticipantsSuggestionsSuggestSmartRepliesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SuggestSmartRepliesResponse) The response message.
      """
      config = self.GetMethodConfig('SuggestSmartReplies')
      return self._RunMethod(
          config, request, global_params=global_params)

    SuggestSmartReplies.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/participants/{participantsId}/suggestions:suggestSmartReplies',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversations.participants.suggestions.suggestSmartReplies',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/suggestions:suggestSmartReplies',
        request_field='googleCloudDialogflowV2SuggestSmartRepliesRequest',
        request_type_name='DialogflowProjectsLocationsConversationsParticipantsSuggestionsSuggestSmartRepliesRequest',
        response_type_name='GoogleCloudDialogflowV2SuggestSmartRepliesResponse',
        supports_download=False,
    )

  class ProjectsLocationsConversationsParticipantsService(base_api.BaseApiService):
    """Service class for the projects_locations_conversations_participants resource."""

    _NAME = 'projects_locations_conversations_participants'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsConversationsParticipantsService, self).__init__(client)
      self._upload_configs = {
          }

    def AnalyzeContent(self, request, global_params=None):
      r"""Adds a text (chat, for example), or audio (phone recording, for example) message from a participant into the conversation. Note: Always use agent versions for production traffic sent to virtual agents. See [Versions and environments](https://cloud.google.com/dialogflow/es/docs/agents-versions).

      Args:
        request: (DialogflowProjectsLocationsConversationsParticipantsAnalyzeContentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2AnalyzeContentResponse) The response message.
      """
      config = self.GetMethodConfig('AnalyzeContent')
      return self._RunMethod(
          config, request, global_params=global_params)

    AnalyzeContent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/participants/{participantsId}:analyzeContent',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversations.participants.analyzeContent',
        ordered_params=['participant'],
        path_params=['participant'],
        query_params=[],
        relative_path='v2/{+participant}:analyzeContent',
        request_field='googleCloudDialogflowV2AnalyzeContentRequest',
        request_type_name='DialogflowProjectsLocationsConversationsParticipantsAnalyzeContentRequest',
        response_type_name='GoogleCloudDialogflowV2AnalyzeContentResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new participant in a conversation.

      Args:
        request: (DialogflowProjectsLocationsConversationsParticipantsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Participant) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/participants',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversations.participants.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/participants',
        request_field='googleCloudDialogflowV2Participant',
        request_type_name='DialogflowProjectsLocationsConversationsParticipantsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Participant',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a conversation participant.

      Args:
        request: (DialogflowProjectsLocationsConversationsParticipantsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Participant) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/participants/{participantsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversations.participants.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationsParticipantsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Participant',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all participants in the specified conversation.

      Args:
        request: (DialogflowProjectsLocationsConversationsParticipantsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListParticipantsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/participants',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversations.participants.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2/{+parent}/participants',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationsParticipantsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListParticipantsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified participant.

      Args:
        request: (DialogflowProjectsLocationsConversationsParticipantsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Participant) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/participants/{participantsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.conversations.participants.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Participant',
        request_type_name='DialogflowProjectsLocationsConversationsParticipantsPatchRequest',
        response_type_name='GoogleCloudDialogflowV2Participant',
        supports_download=False,
    )

  class ProjectsLocationsConversationsSuggestionsService(base_api.BaseApiService):
    """Service class for the projects_locations_conversations_suggestions resource."""

    _NAME = 'projects_locations_conversations_suggestions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsConversationsSuggestionsService, self).__init__(client)
      self._upload_configs = {
          }

    def SuggestConversationSummary(self, request, global_params=None):
      r"""Suggests summary for a conversation based on specific historical messages. The range of the messages to be used for summary can be specified in the request.

      Args:
        request: (DialogflowProjectsLocationsConversationsSuggestionsSuggestConversationSummaryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2SuggestConversationSummaryResponse) The response message.
      """
      config = self.GetMethodConfig('SuggestConversationSummary')
      return self._RunMethod(
          config, request, global_params=global_params)

    SuggestConversationSummary.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/suggestions:suggestConversationSummary',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversations.suggestions.suggestConversationSummary',
        ordered_params=['conversation'],
        path_params=['conversation'],
        query_params=[],
        relative_path='v2/{+conversation}/suggestions:suggestConversationSummary',
        request_field='googleCloudDialogflowV2SuggestConversationSummaryRequest',
        request_type_name='DialogflowProjectsLocationsConversationsSuggestionsSuggestConversationSummaryRequest',
        response_type_name='GoogleCloudDialogflowV2SuggestConversationSummaryResponse',
        supports_download=False,
    )

  class ProjectsLocationsConversationsService(base_api.BaseApiService):
    """Service class for the projects_locations_conversations resource."""

    _NAME = 'projects_locations_conversations'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsConversationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Complete(self, request, global_params=None):
      r"""Completes the specified conversation. Finished conversations are purged from the database after 30 days.

      Args:
        request: (DialogflowProjectsLocationsConversationsCompleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Conversation) The response message.
      """
      config = self.GetMethodConfig('Complete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Complete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}:complete',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversations.complete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:complete',
        request_field='googleCloudDialogflowV2CompleteConversationRequest',
        request_type_name='DialogflowProjectsLocationsConversationsCompleteRequest',
        response_type_name='GoogleCloudDialogflowV2Conversation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new conversation. Conversations are auto-completed after 24 hours. Conversation Lifecycle: There are two stages during a conversation: Automated Agent Stage and Assist Stage. For Automated Agent Stage, there will be a dialogflow agent responding to user queries. For Assist Stage, there's no dialogflow agent responding to user queries. But we will provide suggestions which are generated from conversation. If Conversation.conversation_profile is configured for a dialogflow agent, conversation will start from `Automated Agent Stage`, otherwise, it will start from `Assist Stage`. And during `Automated Agent Stage`, once an Intent with Intent.live_agent_handoff is triggered, conversation will transfer to Assist Stage.

      Args:
        request: (DialogflowProjectsLocationsConversationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Conversation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations',
        http_method='POST',
        method_id='dialogflow.projects.locations.conversations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['conversationId'],
        relative_path='v2/{+parent}/conversations',
        request_field='googleCloudDialogflowV2Conversation',
        request_type_name='DialogflowProjectsLocationsConversationsCreateRequest',
        response_type_name='GoogleCloudDialogflowV2Conversation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specific conversation.

      Args:
        request: (DialogflowProjectsLocationsConversationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Conversation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Conversation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all conversations in the specified project.

      Args:
        request: (DialogflowProjectsLocationsConversationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListConversationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/conversations',
        http_method='GET',
        method_id='dialogflow.projects.locations.conversations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/conversations',
        request_field='',
        request_type_name='DialogflowProjectsLocationsConversationsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListConversationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsKnowledgeBasesDocumentsService(base_api.BaseApiService):
    """Service class for the projects_locations_knowledgeBases_documents resource."""

    _NAME = 'projects_locations_knowledgeBases_documents'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsKnowledgeBasesDocumentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new document. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesDocumentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}/documents',
        http_method='POST',
        method_id='dialogflow.projects.locations.knowledgeBases.documents.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/documents',
        request_field='googleCloudDialogflowV2Document',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesDocumentsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified document. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: An [Empty message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#empty).

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesDocumentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.knowledgeBases.documents.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesDocumentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports a smart messaging candidate document into the specified destination. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesDocumentsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}:export',
        http_method='POST',
        method_id='dialogflow.projects.locations.knowledgeBases.documents.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:export',
        request_field='googleCloudDialogflowV2ExportDocumentRequest',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesDocumentsExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified document.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesDocumentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Document) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.knowledgeBases.documents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesDocumentsGetRequest',
        response_type_name='GoogleCloudDialogflowV2Document',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Creates documents by importing data from external sources. Dialogflow supports up to 350 documents in each request. If you try to import more, Dialogflow will return an error. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: ImportDocumentsResponse.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesDocumentsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}/documents:import',
        http_method='POST',
        method_id='dialogflow.projects.locations.knowledgeBases.documents.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/documents:import',
        request_field='googleCloudDialogflowV2ImportDocumentsRequest',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesDocumentsImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all documents of the knowledge base.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesDocumentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListDocumentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}/documents',
        http_method='GET',
        method_id='dialogflow.projects.locations.knowledgeBases.documents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/documents',
        request_field='',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesDocumentsListRequest',
        response_type_name='GoogleCloudDialogflowV2ListDocumentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified document. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesDocumentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.knowledgeBases.documents.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2Document',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesDocumentsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Reload(self, request, global_params=None):
      r"""Reloads the specified document from its specified source, content_uri or content. The previously loaded content of the document will be deleted. Note: Even when the content of the document has not changed, there still may be side effects because of internal implementation changes. This method is a [long-running operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation). The returned `Operation` type has the following method-specific fields: - `metadata`: KnowledgeOperationMetadata - `response`: Document Note: The `projects.agent.knowledgeBases.documents` resource is deprecated; only use `projects.knowledgeBases.documents`.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesDocumentsReloadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Reload')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}/documents/{documentsId}:reload',
        http_method='POST',
        method_id='dialogflow.projects.locations.knowledgeBases.documents.reload',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:reload',
        request_field='googleCloudDialogflowV2ReloadDocumentRequest',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesDocumentsReloadRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsKnowledgeBasesService(base_api.BaseApiService):
    """Service class for the projects_locations_knowledgeBases resource."""

    _NAME = 'projects_locations_knowledgeBases'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsKnowledgeBasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a knowledge base.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2KnowledgeBase) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases',
        http_method='POST',
        method_id='dialogflow.projects.locations.knowledgeBases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/knowledgeBases',
        request_field='googleCloudDialogflowV2KnowledgeBase',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesCreateRequest',
        response_type_name='GoogleCloudDialogflowV2KnowledgeBase',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified knowledge base.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.knowledgeBases.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified knowledge base.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2KnowledgeBase) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.knowledgeBases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesGetRequest',
        response_type_name='GoogleCloudDialogflowV2KnowledgeBase',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of all knowledge bases of the specified agent.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2ListKnowledgeBasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases',
        http_method='GET',
        method_id='dialogflow.projects.locations.knowledgeBases.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/knowledgeBases',
        request_field='',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesListRequest',
        response_type_name='GoogleCloudDialogflowV2ListKnowledgeBasesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified knowledge base.

      Args:
        request: (DialogflowProjectsLocationsKnowledgeBasesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2KnowledgeBase) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/knowledgeBases/{knowledgeBasesId}',
        http_method='PATCH',
        method_id='dialogflow.projects.locations.knowledgeBases.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v2/{+name}',
        request_field='googleCloudDialogflowV2KnowledgeBase',
        request_type_name='DialogflowProjectsLocationsKnowledgeBasesPatchRequest',
        response_type_name='GoogleCloudDialogflowV2KnowledgeBase',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (DialogflowProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='dialogflow.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='',
        request_type_name='DialogflowProjectsLocationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (DialogflowProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (DialogflowProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='dialogflow.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='DialogflowProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSuggestionsService(base_api.BaseApiService):
    """Service class for the projects_locations_suggestions resource."""

    _NAME = 'projects_locations_suggestions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsSuggestionsService, self).__init__(client)
      self._upload_configs = {
          }

    def GenerateStatelessSummary(self, request, global_params=None):
      r"""Generates and returns a summary for a conversation that does not have a resource created for it.

      Args:
        request: (DialogflowProjectsLocationsSuggestionsGenerateStatelessSummaryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2GenerateStatelessSummaryResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateStatelessSummary')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateStatelessSummary.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/suggestions:generateStatelessSummary',
        http_method='POST',
        method_id='dialogflow.projects.locations.suggestions.generateStatelessSummary',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/suggestions:generateStatelessSummary',
        request_field='googleCloudDialogflowV2GenerateStatelessSummaryRequest',
        request_type_name='DialogflowProjectsLocationsSuggestionsGenerateStatelessSummaryRequest',
        response_type_name='GoogleCloudDialogflowV2GenerateStatelessSummaryResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(DialogflowV2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def DeleteAgent(self, request, global_params=None):
      r"""Deletes the specified agent.

      Args:
        request: (DialogflowProjectsLocationsDeleteAgentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('DeleteAgent')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteAgent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent',
        http_method='DELETE',
        method_id='dialogflow.projects.locations.deleteAgent',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent',
        request_field='',
        request_type_name='DialogflowProjectsLocationsDeleteAgentRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (DialogflowProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='dialogflow.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsLocationsGetRequest',
        response_type_name='GoogleCloudLocationLocation',
        supports_download=False,
    )

    def GetAgent(self, request, global_params=None):
      r"""Retrieves the specified agent.

      Args:
        request: (DialogflowProjectsLocationsGetAgentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Agent) The response message.
      """
      config = self.GetMethodConfig('GetAgent')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetAgent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent',
        http_method='GET',
        method_id='dialogflow.projects.locations.getAgent',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent',
        request_field='',
        request_type_name='DialogflowProjectsLocationsGetAgentRequest',
        response_type_name='GoogleCloudDialogflowV2Agent',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (DialogflowProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations',
        http_method='GET',
        method_id='dialogflow.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/locations',
        request_field='',
        request_type_name='DialogflowProjectsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

    def SetAgent(self, request, global_params=None):
      r"""Creates/updates the specified agent. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsLocationsSetAgentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Agent) The response message.
      """
      config = self.GetMethodConfig('SetAgent')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetAgent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/agent',
        http_method='POST',
        method_id='dialogflow.projects.locations.setAgent',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['updateMask'],
        relative_path='v2/{+parent}/agent',
        request_field='googleCloudDialogflowV2Agent',
        request_type_name='DialogflowProjectsLocationsSetAgentRequest',
        response_type_name='GoogleCloudDialogflowV2Agent',
        supports_download=False,
    )

  class ProjectsOperationsService(base_api.BaseApiService):
    """Service class for the projects_operations resource."""

    _NAME = 'projects_operations'

    def __init__(self, client):
      super(DialogflowV2.ProjectsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (DialogflowProjectsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='dialogflow.projects.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='',
        request_type_name='DialogflowProjectsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (DialogflowProjectsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/operations/{operationsId}',
        http_method='GET',
        method_id='dialogflow.projects.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DialogflowProjectsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (DialogflowProjectsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/operations',
        http_method='GET',
        method_id='dialogflow.projects.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='DialogflowProjectsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsSuggestionsService(base_api.BaseApiService):
    """Service class for the projects_suggestions resource."""

    _NAME = 'projects_suggestions'

    def __init__(self, client):
      super(DialogflowV2.ProjectsSuggestionsService, self).__init__(client)
      self._upload_configs = {
          }

    def GenerateStatelessSummary(self, request, global_params=None):
      r"""Generates and returns a summary for a conversation that does not have a resource created for it.

      Args:
        request: (DialogflowProjectsSuggestionsGenerateStatelessSummaryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2GenerateStatelessSummaryResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateStatelessSummary')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateStatelessSummary.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/suggestions:generateStatelessSummary',
        http_method='POST',
        method_id='dialogflow.projects.suggestions.generateStatelessSummary',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/suggestions:generateStatelessSummary',
        request_field='googleCloudDialogflowV2GenerateStatelessSummaryRequest',
        request_type_name='DialogflowProjectsSuggestionsGenerateStatelessSummaryRequest',
        response_type_name='GoogleCloudDialogflowV2GenerateStatelessSummaryResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(DialogflowV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def DeleteAgent(self, request, global_params=None):
      r"""Deletes the specified agent.

      Args:
        request: (DialogflowProjectsDeleteAgentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('DeleteAgent')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteAgent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent',
        http_method='DELETE',
        method_id='dialogflow.projects.deleteAgent',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent',
        request_field='',
        request_type_name='DialogflowProjectsDeleteAgentRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def GetAgent(self, request, global_params=None):
      r"""Retrieves the specified agent.

      Args:
        request: (DialogflowProjectsGetAgentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Agent) The response message.
      """
      config = self.GetMethodConfig('GetAgent')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetAgent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent',
        http_method='GET',
        method_id='dialogflow.projects.getAgent',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/agent',
        request_field='',
        request_type_name='DialogflowProjectsGetAgentRequest',
        response_type_name='GoogleCloudDialogflowV2Agent',
        supports_download=False,
    )

    def SetAgent(self, request, global_params=None):
      r"""Creates/updates the specified agent. Note: You should always train an agent prior to sending it queries. See the [training documentation](https://cloud.google.com/dialogflow/es/docs/training).

      Args:
        request: (DialogflowProjectsSetAgentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudDialogflowV2Agent) The response message.
      """
      config = self.GetMethodConfig('SetAgent')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetAgent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/agent',
        http_method='POST',
        method_id='dialogflow.projects.setAgent',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['updateMask'],
        relative_path='v2/{+parent}/agent',
        request_field='googleCloudDialogflowV2Agent',
        request_type_name='DialogflowProjectsSetAgentRequest',
        response_type_name='GoogleCloudDialogflowV2Agent',
        supports_download=False,
    )

"""Generated message classes for alloydb version v1alpha.

AlloyDB for PostgreSQL is an open source-compatible database service that
provides a powerful option for migrating, modernizing, or building commercial-
grade applications. It offers full compatibility with standard PostgreSQL, and
is more than 4x faster for transactional workloads and up to 100x faster for
analytical queries than standard PostgreSQL in our performance tests. AlloyDB
for PostgreSQL offers a 99.99 percent availability SLA inclusive of
maintenance. AlloyDB is optimized for the most demanding use cases, allowing
you to build new applications that require high transaction throughput, large
database sizes, or multiple read resources; scale existing PostgreSQL
workloads with no application changes; and modernize legacy proprietary
databases.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'alloydb'


class AlloydbProjectsLocationsBackupsCreateRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsCreateRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    backupId: Required. ID of the requesting object.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  backup = _messages.MessageField('Backup', 1)
  backupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsBackupsDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsDeleteRequest object.

  Fields:
    etag: Optional. The current etag of the Backup. If an etag is provided and
      does not match the current etag of the Backup, deletion will be blocked
      and an ABORTED error will be returned.
    name: Required. Name of the resource. For the required format, see the
      comment on the Backup.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class AlloydbProjectsLocationsBackupsGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsBackupsListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListBackupsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AlloydbProjectsLocationsBackupsPatchRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsBackupsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, update succeeds even if instance
      is not found. In that case, a new backup is created and `update_mask` is
      ignored.
    backup: A Backup resource to be passed as the request body.
    name: Output only. The name of the backup resource with the format: *
      projects/{project}/locations/{region}/backups/{backup_id} where the
      cluster and backup ID segments should satisfy the regex expression
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of lowercase
      letters, numbers, and dashes, starting with a letter, and ending with a
      letter or number. For more details see https://google.aip.dev/122. The
      prefix of the backup resource name is the name of the parent resource: *
      projects/{project}/locations/{region}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Backup resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  allowMissing = _messages.BooleanField(1)
  backup = _messages.MessageField('Backup', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class AlloydbProjectsLocationsClustersCreateRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersCreateRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. ID of the requesting object.
    parent: Required. The location of the new cluster. For the required
      format, see the comment on the Cluster.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the create request.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersCreatesecondaryRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersCreatesecondaryRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. ID of the requesting object (the secondary cluster).
    parent: Required. The location of the new cluster. For the required
      format, see the comment on the Cluster.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the create request.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersDeleteRequest object.

  Fields:
    etag: Optional. The current etag of the Cluster. If an etag is provided
      and does not match the current etag of the Cluster, deletion will be
      blocked and an ABORTED error will be returned.
    force: Optional. Whether to cascade delete child instances for given
      cluster.
    name: Required. The name of the resource. For the required format, see the
      comment on the Cluster.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the delete.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersGenerateClientCertificateRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersGenerateClientCertificateRequest
  object.

  Fields:
    generateClientCertificateRequest: A GenerateClientCertificateRequest
      resource to be passed as the request body.
    parent: Required. The name of the parent resource. The required format is:
      * projects/{project}/locations/{location}/clusters/{cluster}
  """

  generateClientCertificateRequest = _messages.MessageField('GenerateClientCertificateRequest', 1)
  parent = _messages.StringField(2, required=True)


class AlloydbProjectsLocationsClustersGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. The view of the cluster to return. Returns
      all default fields if not set.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the Cluster.name field.
    view: Optional. The view of the cluster to return. Returns all default
      fields if not set.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. The view of the cluster to return. Returns all default
    fields if not set.

    Values:
      CLUSTER_VIEW_UNSPECIFIED: CLUSTER_VIEW_UNSPECIFIED Not specified,
        equivalent to BASIC.
      CLUSTER_VIEW_BASIC: BASIC server responses include all the relevant
        cluster details, excluding
        Cluster.ContinuousBackupInfo.EarliestRestorableTime and other view-
        specific fields. The default value.
      CLUSTER_VIEW_CONTINUOUS_BACKUP: CONTINUOUS_BACKUP response returns all
        the fields from BASIC plus the earliest restorable time if continuous
        backups are enabled. May increase latency.
    """
    CLUSTER_VIEW_UNSPECIFIED = 0
    CLUSTER_VIEW_BASIC = 1
    CLUSTER_VIEW_CONTINUOUS_BACKUP = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class AlloydbProjectsLocationsClustersInstancesCreateRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. ID of the requesting object.
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Instance.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the create request.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersInstancesCreatesecondaryRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesCreatesecondaryRequest
  object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. ID of the requesting object.
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Instance.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the create request.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersInstancesDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesDeleteRequest object.

  Fields:
    etag: Optional. The current etag of the Instance. If an etag is provided
      and does not match the current etag of the Instance, deletion will be
      blocked and an ABORTED error will be returned.
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the delete.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class AlloydbProjectsLocationsClustersInstancesFailoverRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesFailoverRequest object.

  Fields:
    failoverInstanceRequest: A FailoverInstanceRequest resource to be passed
      as the request body.
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
  """

  failoverInstanceRequest = _messages.MessageField('FailoverInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class AlloydbProjectsLocationsClustersInstancesGetConnectionInfoRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesGetConnectionInfoRequest
  object.

  Fields:
    parent: Required. The name of the parent resource. The required format is:
      projects/{project}/locations/{location}/clusters/{cluster}/instances/{in
      stance}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class AlloydbProjectsLocationsClustersInstancesGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesGetRequest object.

  Enums:
    ViewValueValuesEnum: The view of the instance to return.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
    view: The view of the instance to return.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The view of the instance to return.

    Values:
      INSTANCE_VIEW_UNSPECIFIED: INSTANCE_VIEW_UNSPECIFIED Not specified,
        equivalent to BASIC.
      INSTANCE_VIEW_BASIC: BASIC server responses for a primary or read
        instance include all the relevant instance details, excluding the
        details of each node in the instance. The default value.
      INSTANCE_VIEW_FULL: FULL response is equivalent to BASIC for primary
        instance (for now). For read pool instance, this includes details of
        each node in the pool.
    """
    INSTANCE_VIEW_UNSPECIFIED = 0
    INSTANCE_VIEW_BASIC = 1
    INSTANCE_VIEW_FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class AlloydbProjectsLocationsClustersInstancesInjectFaultRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesInjectFaultRequest object.

  Fields:
    injectFaultRequest: A InjectFaultRequest resource to be passed as the
      request body.
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
  """

  injectFaultRequest = _messages.MessageField('InjectFaultRequest', 1)
  name = _messages.StringField(2, required=True)


class AlloydbProjectsLocationsClustersInstancesListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Instance.name field. Additionally, you
      can perform an aggregated list operation by specifying a value with one
      of the following formats: * projects/{project}/locations/-/clusters/- *
      projects/{project}/locations/{region}/clusters/-
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AlloydbProjectsLocationsClustersInstancesPatchRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, update succeeds even if instance
      is not found. In that case, a new instance is created and `update_mask`
      is ignored.
    instance: A Instance resource to be passed as the request body.
    name: Output only. The name of the instance resource with the format: * pr
      ojects/{project}/locations/{region}/clusters/{cluster_id}/instances/{ins
      tance_id} where the cluster and instance ID segments should satisfy the
      regex expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters
      of lowercase letters, numbers, and dashes, starting with a letter, and
      ending with a letter or number. For more details see
      https://google.aip.dev/122. The prefix of the instance resource name is
      the name of the parent resource: *
      projects/{project}/locations/{region}/clusters/{cluster_id}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Instance resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the update request.
  """

  allowMissing = _messages.BooleanField(1)
  instance = _messages.MessageField('Instance', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class AlloydbProjectsLocationsClustersInstancesRestartRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersInstancesRestartRequest object.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the Instance.name field.
    restartInstanceRequest: A RestartInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  restartInstanceRequest = _messages.MessageField('RestartInstanceRequest', 2)


class AlloydbProjectsLocationsClustersListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Cluster.name field. Additionally, you can
      perform an aggregated list operation by specifying a value with the
      following format: * projects/{project}/locations/-
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AlloydbProjectsLocationsClustersPatchRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, update succeeds even if cluster is
      not found. In that case, a new cluster is created and `update_mask` is
      ignored.
    cluster: A Cluster resource to be passed as the request body.
    name: Output only. The name of the cluster resource with the format: *
      projects/{project}/locations/{region}/clusters/{cluster_id} where the
      cluster ID segment should satisfy the regex expression `[a-z0-9-]+`. For
      more details see https://google.aip.dev/122. The prefix of the cluster
      resource name is the name of the parent resource: *
      projects/{project}/locations/{region}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Cluster resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the update request.
  """

  allowMissing = _messages.BooleanField(1)
  cluster = _messages.MessageField('Cluster', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class AlloydbProjectsLocationsClustersPromoteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersPromoteRequest object.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the Cluster.name field
    promoteClusterRequest: A PromoteClusterRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  promoteClusterRequest = _messages.MessageField('PromoteClusterRequest', 2)


class AlloydbProjectsLocationsClustersRestoreRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersRestoreRequest object.

  Fields:
    parent: Required. The name of the parent resource. For the required
      format, see the comment on the Cluster.name field.
    restoreClusterRequest: A RestoreClusterRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  restoreClusterRequest = _messages.MessageField('RestoreClusterRequest', 2)


class AlloydbProjectsLocationsClustersUsersCreateRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    user: A User resource to be passed as the request body.
    userId: Required. ID of the requesting object.
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  user = _messages.MessageField('User', 3)
  userId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class AlloydbProjectsLocationsClustersUsersDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersDeleteRequest object.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the User.name field.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class AlloydbProjectsLocationsClustersUsersGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersGetRequest object.

  Fields:
    name: Required. The name of the resource. For the required format, see the
      comment on the User.name field.
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsClustersUsersListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListUsersRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class AlloydbProjectsLocationsClustersUsersPatchRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsClustersUsersPatchRequest object.

  Fields:
    allowMissing: Optional. Allow missing fields in the update mask.
    name: Output only. Name of the resource in the form of
      projects/{project}/locations/{location}/cluster/{cluster}/users/{user}.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the User resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    user: A User resource to be passed as the request body.
    validateOnly: Optional. If set, the backend validates the request, but
      doesn't actually execute it.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)
  user = _messages.MessageField('User', 5)
  validateOnly = _messages.BooleanField(6)


class AlloydbProjectsLocationsGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class AlloydbProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class AlloydbProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class AlloydbProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class AlloydbProjectsLocationsSupportedDatabaseFlagsListRequest(_messages.Message):
  r"""A AlloydbProjectsLocationsSupportedDatabaseFlagsListRequest object.

  Fields:
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. The name of the parent resource. The required format is:
      * projects/{project}/locations/{location} Regardless of the parent
      specified here, as long it is contains a valid project and location, the
      service will return a static list of supported flags resources. Note
      that we do not yet support region-specific flags.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class AutomatedBackupPolicy(_messages.Message):
  r"""Message describing the user-specified automated backup policy. All
  fields in the automated backup policy are optional. Defaults for each field
  are provided if they are not set.

  Messages:
    LabelsValue: Labels to apply to backups created using this configuration.

  Fields:
    backupWindow: The length of the time window during which a backup can be
      taken. If a backup does not succeed within this time window, it will be
      canceled and considered failed. The backup window must be at least 5
      minutes long. There is no upper bound on the window. If not set, it
      defaults to 1 hour.
    enabled: Whether automated automated backups are enabled. If not set,
      defaults to true.
    encryptionConfig: Optional. The encryption config can be specified to
      encrypt the backups with a customer-managed encryption key (CMEK). When
      this field is not specified, the backup will then use default encryption
      scheme to protect the user data.
    labels: Labels to apply to backups created using this configuration.
    location: The location where the backup will be stored. Currently, the
      only supported option is to store the backup in the same region as the
      cluster. If empty, defaults to the region of the cluster.
    quantityBasedRetention: Quantity-based Backup retention policy to retain
      recent backups.
    timeBasedRetention: Time-based Backup retention policy.
    weeklySchedule: Weekly schedule for the Backup.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels to apply to backups created using this configuration.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupWindow = _messages.StringField(1)
  enabled = _messages.BooleanField(2)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  location = _messages.StringField(5)
  quantityBasedRetention = _messages.MessageField('QuantityBasedRetention', 6)
  timeBasedRetention = _messages.MessageField('TimeBasedRetention', 7)
  weeklySchedule = _messages.MessageField('WeeklySchedule', 8)


class Backup(_messages.Message):
  r"""Message describing Backup object

  Enums:
    DatabaseVersionValueValuesEnum: Output only. The database engine major
      version of the cluster this backup was created from. Any restored
      cluster created from this backup will have the same database version.
    StateValueValuesEnum: Output only. The current state of the backup.
    TypeValueValuesEnum: The backup type, which suggests the trigger for the
      backup.

  Messages:
    AnnotationsValue: Annotations to allow client tools to store small amount
      of arbitrary data. This is distinct from labels.
      https://google.aip.dev/128
    LabelsValue: Labels as key value pairs

  Fields:
    annotations: Annotations to allow client tools to store small amount of
      arbitrary data. This is distinct from labels. https://google.aip.dev/128
    clusterName: Required. The full resource name of the backup source cluster
      (e.g., projects/{project}/locations/{region}/clusters/{cluster_id}).
    clusterUid: Output only. The system-generated UID of the cluster which was
      used to create this resource.
    createTime: Output only. Create time stamp
    databaseVersion: Output only. The database engine major version of the
      cluster this backup was created from. Any restored cluster created from
      this backup will have the same database version.
    deleteTime: Output only. Delete time stamp
    description: User-provided description of the backup.
    displayName: User-settable and human-readable display name for the Backup.
    encryptionConfig: Optional. The encryption config can be specified to
      encrypt the backup with a customer-managed encryption key (CMEK). When
      this field is not specified, the backup will then use default encryption
      scheme to protect the user data.
    encryptionInfo: Output only. The encryption information for the backup.
    etag: For Resource freshness validation (https://google.aip.dev/154)
    expiryQuantity: Output only. The QuantityBasedExpiry of the backup,
      specified by the backup's retention policy. Once the expiry quantity is
      over retention, the backup is eligible to be garbage collected.
    expiryTime: Output only. The time at which after the backup is eligible to
      be garbage collected. It is the duration specified by the backup's
      retention policy, added to the backup's create_time.
    labels: Labels as key value pairs
    name: Output only. The name of the backup resource with the format: *
      projects/{project}/locations/{region}/backups/{backup_id} where the
      cluster and backup ID segments should satisfy the regex expression
      `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of lowercase
      letters, numbers, and dashes, starting with a letter, and ending with a
      letter or number. For more details see https://google.aip.dev/122. The
      prefix of the backup resource name is the name of the parent resource: *
      projects/{project}/locations/{region}
    reconciling: Output only. Reconciling
      (https://google.aip.dev/128#reconciliation), if true, indicates that the
      service is actively updating the resource. This can happen due to user-
      triggered updates or system actions like failover or maintenance.
    satisfiesPzs: Reserved for future use.
    sizeBytes: Output only. The size of the backup in bytes.
    state: Output only. The current state of the backup.
    type: The backup type, which suggests the trigger for the backup.
    uid: Output only. The system-generated UID of the resource. The UID is
      assigned when the resource is created, and it is retained until it is
      deleted.
    updateTime: Output only. Update time stamp
  """

  class DatabaseVersionValueValuesEnum(_messages.Enum):
    r"""Output only. The database engine major version of the cluster this
    backup was created from. Any restored cluster created from this backup
    will have the same database version.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the backup.

    Values:
      STATE_UNSPECIFIED: The state of the backup is unknown.
      READY: The backup is ready.
      CREATING: The backup is creating.
      FAILED: The backup failed.
      DELETING: The backup is being deleted.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    CREATING = 2
    FAILED = 3
    DELETING = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""The backup type, which suggests the trigger for the backup.

    Values:
      TYPE_UNSPECIFIED: Backup Type is unknown.
      ON_DEMAND: ON_DEMAND backups that were triggered by the customer (e.g.,
        not AUTOMATED).
      AUTOMATED: AUTOMATED backups triggered by the automated backups
        scheduler pursuant to an automated backup policy.
      CONTINUOUS: CONTINUOUS backups triggered by the automated backups
        scheduler due to a continuous backup policy.
    """
    TYPE_UNSPECIFIED = 0
    ON_DEMAND = 1
    AUTOMATED = 2
    CONTINUOUS = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations to allow client tools to store small amount of arbitrary
    data. This is distinct from labels. https://google.aip.dev/128

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  clusterName = _messages.StringField(2)
  clusterUid = _messages.StringField(3)
  createTime = _messages.StringField(4)
  databaseVersion = _messages.EnumField('DatabaseVersionValueValuesEnum', 5)
  deleteTime = _messages.StringField(6)
  description = _messages.StringField(7)
  displayName = _messages.StringField(8)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 9)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 10)
  etag = _messages.StringField(11)
  expiryQuantity = _messages.MessageField('QuantityBasedExpiry', 12)
  expiryTime = _messages.StringField(13)
  labels = _messages.MessageField('LabelsValue', 14)
  name = _messages.StringField(15)
  reconciling = _messages.BooleanField(16)
  satisfiesPzs = _messages.BooleanField(17)
  sizeBytes = _messages.IntegerField(18)
  state = _messages.EnumField('StateValueValuesEnum', 19)
  type = _messages.EnumField('TypeValueValuesEnum', 20)
  uid = _messages.StringField(21)
  updateTime = _messages.StringField(22)


class BackupSource(_messages.Message):
  r"""Message describing a BackupSource.

  Fields:
    backupName: Required. The name of the backup resource with the format: *
      projects/{project}/locations/{region}/backups/{backup_id}
    backupUid: Output only. The system-generated UID of the backup which was
      used to create this resource. The UID is generated when the backup is
      created, and it is retained until the backup is deleted.
  """

  backupName = _messages.StringField(1)
  backupUid = _messages.StringField(2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ClientConnectionConfig(_messages.Message):
  r"""Client connection configuration

  Fields:
    requireConnectors: Optional. Configuration to enforce connectors only (ex:
      AuthProxy) connections to the database.
    sslConfig: Optional. SSL config option for this instance.
  """

  requireConnectors = _messages.BooleanField(1)
  sslConfig = _messages.MessageField('SslConfig', 2)


class CloudControl2SharedOperationsReconciliationOperationMetadata(_messages.Message):
  r"""Operation metadata returned by the CLH during resource state
  reconciliation.

  Enums:
    ExclusiveActionValueValuesEnum: Excluisive action returned by the CLH.

  Fields:
    deleteResource: DEPRECATED. Use exclusive_action instead.
    exclusiveAction: Excluisive action returned by the CLH.
  """

  class ExclusiveActionValueValuesEnum(_messages.Enum):
    r"""Excluisive action returned by the CLH.

    Values:
      UNKNOWN_REPAIR_ACTION: Unknown repair action.
      DELETE: The resource has to be deleted. When using this bit, the CLH
        should fail the operation. DEPRECATED. Instead use DELETE_RESOURCE
        OperationSignal in SideChannel.
      RETRY: This resource could not be repaired but the repair should be
        tried again at a later time. This can happen if there is a dependency
        that needs to be resolved first- e.g. if a parent resource must be
        repaired before a child resource.
    """
    UNKNOWN_REPAIR_ACTION = 0
    DELETE = 1
    RETRY = 2

  deleteResource = _messages.BooleanField(1)
  exclusiveAction = _messages.EnumField('ExclusiveActionValueValuesEnum', 2)


class Cluster(_messages.Message):
  r"""A cluster is a collection of regional AlloyDB resources. It can include
  a primary instance and one or more read pool instances. All cluster
  resources share a storage layer, which scales as needed.

  Enums:
    ClusterTypeValueValuesEnum: Output only. The type of the cluster. This is
      an output-only field and it's populated at the Cluster creation time or
      the Cluster promotion time. The cluster type is determined by which RPC
      was used to create the cluster (i.e. `CreateCluster` vs.
      `CreateSecondaryCluster`
    DatabaseVersionValueValuesEnum: Optional. The database engine major
      version. This is an optional field and it is populated at the Cluster
      creation time. If a database version is not supplied at cluster creation
      time, then a default database version will be used.
    StateValueValuesEnum: Output only. The current serving state of the
      cluster.

  Messages:
    AnnotationsValue: Annotations to allow client tools to store small amount
      of arbitrary data. This is distinct from labels.
      https://google.aip.dev/128
    LabelsValue: Labels as key value pairs

  Fields:
    annotations: Annotations to allow client tools to store small amount of
      arbitrary data. This is distinct from labels. https://google.aip.dev/128
    automatedBackupPolicy: The automated backup policy for this cluster. If no
      policy is provided then the default policy will be used. If backups are
      supported for the cluster, the default policy takes one backup a day,
      has a backup window of 1 hour, and retains backups for 14 days. For more
      information on the defaults, consult the documentation for the message
      type.
    backupSource: Output only. Cluster created from backup.
    clusterType: Output only. The type of the cluster. This is an output-only
      field and it's populated at the Cluster creation time or the Cluster
      promotion time. The cluster type is determined by which RPC was used to
      create the cluster (i.e. `CreateCluster` vs. `CreateSecondaryCluster`
    continuousBackupConfig: Optional. Continuous backup configuration for this
      cluster.
    continuousBackupInfo: Output only. Continuous backup properties for this
      cluster.
    createTime: Output only. Create time stamp
    databaseVersion: Optional. The database engine major version. This is an
      optional field and it is populated at the Cluster creation time. If a
      database version is not supplied at cluster creation time, then a
      default database version will be used.
    deleteTime: Output only. Delete time stamp
    displayName: User-settable and human-readable display name for the
      Cluster.
    encryptionConfig: Optional. The encryption config can be specified to
      encrypt the data disks and other persistent data resources of a cluster
      with a customer-managed encryption key (CMEK). When this field is not
      specified, the cluster will then use default encryption scheme to
      protect the user data.
    encryptionInfo: Output only. The encryption information for the cluster.
    etag: For Resource freshness validation (https://google.aip.dev/154)
    initialUser: Input only. Initial user to setup during cluster creation.
      Required. If used in `RestoreCluster` this is ignored.
    labels: Labels as key value pairs
    migrationSource: Output only. Cluster created via DMS migration.
    name: Output only. The name of the cluster resource with the format: *
      projects/{project}/locations/{region}/clusters/{cluster_id} where the
      cluster ID segment should satisfy the regex expression `[a-z0-9-]+`. For
      more details see https://google.aip.dev/122. The prefix of the cluster
      resource name is the name of the parent resource: *
      projects/{project}/locations/{region}
    network: Required. The resource link for the VPC network in which cluster
      resources are created and from which they are accessible via Private IP.
      The network must belong to the same project as the cluster. It is
      specified in the form:
      "projects/{project}/global/networks/{network_id}". This is required to
      create a cluster. It can be updated, but it cannot be removed.
    networkConfig: A NetworkConfig attribute.
    primaryConfig: Output only. Cross Region replication config specific to
      PRIMARY cluster.
    reconciling: Output only. Reconciling
      (https://google.aip.dev/128#reconciliation). Set to true if the current
      state of Cluster does not match the user's intended state, and the
      service is actively updating the resource to reconcile them. This can
      happen due to user-triggered updates or system actions like failover or
      maintenance.
    satisfiesPzs: Reserved for future use.
    secondaryConfig: Cross Region replication config specific to SECONDARY
      cluster.
    sslConfig: SSL configuration for this AlloyDB cluster.
    state: Output only. The current serving state of the cluster.
    uid: Output only. The system-generated UID of the resource. The UID is
      assigned when the resource is created, and it is retained until it is
      deleted.
    updateTime: Output only. Update time stamp
  """

  class ClusterTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the cluster. This is an output-only field and
    it's populated at the Cluster creation time or the Cluster promotion time.
    The cluster type is determined by which RPC was used to create the cluster
    (i.e. `CreateCluster` vs. `CreateSecondaryCluster`

    Values:
      CLUSTER_TYPE_UNSPECIFIED: The type of the cluster is unknown.
      PRIMARY: Primary cluster that support read and write operations.
      SECONDARY: Secondary cluster that is replicating from another region.
        This only supports read.
    """
    CLUSTER_TYPE_UNSPECIFIED = 0
    PRIMARY = 1
    SECONDARY = 2

  class DatabaseVersionValueValuesEnum(_messages.Enum):
    r"""Optional. The database engine major version. This is an optional field
    and it is populated at the Cluster creation time. If a database version is
    not supplied at cluster creation time, then a default database version
    will be used.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current serving state of the cluster.

    Values:
      STATE_UNSPECIFIED: The state of the cluster is unknown.
      READY: The cluster is active and running.
      STOPPED: The cluster is stopped. All instances in the cluster are
        stopped. Customers can start a stopped cluster at any point and all
        their instances will come back to life with same names and IP
        resources. In this state, customer pays for storage. Associated
        backups could also be present in a stopped cluster.
      EMPTY: The cluster is empty and has no associated resources. All
        instances, associated storage and backups have been deleted.
      CREATING: The cluster is being created.
      DELETING: The cluster is being deleted.
      FAILED: The creation of the cluster failed.
      BOOTSTRAPPING: The cluster is bootstrapping with data from some other
        source. Direct mutations to the cluster (e.g. adding read pool) are
        not allowed.
      MAINTENANCE: The cluster is under maintenance. AlloyDB regularly
        performs maintenance and upgrades on customer clusters. Updates on the
        cluster are not allowed while the cluster is in this state.
      PROMOTING: The cluster is being promoted.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    STOPPED = 2
    EMPTY = 3
    CREATING = 4
    DELETING = 5
    FAILED = 6
    BOOTSTRAPPING = 7
    MAINTENANCE = 8
    PROMOTING = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations to allow client tools to store small amount of arbitrary
    data. This is distinct from labels. https://google.aip.dev/128

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  automatedBackupPolicy = _messages.MessageField('AutomatedBackupPolicy', 2)
  backupSource = _messages.MessageField('BackupSource', 3)
  clusterType = _messages.EnumField('ClusterTypeValueValuesEnum', 4)
  continuousBackupConfig = _messages.MessageField('ContinuousBackupConfig', 5)
  continuousBackupInfo = _messages.MessageField('ContinuousBackupInfo', 6)
  createTime = _messages.StringField(7)
  databaseVersion = _messages.EnumField('DatabaseVersionValueValuesEnum', 8)
  deleteTime = _messages.StringField(9)
  displayName = _messages.StringField(10)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 11)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 12)
  etag = _messages.StringField(13)
  initialUser = _messages.MessageField('UserPassword', 14)
  labels = _messages.MessageField('LabelsValue', 15)
  migrationSource = _messages.MessageField('MigrationSource', 16)
  name = _messages.StringField(17)
  network = _messages.StringField(18)
  networkConfig = _messages.MessageField('NetworkConfig', 19)
  primaryConfig = _messages.MessageField('PrimaryConfig', 20)
  reconciling = _messages.BooleanField(21)
  satisfiesPzs = _messages.BooleanField(22)
  secondaryConfig = _messages.MessageField('SecondaryConfig', 23)
  sslConfig = _messages.MessageField('SslConfig', 24)
  state = _messages.EnumField('StateValueValuesEnum', 25)
  uid = _messages.StringField(26)
  updateTime = _messages.StringField(27)


class ConnectionInfo(_messages.Message):
  r"""ConnectionInfo singleton resource. https://google.aip.dev/156

  Fields:
    instanceUid: Output only. The unique ID of the Instance.
    ipAddress: Output only. The private network IP address for the Instance.
      This is the default IP for the instance and is always created (even if
      enable_public_ip is set). This is the connection endpoint for an end-
      user application.
    name: The name of the ConnectionInfo singleton resource, e.g.: projects/{p
      roject}/locations/{location}/clusters/*/instances/*/connectionInfo This
      field currently has no semantic meaning.
    pemCertificateChain: Output only. The pem-encoded chain that may be used
      to verify the X.509 certificate. Expected to be in issuer-to-root order
      according to RFC 5246.
    publicIpAddress: Output only. The public IP addresses for the Instance.
      This is available ONLY when enable_public_ip is set. This is the
      connection endpoint for an end-user application.
  """

  instanceUid = _messages.StringField(1)
  ipAddress = _messages.StringField(2)
  name = _messages.StringField(3)
  pemCertificateChain = _messages.StringField(4, repeated=True)
  publicIpAddress = _messages.StringField(5)


class ContinuousBackupConfig(_messages.Message):
  r"""ContinuousBackupConfig describes the continuous backups recovery
  configurations of a cluster.

  Fields:
    enabled: Whether ContinuousBackup is enabled.
    encryptionConfig: The encryption config can be specified to encrypt the
      backups with a customer-managed encryption key (CMEK). When this field
      is not specified, the backup will then use default encryption scheme to
      protect the user data.
    recoveryWindowDays: The number of days that are eligible to restore from
      using PITR. To support the entire recovery window, backups and logs are
      retained for one day more than the recovery window. If not set, defaults
      to 14 days.
  """

  enabled = _messages.BooleanField(1)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 2)
  recoveryWindowDays = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ContinuousBackupInfo(_messages.Message):
  r"""ContinuousBackupInfo describes the continuous backup properties of a
  cluster.

  Enums:
    ScheduleValueListEntryValuesEnum:

  Fields:
    earliestRestorableTime: Output only. The earliest restorable time that can
      be restored to. Output only field.
    enabledTime: Output only. When ContinuousBackup was most recently enabled.
      Set to null if ContinuousBackup is not enabled.
    encryptionInfo: Output only. The encryption information for the WALs and
      backups required for ContinuousBackup.
    schedule: Output only. Days of the week on which a continuous backup is
      taken. Output only field. Ignored if passed into the request.
  """

  class ScheduleValueListEntryValuesEnum(_messages.Enum):
    r"""ScheduleValueListEntryValuesEnum enum type.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  earliestRestorableTime = _messages.StringField(1)
  enabledTime = _messages.StringField(2)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 3)
  schedule = _messages.EnumField('ScheduleValueListEntryValuesEnum', 4, repeated=True)


class ContinuousBackupSource(_messages.Message):
  r"""Message describing a ContinuousBackupSource.

  Fields:
    cluster: Required. The source cluster from which to restore. This cluster
      must have continuous backup enabled for this operation to succeed. For
      the required format, see the comment on the Cluster.name field.
    pointInTime: Required. The point in time to restore to.
  """

  cluster = _messages.StringField(1)
  pointInTime = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfig(_messages.Message):
  r"""EncryptionConfig describes the encryption config of a cluster or a
  backup that is encrypted with a CMEK (customer-managed encryption key).

  Fields:
    kmsKeyName: The fully-qualified resource name of the KMS key. Each Cloud
      KMS key is regionalized and has the following format: projects/[PROJECT]
      /locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]
  """

  kmsKeyName = _messages.StringField(1)


class EncryptionInfo(_messages.Message):
  r"""EncryptionInfo describes the encryption information of a cluster or a
  backup.

  Enums:
    EncryptionTypeValueValuesEnum: Output only. Type of encryption.

  Fields:
    encryptionType: Output only. Type of encryption.
    kmsKeyVersions: Output only. Cloud KMS key versions that are being used to
      protect the database or the backup.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of encryption.

    Values:
      TYPE_UNSPECIFIED: Encryption type not specified. Defaults to
        GOOGLE_DEFAULT_ENCRYPTION.
      GOOGLE_DEFAULT_ENCRYPTION: The data is encrypted at rest with a key that
        is fully managed by Google. No key version will be populated. This is
        the default state.
      CUSTOMER_MANAGED_ENCRYPTION: The data is encrypted at rest with a key
        that is managed by the customer. KMS key versions will be populated.
    """
    TYPE_UNSPECIFIED = 0
    GOOGLE_DEFAULT_ENCRYPTION = 1
    CUSTOMER_MANAGED_ENCRYPTION = 2

  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 1)
  kmsKeyVersions = _messages.StringField(2, repeated=True)


class FailoverInstanceRequest(_messages.Message):
  r"""Message for triggering failover on an Instance

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the failover.
  """

  requestId = _messages.StringField(1)
  validateOnly = _messages.BooleanField(2)


class GenerateClientCertificateRequest(_messages.Message):
  r"""Message for requests to generate a client certificate signed by the
  Cluster CA.

  Fields:
    certDuration: Optional. An optional hint to the endpoint to generate the
      client certificate with the requested duration. The duration can be from
      1 hour to 24 hours. The endpoint may or may not honor the hint. If the
      hint is left unspecified or is not honored, then the endpoint will pick
      an appropriate default duration.
    pemCsr: Optional. A pem-encoded X.509 certificate signing request (CSR).
    publicKey: Optional. The public key from the client.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    useMetadataExchange: Optional. An optional hint to the endpoint to
      generate a client ceritificate that can be used by AlloyDB connectors to
      exchange additional metadata with the server after TLS handshake.
  """

  certDuration = _messages.StringField(1)
  pemCsr = _messages.StringField(2)
  publicKey = _messages.StringField(3)
  requestId = _messages.StringField(4)
  useMetadataExchange = _messages.BooleanField(5)


class GenerateClientCertificateResponse(_messages.Message):
  r"""Message returned by a GenerateClientCertificate operation.

  Fields:
    caCert: Optional. The pem-encoded cluster ca X.509 certificate.
    pemCertificate: Output only. The pem-encoded, signed X.509 certificate.
    pemCertificateChain: Output only. The pem-encoded chain that may be used
      to verify the X.509 certificate. Expected to be in issuer-to-root order
      according to RFC 5246.
  """

  caCert = _messages.StringField(1)
  pemCertificate = _messages.StringField(2)
  pemCertificateChain = _messages.StringField(3, repeated=True)


class GoogleCloudLocationListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('GoogleCloudLocationLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudLocationLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class GoogleTypeTimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of day in 24 hour format. Should be from 0 to 23. An API may
      choose to allow the value "24:00:00" for scenarios like business closing
      time.
    minutes: Minutes of hour of day. Must be from 0 to 59.
    nanos: Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
    seconds: Seconds of minutes of the time. Must normally be from 0 to 59. An
      API may allow the value 60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class InjectFaultRequest(_messages.Message):
  r"""Message for triggering fault injection on an instance

  Enums:
    FaultTypeValueValuesEnum: Required. The type of fault to be injected in an
      instance.

  Fields:
    faultType: Required. The type of fault to be injected in an instance.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the fault injection.
  """

  class FaultTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of fault to be injected in an instance.

    Values:
      FAULT_TYPE_UNSPECIFIED: The fault type is unknown.
      STOP_VM: Stop the VM
    """
    FAULT_TYPE_UNSPECIFIED = 0
    STOP_VM = 1

  faultType = _messages.EnumField('FaultTypeValueValuesEnum', 1)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class Instance(_messages.Message):
  r"""An Instance is a computing unit that an end customer can connect to.
  It's the main unit of computing resources in AlloyDB.

  Enums:
    AvailabilityTypeValueValuesEnum: Availability type of an Instance. If
      empty, defaults to REGIONAL for primary instances. For read pools,
      availability_type is always UNSPECIFIED. Instances in the read pools are
      evenly distributed across available zones within the region (i.e. read
      pools with more than one node will have a node in at least two zones).
    InstanceTypeValueValuesEnum: Required. The type of the instance. Specified
      at creation time.
    StateValueValuesEnum: Output only. The current serving state of the
      instance.

  Messages:
    AnnotationsValue: Annotations to allow client tools to store small amount
      of arbitrary data. This is distinct from labels.
      https://google.aip.dev/128
    DatabaseFlagsValue: Database flags. Set at instance level. * They are
      copied from primary instance on read instance creation. * Read instances
      can set new or override existing flags that are relevant for reads, e.g.
      for enabling columnar cache on a read instance. Flags set on read
      instance may or may not be present on primary. This is a list of "key":
      "value" pairs. "key": The name of the flag. These flags are passed at
      instance setup time, so include both server options and system variables
      for Postgres. Flags are specified with underscores, not hyphens.
      "value": The value of the flag. Booleans are set to **on** for true and
      **off** for false. This field must be omitted if the flag doesn't take a
      value.
    LabelsValue: Labels as key value pairs

  Fields:
    annotations: Annotations to allow client tools to store small amount of
      arbitrary data. This is distinct from labels. https://google.aip.dev/128
    availabilityType: Availability type of an Instance. If empty, defaults to
      REGIONAL for primary instances. For read pools, availability_type is
      always UNSPECIFIED. Instances in the read pools are evenly distributed
      across available zones within the region (i.e. read pools with more than
      one node will have a node in at least two zones).
    clientConnectionConfig: Optional. Client connection specific
      configurations
    createTime: Output only. Create time stamp
    databaseFlags: Database flags. Set at instance level. * They are copied
      from primary instance on read instance creation. * Read instances can
      set new or override existing flags that are relevant for reads, e.g. for
      enabling columnar cache on a read instance. Flags set on read instance
      may or may not be present on primary. This is a list of "key": "value"
      pairs. "key": The name of the flag. These flags are passed at instance
      setup time, so include both server options and system variables for
      Postgres. Flags are specified with underscores, not hyphens. "value":
      The value of the flag. Booleans are set to **on** for true and **off**
      for false. This field must be omitted if the flag doesn't take a value.
    deleteTime: Output only. Delete time stamp
    displayName: User-settable and human-readable display name for the
      Instance.
    enablePublicIp: Optional. Enabling public ip for the Instance.
    etag: For Resource freshness validation (https://google.aip.dev/154)
    gceZone: The Compute Engine zone that the instance should serve from, per
      https://cloud.google.com/compute/docs/regions-zones This can ONLY be
      specified for ZONAL instances. If present for a REGIONAL instance, an
      error will be thrown. If this is absent for a ZONAL instance, instance
      is created in a random zone with available capacity.
    instanceType: Required. The type of the instance. Specified at creation
      time.
    ipAddress: Output only. The IP address for the Instance. This is the
      connection endpoint for an end-user application.
    labels: Labels as key value pairs
    machineConfig: Configurations for the machines that host the underlying
      database engine.
    name: Output only. The name of the instance resource with the format: * pr
      ojects/{project}/locations/{region}/clusters/{cluster_id}/instances/{ins
      tance_id} where the cluster and instance ID segments should satisfy the
      regex expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters
      of lowercase letters, numbers, and dashes, starting with a letter, and
      ending with a letter or number. For more details see
      https://google.aip.dev/122. The prefix of the instance resource name is
      the name of the parent resource: *
      projects/{project}/locations/{region}/clusters/{cluster_id}
    nodes: Output only. List of available read-only VMs in this instance,
      including the standby for a PRIMARY instance.
    queryInsightsConfig: Configuration for query insights.
    readPoolConfig: Read pool specific config.
    reconciling: Output only. Reconciling
      (https://google.aip.dev/128#reconciliation). Set to true if the current
      state of Instance does not match the user's intended state, and the
      service is actively updating the resource to reconcile them. This can
      happen due to user-triggered updates or system actions like failover or
      maintenance.
    satisfiesPzs: Reserved for future use.
    state: Output only. The current serving state of the instance.
    uid: Output only. The system-generated UID of the resource. The UID is
      assigned when the resource is created, and it is retained until it is
      deleted.
    updatePolicy: Update policy that will be applied during instance update.
      This field is not persisted when you update the instance. To use a non-
      default update policy, you must specify explicitly specify the value in
      each update request.
    updateTime: Output only. Update time stamp
    writableNode: Output only. This is set for the read-write VM of the
      PRIMARY instance only.
  """

  class AvailabilityTypeValueValuesEnum(_messages.Enum):
    r"""Availability type of an Instance. If empty, defaults to REGIONAL for
    primary instances. For read pools, availability_type is always
    UNSPECIFIED. Instances in the read pools are evenly distributed across
    available zones within the region (i.e. read pools with more than one node
    will have a node in at least two zones).

    Values:
      AVAILABILITY_TYPE_UNSPECIFIED: This is an unknown Availability type.
      ZONAL: Zonal available instance.
      REGIONAL: Regional (or Highly) available instance.
    """
    AVAILABILITY_TYPE_UNSPECIFIED = 0
    ZONAL = 1
    REGIONAL = 2

  class InstanceTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the instance. Specified at creation time.

    Values:
      INSTANCE_TYPE_UNSPECIFIED: The type of the instance is unknown.
      PRIMARY: PRIMARY instances support read and write operations.
      READ_POOL: READ POOL instances support read operations only. Each read
        pool instance consists of one or more homogeneous nodes. * Read pool
        of size 1 can only have zonal availability. * Read pools with node
        count of 2 or more can have regional availability (nodes are present
        in 2 or more zones in a region).
      SECONDARY: SECONDARY instances support read operations only. SECONDARY
        instance is a cross-region read replica
    """
    INSTANCE_TYPE_UNSPECIFIED = 0
    PRIMARY = 1
    READ_POOL = 2
    SECONDARY = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current serving state of the instance.

    Values:
      STATE_UNSPECIFIED: The state of the instance is unknown.
      READY: The instance is active and running.
      STOPPED: The instance is stopped. Instance name and IP resources are
        preserved.
      CREATING: The instance is being created.
      DELETING: The instance is being deleted.
      MAINTENANCE: The instance is down for maintenance.
      FAILED: The creation of the instance failed or a fatal error occurred
        during an operation on the instance. Note: Instances in this state
        would tried to be auto-repaired. And Customers should be able to
        restart, update or delete these instances.
      BOOTSTRAPPING: Index 7 is used in the producer apis for ROLLED_BACK
        state. Keeping that index unused in case that state also needs to
        exposed via consumer apis in future. The instance has been configured
        to sync data from some other source.
      PROMOTING: The instance is being promoted.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    STOPPED = 2
    CREATING = 3
    DELETING = 4
    MAINTENANCE = 5
    FAILED = 6
    BOOTSTRAPPING = 7
    PROMOTING = 8

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations to allow client tools to store small amount of arbitrary
    data. This is distinct from labels. https://google.aip.dev/128

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DatabaseFlagsValue(_messages.Message):
    r"""Database flags. Set at instance level. * They are copied from primary
    instance on read instance creation. * Read instances can set new or
    override existing flags that are relevant for reads, e.g. for enabling
    columnar cache on a read instance. Flags set on read instance may or may
    not be present on primary. This is a list of "key": "value" pairs. "key":
    The name of the flag. These flags are passed at instance setup time, so
    include both server options and system variables for Postgres. Flags are
    specified with underscores, not hyphens. "value": The value of the flag.
    Booleans are set to **on** for true and **off** for false. This field must
    be omitted if the flag doesn't take a value.

    Messages:
      AdditionalProperty: An additional property for a DatabaseFlagsValue
        object.

    Fields:
      additionalProperties: Additional properties of type DatabaseFlagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DatabaseFlagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  availabilityType = _messages.EnumField('AvailabilityTypeValueValuesEnum', 2)
  clientConnectionConfig = _messages.MessageField('ClientConnectionConfig', 3)
  createTime = _messages.StringField(4)
  databaseFlags = _messages.MessageField('DatabaseFlagsValue', 5)
  deleteTime = _messages.StringField(6)
  displayName = _messages.StringField(7)
  enablePublicIp = _messages.BooleanField(8)
  etag = _messages.StringField(9)
  gceZone = _messages.StringField(10)
  instanceType = _messages.EnumField('InstanceTypeValueValuesEnum', 11)
  ipAddress = _messages.StringField(12)
  labels = _messages.MessageField('LabelsValue', 13)
  machineConfig = _messages.MessageField('MachineConfig', 14)
  name = _messages.StringField(15)
  nodes = _messages.MessageField('Node', 16, repeated=True)
  queryInsightsConfig = _messages.MessageField('QueryInsightsInstanceConfig', 17)
  readPoolConfig = _messages.MessageField('ReadPoolConfig', 18)
  reconciling = _messages.BooleanField(19)
  satisfiesPzs = _messages.BooleanField(20)
  state = _messages.EnumField('StateValueValuesEnum', 21)
  uid = _messages.StringField(22)
  updatePolicy = _messages.MessageField('UpdatePolicy', 23)
  updateTime = _messages.StringField(24)
  writableNode = _messages.MessageField('Node', 25)


class IntegerRestrictions(_messages.Message):
  r"""Restrictions on INTEGER type values.

  Fields:
    maxValue: The maximum value that can be specified, if applicable.
    minValue: The minimum value that can be specified, if applicable.
  """

  maxValue = _messages.IntegerField(1)
  minValue = _messages.IntegerField(2)


class ListBackupsResponse(_messages.Message):
  r"""Message for response to listing Backups

  Fields:
    backups: The list of Backup
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListClustersResponse(_messages.Message):
  r"""Message for response to listing Clusters

  Fields:
    clusters: The list of Cluster
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""Message for response to listing Instances

  Fields:
    instances: The list of Instance
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListSupportedDatabaseFlagsResponse(_messages.Message):
  r"""Message for response to listing SupportedDatabaseFlags.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    supportedDatabaseFlags: The list of SupportedDatabaseFlags.
  """

  nextPageToken = _messages.StringField(1)
  supportedDatabaseFlags = _messages.MessageField('SupportedDatabaseFlag', 2, repeated=True)


class ListUsersResponse(_messages.Message):
  r"""Message for response to listing Users

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
    users: The list of User
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  users = _messages.MessageField('User', 3, repeated=True)


class MachineConfig(_messages.Message):
  r"""MachineConfig describes the configuration of a machine.

  Fields:
    cpuCount: The number of CPU's in the VM instance.
  """

  cpuCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class MigrationSource(_messages.Message):
  r"""Subset of the source instance configuration that is available when
  reading the cluster resource.

  Enums:
    SourceTypeValueValuesEnum: Output only. Type of migration source.

  Fields:
    hostPort: Output only. The host and port of the on-premises instance in
      host:port format
    referenceId: Output only. Place holder for the external source
      identifier(e.g DMS job name) that created the cluster.
    sourceType: Output only. Type of migration source.
  """

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of migration source.

    Values:
      MIGRATION_SOURCE_TYPE_UNSPECIFIED: Migration source is unknown.
      DMS: DMS source means the cluster was created via DMS migration job.
    """
    MIGRATION_SOURCE_TYPE_UNSPECIFIED = 0
    DMS = 1

  hostPort = _messages.StringField(1)
  referenceId = _messages.StringField(2)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 3)


class NetworkConfig(_messages.Message):
  r"""Metadata related to network configuration.

  Fields:
    allocatedIpRange: Optional. The name of the allocated IP range for the
      private IP AlloyDB cluster. For example: "google-managed-services-
      default". If set, the instance IPs for this cluster will be created in
      the allocated range. The range name must comply with RFC 1035.
      Specifically, the name must be 1-63 characters long and match the
      regular expression [a-z]([-a-z0-9]*[a-z0-9])?. Field name is intended to
      be consistent with CloudSQL.
    network: Required. The resource link for the VPC network in which cluster
      resources are created and from which they are accessible via Private IP.
      The network must belong to the same project as the cluster. It is
      specified in the form:
      "projects/{project_number}/global/networks/{network_id}". This is
      required to create a cluster. It can be updated, but it cannot be
      removed.
  """

  allocatedIpRange = _messages.StringField(1)
  network = _messages.StringField(2)


class Node(_messages.Message):
  r"""Details of a single node in the instance. Nodes in an AlloyDB instance
  are ephemereal, they can change during update, failover, autohealing and
  resize operations.

  Fields:
    id: The identifier of the VM e.g. "test-read-0601-407e52be-ms3l".
    ip: The private IP address of the VM e.g. "**********".
    state: Determined by state of the compute VM and postgres-service health.
      Compute VM state can have values listed in
      https://cloud.google.com/compute/docs/instances/instance-life-cycle and
      postgres-service health can have values: HEALTHY and UNHEALTHY.
    zoneId: The Compute Engine zone of the VM e.g. "us-central1-b".
  """

  id = _messages.StringField(1)
  ip = _messages.StringField(2)
  state = _messages.StringField(3)
  zoneId = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PrimaryConfig(_messages.Message):
  r"""Configuration for the primary cluster. It has the list of clusters that
  are replicating from this cluster. This should be set if and only if the
  cluster is of type PRIMARY.

  Fields:
    secondaryClusterNames: Output only. Names of the clusters that are
      replicating from this cluster.
  """

  secondaryClusterNames = _messages.StringField(1, repeated=True)


class PromoteClusterRequest(_messages.Message):
  r"""Message for promoting a Cluster

  Fields:
    etag: Optional. The current etag of the Cluster. If an etag is provided
      and does not match the current etag of the Cluster, deletion will be
      blocked and an ABORTED error will be returned.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the delete.
  """

  etag = _messages.StringField(1)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class QuantityBasedExpiry(_messages.Message):
  r"""A backup's position in a quantity-based retention queue, of backups with
  the same source cluster and type, with length, retention, specified by the
  backup's retention policy. Once the position is greater than the retention,
  the backup is eligible to be garbage collected. Example: 5 backups from the
  same source cluster and type with a quantity-based retention of 3 and
  denoted by backup_id (position, retention). Safe: backup_5 (1, 3), backup_4,
  (2, 3), backup_3 (3, 3). Awaiting garbage collection: backup_2 (4, 3),
  backup_1 (5, 3)

  Fields:
    retentionCount: Output only. The backup's position among its backups with
      the same source cluster and type, by descending chronological order
      create time(i.e. newest first).
    totalRetentionCount: Output only. The length of the quantity-based queue,
      specified by the backup's retention policy.
  """

  retentionCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  totalRetentionCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class QuantityBasedRetention(_messages.Message):
  r"""A quantity based policy specifies that a certain number of the most
  recent successful backups should be retained.

  Fields:
    count: The number of backups to retain.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class QueryInsightsInstanceConfig(_messages.Message):
  r"""QueryInsights Instance specific configuration.

  Fields:
    queryPlansPerMinute: Number of query execution plans captured by Insights
      per minute for all queries combined. The default value is 5. Any integer
      between 0 and 20 is considered valid.
    queryStringLength: Query string length. The default value is 1024. Any
      integer between 256 and 4500 is considered valid.
    recordApplicationTags: Record application tags for an instance. This flag
      is turned "on" by default.
    recordClientAddress: Record client address for an instance. Client address
      is PII information. This flag is turned "on" by default.
  """

  queryPlansPerMinute = _messages.IntegerField(1, variant=_messages.Variant.UINT32)
  queryStringLength = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  recordApplicationTags = _messages.BooleanField(3)
  recordClientAddress = _messages.BooleanField(4)


class ReadPoolConfig(_messages.Message):
  r"""Configuration for a read pool instance.

  Fields:
    nodeCount: Read capacity, i.e. number of nodes in a read pool instance.
  """

  nodeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class RestartInstanceRequest(_messages.Message):
  r"""A RestartInstanceRequest object.

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the restart.
  """

  requestId = _messages.StringField(1)
  validateOnly = _messages.BooleanField(2)


class RestoreClusterRequest(_messages.Message):
  r"""Message for restoring a Cluster from a backup or another cluster at a
  given point in time.

  Fields:
    backupSource: Backup source.
    cluster: Required. The resource being created
    clusterId: Required. ID of the requesting object.
    continuousBackupSource: ContinuousBackup source. Continuous backup needs
      to be enabled in the source cluster for this operation to succeed.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, performs request validation (e.g.
      permission checks and any other type of validation), but do not actually
      execute the import request.
  """

  backupSource = _messages.MessageField('BackupSource', 1)
  cluster = _messages.MessageField('Cluster', 2)
  clusterId = _messages.StringField(3)
  continuousBackupSource = _messages.MessageField('ContinuousBackupSource', 4)
  requestId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SecondaryConfig(_messages.Message):
  r"""Configuration information for the secondary cluster. This should be set
  if and only if the cluster is of type SECONDARY.

  Fields:
    primaryClusterName: The name of the primary cluster name with the format:
      * projects/{project}/locations/{region}/clusters/{cluster_id}
  """

  primaryClusterName = _messages.StringField(1)


class SslConfig(_messages.Message):
  r"""SSL configuration.

  Enums:
    CaSourceValueValuesEnum: Optional. Certificate Authority (CA) source. Only
      CA_SOURCE_MANAGED is supported currently, and is the default value.
    SslModeValueValuesEnum: Optional. SSL mode. Specifies client-server
      SSL/TLS connection behavior.

  Fields:
    caSource: Optional. Certificate Authority (CA) source. Only
      CA_SOURCE_MANAGED is supported currently, and is the default value.
    sslMode: Optional. SSL mode. Specifies client-server SSL/TLS connection
      behavior.
  """

  class CaSourceValueValuesEnum(_messages.Enum):
    r"""Optional. Certificate Authority (CA) source. Only CA_SOURCE_MANAGED is
    supported currently, and is the default value.

    Values:
      CA_SOURCE_UNSPECIFIED: Certificate Authority (CA) source not specified.
        Defaults to CA_SOURCE_MANAGED.
      CA_SOURCE_MANAGED: Certificate Authority (CA) managed by the AlloyDB
        Cluster.
    """
    CA_SOURCE_UNSPECIFIED = 0
    CA_SOURCE_MANAGED = 1

  class SslModeValueValuesEnum(_messages.Enum):
    r"""Optional. SSL mode. Specifies client-server SSL/TLS connection
    behavior.

    Values:
      SSL_MODE_UNSPECIFIED: SSL mode not specified. Defaults to
        ENCRYPTED_ONLY.
      SSL_MODE_ALLOW: SSL connections are optional. CA verification not
        enforced.
      SSL_MODE_REQUIRE: SSL connections are required. CA verification not
        enforced. Clients may use locally self-signed certificates (default
        psql client behavior).
      SSL_MODE_VERIFY_CA: SSL connections are required. CA verification
        enforced. Clients must have certificates signed by a Cluster CA, e.g.
        via GenerateClientCertificate.
      ALLOW_UNENCRYPTED_AND_ENCRYPTED: SSL connections are optional. CA
        verification not enforced.
      ENCRYPTED_ONLY: SSL connections are required. CA verification not
        enforced.
    """
    SSL_MODE_UNSPECIFIED = 0
    SSL_MODE_ALLOW = 1
    SSL_MODE_REQUIRE = 2
    SSL_MODE_VERIFY_CA = 3
    ALLOW_UNENCRYPTED_AND_ENCRYPTED = 4
    ENCRYPTED_ONLY = 5

  caSource = _messages.EnumField('CaSourceValueValuesEnum', 1)
  sslMode = _messages.EnumField('SslModeValueValuesEnum', 2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StorageDatabasecenterPartnerapiV1mainAvailabilityConfiguration(_messages.Message):
  r"""Configuration for availability of database instance

  Enums:
    AvailabilityTypeValueValuesEnum: Availability type. Potential values: *
      `ZONAL`: The instance serves data from only one zone. Outages in that
      zone affect data accessibility. * `REGIONAL`: The instance can serve
      data from more than one zone in a region (it is highly available).

  Fields:
    availabilityType: Availability type. Potential values: * `ZONAL`: The
      instance serves data from only one zone. Outages in that zone affect
      data accessibility. * `REGIONAL`: The instance can serve data from more
      than one zone in a region (it is highly available).
    externalReplicaConfigured: A boolean attribute.
    promotableReplicaConfigured: A boolean attribute.
  """

  class AvailabilityTypeValueValuesEnum(_messages.Enum):
    r"""Availability type. Potential values: * `ZONAL`: The instance serves
    data from only one zone. Outages in that zone affect data accessibility. *
    `REGIONAL`: The instance can serve data from more than one zone in a
    region (it is highly available).

    Values:
      AVAILABILITY_TYPE_UNSPECIFIED: <no description>
      ZONAL: Zonal available instance.
      REGIONAL: Regional available instance.
      MULTI_REGIONAL: Multi regional instance
      AVAILABILITY_TYPE_OTHER: For rest of the other category
    """
    AVAILABILITY_TYPE_UNSPECIFIED = 0
    ZONAL = 1
    REGIONAL = 2
    MULTI_REGIONAL = 3
    AVAILABILITY_TYPE_OTHER = 4

  availabilityType = _messages.EnumField('AvailabilityTypeValueValuesEnum', 1)
  externalReplicaConfigured = _messages.BooleanField(2)
  promotableReplicaConfigured = _messages.BooleanField(3)


class StorageDatabasecenterPartnerapiV1mainBackupConfiguration(_messages.Message):
  r"""Configuration for automatic backups

  Fields:
    automatedBackupEnabled: Whether customer visible automated backups are
      enabled on the instance.
    backupRetentionSettings: Backup retention settings.
  """

  automatedBackupEnabled = _messages.BooleanField(1)
  backupRetentionSettings = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainRetentionSettings', 2)


class StorageDatabasecenterPartnerapiV1mainBackupRun(_messages.Message):
  r"""A backup run.

  Enums:
    StatusValueValuesEnum: The status of this run. REQUIRED

  Fields:
    endTime: The time the backup operation completed. REQUIRED
    error: Information about why the backup operation failed. This is only
      present if the run has the FAILED status. OPTIONAL
    startTime: The time the backup operation started. REQUIRED
    status: The status of this run. REQUIRED
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status of this run. REQUIRED

    Values:
      STATUS_UNSPECIFIED: <no description>
      SUCCESSFUL: The backup was successful.
      FAILED: The backup was unsuccessful.
    """
    STATUS_UNSPECIFIED = 0
    SUCCESSFUL = 1
    FAILED = 2

  endTime = _messages.StringField(1)
  error = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainOperationError', 2)
  startTime = _messages.StringField(3)
  status = _messages.EnumField('StatusValueValuesEnum', 4)


class StorageDatabasecenterPartnerapiV1mainDatabaseResourceFeed(_messages.Message):
  r"""DatabaseResourceFeed is the top level proto to be used to ingest
  different database resource level events into Condor platform.

  Enums:
    FeedTypeValueValuesEnum: Required. Type feed to be ingested into condor

  Fields:
    feedTimestamp: Required. Timestamp when feed is generated.
    feedType: Required. Type feed to be ingested into condor
    resourceId: Required. Primary key associated with the Resource
    resourceMetadata: More feed data would be added in subsequent CLs
  """

  class FeedTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type feed to be ingested into condor

    Values:
      FEEDTYPE_UNSPECIFIED: <no description>
      RESOURCE_METADATA: Database resource metadata feed from control plane
      OBSERVABILITY_DATA: Database resource monitoring data
      COMPLIANCE_DATA: Database resource compliance feed
    """
    FEEDTYPE_UNSPECIFIED = 0
    RESOURCE_METADATA = 1
    OBSERVABILITY_DATA = 2
    COMPLIANCE_DATA = 3

  feedTimestamp = _messages.StringField(1)
  feedType = _messages.EnumField('FeedTypeValueValuesEnum', 2)
  resourceId = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceId', 3)
  resourceMetadata = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceMetadata', 4)


class StorageDatabasecenterPartnerapiV1mainDatabaseResourceId(_messages.Message):
  r"""DatabaseResourceId will serve as primary key for any resource ingestion
  event.

  Enums:
    ProviderValueValuesEnum: Required. Cloud provider name. Ex:
      GCP/AWS/Azure/OnPrem/SelfManaged

  Fields:
    provider: Required. Cloud provider name. Ex:
      GCP/AWS/Azure/OnPrem/SelfManaged
    resourceType: Required. The type of resource this ID is identifying. Ex
      google.sqladmin.Instance, google.alloydb.cluster, google.sqladmin.Backup
      REQUIRED
    uniqueId: Required. A service-local token that distinguishes this resource
      from other resources within the same service.
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""Required. Cloud provider name. Ex: GCP/AWS/Azure/OnPrem/SelfManaged

    Values:
      PROVIDER_UNSPECIFIED: <no description>
      GCP: Google cloud platform provider
      AWS: Amazon web service
      AZURE: Azure web service
      ONPREM: On-prem database provider
      SELFMANAGED: Self-managed database provider
      PROVIDER_OTHER: For rest of the other category
    """
    PROVIDER_UNSPECIFIED = 0
    GCP = 1
    AWS = 2
    AZURE = 3
    ONPREM = 4
    SELFMANAGED = 5
    PROVIDER_OTHER = 6

  provider = _messages.EnumField('ProviderValueValuesEnum', 1)
  resourceType = _messages.StringField(2)
  uniqueId = _messages.StringField(3)


class StorageDatabasecenterPartnerapiV1mainDatabaseResourceMetadata(_messages.Message):
  r"""Common model for database resource instance metadata.

  Enums:
    CurrentStateValueValuesEnum: Current state of the instance.
    ExpectedStateValueValuesEnum: The actual instance state.
    InstanceTypeValueValuesEnum: The type of the instance. Specified at
      creation time.

  Messages:
    UserLabelsValue: User-provided labels, represented as a dictionary where
      each label is a single key value pair.

  Fields:
    availabilityConfiguration: Availability configuration for this instance
    backupConfiguration: Backup configuration for this instance
    backupRun: Latest backup run information for this instance
    creationTime: The creation time of the resource, i.e. the time when
      resource is created and recorded in partner service.
    currentState: Current state of the instance.
    expectedState: The actual instance state.
    id: Required. Unique identifier for a Database resource
    instanceType: The type of the instance. Specified at creation time.
    location: The resource location. REQUIRED
    primaryResourceId: Unique identifier for this resource's immediate parent
      resource. This parent resource id would be used to build resource
      hierarchy in condor platform.
    product: The product this resource represents.
    resourceContainer: Closest parent Cloud Resource Manager container of this
      resource. It must either be resource name of a Cloud Resource Manager
      project, for ex: "projects/123".
    resourceName: Required. Different from unique_id, a resource name can be
      reused over time. That is after a resource named "ABC" is deleted, the
      name "ABC" can be used to to create a new resource within the same
      source.
    updationTime: The time at which the resource was updated and recorded at
      partner service.
    userLabels: User-provided labels, represented as a dictionary where each
      label is a single key value pair.
  """

  class CurrentStateValueValuesEnum(_messages.Enum):
    r"""Current state of the instance.

    Values:
      STATE_UNSPECIFIED: <no description>
      HEALTHY: The instance is running.
      UNHEALTHY: Instance being created, updated, deleted or under maintenance
      STATE_OTHER: For rest of the other category
    """
    STATE_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    STATE_OTHER = 3

  class ExpectedStateValueValuesEnum(_messages.Enum):
    r"""The actual instance state.

    Values:
      STATE_UNSPECIFIED: <no description>
      HEALTHY: The instance is running.
      UNHEALTHY: Instance being created, updated, deleted or under maintenance
      STATE_OTHER: For rest of the other category
    """
    STATE_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    STATE_OTHER = 3

  class InstanceTypeValueValuesEnum(_messages.Enum):
    r"""The type of the instance. Specified at creation time.

    Values:
      INSTANCE_TYPE_UNSPECIFIED: <no description>
      PRIMARY: A regular primary database instance.
      READ_REPLICA: An instance acting as a read-replica.
      OTHER: For rest of the other categories.
    """
    INSTANCE_TYPE_UNSPECIFIED = 0
    PRIMARY = 1
    READ_REPLICA = 2
    OTHER = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UserLabelsValue(_messages.Message):
    r"""User-provided labels, represented as a dictionary where each label is
    a single key value pair.

    Messages:
      AdditionalProperty: An additional property for a UserLabelsValue object.

    Fields:
      additionalProperties: Additional properties of type UserLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UserLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  availabilityConfiguration = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainAvailabilityConfiguration', 1)
  backupConfiguration = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainBackupConfiguration', 2)
  backupRun = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainBackupRun', 3)
  creationTime = _messages.StringField(4)
  currentState = _messages.EnumField('CurrentStateValueValuesEnum', 5)
  expectedState = _messages.EnumField('ExpectedStateValueValuesEnum', 6)
  id = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceId', 7)
  instanceType = _messages.EnumField('InstanceTypeValueValuesEnum', 8)
  location = _messages.StringField(9)
  primaryResourceId = _messages.MessageField('StorageDatabasecenterPartnerapiV1mainDatabaseResourceId', 10)
  product = _messages.MessageField('StorageDatabasecenterProtoCommonProduct', 11)
  resourceContainer = _messages.StringField(12)
  resourceName = _messages.StringField(13)
  updationTime = _messages.StringField(14)
  userLabels = _messages.MessageField('UserLabelsValue', 15)


class StorageDatabasecenterPartnerapiV1mainOperationError(_messages.Message):
  r"""An error that occurred during a backup creation operation.

  Fields:
    code: Identifies the specific error that occurred. REQUIRED
    message: Additional information about the error encountered. REQUIRED
  """

  code = _messages.StringField(1)
  message = _messages.StringField(2)


class StorageDatabasecenterPartnerapiV1mainRetentionSettings(_messages.Message):
  r"""A StorageDatabasecenterPartnerapiV1mainRetentionSettings object.

  Enums:
    RetentionUnitValueValuesEnum: The unit that 'retained_backups' represents.

  Fields:
    quantityBasedRetention: A integer attribute.
    retentionUnit: The unit that 'retained_backups' represents.
    timeBasedRetention: A string attribute.
  """

  class RetentionUnitValueValuesEnum(_messages.Enum):
    r"""The unit that 'retained_backups' represents.

    Values:
      RETENTION_UNIT_UNSPECIFIED: Backup retention unit is unspecified, will
        be treated as COUNT.
      COUNT: Retention will be by count, eg. "retain the most recent 7
        backups".
      TIME: Retention will be by Time, eg. "retain the last 7 days backups".
      RETENTION_UNIT_OTHER: For rest of the other category
    """
    RETENTION_UNIT_UNSPECIFIED = 0
    COUNT = 1
    TIME = 2
    RETENTION_UNIT_OTHER = 3

  quantityBasedRetention = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  retentionUnit = _messages.EnumField('RetentionUnitValueValuesEnum', 2)
  timeBasedRetention = _messages.StringField(3)


class StorageDatabasecenterProtoCommonProduct(_messages.Message):
  r"""Product specification for Condor resources.

  Enums:
    EngineValueValuesEnum: The specific engine that the underlying database is
      running.
    TypeValueValuesEnum: Type of specific database product. It could be
      CloudSQL, AlloyDB etc..

  Fields:
    engine: The specific engine that the underlying database is running.
    type: Type of specific database product. It could be CloudSQL, AlloyDB
      etc..
    version: Version of the underlying database engine. Example values: For
      MySQL, it could be "8.0", "5.7" etc.. For Postgres, it could be "14",
      "15" etc..
  """

  class EngineValueValuesEnum(_messages.Enum):
    r"""The specific engine that the underlying database is running.

    Values:
      ENGINE_UNSPECIFIED: UNSPECIFIED means engine type is not known or
        available.
      MYSQL: MySQL binary running as engine in database instance.
      POSTGRES: Postgres binary running as engine in database instance.
      SQL_SERVER: SQLServer binary running as engine in database instance.
      NATIVE: Native database binary running as engine in instance.
      SPANGRES: Cloud Spanner with Postgres dialect.
      ENGINE_OTHER: Other refers to rest of other database engine. This is to
        be when engine is known, but it is not present in this enum.
    """
    ENGINE_UNSPECIFIED = 0
    MYSQL = 1
    POSTGRES = 2
    SQL_SERVER = 3
    NATIVE = 4
    SPANGRES = 5
    ENGINE_OTHER = 6

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of specific database product. It could be CloudSQL, AlloyDB etc..

    Values:
      PRODUCT_TYPE_UNSPECIFIED: UNSPECIFIED means product type is not known or
        available.
      CLOUD_SQL: Cloud SQL product area in GCP
      ALLOYDB: AlloyDB product area in GCP
      SPANNER: Spanner product area in GCP
      ON_PREM: On premises database product.
      PRODUCT_TYPE_OTHER: Other refers to rest of other product type. This is
        to be when product type is known, but it is not present in this enum.
    """
    PRODUCT_TYPE_UNSPECIFIED = 0
    CLOUD_SQL = 1
    ALLOYDB = 2
    SPANNER = 3
    ON_PREM = 4
    PRODUCT_TYPE_OTHER = 5

  engine = _messages.EnumField('EngineValueValuesEnum', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)
  version = _messages.StringField(3)


class StringRestrictions(_messages.Message):
  r"""Restrictions on STRING type values

  Fields:
    allowedValues: The list of allowed values, if bounded. This field will be
      empty if there is a unbounded number of allowed values.
  """

  allowedValues = _messages.StringField(1, repeated=True)


class SupportedDatabaseFlag(_messages.Message):
  r"""SupportedDatabaseFlag gives general information about a database flag,
  like type and allowed values. This is a static value that is defined on the
  server side, and it cannot be modified by callers. To set the Database flags
  on a particular Instance, a caller should modify the Instance.database_flags
  field.

  Enums:
    SupportedDbVersionsValueListEntryValuesEnum:
    ValueTypeValueValuesEnum:

  Fields:
    acceptsMultipleValues: Whether the database flag accepts multiple values.
      If true, a comma-separated list of stringified values may be specified.
    flagName: The name of the database flag, e.g. "max_allowed_packets". The
      is a possibly key for the Instance.database_flags map field.
    integerRestrictions: Restriction on INTEGER type value.
    name: The name of the flag resource, following Google Cloud conventions,
      e.g.: * projects/{project}/locations/{location}/flags/{flag} This field
      currently has no semantic meaning.
    requiresDbRestart: Whether setting or updating this flag on an Instance
      requires a database restart. If a flag that requires database restart is
      set, the backend will automatically restart the database (making sure to
      satisfy any availability SLO's).
    stringRestrictions: Restriction on STRING type value.
    supportedDbVersions: Major database engine versions for which this flag is
      supported.
    valueType: A ValueTypeValueValuesEnum attribute.
  """

  class SupportedDbVersionsValueListEntryValuesEnum(_messages.Enum):
    r"""SupportedDbVersionsValueListEntryValuesEnum enum type.

    Values:
      DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      POSTGRES_13: DEPRECATED - The database version is Postgres 13.
      POSTGRES_14: The database version is Postgres 14.
      POSTGRES_15: The database version is Postgres 15.
    """
    DATABASE_VERSION_UNSPECIFIED = 0
    POSTGRES_13 = 1
    POSTGRES_14 = 2
    POSTGRES_15 = 3

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""ValueTypeValueValuesEnum enum type.

    Values:
      VALUE_TYPE_UNSPECIFIED: This is an unknown flag type.
      STRING: String type flag.
      INTEGER: Integer type flag.
      FLOAT: Float type flag.
      NONE: Denotes that the flag does not accept any values.
    """
    VALUE_TYPE_UNSPECIFIED = 0
    STRING = 1
    INTEGER = 2
    FLOAT = 3
    NONE = 4

  acceptsMultipleValues = _messages.BooleanField(1)
  flagName = _messages.StringField(2)
  integerRestrictions = _messages.MessageField('IntegerRestrictions', 3)
  name = _messages.StringField(4)
  requiresDbRestart = _messages.BooleanField(5)
  stringRestrictions = _messages.MessageField('StringRestrictions', 6)
  supportedDbVersions = _messages.EnumField('SupportedDbVersionsValueListEntryValuesEnum', 7, repeated=True)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 8)


class TimeBasedRetention(_messages.Message):
  r"""A time based retention policy specifies that all backups within a
  certain time period should be retained.

  Fields:
    retentionPeriod: The retention period.
  """

  retentionPeriod = _messages.StringField(1)


class UpdatePolicy(_messages.Message):
  r"""Policy to be used while updating the instance.

  Enums:
    ModeValueValuesEnum: Mode for updating the instance.

  Fields:
    mode: Mode for updating the instance.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Mode for updating the instance.

    Values:
      MODE_UNSPECIFIED: Mode is unknown.
      DEFAULT: Least disruptive way to apply the update.
      FORCE_APPLY: Performs a forced update when applicable. This will be fast
        but may incur a downtime.
    """
    MODE_UNSPECIFIED = 0
    DEFAULT = 1
    FORCE_APPLY = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)


class User(_messages.Message):
  r"""Message describing User object.

  Enums:
    UserTypeValueValuesEnum: Optional. Type of this user.

  Fields:
    databaseRoles: Optional. List of database roles this user has. The
      database role strings are subject to the PostgreSQL naming conventions.
    name: Output only. Name of the resource in the form of
      projects/{project}/locations/{location}/cluster/{cluster}/users/{user}.
    password: Input only. Password for the user.
    userType: Optional. Type of this user.
  """

  class UserTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of this user.

    Values:
      USER_TYPE_UNSPECIFIED: Unspecified user type.
      ALLOYDB_BUILT_IN: The default user type that authenticates via password-
        based authentication.
      ALLOYDB_IAM_USER: Database user that can authenticate via IAM-Based
        authentication.
    """
    USER_TYPE_UNSPECIFIED = 0
    ALLOYDB_BUILT_IN = 1
    ALLOYDB_IAM_USER = 2

  databaseRoles = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)
  password = _messages.StringField(3)
  userType = _messages.EnumField('UserTypeValueValuesEnum', 4)


class UserPassword(_messages.Message):
  r"""The username/password for a database user. Used for specifying initial
  users at cluster creation time.

  Fields:
    password: The initial password for the user.
    user: The database username.
  """

  password = _messages.StringField(1)
  user = _messages.StringField(2)


class WeeklySchedule(_messages.Message):
  r"""A weekly schedule starts a backup at prescribed start times within a
  day, for the specified days of the week. The weekly schedule message is
  flexible and can be used to create many types of schedules. For example, to
  have a daily backup that starts at 22:00, configure the `start_times` field
  to have one element "22:00" and the `days_of_week` field to have all seven
  days of the week.

  Enums:
    DaysOfWeekValueListEntryValuesEnum:

  Fields:
    daysOfWeek: The days of the week to perform a backup. If this field is
      left empty, the default of every day of the week is used.
    startTimes: The times during the day to start a backup. The start times
      are assumed to be in UTC and to be an exact hour (e.g., 04:00:00). If no
      start times are provided, a single fixed start time is chosen
      arbitrarily.
  """

  class DaysOfWeekValueListEntryValuesEnum(_messages.Enum):
    r"""DaysOfWeekValueListEntryValuesEnum enum type.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  daysOfWeek = _messages.EnumField('DaysOfWeekValueListEntryValuesEnum', 1, repeated=True)
  startTimes = _messages.MessageField('GoogleTypeTimeOfDay', 2, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

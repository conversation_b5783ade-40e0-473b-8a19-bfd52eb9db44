"""Generated message classes for cloudresourcemanager version v3.

Creates, reads, and updates metadata for Google Cloud Platform resource
containers.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudresourcemanager'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CloudresourcemanagerEffectiveTagsListRequest(_messages.Message):
  r"""A CloudresourcemanagerEffectiveTagsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of effective tags to return in the
      response. The server allows a maximum of 300 effective tags to return in
      a single page. If unspecified, the server will use 100 as the default.
    pageToken: Optional. A pagination token returned from a previous call to
      `ListEffectiveTags` that indicates from where this listing should
      continue.
    parent: Required. The full resource name of a resource for which you want
      to list the effective tags. E.g.
      "//cloudresourcemanager.googleapis.com/projects/123"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class CloudresourcemanagerFoldersDeleteRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersDeleteRequest object.

  Fields:
    name: Required. The resource name of the folder to be deleted. Must be of
      the form `folders/{folder_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerFoldersGetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class CloudresourcemanagerFoldersGetRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersGetRequest object.

  Fields:
    name: Required. The resource name of the folder to retrieve. Must be of
      the form `folders/{folder_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerFoldersListRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersListRequest object.

  Fields:
    pageSize: Optional. The maximum number of folders to return in the
      response. The server can return fewer folders than requested. If
      unspecified, server picks an appropriate default.
    pageToken: Optional. A pagination token returned from a previous call to
      `ListFolders` that indicates where this listing should continue from.
    parent: Required. The name of the parent resource whose folders are being
      listed. Only children of this parent resource are listed; descendants
      are not listed. If the parent is a folder, use the value
      `folders/{folder_id}`. If the parent is an organization, use the value
      `organizations/{org_id}`. Access to this method is controlled by
      checking the `resourcemanager.folders.list` permission on the `parent`.
    showDeleted: Optional. Controls whether folders in the DELETE_REQUESTED
      state should be returned. Defaults to false.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)
  showDeleted = _messages.BooleanField(4)


class CloudresourcemanagerFoldersMoveRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersMoveRequest object.

  Fields:
    moveFolderRequest: A MoveFolderRequest resource to be passed as the
      request body.
    name: Required. The resource name of the Folder to move. Must be of the
      form folders/{folder_id}
  """

  moveFolderRequest = _messages.MessageField('MoveFolderRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudresourcemanagerFoldersPatchRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersPatchRequest object.

  Fields:
    folder: A Folder resource to be passed as the request body.
    name: Output only. The resource name of the folder. Its format is
      `folders/{folder_id}`, for example: "folders/1234".
    updateMask: Required. Fields to be updated. Only the `display_name` can be
      updated.
  """

  folder = _messages.MessageField('Folder', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudresourcemanagerFoldersSearchRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersSearchRequest object.

  Fields:
    pageSize: Optional. The maximum number of folders to return in the
      response. The server can return fewer folders than requested. If
      unspecified, server picks an appropriate default.
    pageToken: Optional. A pagination token returned from a previous call to
      `SearchFolders` that indicates from where search should continue.
    query: Optional. Search criteria used to select the folders to return. If
      no search criteria is specified then all accessible folders will be
      returned. Query expressions can be used to restrict results based upon
      displayName, state and parent, where the operators `=` (`:`) `NOT`,
      `AND` and `OR` can be used along with the suffix wildcard symbol `*`.
      The `displayName` field in a query expression should use escaped quotes
      for values that include whitespace to prevent unexpected behavior. ``` |
      Field | Description |
      |-------------------------|----------------------------------------| |
      displayName | Filters by displayName. | | parent | Filters by parent
      (for example: folders/123). | | state, lifecycleState | Filters by
      state. | ``` Some example queries are: * Query `displayName=Test*`
      returns Folder resources whose display name starts with "Test". * Query
      `state=ACTIVE` returns Folder resources with `state` set to `ACTIVE`. *
      Query `parent=folders/123` returns Folder resources that have
      `folders/123` as a parent resource. * Query `parent=folders/123 AND
      state=ACTIVE` returns active Folder resources that have `folders/123` as
      a parent resource. * Query `displayName=\\"Test String\\"` returns
      Folder resources with display names that include both "Test" and
      "String".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  query = _messages.StringField(3)


class CloudresourcemanagerFoldersSetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudresourcemanagerFoldersTestIamPermissionsRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudresourcemanagerFoldersUndeleteRequest(_messages.Message):
  r"""A CloudresourcemanagerFoldersUndeleteRequest object.

  Fields:
    name: Required. The resource name of the folder to undelete. Must be of
      the form `folders/{folder_id}`.
    undeleteFolderRequest: A UndeleteFolderRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteFolderRequest = _messages.MessageField('UndeleteFolderRequest', 2)


class CloudresourcemanagerGoogleCloudResourcemanagerV2alpha1FolderOperation(_messages.Message):
  r"""Metadata describing a long running folder operation

  Enums:
    OperationTypeValueValuesEnum: The type of this operation.

  Fields:
    destinationParent: The resource name of the folder or organization we are
      either creating the folder under or moving the folder to.
    displayName: The display name of the folder.
    operationType: The type of this operation.
    sourceParent: The resource name of the folder's parent. Only applicable
      when the operation_type is MOVE.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""The type of this operation.

    Values:
      OPERATION_TYPE_UNSPECIFIED: Operation type not specified.
      CREATE: A create folder operation.
      MOVE: A move folder operation.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    CREATE = 1
    MOVE = 2

  destinationParent = _messages.StringField(1)
  displayName = _messages.StringField(2)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 3)
  sourceParent = _messages.StringField(4)


class CloudresourcemanagerGoogleCloudResourcemanagerV2beta1FolderOperation(_messages.Message):
  r"""Metadata describing a long running folder operation

  Enums:
    OperationTypeValueValuesEnum: The type of this operation.

  Fields:
    destinationParent: The resource name of the folder or organization we are
      either creating the folder under or moving the folder to.
    displayName: The display name of the folder.
    operationType: The type of this operation.
    sourceParent: The resource name of the folder's parent. Only applicable
      when the operation_type is MOVE.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""The type of this operation.

    Values:
      OPERATION_TYPE_UNSPECIFIED: Operation type not specified.
      CREATE: A create folder operation.
      MOVE: A move folder operation.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    CREATE = 1
    MOVE = 2

  destinationParent = _messages.StringField(1)
  displayName = _messages.StringField(2)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 3)
  sourceParent = _messages.StringField(4)


class CloudresourcemanagerLiensDeleteRequest(_messages.Message):
  r"""A CloudresourcemanagerLiensDeleteRequest object.

  Fields:
    name: Required. The name/identifier of the Lien to delete.
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerLiensGetRequest(_messages.Message):
  r"""A CloudresourcemanagerLiensGetRequest object.

  Fields:
    name: Required. The name/identifier of the Lien.
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerLiensListRequest(_messages.Message):
  r"""A CloudresourcemanagerLiensListRequest object.

  Fields:
    pageSize: The maximum number of items to return. This is a suggestion for
      the server. The server can return fewer liens than requested. If
      unspecified, server picks an appropriate default.
    pageToken: The `next_page_token` value returned from a previous List
      request, if any.
    parent: Required. The name of the resource to list all attached Liens. For
      example, `projects/1234`. (google.api.field_policy).resource_type
      annotation is not set since the parent depends on the meta api
      implementation. This field could be a project or other sub project
      resources.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class CloudresourcemanagerOperationsGetRequest(_messages.Message):
  r"""A CloudresourcemanagerOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerOrganizationsGetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerOrganizationsGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class CloudresourcemanagerOrganizationsGetRequest(_messages.Message):
  r"""A CloudresourcemanagerOrganizationsGetRequest object.

  Fields:
    name: Required. The resource name of the Organization to fetch. This is
      the organization's relative path in the API, formatted as
      "organizations/[organizationId]". For example, "organizations/1234".
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerOrganizationsSearchRequest(_messages.Message):
  r"""A CloudresourcemanagerOrganizationsSearchRequest object.

  Fields:
    pageSize: Optional. The maximum number of organizations to return in the
      response. The server can return fewer organizations than requested. If
      unspecified, server picks an appropriate default.
    pageToken: Optional. A pagination token returned from a previous call to
      `SearchOrganizations` that indicates from where listing should continue.
    query: Optional. An optional query string used to filter the Organizations
      to return in the response. Query rules are case-insensitive. ``` | Field
      | Description |
      |------------------|--------------------------------------------| |
      directoryCustomerId, owner.directoryCustomerId | Filters by directory
      customer id. | | domain | Filters by domain. | ``` Organizations may be
      queried by `directoryCustomerId` or by `domain`, where the domain is a G
      Suite domain, for example: * Query `directorycustomerid:123456789`
      returns Organization resources with `owner.directory_customer_id` equal
      to `123456789`. * Query `domain:google.com` returns Organization
      resources corresponding to the domain `google.com`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  query = _messages.StringField(3)


class CloudresourcemanagerOrganizationsSetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerOrganizationsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudresourcemanagerOrganizationsTestIamPermissionsRequest(_messages.Message):
  r"""A CloudresourcemanagerOrganizationsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudresourcemanagerProjectsDeleteRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsDeleteRequest object.

  Fields:
    name: Required. The name of the Project (for example,
      `projects/415104041262`).
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerProjectsGetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class CloudresourcemanagerProjectsGetRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsGetRequest object.

  Fields:
    name: Required. The name of the project (for example,
      `projects/415104041262`).
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerProjectsListRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of projects to return in the
      response. The server can return fewer projects than requested. If
      unspecified, server picks an appropriate default.
    pageToken: Optional. A pagination token returned from a previous call to
      ListProjects that indicates from where listing should continue.
    parent: Required. The name of the parent resource whose projects are being
      listed. Only children of this parent resource are listed; descendants
      are not listed. If the parent is a folder, use the value
      `folders/{folder_id}`. If the parent is an organization, use the value
      `organizations/{org_id}`.
    showDeleted: Optional. Indicate that projects in the `DELETE_REQUESTED`
      state should also be returned. Normally only `ACTIVE` projects are
      returned.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)
  showDeleted = _messages.BooleanField(4)


class CloudresourcemanagerProjectsMoveRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsMoveRequest object.

  Fields:
    moveProjectRequest: A MoveProjectRequest resource to be passed as the
      request body.
    name: Required. The name of the project to move.
  """

  moveProjectRequest = _messages.MessageField('MoveProjectRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudresourcemanagerProjectsPatchRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsPatchRequest object.

  Fields:
    name: Output only. The unique resource name of the project. It is an int64
      generated number prefixed by "projects/". Example:
      `projects/415104041262`
    project: A Project resource to be passed as the request body.
    updateMask: Optional. An update mask to selectively update fields.
  """

  name = _messages.StringField(1, required=True)
  project = _messages.MessageField('Project', 2)
  updateMask = _messages.StringField(3)


class CloudresourcemanagerProjectsSearchRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsSearchRequest object.

  Fields:
    pageSize: Optional. The maximum number of projects to return in the
      response. The server can return fewer projects than requested. If
      unspecified, server picks an appropriate default.
    pageToken: Optional. A pagination token returned from a previous call to
      ListProjects that indicates from where listing should continue.
    query: Optional. A query string for searching for projects that the caller
      has `resourcemanager.projects.get` permission to. If multiple fields are
      included in the query, then it will return results that match any of the
      fields. Some eligible fields are: ``` | Field | Description | |---------
      ----------------|----------------------------------------------| |
      displayName, name | Filters by displayName. | | parent | Project's
      parent (for example: folders/123, organizations/*). Prefer parent field
      over parent.type and parent.id.| | parent.type | Parent's type: `folder`
      or `organization`. | | parent.id | Parent's id number (for example: 123)
      | | id, projectId | Filters by projectId. | | state, lifecycleState |
      Filters by state. | | labels | Filters by label name or value. | |
      labels.\ (where *key* is the name of a label) | Filters by label name.|
      ``` Search expressions are case insensitive. Some examples queries: ```
      | Query | Description | |------------------|----------------------------
      -------------------------| | name:how* | The project's name starts with
      "how". | | name:Howl | The project's name is `Howl` or `howl`. | |
      name:HOWL | Equivalent to above. | | NAME:howl | Equivalent to above. |
      | labels.color:* | The project has the label `color`. | |
      labels.color:red | The project's label `color` has the value `red`. | |
      labels.color:red labels.size:big | The project's label `color` has the
      value `red` or its label `size` has the value `big`. | ``` If no query
      is specified, the call will return projects for which the user has the
      `resourcemanager.projects.get` permission.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  query = _messages.StringField(3)


class CloudresourcemanagerProjectsSetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudresourcemanagerProjectsTestIamPermissionsRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudresourcemanagerProjectsUndeleteRequest(_messages.Message):
  r"""A CloudresourcemanagerProjectsUndeleteRequest object.

  Fields:
    name: Required. The name of the project (for example,
      `projects/415104041262`). Required.
    undeleteProjectRequest: A UndeleteProjectRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteProjectRequest = _messages.MessageField('UndeleteProjectRequest', 2)


class CloudresourcemanagerTagBindingsCreateRequest(_messages.Message):
  r"""A CloudresourcemanagerTagBindingsCreateRequest object.

  Fields:
    tagBinding: A TagBinding resource to be passed as the request body.
    validateOnly: Optional. Set to true to perform the validations necessary
      for creating the resource, but not actually perform the action.
  """

  tagBinding = _messages.MessageField('TagBinding', 1)
  validateOnly = _messages.BooleanField(2)


class CloudresourcemanagerTagBindingsDeleteRequest(_messages.Message):
  r"""A CloudresourcemanagerTagBindingsDeleteRequest object.

  Fields:
    name: Required. The name of the TagBinding. This is a String of the form:
      `tagBindings/{id}` (e.g. `tagBindings/%2F%2Fcloudresourcemanager.googlea
      pis.com%2Fprojects%2F123/tagValues/456`).
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerTagBindingsListRequest(_messages.Message):
  r"""A CloudresourcemanagerTagBindingsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of TagBindings to return in the
      response. The server allows a maximum of 300 TagBindings to return. If
      unspecified, the server will use 100 as the default.
    pageToken: Optional. A pagination token returned from a previous call to
      `ListTagBindings` that indicates where this listing should continue
      from.
    parent: Required. The full resource name of a resource for which you want
      to list existing TagBindings. E.g.
      "//cloudresourcemanager.googleapis.com/projects/123"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class CloudresourcemanagerTagKeysCreateRequest(_messages.Message):
  r"""A CloudresourcemanagerTagKeysCreateRequest object.

  Fields:
    tagKey: A TagKey resource to be passed as the request body.
    validateOnly: Optional. Set to true to perform validations necessary for
      creating the resource, but not actually perform the action.
  """

  tagKey = _messages.MessageField('TagKey', 1)
  validateOnly = _messages.BooleanField(2)


class CloudresourcemanagerTagKeysDeleteRequest(_messages.Message):
  r"""A CloudresourcemanagerTagKeysDeleteRequest object.

  Fields:
    etag: Optional. The etag known to the client for the expected state of the
      TagKey. This is to be used for optimistic concurrency.
    name: Required. The resource name of a TagKey to be deleted in the format
      `tagKeys/123`. The TagKey cannot be a parent of any existing TagValues
      or it will not be deleted successfully.
    validateOnly: Optional. Set as true to perform validations necessary for
      deletion, but not actually perform the action.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class CloudresourcemanagerTagKeysGetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerTagKeysGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class CloudresourcemanagerTagKeysGetNamespacedRequest(_messages.Message):
  r"""A CloudresourcemanagerTagKeysGetNamespacedRequest object.

  Fields:
    name: Required. A namespaced tag key name in the format
      `{parentId}/{tagKeyShort}`, such as `42/foo` for a key with short name
      "foo" under the organization with ID 42 or `r2-d2/bar` for a key with
      short name "bar" under the project `r2-d2`.
  """

  name = _messages.StringField(1)


class CloudresourcemanagerTagKeysGetRequest(_messages.Message):
  r"""A CloudresourcemanagerTagKeysGetRequest object.

  Fields:
    name: Required. A resource name in the format `tagKeys/{id}`, such as
      `tagKeys/123`.
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerTagKeysListRequest(_messages.Message):
  r"""A CloudresourcemanagerTagKeysListRequest object.

  Fields:
    pageSize: Optional. The maximum number of TagKeys to return in the
      response. The server allows a maximum of 300 TagKeys to return. If
      unspecified, the server will use 100 as the default.
    pageToken: Optional. A pagination token returned from a previous call to
      `ListTagKey` that indicates where this listing should continue from.
    parent: Required. The resource name of the TagKey's parent. Must be of the
      form `organizations/{org_id}` or `projects/{project_id}` or
      `projects/{project_number}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class CloudresourcemanagerTagKeysPatchRequest(_messages.Message):
  r"""A CloudresourcemanagerTagKeysPatchRequest object.

  Fields:
    name: Immutable. The resource name for a TagKey. Must be in the format
      `tagKeys/{tag_key_id}`, where `tag_key_id` is the generated numeric id
      for the TagKey.
    tagKey: A TagKey resource to be passed as the request body.
    updateMask: Fields to be updated. The mask may only contain `description`
      or `etag`. If omitted entirely, both `description` and `etag` are
      assumed to be significant.
    validateOnly: Set as true to perform validations necessary for updating
      the resource, but not actually perform the action.
  """

  name = _messages.StringField(1, required=True)
  tagKey = _messages.MessageField('TagKey', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CloudresourcemanagerTagKeysSetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerTagKeysSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudresourcemanagerTagKeysTestIamPermissionsRequest(_messages.Message):
  r"""A CloudresourcemanagerTagKeysTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudresourcemanagerTagValuesCreateRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesCreateRequest object.

  Fields:
    tagValue: A TagValue resource to be passed as the request body.
    validateOnly: Optional. Set as true to perform the validations necessary
      for creating the resource, but not actually perform the action.
  """

  tagValue = _messages.MessageField('TagValue', 1)
  validateOnly = _messages.BooleanField(2)


class CloudresourcemanagerTagValuesDeleteRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesDeleteRequest object.

  Fields:
    etag: Optional. The etag known to the client for the expected state of the
      TagValue. This is to be used for optimistic concurrency.
    name: Required. Resource name for TagValue to be deleted in the format
      tagValues/456.
    validateOnly: Optional. Set as true to perform the validations necessary
      for deletion, but not actually perform the action.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class CloudresourcemanagerTagValuesGetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class CloudresourcemanagerTagValuesGetNamespacedRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesGetNamespacedRequest object.

  Fields:
    name: Required. A namespaced tag value name in the following format:
      `{parentId}/{tagKeyShort}/{tagValueShort}` Examples: - `42/foo/abc` for
      a value with short name "abc" under the key with short name "foo" under
      the organization with ID 42 - `r2-d2/bar/xyz` for a value with short
      name "xyz" under the key with short name "bar" under the project with ID
      "r2-d2"
  """

  name = _messages.StringField(1)


class CloudresourcemanagerTagValuesGetRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesGetRequest object.

  Fields:
    name: Required. Resource name for TagValue to be fetched in the format
      `tagValues/456`.
  """

  name = _messages.StringField(1, required=True)


class CloudresourcemanagerTagValuesListRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of TagValues to return in the
      response. The server allows a maximum of 300 TagValues to return. If
      unspecified, the server will use 100 as the default.
    pageToken: Optional. A pagination token returned from a previous call to
      `ListTagValues` that indicates where this listing should continue from.
    parent: Required. Resource name for the parent of the TagValues to be
      listed, in the format `tagKeys/123` or `tagValues/123`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class CloudresourcemanagerTagValuesPatchRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesPatchRequest object.

  Fields:
    name: Immutable. Resource name for TagValue in the format `tagValues/456`.
    tagValue: A TagValue resource to be passed as the request body.
    updateMask: Optional. Fields to be updated.
    validateOnly: Optional. True to perform validations necessary for updating
      the resource, but not actually perform the action.
  """

  name = _messages.StringField(1, required=True)
  tagValue = _messages.MessageField('TagValue', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class CloudresourcemanagerTagValuesSetIamPolicyRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudresourcemanagerTagValuesTagHoldsCreateRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesTagHoldsCreateRequest object.

  Fields:
    parent: Required. The resource name of the TagHold's parent TagValue. Must
      be of the form: `tagValues/{tag-value-id}`.
    tagHold: A TagHold resource to be passed as the request body.
    validateOnly: Optional. Set to true to perform the validations necessary
      for creating the resource, but not actually perform the action.
  """

  parent = _messages.StringField(1, required=True)
  tagHold = _messages.MessageField('TagHold', 2)
  validateOnly = _messages.BooleanField(3)


class CloudresourcemanagerTagValuesTagHoldsDeleteRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesTagHoldsDeleteRequest object.

  Fields:
    name: Required. The resource name of the TagHold to delete. Must be of the
      form: `tagValues/{tag-value-id}/tagHolds/{tag-hold-id}`.
    validateOnly: Optional. Set to true to perform the validations necessary
      for deleting the resource, but not actually perform the action.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class CloudresourcemanagerTagValuesTagHoldsListRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesTagHoldsListRequest object.

  Fields:
    filter: Optional. Criteria used to select a subset of TagHolds parented by
      the TagValue to return. This field follows the syntax defined by
      aip.dev/160; the `holder` and `origin` fields are supported for
      filtering. Currently only `AND` syntax is supported. Some example
      queries are: * `holder =
      //compute.googleapis.com/compute/projects/myproject/regions/us-
      east-1/instanceGroupManagers/instance-group` * `origin = 35678234` *
      `holder =
      //compute.googleapis.com/compute/projects/myproject/regions/us-
      east-1/instanceGroupManagers/instance-group AND origin = 35678234`
    pageSize: Optional. The maximum number of TagHolds to return in the
      response. The server allows a maximum of 300 TagHolds to return. If
      unspecified, the server will use 100 as the default.
    pageToken: Optional. A pagination token returned from a previous call to
      `ListTagHolds` that indicates where this listing should continue from.
    parent: Required. The resource name of the parent TagValue. Must be of the
      form: `tagValues/{tag-value-id}`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class CloudresourcemanagerTagValuesTestIamPermissionsRequest(_messages.Message):
  r"""A CloudresourcemanagerTagValuesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CreateFolderMetadata(_messages.Message):
  r"""Metadata pertaining to the Folder creation process.

  Fields:
    displayName: The display name of the folder.
    parent: The resource name of the folder or organization we are creating
      the folder under.
  """

  displayName = _messages.StringField(1)
  parent = _messages.StringField(2)


class CreateProjectMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the Operation
  returned by CreateProject. It provides insight for when significant phases
  of Project creation have completed.

  Fields:
    createTime: Creation time of the project creation workflow.
    gettable: True if the project can be retrieved using `GetProject`. No
      other operations on the project are guaranteed to work until the project
      creation is complete.
    ready: True if the project creation process is complete.
  """

  createTime = _messages.StringField(1)
  gettable = _messages.BooleanField(2)
  ready = _messages.BooleanField(3)


class CreateTagBindingMetadata(_messages.Message):
  r"""Runtime operation information for creating a TagValue."""


class CreateTagKeyMetadata(_messages.Message):
  r"""Runtime operation information for creating a TagKey."""


class CreateTagValueMetadata(_messages.Message):
  r"""Runtime operation information for creating a TagValue."""


class DeleteFolderMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the
  `Operation` returned by `DeleteFolder`.
  """



class DeleteOrganizationMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the operation
  returned by DeleteOrganization.
  """



class DeleteProjectMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the Operation
  returned by `DeleteProject`.
  """



class DeleteTagBindingMetadata(_messages.Message):
  r"""Runtime operation information for deleting a TagBinding."""


class DeleteTagKeyMetadata(_messages.Message):
  r"""Runtime operation information for deleting a TagKey."""


class DeleteTagValueMetadata(_messages.Message):
  r"""Runtime operation information for deleting a TagValue."""


class EffectiveTag(_messages.Message):
  r"""An EffectiveTag represents a tag that applies to a resource during
  policy evaluation. Tags can be either directly bound to a resource or
  inherited from its ancestor. EffectiveTag contains the name and
  namespaced_name of the tag value and tag key, with additional fields of
  `inherited` to indicate the inheritance status of the effective tag.

  Fields:
    inherited: Indicates the inheritance status of a tag value attached to the
      given resource. If the tag value is inherited from one of the resource's
      ancestors, inherited will be true. If false, then the tag value is
      directly attached to the resource, inherited will be false.
    namespacedTagKey: The namespaced name of the TagKey. Can be in the form
      `{organization_id}/{tag_key_short_name}` or
      `{project_id}/{tag_key_short_name}` or
      `{project_number}/{tag_key_short_name}`.
    namespacedTagValue: The namespaced name of the TagValue. Can be in the
      form `{organization_id}/{tag_key_short_name}/{tag_value_short_name}` or
      `{project_id}/{tag_key_short_name}/{tag_value_short_name}` or
      `{project_number}/{tag_key_short_name}/{tag_value_short_name}`.
    tagKey: The name of the TagKey, in the format `tagKeys/{id}`, such as
      `tagKeys/123`.
    tagKeyParentName: The parent name of the tag key. Must be in the format
      `organizations/{organization_id}` or `projects/{project_number}`
    tagValue: Resource name for TagValue in the format `tagValues/456`.
  """

  inherited = _messages.BooleanField(1)
  namespacedTagKey = _messages.StringField(2)
  namespacedTagValue = _messages.StringField(3)
  tagKey = _messages.StringField(4)
  tagKeyParentName = _messages.StringField(5)
  tagValue = _messages.StringField(6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Folder(_messages.Message):
  r"""A folder in an organization's resource hierarchy, used to organize that
  organization's resources.

  Enums:
    StateValueValuesEnum: Output only. The lifecycle state of the folder.
      Updates to the state must be performed using DeleteFolder and
      UndeleteFolder.

  Fields:
    createTime: Output only. Timestamp when the folder was created.
    deleteTime: Output only. Timestamp when the folder was requested to be
      deleted.
    displayName: The folder's display name. A folder's display name must be
      unique amongst its siblings. For example, no two folders with the same
      parent can share the same display name. The display name must start and
      end with a letter or digit, may contain letters, digits, spaces, hyphens
      and underscores and can be no longer than 30 characters. This is
      captured by the regular expression: `[\p{L}\p{N}]([\p{L}\p{N}_-
      ]{0,28}[\p{L}\p{N}])?`.
    etag: Output only. A checksum computed by the server based on the current
      value of the folder resource. This may be sent on update and delete
      requests to ensure the client has an up-to-date value before proceeding.
    name: Output only. The resource name of the folder. Its format is
      `folders/{folder_id}`, for example: "folders/1234".
    parent: Required. The folder's parent's resource name. Updates to the
      folder's parent must be performed using MoveFolder.
    state: Output only. The lifecycle state of the folder. Updates to the
      state must be performed using DeleteFolder and UndeleteFolder.
    updateTime: Output only. Timestamp when the folder was last modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The lifecycle state of the folder. Updates to the state
    must be performed using DeleteFolder and UndeleteFolder.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The normal and active state.
      DELETE_REQUESTED: The folder has been marked for deletion by the user.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETE_REQUESTED = 2

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  etag = _messages.StringField(4)
  name = _messages.StringField(5)
  parent = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class FolderOperation(_messages.Message):
  r"""Metadata describing a long running folder operation

  Enums:
    OperationTypeValueValuesEnum: The type of this operation.

  Fields:
    destinationParent: The resource name of the folder or organization we are
      either creating the folder under or moving the folder to.
    displayName: The display name of the folder.
    operationType: The type of this operation.
    sourceParent: The resource name of the folder's parent. Only applicable
      when the operation_type is MOVE.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""The type of this operation.

    Values:
      OPERATION_TYPE_UNSPECIFIED: Operation type not specified.
      CREATE: A create folder operation.
      MOVE: A move folder operation.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    CREATE = 1
    MOVE = 2

  destinationParent = _messages.StringField(1)
  displayName = _messages.StringField(2)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 3)
  sourceParent = _messages.StringField(4)


class FolderOperationError(_messages.Message):
  r"""A classification of the Folder Operation error.

  Enums:
    ErrorMessageIdValueValuesEnum: The type of operation error experienced.

  Fields:
    errorMessageId: The type of operation error experienced.
  """

  class ErrorMessageIdValueValuesEnum(_messages.Enum):
    r"""The type of operation error experienced.

    Values:
      ERROR_TYPE_UNSPECIFIED: The error type was unrecognized or unspecified.
      ACTIVE_FOLDER_HEIGHT_VIOLATION: The attempted action would violate the
        max folder depth constraint.
      MAX_CHILD_FOLDERS_VIOLATION: The attempted action would violate the max
        child folders constraint.
      FOLDER_NAME_UNIQUENESS_VIOLATION: The attempted action would violate the
        locally-unique folder display_name constraint.
      RESOURCE_DELETED_VIOLATION: The resource being moved has been deleted.
      PARENT_DELETED_VIOLATION: The resource a folder was being added to has
        been deleted.
      CYCLE_INTRODUCED_VIOLATION: The attempted action would introduce cycle
        in resource path.
      FOLDER_BEING_MOVED_VIOLATION: The attempted action would move a folder
        that is already being moved.
      FOLDER_TO_DELETE_NON_EMPTY_VIOLATION: The folder the caller is trying to
        delete contains active resources.
      DELETED_FOLDER_HEIGHT_VIOLATION: The attempted action would violate the
        max deleted folder depth constraint.
    """
    ERROR_TYPE_UNSPECIFIED = 0
    ACTIVE_FOLDER_HEIGHT_VIOLATION = 1
    MAX_CHILD_FOLDERS_VIOLATION = 2
    FOLDER_NAME_UNIQUENESS_VIOLATION = 3
    RESOURCE_DELETED_VIOLATION = 4
    PARENT_DELETED_VIOLATION = 5
    CYCLE_INTRODUCED_VIOLATION = 6
    FOLDER_BEING_MOVED_VIOLATION = 7
    FOLDER_TO_DELETE_NON_EMPTY_VIOLATION = 8
    DELETED_FOLDER_HEIGHT_VIOLATION = 9

  errorMessageId = _messages.EnumField('ErrorMessageIdValueValuesEnum', 1)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class Lien(_messages.Message):
  r"""A Lien represents an encumbrance on the actions that can be performed on
  a resource.

  Fields:
    createTime: The creation time of this Lien.
    name: A system-generated unique identifier for this Lien. Example:
      `liens/1234abcd`
    origin: A stable, user-visible/meaningful string identifying the origin of
      the Lien, intended to be inspected programmatically. Maximum length of
      200 characters. Example: 'compute.googleapis.com'
    parent: A reference to the resource this Lien is attached to. The server
      will validate the parent against those for which Liens are supported.
      Example: `projects/1234`
    reason: Concise user-visible strings indicating why an action cannot be
      performed on a resource. Maximum length of 200 characters. Example:
      'Holds production API key'
    restrictions: The types of operations which should be blocked as a result
      of this Lien. Each value should correspond to an IAM permission. The
      server will validate the permissions against those for which Liens are
      supported. An empty list is meaningless and will be rejected. Example:
      ['resourcemanager.projects.delete']
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  origin = _messages.StringField(3)
  parent = _messages.StringField(4)
  reason = _messages.StringField(5)
  restrictions = _messages.StringField(6, repeated=True)


class ListEffectiveTagsResponse(_messages.Message):
  r"""The response of ListEffectiveTags.

  Fields:
    effectiveTags: A possibly paginated list of effective tags for the
      specified resource.
    nextPageToken: Pagination token. If the result set is too large to fit in
      a single response, this token is returned. It encodes the position of
      the current result cursor. Feeding this value into a new list request
      with the `page_token` parameter gives the next page of the results. When
      `next_page_token` is not filled in, there is no next page and the list
      returned is the last page in the result set. Pagination tokens have a
      limited lifetime.
  """

  effectiveTags = _messages.MessageField('EffectiveTag', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListFoldersResponse(_messages.Message):
  r"""The ListFolders response message.

  Fields:
    folders: A possibly paginated list of folders that are direct descendants
      of the specified parent resource.
    nextPageToken: A pagination token returned from a previous call to
      `ListFolders` that indicates from where listing should continue.
  """

  folders = _messages.MessageField('Folder', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLiensResponse(_messages.Message):
  r"""The response message for Liens.ListLiens.

  Fields:
    liens: A list of Liens.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  liens = _messages.MessageField('Lien', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListProjectsResponse(_messages.Message):
  r"""A page of the response received from the ListProjects method. A
  paginated response where more pages are available has `next_page_token` set.
  This token can be used in a subsequent request to retrieve the next request
  page. NOTE: A response may contain fewer elements than the request
  `page_size` and still have a `next_page_token`.

  Fields:
    nextPageToken: Pagination token. If the result set is too large to fit in
      a single response, this token is returned. It encodes the position of
      the current result cursor. Feeding this value into a new list request
      with the `page_token` parameter gives the next page of the results. When
      `next_page_token` is not filled in, there is no next page and the list
      returned is the last page in the result set. Pagination tokens have a
      limited lifetime.
    projects: The list of Projects under the parent. This list can be
      paginated.
  """

  nextPageToken = _messages.StringField(1)
  projects = _messages.MessageField('Project', 2, repeated=True)


class ListTagBindingsResponse(_messages.Message):
  r"""The ListTagBindings response.

  Fields:
    nextPageToken: Pagination token. If the result set is too large to fit in
      a single response, this token is returned. It encodes the position of
      the current result cursor. Feeding this value into a new list request
      with the `page_token` parameter gives the next page of the results. When
      `next_page_token` is not filled in, there is no next page and the list
      returned is the last page in the result set. Pagination tokens have a
      limited lifetime.
    tagBindings: A possibly paginated list of TagBindings for the specified
      resource.
  """

  nextPageToken = _messages.StringField(1)
  tagBindings = _messages.MessageField('TagBinding', 2, repeated=True)


class ListTagHoldsResponse(_messages.Message):
  r"""The ListTagHolds response.

  Fields:
    nextPageToken: Pagination token. If the result set is too large to fit in
      a single response, this token is returned. It encodes the position of
      the current result cursor. Feeding this value into a new list request
      with the `page_token` parameter gives the next page of the results. When
      `next_page_token` is not filled in, there is no next page and the list
      returned is the last page in the result set. Pagination tokens have a
      limited lifetime.
    tagHolds: A possibly paginated list of TagHolds.
  """

  nextPageToken = _messages.StringField(1)
  tagHolds = _messages.MessageField('TagHold', 2, repeated=True)


class ListTagKeysResponse(_messages.Message):
  r"""The ListTagKeys response message.

  Fields:
    nextPageToken: A pagination token returned from a previous call to
      `ListTagKeys` that indicates from where listing should continue.
    tagKeys: List of TagKeys that live under the specified parent in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  tagKeys = _messages.MessageField('TagKey', 2, repeated=True)


class ListTagValuesResponse(_messages.Message):
  r"""The ListTagValues response.

  Fields:
    nextPageToken: A pagination token returned from a previous call to
      `ListTagValues` that indicates from where listing should continue. This
      is currently not used, but the server may at any point start supplying a
      valid token.
    tagValues: A possibly paginated list of TagValues that are direct
      descendants of the specified parent TagKey.
  """

  nextPageToken = _messages.StringField(1)
  tagValues = _messages.MessageField('TagValue', 2, repeated=True)


class MoveFolderMetadata(_messages.Message):
  r"""Metadata pertaining to the folder move process.

  Fields:
    destinationParent: The resource name of the folder or organization to move
      the folder to.
    displayName: The display name of the folder.
    sourceParent: The resource name of the folder's parent.
  """

  destinationParent = _messages.StringField(1)
  displayName = _messages.StringField(2)
  sourceParent = _messages.StringField(3)


class MoveFolderRequest(_messages.Message):
  r"""The MoveFolder request message.

  Fields:
    destinationParent: Required. The resource name of the folder or
      organization which should be the folder's new parent. Must be of the
      form `folders/{folder_id}` or `organizations/{org_id}`.
  """

  destinationParent = _messages.StringField(1)


class MoveProjectMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the Operation
  returned by MoveProject.
  """



class MoveProjectRequest(_messages.Message):
  r"""The request sent to MoveProject method.

  Fields:
    destinationParent: Required. The new parent to move the Project under.
  """

  destinationParent = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class Organization(_messages.Message):
  r"""The root node in the resource hierarchy to which a particular entity's
  (a company, for example) resources belong.

  Enums:
    StateValueValuesEnum: Output only. The organization's current lifecycle
      state.

  Fields:
    createTime: Output only. Timestamp when the Organization was created.
    deleteTime: Output only. Timestamp when the Organization was requested for
      deletion.
    directoryCustomerId: Immutable. The G Suite / Workspace customer id used
      in the Directory API.
    displayName: Output only. A human-readable string that refers to the
      organization in the Google Cloud Console. This string is set by the
      server and cannot be changed. The string will be set to the primary
      domain (for example, "google.com") of the Google Workspace customer that
      owns the organization.
    etag: Output only. A checksum computed by the server based on the current
      value of the Organization resource. This may be sent on update and
      delete requests to ensure the client has an up-to-date value before
      proceeding.
    name: Output only. The resource name of the organization. This is the
      organization's relative path in the API. Its format is
      "organizations/[organization_id]". For example, "organizations/1234".
    state: Output only. The organization's current lifecycle state.
    updateTime: Output only. Timestamp when the Organization was last
      modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The organization's current lifecycle state.

    Values:
      STATE_UNSPECIFIED: Unspecified state. This is only useful for
        distinguishing unset values.
      ACTIVE: The normal and active state.
      DELETE_REQUESTED: The organization has been marked for deletion by the
        user.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETE_REQUESTED = 2

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  directoryCustomerId = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Project(_messages.Message):
  r"""A project is a high-level Google Cloud entity. It is a container for
  ACLs, APIs, App Engine Apps, VMs, and other Google Cloud Platform resources.

  Enums:
    StateValueValuesEnum: Output only. The project lifecycle state.

  Messages:
    LabelsValue: Optional. The labels associated with this project. Label keys
      must be between 1 and 63 characters long and must conform to the
      following regular expression: \[a-z\](\[-a-z0-9\]*\[a-z0-9\])?. Label
      values must be between 0 and 63 characters long and must conform to the
      regular expression (\[a-z\](\[-a-z0-9\]*\[a-z0-9\])?)?. No more than 64
      labels can be associated with a given resource. Clients should store
      labels in a representation such as JSON that does not depend on specific
      characters being disallowed. Example: `"myBusinessDimension" :
      "businessValue"`

  Fields:
    createTime: Output only. Creation time.
    deleteTime: Output only. The time at which this resource was requested for
      deletion.
    displayName: Optional. A user-assigned display name of the project. When
      present it must be between 4 to 30 characters. Allowed characters are:
      lowercase and uppercase letters, numbers, hyphen, single-quote, double-
      quote, space, and exclamation point. Example: `My Project`
    etag: Output only. A checksum computed by the server based on the current
      value of the Project resource. This may be sent on update and delete
      requests to ensure the client has an up-to-date value before proceeding.
    labels: Optional. The labels associated with this project. Label keys must
      be between 1 and 63 characters long and must conform to the following
      regular expression: \[a-z\](\[-a-z0-9\]*\[a-z0-9\])?. Label values must
      be between 0 and 63 characters long and must conform to the regular
      expression (\[a-z\](\[-a-z0-9\]*\[a-z0-9\])?)?. No more than 64 labels
      can be associated with a given resource. Clients should store labels in
      a representation such as JSON that does not depend on specific
      characters being disallowed. Example: `"myBusinessDimension" :
      "businessValue"`
    name: Output only. The unique resource name of the project. It is an int64
      generated number prefixed by "projects/". Example:
      `projects/415104041262`
    parent: Optional. A reference to a parent Resource. eg.,
      `organizations/123` or `folders/876`.
    projectId: Immutable. The unique, user-assigned id of the project. It must
      be 6 to 30 lowercase ASCII letters, digits, or hyphens. It must start
      with a letter. Trailing hyphens are prohibited. Example: `tokyo-
      rain-123`
    state: Output only. The project lifecycle state.
    updateTime: Output only. The most recent time this resource was modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The project lifecycle state.

    Values:
      STATE_UNSPECIFIED: Unspecified state. This is only used/useful for
        distinguishing unset values.
      ACTIVE: The normal and active state.
      DELETE_REQUESTED: The project has been marked for deletion by the user
        (by invoking DeleteProject) or by the system (Google Cloud Platform).
        This can generally be reversed by invoking UndeleteProject.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETE_REQUESTED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels associated with this project. Label keys must be
    between 1 and 63 characters long and must conform to the following regular
    expression: \[a-z\](\[-a-z0-9\]*\[a-z0-9\])?. Label values must be between
    0 and 63 characters long and must conform to the regular expression
    (\[a-z\](\[-a-z0-9\]*\[a-z0-9\])?)?. No more than 64 labels can be
    associated with a given resource. Clients should store labels in a
    representation such as JSON that does not depend on specific characters
    being disallowed. Example: `"myBusinessDimension" : "businessValue"`

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  parent = _messages.StringField(7)
  projectId = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  updateTime = _messages.StringField(10)


class ProjectCreationStatus(_messages.Message):
  r"""A status object which is used as the `metadata` field for the Operation
  returned by CreateProject. It provides insight for when significant phases
  of Project creation have completed.

  Fields:
    createTime: Creation time of the project creation workflow.
    gettable: True if the project can be retrieved using GetProject. No other
      operations on the project are guaranteed to work until the project
      creation is complete.
    ready: True if the project creation process is complete.
  """

  createTime = _messages.StringField(1)
  gettable = _messages.BooleanField(2)
  ready = _messages.BooleanField(3)


class SearchFoldersResponse(_messages.Message):
  r"""The response message for searching folders.

  Fields:
    folders: A possibly paginated folder search results. the specified parent
      resource.
    nextPageToken: A pagination token returned from a previous call to
      `SearchFolders` that indicates from where searching should continue.
  """

  folders = _messages.MessageField('Folder', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SearchOrganizationsResponse(_messages.Message):
  r"""The response returned from the `SearchOrganizations` method.

  Fields:
    nextPageToken: A pagination token to be used to retrieve the next page of
      results. If the result is too large to fit within the page size
      specified in the request, this field will be set with a token that can
      be used to fetch the next page of results. If this field is empty, it
      indicates that this response contains the last page of results.
    organizations: The list of Organizations that matched the search query,
      possibly paginated.
  """

  nextPageToken = _messages.StringField(1)
  organizations = _messages.MessageField('Organization', 2, repeated=True)


class SearchProjectsResponse(_messages.Message):
  r"""A page of the response received from the SearchProjects method. A
  paginated response where more pages are available has `next_page_token` set.
  This token can be used in a subsequent request to retrieve the next request
  page.

  Fields:
    nextPageToken: Pagination token. If the result set is too large to fit in
      a single response, this token is returned. It encodes the position of
      the current result cursor. Feeding this value into a new list request
      with the `page_token` parameter gives the next page of the results. When
      `next_page_token` is not filled in, there is no next page and the list
      returned is the last page in the result set. Pagination tokens have a
      limited lifetime.
    projects: The list of Projects that matched the list filter query. This
      list can be paginated.
  """

  nextPageToken = _messages.StringField(1)
  projects = _messages.MessageField('Project', 2, repeated=True)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TagBinding(_messages.Message):
  r"""A TagBinding represents a connection between a TagValue and a cloud
  resource Once a TagBinding is created, the TagValue is applied to all the
  descendants of the Google Cloud resource.

  Fields:
    name: Output only. The name of the TagBinding. This is a String of the
      form: `tagBindings/{full-resource-name}/{tag-value-name}` (e.g. `tagBind
      ings/%2F%2Fcloudresourcemanager.googleapis.com%2Fprojects%2F123/tagValue
      s/456`).
    parent: The full resource name of the resource the TagValue is bound to.
      E.g. `//cloudresourcemanager.googleapis.com/projects/123`
    tagValue: The TagValue of the TagBinding. Must be of the form
      `tagValues/456`.
    tagValueNamespacedName: The namespaced name for the TagValue of the
      TagBinding. Must be in the format
      `{parent_id}/{tag_key_short_name}/{short_name}`. For methods that
      support TagValue namespaced name, only one of tag_value_namespaced_name
      or tag_value may be filled. Requests with both fields will be rejected.
  """

  name = _messages.StringField(1)
  parent = _messages.StringField(2)
  tagValue = _messages.StringField(3)
  tagValueNamespacedName = _messages.StringField(4)


class TagHold(_messages.Message):
  r"""A TagHold represents the use of a TagValue that is not captured by
  TagBindings. If a TagValue has any TagHolds, deletion will be blocked. This
  resource is intended to be created in the same cloud location as the
  `holder`.

  Fields:
    createTime: Output only. The time this TagHold was created.
    helpLink: Optional. A URL where an end user can learn more about removing
      this hold. E.g. `https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing`
    holder: Required. The name of the resource where the TagValue is being
      used. Must be less than 200 characters. E.g.
      `//compute.googleapis.com/compute/projects/myproject/regions/us-
      east-1/instanceGroupManagers/instance-group`
    name: Output only. The resource name of a TagHold. This is a String of the
      form: `tagValues/{tag-value-id}/tagHolds/{tag-hold-id}` (e.g.
      `tagValues/123/tagHolds/456`). This resource name is generated by the
      server.
    origin: Optional. An optional string representing the origin of this
      request. This field should include human-understandable information to
      distinguish origins from each other. Must be less than 200 characters.
      E.g. `migs-35678234`
  """

  createTime = _messages.StringField(1)
  helpLink = _messages.StringField(2)
  holder = _messages.StringField(3)
  name = _messages.StringField(4)
  origin = _messages.StringField(5)


class TagKey(_messages.Message):
  r"""A TagKey, used to group a set of TagValues.

  Enums:
    PurposeValueValuesEnum: Optional. A purpose denotes that this Tag is
      intended for use in policies of a specific policy engine, and will
      involve that policy engine in management operations involving this Tag.
      A purpose does not grant a policy engine exclusive rights to the Tag,
      and it may be referenced by other policy engines. A purpose cannot be
      changed once set.

  Messages:
    PurposeDataValue: Optional. Purpose data corresponds to the policy system
      that the tag is intended for. See documentation for `Purpose` for
      formatting of this field. Purpose data cannot be changed once set.

  Fields:
    createTime: Output only. Creation time.
    description: Optional. User-assigned description of the TagKey. Must not
      exceed 256 characters. Read-write.
    etag: Optional. Entity tag which users can pass to prevent race
      conditions. This field is always set in server responses. See
      UpdateTagKeyRequest for details.
    name: Immutable. The resource name for a TagKey. Must be in the format
      `tagKeys/{tag_key_id}`, where `tag_key_id` is the generated numeric id
      for the TagKey.
    namespacedName: Output only. Immutable. Namespaced name of the TagKey.
    parent: Immutable. The resource name of the TagKey's parent. A TagKey can
      be parented by an Organization or a Project. For a TagKey parented by an
      Organization, its parent must be in the form `organizations/{org_id}`.
      For a TagKey parented by a Project, its parent can be in the form
      `projects/{project_id}` or `projects/{project_number}`.
    purpose: Optional. A purpose denotes that this Tag is intended for use in
      policies of a specific policy engine, and will involve that policy
      engine in management operations involving this Tag. A purpose does not
      grant a policy engine exclusive rights to the Tag, and it may be
      referenced by other policy engines. A purpose cannot be changed once
      set.
    purposeData: Optional. Purpose data corresponds to the policy system that
      the tag is intended for. See documentation for `Purpose` for formatting
      of this field. Purpose data cannot be changed once set.
    shortName: Required. Immutable. The user friendly name for a TagKey. The
      short name should be unique for TagKeys within the same tag namespace.
      The short name must be 1-63 characters, beginning and ending with an
      alphanumeric character ([a-z0-9A-Z]) with dashes (-), underscores (_),
      dots (.), and alphanumerics between.
    updateTime: Output only. Update time.
  """

  class PurposeValueValuesEnum(_messages.Enum):
    r"""Optional. A purpose denotes that this Tag is intended for use in
    policies of a specific policy engine, and will involve that policy engine
    in management operations involving this Tag. A purpose does not grant a
    policy engine exclusive rights to the Tag, and it may be referenced by
    other policy engines. A purpose cannot be changed once set.

    Values:
      PURPOSE_UNSPECIFIED: Unspecified purpose.
      GCE_FIREWALL: Purpose for Compute Engine firewalls. A corresponding
        `purpose_data` should be set for the network the tag is intended for.
        The key should be `network` and the value should be in ## either of
        these two formats: `https://www.googleapis.com/compute/{compute_versio
        n}/projects/{project_id}/global/networks/{network_id}` -
        `{project_id}/{network_name}` ## Examples:
        `https://www.googleapis.com/compute/staging_v1/projects/fail-closed-
        load-testing/global/networks/6992953698831725600` - `fail-closed-load-
        testing/load-testing-network`
      DATA_GOVERNANCE: Purpose for data governance. Tag Values created under a
        key with this purpose may have Tag Value children. No `purpose_data`
        should be set.
    """
    PURPOSE_UNSPECIFIED = 0
    GCE_FIREWALL = 1
    DATA_GOVERNANCE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PurposeDataValue(_messages.Message):
    r"""Optional. Purpose data corresponds to the policy system that the tag
    is intended for. See documentation for `Purpose` for formatting of this
    field. Purpose data cannot be changed once set.

    Messages:
      AdditionalProperty: An additional property for a PurposeDataValue
        object.

    Fields:
      additionalProperties: Additional properties of type PurposeDataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PurposeDataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4)
  namespacedName = _messages.StringField(5)
  parent = _messages.StringField(6)
  purpose = _messages.EnumField('PurposeValueValuesEnum', 7)
  purposeData = _messages.MessageField('PurposeDataValue', 8)
  shortName = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class TagValue(_messages.Message):
  r"""A TagValue is a child of a particular TagKey. This is used to group
  cloud resources for the purpose of controlling them using policies.

  Fields:
    createTime: Output only. Creation time.
    description: Optional. User-assigned description of the TagValue. Must not
      exceed 256 characters. Read-write.
    etag: Optional. Entity tag which users can pass to prevent race
      conditions. This field is always set in server responses. See
      UpdateTagValueRequest for details.
    name: Immutable. Resource name for TagValue in the format `tagValues/456`.
    namespacedName: Output only. The namespaced name of the TagValue. Can be
      in the form
      `{organization_id}/{tag_key_short_name}/{tag_value_short_name}` or
      `{project_id}/{tag_key_short_name}/{tag_value_short_name}` or
      `{project_number}/{tag_key_short_name}/{tag_value_short_name}`.
    parent: Immutable. The resource name of the new TagValue's parent TagKey.
      Must be of the form `tagKeys/{tag_key_id}`.
    shortName: Required. Immutable. User-assigned short name for TagValue. The
      short name should be unique for TagValues within the same parent TagKey.
      The short name must be 63 characters or less, beginning and ending with
      an alphanumeric character ([a-z0-9A-Z]) with dashes (-), underscores
      (_), dots (.), and alphanumerics between.
    updateTime: Output only. Update time.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4)
  namespacedName = _messages.StringField(5)
  parent = _messages.StringField(6)
  shortName = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UndeleteFolderMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the
  `Operation` returned by `UndeleteFolder`.
  """



class UndeleteFolderRequest(_messages.Message):
  r"""The UndeleteFolder request message."""


class UndeleteOrganizationMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the Operation
  returned by UndeleteOrganization.
  """



class UndeleteProjectMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the Operation
  returned by `UndeleteProject`.
  """



class UndeleteProjectRequest(_messages.Message):
  r"""The request sent to the UndeleteProject method."""


class UpdateFolderMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the Operation
  returned by UpdateFolder.
  """



class UpdateProjectMetadata(_messages.Message):
  r"""A status object which is used as the `metadata` field for the Operation
  returned by UpdateProject.
  """



class UpdateTagKeyMetadata(_messages.Message):
  r"""Runtime operation information for updating a TagKey."""


class UpdateTagValueMetadata(_messages.Message):
  r"""Runtime operation information for updating a TagValue."""


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated message classes for connectors version v1.

Enables users to create and manage connections to Google Cloud services and
third-party business applications using the Connectors interface.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'connectors'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AuthConfig(_messages.Message):
  r"""AuthConfig defines details of a authentication type.

  Enums:
    AuthTypeValueValuesEnum: The type of authentication configured.

  Fields:
    additionalVariables: List containing additional auth configs.
    authKey: Identifier key for auth config
    authType: The type of authentication configured.
    oauth2AuthCodeFlow: Oauth2AuthCodeFlow.
    oauth2ClientCredentials: Oauth2ClientCredentials.
    oauth2JwtBearer: Oauth2JwtBearer.
    sshPublicKey: SSH Public Key.
    userPassword: UserPassword.
  """

  class AuthTypeValueValuesEnum(_messages.Enum):
    r"""The type of authentication configured.

    Values:
      AUTH_TYPE_UNSPECIFIED: Authentication type not specified.
      USER_PASSWORD: Username and Password Authentication.
      OAUTH2_JWT_BEARER: JSON Web Token (JWT) Profile for Oauth 2.0
        Authorization Grant based authentication
      OAUTH2_CLIENT_CREDENTIALS: Oauth 2.0 Client Credentials Grant
        Authentication
      SSH_PUBLIC_KEY: SSH Public Key Authentication
      OAUTH2_AUTH_CODE_FLOW: Oauth 2.0 Authorization Code Flow
    """
    AUTH_TYPE_UNSPECIFIED = 0
    USER_PASSWORD = 1
    OAUTH2_JWT_BEARER = 2
    OAUTH2_CLIENT_CREDENTIALS = 3
    SSH_PUBLIC_KEY = 4
    OAUTH2_AUTH_CODE_FLOW = 5

  additionalVariables = _messages.MessageField('ConfigVariable', 1, repeated=True)
  authKey = _messages.StringField(2)
  authType = _messages.EnumField('AuthTypeValueValuesEnum', 3)
  oauth2AuthCodeFlow = _messages.MessageField('Oauth2AuthCodeFlow', 4)
  oauth2ClientCredentials = _messages.MessageField('Oauth2ClientCredentials', 5)
  oauth2JwtBearer = _messages.MessageField('Oauth2JwtBearer', 6)
  sshPublicKey = _messages.MessageField('SshPublicKey', 7)
  userPassword = _messages.MessageField('UserPassword', 8)


class AuthConfigTemplate(_messages.Message):
  r"""AuthConfigTemplate defines required field over an authentication type.

  Enums:
    AuthTypeValueValuesEnum: The type of authentication configured.

  Fields:
    authKey: Identifier key for auth config
    authType: The type of authentication configured.
    configVariableTemplates: Config variables to describe an `AuthConfig` for
      a `Connection`.
    description: Connector specific description for an authentication
      template.
    displayName: Display name for authentication template.
  """

  class AuthTypeValueValuesEnum(_messages.Enum):
    r"""The type of authentication configured.

    Values:
      AUTH_TYPE_UNSPECIFIED: Authentication type not specified.
      USER_PASSWORD: Username and Password Authentication.
      OAUTH2_JWT_BEARER: JSON Web Token (JWT) Profile for Oauth 2.0
        Authorization Grant based authentication
      OAUTH2_CLIENT_CREDENTIALS: Oauth 2.0 Client Credentials Grant
        Authentication
      SSH_PUBLIC_KEY: SSH Public Key Authentication
      OAUTH2_AUTH_CODE_FLOW: Oauth 2.0 Authorization Code Flow
    """
    AUTH_TYPE_UNSPECIFIED = 0
    USER_PASSWORD = 1
    OAUTH2_JWT_BEARER = 2
    OAUTH2_CLIENT_CREDENTIALS = 3
    SSH_PUBLIC_KEY = 4
    OAUTH2_AUTH_CODE_FLOW = 5

  authKey = _messages.StringField(1)
  authType = _messages.EnumField('AuthTypeValueValuesEnum', 2)
  configVariableTemplates = _messages.MessageField('ConfigVariableTemplate', 3, repeated=True)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)


class AuthorizationCodeLink(_messages.Message):
  r"""This configuration captures the details required to render an
  authorization link for the OAuth Authorization Code Flow.

  Fields:
    clientId: The client ID assigned to the Google Cloud Connectors OAuth app
      for the connector data source.
    enablePkce: Whether to enable PKCE for the auth code flow.
    scopes: The scopes for which the user will authorize Google Cloud
      Connectors on the connector data source.
    uri: The base URI the user must click to trigger the authorization code
      login flow.
  """

  clientId = _messages.StringField(1)
  enablePkce = _messages.BooleanField(2)
  scopes = _messages.StringField(3, repeated=True)
  uri = _messages.StringField(4)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ConfigVariable(_messages.Message):
  r"""ConfigVariable represents a configuration variable present in a
  Connection. or AuthConfig.

  Fields:
    boolValue: Value is a bool.
    encryptionKeyValue: Value is a Encryption Key.
    intValue: Value is an integer
    key: Key of the config variable.
    secretValue: Value is a secret.
    stringValue: Value is a string.
  """

  boolValue = _messages.BooleanField(1)
  encryptionKeyValue = _messages.MessageField('EncryptionKey', 2)
  intValue = _messages.IntegerField(3)
  key = _messages.StringField(4)
  secretValue = _messages.MessageField('Secret', 5)
  stringValue = _messages.StringField(6)


class ConfigVariableTemplate(_messages.Message):
  r"""ConfigVariableTemplate provides metadata about a `ConfigVariable` that
  is used in a Connection.

  Enums:
    StateValueValuesEnum: State of the config variable.
    ValueTypeValueValuesEnum: Type of the parameter: string, int, bool etc.
      consider custom type for the benefit for the validation.

  Fields:
    authorizationCodeLink: Authorization code link options. To be populated if
      `ValueType` is `AUTHORIZATION_CODE`
    description: Description.
    displayName: Display name of the parameter.
    enumOptions: Enum options. To be populated if `ValueType` is `ENUM`
    isAdvanced: Indicates if current template is part of advanced settings
    key: Key of the config variable.
    required: Flag represents that this `ConfigVariable` must be provided for
      a connection.
    requiredCondition: Condition under which a field would be required. The
      condition can be represented in the form of a logical expression.
    roleGrant: Role grant configuration for the config variable.
    state: State of the config variable.
    validationRegex: Regular expression in RE2 syntax used for validating the
      `value` of a `ConfigVariable`.
    valueType: Type of the parameter: string, int, bool etc. consider custom
      type for the benefit for the validation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the config variable.

    Values:
      STATE_UNSPECIFIED: Status is unspecified.
      ACTIVE: Config variable is active
      DEPRECATED: Config variable is deprecated.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DEPRECATED = 2

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""Type of the parameter: string, int, bool etc. consider custom type for
    the benefit for the validation.

    Values:
      VALUE_TYPE_UNSPECIFIED: Value type is not specified.
      STRING: Value type is string.
      INT: Value type is integer.
      BOOL: Value type is boolean.
      SECRET: Value type is secret.
      ENUM: Value type is enum.
      AUTHORIZATION_CODE: Value type is authorization code.
      ENCRYPTION_KEY: Encryption Key.
    """
    VALUE_TYPE_UNSPECIFIED = 0
    STRING = 1
    INT = 2
    BOOL = 3
    SECRET = 4
    ENUM = 5
    AUTHORIZATION_CODE = 6
    ENCRYPTION_KEY = 7

  authorizationCodeLink = _messages.MessageField('AuthorizationCodeLink', 1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  enumOptions = _messages.MessageField('EnumOption', 4, repeated=True)
  isAdvanced = _messages.BooleanField(5)
  key = _messages.StringField(6)
  required = _messages.BooleanField(7)
  requiredCondition = _messages.MessageField('LogicalExpression', 8)
  roleGrant = _messages.MessageField('RoleGrant', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  validationRegex = _messages.StringField(11)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 12)


class Connection(_messages.Message):
  r"""Connection represents an instance of connector.

  Enums:
    ConnectorVersionLaunchStageValueValuesEnum: Output only. Flag to mark the
      version indicating the launch stage.
    EventingEnablementTypeValueValuesEnum: Optional. Eventing enablement type.
      Will be nil if eventing is not enabled.
    SubscriptionTypeValueValuesEnum: Output only. This subscription type enum
      states the subscription type of the project.

  Messages:
    LabelsValue: Optional. Resource labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    authConfig: Optional. Configuration for establishing the connection's
      authentication with an external system.
    configVariables: Optional. Configuration for configuring the connection
      with an external system.
    connectionRevision: Output only. Connection revision. This field is only
      updated when the connection is created or updated by User.
    connectorVersion: Required. Connector version on which the connection is
      created. The format is:
      projects/*/locations/*/providers/*/connectors/*/versions/* Only global
      location is supported for ConnectorVersion resource.
    connectorVersionInfraConfig: Output only. Infra configs supported by
      Connector Version.
    connectorVersionLaunchStage: Output only. Flag to mark the version
      indicating the launch stage.
    createTime: Output only. Created time.
    description: Optional. Description of the resource.
    destinationConfigs: Optional. Configuration of the Connector's
      destination. Only accepted for Connectors that accepts user defined
      destination(s).
    envoyImageLocation: Output only. GCR location where the envoy image is
      stored. formatted like: gcr.io/{bucketName}/{imageName}
    eventingConfig: Optional. Eventing config of a connection
    eventingEnablementType: Optional. Eventing enablement type. Will be nil if
      eventing is not enabled.
    eventingRuntimeData: Output only. Eventing Runtime Data.
    imageLocation: Output only. GCR location where the runtime image is
      stored. formatted like: gcr.io/{bucketName}/{imageName}
    labels: Optional. Resource labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    lockConfig: Optional. Configuration that indicates whether or not the
      Connection can be edited.
    logConfig: Optional. Log configuration for the connection.
    name: Output only. Resource name of the Connection. Format:
      projects/{project}/locations/{location}/connections/{connection}
    nodeConfig: Optional. Node configuration for the connection.
    serviceAccount: Optional. Service account needed for runtime plane to
      access Google Cloud resources.
    serviceDirectory: Output only. The name of the Service Directory service
      name. Used for Private Harpoon to resolve the ILB address. e.g.
      "projects/cloud-connectors-e2e-testing/locations/us-
      central1/namespaces/istio-system/services/istio-ingressgateway-
      connectors"
    sslConfig: Optional. Ssl config of a connection
    status: Output only. Current status of the connection.
    subscriptionType: Output only. This subscription type enum states the
      subscription type of the project.
    suspended: Optional. Suspended indicates if a user has suspended a
      connection or not.
    updateTime: Output only. Updated time.
  """

  class ConnectorVersionLaunchStageValueValuesEnum(_messages.Enum):
    r"""Output only. Flag to mark the version indicating the launch stage.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: LAUNCH_STAGE_UNSPECIFIED.
      PREVIEW: PREVIEW.
      GA: GA.
      DEPRECATED: DEPRECATED.
      PRIVATE_PREVIEW: PRIVATE_PREVIEW.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    PREVIEW = 1
    GA = 2
    DEPRECATED = 3
    PRIVATE_PREVIEW = 4

  class EventingEnablementTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Eventing enablement type. Will be nil if eventing is not
    enabled.

    Values:
      EVENTING_ENABLEMENT_TYPE_UNSPECIFIED: Eventing Enablement Type
        Unspecifeied.
      EVENTING_AND_CONNECTION: Both connection and eventing.
      ONLY_EVENTING: Only Eventing.
    """
    EVENTING_ENABLEMENT_TYPE_UNSPECIFIED = 0
    EVENTING_AND_CONNECTION = 1
    ONLY_EVENTING = 2

  class SubscriptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. This subscription type enum states the subscription type
    of the project.

    Values:
      SUBSCRIPTION_TYPE_UNSPECIFIED: Unspecified subscription type.
      PAY_G: PayG subscription.
      PAID: Paid Subscription.
    """
    SUBSCRIPTION_TYPE_UNSPECIFIED = 0
    PAY_G = 1
    PAID = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user-provided metadata. Refer
    to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authConfig = _messages.MessageField('AuthConfig', 1)
  configVariables = _messages.MessageField('ConfigVariable', 2, repeated=True)
  connectionRevision = _messages.IntegerField(3)
  connectorVersion = _messages.StringField(4)
  connectorVersionInfraConfig = _messages.MessageField('ConnectorVersionInfraConfig', 5)
  connectorVersionLaunchStage = _messages.EnumField('ConnectorVersionLaunchStageValueValuesEnum', 6)
  createTime = _messages.StringField(7)
  description = _messages.StringField(8)
  destinationConfigs = _messages.MessageField('DestinationConfig', 9, repeated=True)
  envoyImageLocation = _messages.StringField(10)
  eventingConfig = _messages.MessageField('EventingConfig', 11)
  eventingEnablementType = _messages.EnumField('EventingEnablementTypeValueValuesEnum', 12)
  eventingRuntimeData = _messages.MessageField('EventingRuntimeData', 13)
  imageLocation = _messages.StringField(14)
  labels = _messages.MessageField('LabelsValue', 15)
  lockConfig = _messages.MessageField('LockConfig', 16)
  logConfig = _messages.MessageField('ConnectorsLogConfig', 17)
  name = _messages.StringField(18)
  nodeConfig = _messages.MessageField('NodeConfig', 19)
  serviceAccount = _messages.StringField(20)
  serviceDirectory = _messages.StringField(21)
  sslConfig = _messages.MessageField('SslConfig', 22)
  status = _messages.MessageField('ConnectionStatus', 23)
  subscriptionType = _messages.EnumField('SubscriptionTypeValueValuesEnum', 24)
  suspended = _messages.BooleanField(25)
  updateTime = _messages.StringField(26)


class ConnectionSchemaMetadata(_messages.Message):
  r"""ConnectionSchemaMetadata is the singleton resource of each connection.
  It includes the entity and action names of runtime resources exposed by a
  connection backend.

  Enums:
    StateValueValuesEnum: Output only. The current state of runtime schema.

  Fields:
    actions: Output only. List of actions.
    entities: Output only. List of entity names.
    name: Output only. Resource name. Format: projects/{project}/locations/{lo
      cation}/connections/{connection}/connectionSchemaMetadata
    refreshTime: Output only. Timestamp when the connection runtime schema
      refresh was triggered.
    state: Output only. The current state of runtime schema.
    updateTime: Output only. Timestamp when the connection runtime schema was
      updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of runtime schema.

    Values:
      STATE_UNSPECIFIED: Default state.
      REFRESHING: Schema refresh is in progress.
      UPDATED: Schema has been updated.
    """
    STATE_UNSPECIFIED = 0
    REFRESHING = 1
    UPDATED = 2

  actions = _messages.StringField(1, repeated=True)
  entities = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  refreshTime = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  updateTime = _messages.StringField(6)


class ConnectionStatus(_messages.Message):
  r"""ConnectionStatus indicates the state of the connection.

  Enums:
    StateValueValuesEnum: State.

  Fields:
    description: Description.
    state: State.
    status: Status provides detailed information for the state.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State.

    Values:
      STATE_UNSPECIFIED: Connection does not have a state yet.
      CREATING: Connection is being created.
      ACTIVE: Connection is running and ready for requests.
      INACTIVE: Connection is stopped.
      DELETING: Connection is being deleted.
      UPDATING: Connection is being updated.
      ERROR: Connection is not running due to an error.
      AUTHORIZATION_REQUIRED: Connection is not running because the
        authorization configuration is not complete.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    INACTIVE = 3
    DELETING = 4
    UPDATING = 5
    ERROR = 6
    AUTHORIZATION_REQUIRED = 7

  description = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  status = _messages.StringField(3)


class Connector(_messages.Message):
  r"""Connectors indicates a specific connector type, e.x. Salesforce, SAP
  etc.

  Enums:
    LaunchStageValueValuesEnum: Output only. Flag to mark the version
      indicating the launch stage.

  Messages:
    LabelsValue: Output only. Resource labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    createTime: Output only. Created time.
    description: Output only. Description of the resource.
    displayName: Output only. Display name.
    documentationUri: Output only. Link to documentation page.
    eventingDetails: Output only. Eventing details. Will be null if eventing
      is not supported.
    externalUri: Output only. Link to external page.
    labels: Output only. Resource labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    launchStage: Output only. Flag to mark the version indicating the launch
      stage.
    name: Output only. Resource name of the Connector. Format: projects/{proje
      ct}/locations/{location}/providers/{provider}/connectors/{connector}
      Only global location is supported for Connector resource.
    updateTime: Output only. Updated time.
    webAssetsLocation: Output only. Cloud storage location of icons etc
      consumed by UI.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Output only. Flag to mark the version indicating the launch stage.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: LAUNCH_STAGE_UNSPECIFIED.
      PREVIEW: PREVIEW.
      GA: GA.
      DEPRECATED: DEPRECATED.
      PRIVATE_PREVIEW: PRIVATE_PREVIEW.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    PREVIEW = 1
    GA = 2
    DEPRECATED = 3
    PRIVATE_PREVIEW = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Output only. Resource labels to represent user-provided metadata.
    Refer to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  documentationUri = _messages.StringField(4)
  eventingDetails = _messages.MessageField('EventingDetails', 5)
  externalUri = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 8)
  name = _messages.StringField(9)
  updateTime = _messages.StringField(10)
  webAssetsLocation = _messages.StringField(11)


class ConnectorInfraConfig(_messages.Message):
  r"""This cofiguration provides infra configs like rate limit threshold which
  need to be configurable for every connector version

  Fields:
    internalclientRatelimitThreshold: Max QPS supported for internal requests
      originating from Connd.
    ratelimitThreshold: Max QPS supported by the connector version before
      throttling of requests.
  """

  internalclientRatelimitThreshold = _messages.IntegerField(1)
  ratelimitThreshold = _messages.IntegerField(2)


class ConnectorVersion(_messages.Message):
  r"""ConnectorVersion indicates a specific version of a connector.

  Enums:
    LaunchStageValueValuesEnum: Output only. Flag to mark the version
      indicating the launch stage.

  Messages:
    LabelsValue: Output only. Resource labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    authConfigTemplates: Output only. List of auth configs supported by the
      Connector Version.
    configVariableTemplates: Output only. List of config variables needed to
      create a connection.
    connectorInfraConfig: Output only. Infra configs supported by Connector.
    createTime: Output only. Created time.
    destinationConfigTemplates: Output only. List of destination configs
      needed to create a connection.
    displayName: Output only. Display name.
    egressControlConfig: Output only. Configuration for Egress Control.
    eventingConfigTemplate: Output only. Eventing configuration supported by
      the Connector.
    labels: Output only. Resource labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    launchStage: Output only. Flag to mark the version indicating the launch
      stage.
    name: Output only. Resource name of the Version. Format: projects/{project
      }/locations/{location}/providers/{provider}/connectors/{connector}/versi
      ons/{version} Only global location is supported for Connector resource.
    releaseVersion: Output only. ReleaseVersion of the connector, for example:
      "1.0.1-alpha".
    roleGrant: Output only. Role grant configuration for this config variable.
      It will be DEPRECATED soon.
    roleGrants: Output only. Role grant configurations for this connector
      version.
    sslConfigTemplate: Output only. Ssl configuration supported by the
      Connector.
    supportedRuntimeFeatures: Output only. Information about the runtime
      features supported by the Connector.
    updateTime: Output only. Updated time.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Output only. Flag to mark the version indicating the launch stage.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: LAUNCH_STAGE_UNSPECIFIED.
      PREVIEW: PREVIEW.
      GA: GA.
      DEPRECATED: DEPRECATED.
      PRIVATE_PREVIEW: PRIVATE_PREVIEW.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    PREVIEW = 1
    GA = 2
    DEPRECATED = 3
    PRIVATE_PREVIEW = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Output only. Resource labels to represent user-provided metadata.
    Refer to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authConfigTemplates = _messages.MessageField('AuthConfigTemplate', 1, repeated=True)
  configVariableTemplates = _messages.MessageField('ConfigVariableTemplate', 2, repeated=True)
  connectorInfraConfig = _messages.MessageField('ConnectorInfraConfig', 3)
  createTime = _messages.StringField(4)
  destinationConfigTemplates = _messages.MessageField('DestinationConfigTemplate', 5, repeated=True)
  displayName = _messages.StringField(6)
  egressControlConfig = _messages.MessageField('EgressControlConfig', 7)
  eventingConfigTemplate = _messages.MessageField('EventingConfigTemplate', 8)
  labels = _messages.MessageField('LabelsValue', 9)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 10)
  name = _messages.StringField(11)
  releaseVersion = _messages.StringField(12)
  roleGrant = _messages.MessageField('RoleGrant', 13)
  roleGrants = _messages.MessageField('RoleGrant', 14, repeated=True)
  sslConfigTemplate = _messages.MessageField('SslConfigTemplate', 15)
  supportedRuntimeFeatures = _messages.MessageField('SupportedRuntimeFeatures', 16)
  updateTime = _messages.StringField(17)


class ConnectorVersionInfraConfig(_messages.Message):
  r"""This cofiguration provides infra configs like rate limit threshold which
  need to be configurable for every connector version

  Fields:
    internalclientRatelimitThreshold: Output only. Max QPS supported for
      internal requests originating from Connd.
    ratelimitThreshold: Output only. Max QPS supported by the connector
      version before throttling of requests.
  """

  internalclientRatelimitThreshold = _messages.IntegerField(1)
  ratelimitThreshold = _messages.IntegerField(2)


class ConnectorsLogConfig(_messages.Message):
  r"""Log configuration for the connection.

  Fields:
    enabled: Enabled represents whether logging is enabled or not for a
      connection.
  """

  enabled = _messages.BooleanField(1)


class ConnectorsProjectsLocationsConnectionsConnectionSchemaMetadataRefreshRequest(_messages.Message):
  r"""A
  ConnectorsProjectsLocationsConnectionsConnectionSchemaMetadataRefreshRequest
  object.

  Fields:
    name: Required. Resource name. Format: projects/{project}/locations/{locat
      ion}/connections/{connection}/connectionSchemaMetadata
    refreshConnectionSchemaMetadataRequest: A
      RefreshConnectionSchemaMetadataRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  refreshConnectionSchemaMetadataRequest = _messages.MessageField('RefreshConnectionSchemaMetadataRequest', 2)


class ConnectorsProjectsLocationsConnectionsCreateRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsCreateRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    connectionId: Required. Identifier to assign to the Connection. Must be
      unique within scope of the parent resource.
    parent: Required. Parent resource of the Connection, of the form:
      `projects/*/locations/*`
  """

  connection = _messages.MessageField('Connection', 1)
  connectionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ConnectorsProjectsLocationsConnectionsDeleteRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsDeleteRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/connections/*`
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsConnectionsEventSubscriptionsCreateRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsEventSubscriptionsCreateRequest
  object.

  Fields:
    eventSubscription: A EventSubscription resource to be passed as the
      request body.
    eventSubscriptionId: Required. Identifier to assign to the Event
      Subscription. Must be unique within scope of the parent resource.
    parent: Required. Parent resource of the EventSubscription, of the form:
      `projects/*/locations/*/connections/*`
  """

  eventSubscription = _messages.MessageField('EventSubscription', 1)
  eventSubscriptionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ConnectorsProjectsLocationsConnectionsEventSubscriptionsDeleteRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsEventSubscriptionsDeleteRequest
  object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/connections/*/eventsubscriptions/*`
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsConnectionsEventSubscriptionsGetRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsEventSubscriptionsGetRequest
  object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/connections/*/eventSubscriptions/*`
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsConnectionsEventSubscriptionsListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsEventSubscriptionsListRequest
  object.

  Fields:
    filter: Filter.
    orderBy: Order by parameters.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the EventSubscription, of the form:
      `projects/*/locations/*/connections/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ConnectorsProjectsLocationsConnectionsEventSubscriptionsPatchRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsEventSubscriptionsPatchRequest
  object.

  Fields:
    eventSubscription: A EventSubscription resource to be passed as the
      request body.
    name: Required. Resource name of the EventSubscription. Format: projects/{
      project}/locations/{location}/connections/{connection}/eventSubscription
      s/{event_subscription}
    updateMask: Required. The list of fields to update. Fields are specified
      relative to the Subscription. A field will be overwritten if it is in
      the mask. You can modify only the fields listed below. To update the
      EventSubscription details: * `serviceAccount`
  """

  eventSubscription = _messages.MessageField('EventSubscription', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ConnectorsProjectsLocationsConnectionsEventSubscriptionsRetryRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsEventSubscriptionsRetryRequest
  object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/connections/*/eventSubscriptions/*`
    retryEventSubscriptionRequest: A RetryEventSubscriptionRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  retryEventSubscriptionRequest = _messages.MessageField('RetryEventSubscriptionRequest', 2)


class ConnectorsProjectsLocationsConnectionsGetConnectionSchemaMetadataRequest(_messages.Message):
  r"""A
  ConnectorsProjectsLocationsConnectionsGetConnectionSchemaMetadataRequest
  object.

  Fields:
    name: Required. Connection name Format: projects/{project}/locations/{loca
      tion}/connections/{connection}/connectionSchemaMetadata
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsConnectionsGetIamPolicyRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ConnectorsProjectsLocationsConnectionsGetRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsGetRequest object.

  Enums:
    ViewValueValuesEnum: Specifies which fields of the Connection are returned
      in the response. Defaults to `BASIC` view.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/connections/*`
    view: Specifies which fields of the Connection are returned in the
      response. Defaults to `BASIC` view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which fields of the Connection are returned in the response.
    Defaults to `BASIC` view.

    Values:
      CONNECTION_VIEW_UNSPECIFIED: CONNECTION_UNSPECIFIED.
      BASIC: Do not include runtime required configs.
      FULL: Include runtime required configs.
    """
    CONNECTION_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class ConnectorsProjectsLocationsConnectionsListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsListRequest object.

  Enums:
    ViewValueValuesEnum: Specifies which fields of the Connection are returned
      in the response. Defaults to `BASIC` view.

  Fields:
    filter: Filter.
    orderBy: Order by parameters.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the Connection, of the form:
      `projects/*/locations/*`
    view: Specifies which fields of the Connection are returned in the
      response. Defaults to `BASIC` view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which fields of the Connection are returned in the response.
    Defaults to `BASIC` view.

    Values:
      CONNECTION_VIEW_UNSPECIFIED: CONNECTION_UNSPECIFIED.
      BASIC: Do not include runtime required configs.
      FULL: Include runtime required configs.
    """
    CONNECTION_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class ConnectorsProjectsLocationsConnectionsPatchRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsPatchRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    name: Output only. Resource name of the Connection. Format:
      projects/{project}/locations/{location}/connections/{connection}
    updateMask: Required. You can modify only the fields listed below. To
      lock/unlock a connection: * `lock_config` To suspend/resume a
      connection: * `suspended` To update the connection details: *
      `description` * `labels` * `connector_version` * `config_variables` *
      `auth_config` * `destination_configs` * `node_config` * `log_config` *
      `ssl_config` * `eventing_enablement_type` * `eventing_config`
  """

  connection = _messages.MessageField('Connection', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ConnectorsProjectsLocationsConnectionsRepairEventingRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsRepairEventingRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/connections/*`
    repairEventingRequest: A RepairEventingRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  repairEventingRequest = _messages.MessageField('RepairEventingRequest', 2)


class ConnectorsProjectsLocationsConnectionsRuntimeActionSchemasListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsRuntimeActionSchemasListRequest
  object.

  Fields:
    filter: Required. Filter Format: action="{actionId}" Only action field is
      supported with literal equality operator. Accepted filter example:
      action="CancelOrder" Wildcards are not supported in the filter
      currently.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of RuntimeActionSchema Format:
      projects/{project}/locations/{location}/connections/{connection}
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ConnectorsProjectsLocationsConnectionsRuntimeEntitySchemasListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsRuntimeEntitySchemasListRequest
  object.

  Fields:
    filter: Required. Filter Format: entity="{entityId}" Only entity field is
      supported with literal equality operator. Accepted filter example:
      entity="Order" Wildcards are not supported in the filter currently.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of RuntimeEntitySchema Format:
      projects/{project}/locations/{location}/connections/{connection}
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ConnectorsProjectsLocationsConnectionsSetIamPolicyRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ConnectorsProjectsLocationsConnectionsTestIamPermissionsRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsConnectionsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ConnectorsProjectsLocationsEndpointAttachmentsCreateRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsEndpointAttachmentsCreateRequest object.

  Fields:
    endpointAttachment: A EndpointAttachment resource to be passed as the
      request body.
    endpointAttachmentId: Required. Identifier to assign to the
      EndpointAttachment. Must be unique within scope of the parent resource.
    parent: Required. Parent resource of the EndpointAttachment, of the form:
      `projects/*/locations/*`
  """

  endpointAttachment = _messages.MessageField('EndpointAttachment', 1)
  endpointAttachmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ConnectorsProjectsLocationsEndpointAttachmentsDeleteRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsEndpointAttachmentsDeleteRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/endpointAttachments/*`
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsEndpointAttachmentsGetRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsEndpointAttachmentsGetRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/endpointAttachments/*`
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsEndpointAttachmentsListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsEndpointAttachmentsListRequest object.

  Fields:
    filter: Filter.
    orderBy: Order by parameters.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource od the EndpointAttachment, of the form:
      `projects/*/locations/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ConnectorsProjectsLocationsEndpointAttachmentsPatchRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsEndpointAttachmentsPatchRequest object.

  Fields:
    endpointAttachment: A EndpointAttachment resource to be passed as the
      request body.
    name: Output only. Resource name of the Endpoint Attachment. Format: proje
      cts/{project}/locations/{location}/endpointAttachments/{endpoint_attachm
      ent}
    updateMask: Required. The list of fields to update. Fields are specified
      relative to the endpointAttachment. A field will be overwritten if it is
      in the mask. You can modify only the fields listed below. To update the
      endpointAttachment details: * `description` * `labels`
  """

  endpointAttachment = _messages.MessageField('EndpointAttachment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ConnectorsProjectsLocationsGetRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsGetRuntimeConfigRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsGetRuntimeConfigRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/runtimeConfig`
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsGlobalGetSettingsRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsGlobalGetSettingsRequest object.

  Fields:
    name: Required. The resource name of the Settings.
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsGlobalManagedZonesCreateRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsGlobalManagedZonesCreateRequest object.

  Fields:
    managedZone: A ManagedZone resource to be passed as the request body.
    managedZoneId: Required. Identifier to assign to the ManagedZone. Must be
      unique within scope of the parent resource.
    parent: Required. Parent resource of the ManagedZone, of the form:
      `projects/*/locations/global`
  """

  managedZone = _messages.MessageField('ManagedZone', 1)
  managedZoneId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ConnectorsProjectsLocationsGlobalManagedZonesDeleteRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsGlobalManagedZonesDeleteRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/global/managedZones/*`
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsGlobalManagedZonesGetRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsGlobalManagedZonesGetRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/global/managedZones/*`
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsGlobalManagedZonesListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsGlobalManagedZonesListRequest object.

  Fields:
    filter: Filter.
    orderBy: Order by parameters.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the Managed Zone, of the form:
      `projects/*/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ConnectorsProjectsLocationsGlobalManagedZonesPatchRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsGlobalManagedZonesPatchRequest object.

  Fields:
    managedZone: A ManagedZone resource to be passed as the request body.
    name: Output only. Resource name of the Managed Zone. Format:
      projects/{project}/locations/global/managedZones/{managed_zone}
    updateMask: Required. The list of fields to update. Fields are specified
      relative to the managedZone. A field will be overwritten if it is in the
      mask. You can modify only the fields listed below. To update the
      managedZone details: * `description` * `labels` * `target_project` *
      `target_network`
  """

  managedZone = _messages.MessageField('ManagedZone', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ConnectorsProjectsLocationsGlobalUpdateSettingsRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsGlobalUpdateSettingsRequest object.

  Fields:
    name: Output only. Resource name of the Connection. Format:
      projects/{project}/locations/global/settings}
    settings: A Settings resource to be passed as the request body.
    updateMask: Required. The list of fields to update.
  """

  name = _messages.StringField(1, required=True)
  settings = _messages.MessageField('Settings', 2)
  updateMask = _messages.StringField(3)


class ConnectorsProjectsLocationsListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ConnectorsProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ConnectorsProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ConnectorsProjectsLocationsProvidersConnectorsGetRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsProvidersConnectorsGetRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/providers/*/connectors/*` Only global location
      is supported for Connector resource.
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsProvidersConnectorsListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsProvidersConnectorsListRequest object.

  Fields:
    filter: Filter string.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the connectors, of the form:
      `projects/*/locations/*/providers/*` Only global location is supported
      for Connector resource.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ConnectorsProjectsLocationsProvidersConnectorsVersionsEventtypesGetRequest(_messages.Message):
  r"""A
  ConnectorsProjectsLocationsProvidersConnectorsVersionsEventtypesGetRequest
  object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/providers/*/connectors/*/versions/*/eventtypes/*
      ` Only global location is supported for EventType resource.
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsProvidersConnectorsVersionsEventtypesListRequest(_messages.Message):
  r"""A
  ConnectorsProjectsLocationsProvidersConnectorsVersionsEventtypesListRequest
  object.

  Fields:
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the connectors, of the form:
      `projects/*/locations/*/providers/*/connectors/*/versions/*` Only global
      location is supported for EventType resource.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ConnectorsProjectsLocationsProvidersConnectorsVersionsGetRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsProvidersConnectorsVersionsGetRequest
  object.

  Enums:
    ViewValueValuesEnum: Specifies which fields of the ConnectorVersion are
      returned in the response. Defaults to `CUSTOMER` view.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/providers/*/connectors/*/versions/*` Only global
      location is supported for ConnectorVersion resource.
    view: Specifies which fields of the ConnectorVersion are returned in the
      response. Defaults to `CUSTOMER` view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which fields of the ConnectorVersion are returned in the
    response. Defaults to `CUSTOMER` view.

    Values:
      CONNECTOR_VERSION_VIEW_UNSPECIFIED: CONNECTOR_VERSION_VIEW_UNSPECIFIED.
      CONNECTOR_VERSION_VIEW_BASIC: Do not include role grant configs.
      CONNECTOR_VERSION_VIEW_FULL: Include role grant configs.
    """
    CONNECTOR_VERSION_VIEW_UNSPECIFIED = 0
    CONNECTOR_VERSION_VIEW_BASIC = 1
    CONNECTOR_VERSION_VIEW_FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class ConnectorsProjectsLocationsProvidersConnectorsVersionsListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsProvidersConnectorsVersionsListRequest
  object.

  Enums:
    ViewValueValuesEnum: Specifies which fields of the ConnectorVersion are
      returned in the response. Defaults to `BASIC` view.

  Fields:
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the connectors, of the form:
      `projects/*/locations/*/providers/*/connectors/*` Only global location
      is supported for ConnectorVersion resource.
    view: Specifies which fields of the ConnectorVersion are returned in the
      response. Defaults to `BASIC` view.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which fields of the ConnectorVersion are returned in the
    response. Defaults to `BASIC` view.

    Values:
      CONNECTOR_VERSION_VIEW_UNSPECIFIED: CONNECTOR_VERSION_VIEW_UNSPECIFIED.
      CONNECTOR_VERSION_VIEW_BASIC: Do not include role grant configs.
      CONNECTOR_VERSION_VIEW_FULL: Include role grant configs.
    """
    CONNECTOR_VERSION_VIEW_UNSPECIFIED = 0
    CONNECTOR_VERSION_VIEW_BASIC = 1
    CONNECTOR_VERSION_VIEW_FULL = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class ConnectorsProjectsLocationsProvidersGetIamPolicyRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsProvidersGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ConnectorsProjectsLocationsProvidersGetRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsProvidersGetRequest object.

  Fields:
    name: Required. Resource name of the form:
      `projects/*/locations/*/providers/*` Only global location is supported
      for Provider resource.
  """

  name = _messages.StringField(1, required=True)


class ConnectorsProjectsLocationsProvidersListRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsProvidersListRequest object.

  Fields:
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Parent resource of the API, of the form:
      `projects/*/locations/*` Only global location is supported for Provider
      resource.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ConnectorsProjectsLocationsProvidersSetIamPolicyRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsProvidersSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ConnectorsProjectsLocationsProvidersTestIamPermissionsRequest(_messages.Message):
  r"""A ConnectorsProjectsLocationsProvidersTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class Destination(_messages.Message):
  r"""A Destination object.

  Fields:
    host: For publicly routable host.
    port: The port is the target port number that is accepted by the
      destination.
    serviceAttachment: PSC service attachments. Format:
      projects/*/regions/*/serviceAttachments/*
  """

  host = _messages.StringField(1)
  port = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  serviceAttachment = _messages.StringField(3)


class DestinationConfig(_messages.Message):
  r"""Define the Connectors target endpoint.

  Fields:
    destinations: The destinations for the key.
    key: The key is the destination identifier that is supported by the
      Connector.
  """

  destinations = _messages.MessageField('Destination', 1, repeated=True)
  key = _messages.StringField(2)


class DestinationConfigTemplate(_messages.Message):
  r"""DestinationConfigTemplate defines required destinations supported by the
  Connector.

  Enums:
    PortFieldTypeValueValuesEnum: Whether port number should be provided by
      customers.

  Fields:
    defaultPort: The default port.
    description: Description.
    displayName: Display name of the parameter.
    isAdvanced: Whether the current destination tempalate is part of Advanced
      settings
    key: Key of the destination.
    max: The maximum number of destinations supported for this key.
    min: The minimum number of destinations supported for this key.
    portFieldType: Whether port number should be provided by customers.
    regexPattern: Regex pattern for host.
  """

  class PortFieldTypeValueValuesEnum(_messages.Enum):
    r"""Whether port number should be provided by customers.

    Values:
      FIELD_TYPE_UNSPECIFIED: <no description>
      REQUIRED: <no description>
      OPTIONAL: <no description>
      NOT_USED: <no description>
    """
    FIELD_TYPE_UNSPECIFIED = 0
    REQUIRED = 1
    OPTIONAL = 2
    NOT_USED = 3

  defaultPort = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  isAdvanced = _messages.BooleanField(4)
  key = _messages.StringField(5)
  max = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  min = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  portFieldType = _messages.EnumField('PortFieldTypeValueValuesEnum', 8)
  regexPattern = _messages.StringField(9)


class EgressControlConfig(_messages.Message):
  r"""Egress control config for connector runtime. These configurations define
  the rules to identify which outbound domains/hosts needs to be whitelisted.
  It may be a static information for a particular connector version or it is
  derived from the configurations provided by the customer in Connection
  resource.

  Fields:
    backends: Static Comma separated backends which are common for all
      Connection resources. Supported formats for each backend are host:port
      or just host (host can be ip address or domain name).
    extractionRules: Extractions Rules to extract the backends from customer
      provided configuration.
  """

  backends = _messages.StringField(1)
  extractionRules = _messages.MessageField('ExtractionRules', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionKey(_messages.Message):
  r"""Encryption Key value.

  Enums:
    TypeValueValuesEnum: Type.

  Fields:
    kmsKeyName: The [KMS key name] with which the content of the Operation is
      encrypted. The expected format:
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`. Will be empty string
      if google managed.
    type: Type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type.

    Values:
      TYPE_UNSPECIFIED: Value type is not specified.
      GOOGLE_MANAGED: Google Managed.
      CUSTOMER_MANAGED: Customer Managed.
    """
    TYPE_UNSPECIFIED = 0
    GOOGLE_MANAGED = 1
    CUSTOMER_MANAGED = 2

  kmsKeyName = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class EndPoint(_messages.Message):
  r"""Endpoint message includes details of the Destination endpoint.

  Fields:
    endpointUri: The URI of the Endpoint.
    headers: List of Header to be added to the Endpoint.
  """

  endpointUri = _messages.StringField(1)
  headers = _messages.MessageField('Header', 2, repeated=True)


class EndpointAttachment(_messages.Message):
  r"""represents the Connector's Endpoint Attachment resource

  Messages:
    LabelsValue: Optional. Resource labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    createTime: Output only. Created time.
    description: Optional. Description of the resource.
    endpointIp: Output only. The Private Service Connect connection endpoint
      ip
    labels: Optional. Resource labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    name: Output only. Resource name of the Endpoint Attachment. Format: proje
      cts/{project}/locations/{location}/endpointAttachments/{endpoint_attachm
      ent}
    serviceAttachment: Required. The path of the service attachment
    updateTime: Output only. Updated time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user-provided metadata. Refer
    to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  endpointIp = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  serviceAttachment = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class EnumOption(_messages.Message):
  r"""EnumOption definition

  Fields:
    displayName: Display name of the option.
    id: Id of the option.
  """

  displayName = _messages.StringField(1)
  id = _messages.StringField(2)


class EventSubscription(_messages.Message):
  r"""represents the Connector's EventSubscription resource

  Fields:
    createTime: Output only. Created time.
    destinations: Optional. The destination to hit when we receive an event
    eventTypeId: Optional. Event type id of the event of current
      EventSubscription.
    name: Required. Resource name of the EventSubscription. Format: projects/{
      project}/locations/{location}/connections/{connection}/eventSubscription
      s/{event_subscription}
    status: Optional. Status indicates the status of the event subscription
      resource
    subscriber: Optional. name of the Subscriber for the current
      EventSubscription.
    subscriberLink: Optional. Link for Subscriber of the current
      EventSubscription.
    updateTime: Output only. Updated time.
  """

  createTime = _messages.StringField(1)
  destinations = _messages.MessageField('EventSubscriptionDestination', 2)
  eventTypeId = _messages.StringField(3)
  name = _messages.StringField(4)
  status = _messages.MessageField('EventSubscriptionStatus', 5)
  subscriber = _messages.StringField(6)
  subscriberLink = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class EventSubscriptionDestination(_messages.Message):
  r"""Message for EventSubscription Destination to act on receiving an event

  Enums:
    TypeValueValuesEnum: type of the destination

  Fields:
    endpoint: OPTION 1: Hit an endpoint when we receive an event.
    serviceAccount: Service account needed for runtime plane to trigger IP
      workflow.
    type: type of the destination
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""type of the destination

    Values:
      TYPE_UNSPECIFIED: Default state.
      ENDPOINT: Endpoint - Hit the value of endpoint when event is received
    """
    TYPE_UNSPECIFIED = 0
    ENDPOINT = 1

  endpoint = _messages.MessageField('EndPoint', 1)
  serviceAccount = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class EventSubscriptionStatus(_messages.Message):
  r"""EventSubscription Status denotes the status of the EventSubscription
  resource.

  Enums:
    StateValueValuesEnum: Output only. State of Event Subscription resource.

  Fields:
    description: Output only. Description of the state.
    state: Output only. State of Event Subscription resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of Event Subscription resource.

    Values:
      STATE_UNSPECIFIED: Default state.
      CREATING: EventSubscription creation is in progress.
      UPDATING: EventSubscription is in Updating status.
      ACTIVE: EventSubscription is in Active state and is ready to receive
        events.
      SUSPENDED: EventSubscription is currently suspended.
      ERROR: EventSubscription is in Error state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    UPDATING = 2
    ACTIVE = 3
    SUSPENDED = 4
    ERROR = 5

  description = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class EventType(_messages.Message):
  r"""EventType includes fields.

  Fields:
    createTime: Output only. Created time.
    enrichedEventPayloadSchema: Output only. Schema of the event payload after
      enriched. Will be null if read before send is not supported.
    entityType: Output only. Runtime entity type name. Will be null if entity
      type map is not available. Used for read before send feature.
    eventPayloadSchema: Output only. Schema of webhook event payload.
    eventTypeId: Output only. Event type id. Example: `ticket.created`.
    idPath: Output only. Id path denotes the path of id in webhook payload.
    name: Output only. Resource name of the eventtype. Format: projects/{proje
      ct}/locations/{location}/providers/{provider}/connectors/{connector}/ver
      sions/{version}/eventtypes/{eventtype} Only global location is supported
      for Connector resource.
    updateTime: Output only. Updated time.
  """

  createTime = _messages.StringField(1)
  enrichedEventPayloadSchema = _messages.StringField(2)
  entityType = _messages.StringField(3)
  eventPayloadSchema = _messages.StringField(4)
  eventTypeId = _messages.StringField(5)
  idPath = _messages.StringField(6)
  name = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class EventingConfig(_messages.Message):
  r"""Eventing Configuration of a connection

  Fields:
    additionalVariables: Additional eventing related field values
    authConfig: Auth details for the webhook adapter.
    encryptionKey: Encryption key (can be either Google managed or CMEK).
    enrichmentEnabled: Enrichment Enabled.
    registrationDestinationConfig: Registration endpoint for auto
      regsitration.
  """

  additionalVariables = _messages.MessageField('ConfigVariable', 1, repeated=True)
  authConfig = _messages.MessageField('AuthConfig', 2)
  encryptionKey = _messages.MessageField('ConfigVariable', 3)
  enrichmentEnabled = _messages.BooleanField(4)
  registrationDestinationConfig = _messages.MessageField('DestinationConfig', 5)


class EventingConfigTemplate(_messages.Message):
  r"""Eventing Config details of a connector version.

  Fields:
    additionalVariables: Additional fields that need to be rendered.
    authConfigTemplates: AuthConfigTemplates represents the auth values for
      the webhook adapter.
    autoRefresh: Auto refresh to extend webhook life.
    autoRegistrationSupported: Auto Registration supported.
    encryptionKeyTemplate: Encryption key (can be either Google managed or
      CMEK).
    enrichmentSupported: Enrichment Supported.
    isEventingSupported: Is Eventing Supported.
    registrationDestinationConfig: Registration host destination config
      template.
  """

  additionalVariables = _messages.MessageField('ConfigVariableTemplate', 1, repeated=True)
  authConfigTemplates = _messages.MessageField('AuthConfigTemplate', 2, repeated=True)
  autoRefresh = _messages.BooleanField(3)
  autoRegistrationSupported = _messages.BooleanField(4)
  encryptionKeyTemplate = _messages.MessageField('ConfigVariableTemplate', 5)
  enrichmentSupported = _messages.BooleanField(6)
  isEventingSupported = _messages.BooleanField(7)
  registrationDestinationConfig = _messages.MessageField('DestinationConfigTemplate', 8)


class EventingDetails(_messages.Message):
  r"""Eventing Details message.

  Enums:
    LaunchStageValueValuesEnum: Output only. Eventing Launch Stage.

  Fields:
    customEventTypes: Output only. Custom Event Types.
    description: Output only. Description.
    documentationLink: Output only. Link to public documentation.
    iconLocation: Output only. Cloud storage location of the icon.
    launchStage: Output only. Eventing Launch Stage.
    name: Output only. Name of the Eventing trigger.
    searchTags: Output only. Array of search keywords.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Output only. Eventing Launch Stage.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: LAUNCH_STAGE_UNSPECIFIED.
      PREVIEW: PREVIEW.
      GA: GA.
      DEPRECATED: DEPRECATED.
      PRIVATE_PREVIEW: PRIVATE_PREVIEW.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    PREVIEW = 1
    GA = 2
    DEPRECATED = 3
    PRIVATE_PREVIEW = 4

  customEventTypes = _messages.BooleanField(1)
  description = _messages.StringField(2)
  documentationLink = _messages.StringField(3)
  iconLocation = _messages.StringField(4)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 5)
  name = _messages.StringField(6)
  searchTags = _messages.StringField(7, repeated=True)


class EventingRuntimeData(_messages.Message):
  r"""Eventing runtime data has the details related to eventing managed by the
  system.

  Fields:
    eventsListenerEndpoint: Output only. Events listener endpoint. The value
      will populated after provisioning the events listener.
    status: Output only. Current status of eventing.
  """

  eventsListenerEndpoint = _messages.StringField(1)
  status = _messages.MessageField('EventingStatus', 2)


class EventingStatus(_messages.Message):
  r"""EventingStatus indicates the state of eventing.

  Enums:
    StateValueValuesEnum: Output only. State.

  Fields:
    description: Output only. Description of error if State is set to "ERROR".
    state: Output only. State.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State.

    Values:
      STATE_UNSPECIFIED: Default state.
      ACTIVE: Eventing is enabled and ready to receive events.
      ERROR: Eventing is not active due to an error.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    ERROR = 2

  description = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExtractionRule(_messages.Message):
  r"""Extraction Rule.

  Fields:
    extractionRegex: Regex used to extract backend details from source. If
      empty, whole source value will be used.
    source: Source on which the rule is applied.
  """

  extractionRegex = _messages.StringField(1)
  source = _messages.MessageField('Source', 2)


class ExtractionRules(_messages.Message):
  r"""Extraction Rules to identity the backends from customer provided
  configuration in Connection resource.

  Fields:
    extractionRule: Collection of Extraction Rule.
  """

  extractionRule = _messages.MessageField('ExtractionRule', 1, repeated=True)


class Field(_messages.Message):
  r"""Metadata of an entity field.

  Enums:
    DataTypeValueValuesEnum: The data type of the Field.

  Messages:
    AdditionalDetailsValue: The following map contains fields that are not
      explicitly mentioned above,this give connectors the flexibility to add
      new metadata fields.

  Fields:
    additionalDetails: The following map contains fields that are not
      explicitly mentioned above,this give connectors the flexibility to add
      new metadata fields.
    dataType: The data type of the Field.
    defaultValue: The following field specifies the default value of the Field
      provided by the external system if a value is not provided.
    description: A brief description of the Field.
    field: Name of the Field.
    key: The following boolean field specifies if the current Field acts as a
      primary key or id if the parent is of type entity.
    nullable: Specifies whether a null value is allowed.
    readonly: Specifies if the Field is readonly.
  """

  class DataTypeValueValuesEnum(_messages.Enum):
    r"""The data type of the Field.

    Values:
      DATA_TYPE_UNSPECIFIED: Data type is not specified.
      DATA_TYPE_INT: DEPRECATED! Use DATA_TYPE_INTEGER.
      DATA_TYPE_SMALLINT: Short integer(int16) data type.
      DATA_TYPE_DOUBLE: Double data type.
      DATA_TYPE_DATE: Date data type.
      DATA_TYPE_DATETIME: DEPRECATED! Use DATA_TYPE_TIMESTAMP.
      DATA_TYPE_TIME: Time data type.
      DATA_TYPE_STRING: DEPRECATED! Use DATA_TYPE_VARCHAR.
      DATA_TYPE_LONG: DEPRECATED! Use DATA_TYPE_BIGINT.
      DATA_TYPE_BOOLEAN: Boolean data type.
      DATA_TYPE_DECIMAL: Decimal data type.
      DATA_TYPE_UUID: DEPRECATED! Use DATA_TYPE_VARCHAR.
      DATA_TYPE_BLOB: UNSUPPORTED! Binary data type.
      DATA_TYPE_BIT: Bit data type.
      DATA_TYPE_TINYINT: Small integer(int8) data type.
      DATA_TYPE_INTEGER: Integer(int32) data type.
      DATA_TYPE_BIGINT: Long integer(int64) data type.
      DATA_TYPE_FLOAT: Float data type.
      DATA_TYPE_REAL: Real data type.
      DATA_TYPE_NUMERIC: Numeric data type.
      DATA_TYPE_CHAR: Char data type.
      DATA_TYPE_VARCHAR: Varchar data type.
      DATA_TYPE_LONGVARCHAR: Longvarchar data type.
      DATA_TYPE_TIMESTAMP: Timestamp data type.
      DATA_TYPE_NCHAR: Nchar data type.
      DATA_TYPE_NVARCHAR: Nvarchar data type.
      DATA_TYPE_LONGNVARCHAR: Longnvarchar data type.
      DATA_TYPE_NULL: Null data type.
      DATA_TYPE_OTHER: UNSUPPORTED! Binary data type.
      DATA_TYPE_JAVA_OBJECT: UNSUPPORTED! Binary data type.
      DATA_TYPE_DISTINCT: UNSUPPORTED! Binary data type.
      DATA_TYPE_STRUCT: UNSUPPORTED! Binary data type.
      DATA_TYPE_ARRAY: UNSUPPORTED! Binary data type.
      DATA_TYPE_CLOB: UNSUPPORTED! Binary data type.
      DATA_TYPE_REF: UNSUPPORTED! Binary data type.
      DATA_TYPE_DATALINK: UNSUPPORTED! Binary data type.
      DATA_TYPE_ROWID: UNSUPPORTED! Row id data type.
      DATA_TYPE_BINARY: UNSUPPORTED! Binary data type.
      DATA_TYPE_VARBINARY: UNSUPPORTED! Variable binary data type.
      DATA_TYPE_LONGVARBINARY: UNSUPPORTED! Long variable binary data type.
      DATA_TYPE_NCLOB: UNSUPPORTED! NCLOB data type.
      DATA_TYPE_SQLXML: UNSUPPORTED! SQL XML data type is not supported.
      DATA_TYPE_REF_CURSOR: UNSUPPORTED! Cursor reference type is not
        supported.
      DATA_TYPE_TIME_WITH_TIMEZONE: UNSUPPORTED! Use TIME or TIMESTAMP
        instead.
      DATA_TYPE_TIMESTAMP_WITH_TIMEZONE: UNSUPPORTED! Use TIMESTAMP instead.
    """
    DATA_TYPE_UNSPECIFIED = 0
    DATA_TYPE_INT = 1
    DATA_TYPE_SMALLINT = 2
    DATA_TYPE_DOUBLE = 3
    DATA_TYPE_DATE = 4
    DATA_TYPE_DATETIME = 5
    DATA_TYPE_TIME = 6
    DATA_TYPE_STRING = 7
    DATA_TYPE_LONG = 8
    DATA_TYPE_BOOLEAN = 9
    DATA_TYPE_DECIMAL = 10
    DATA_TYPE_UUID = 11
    DATA_TYPE_BLOB = 12
    DATA_TYPE_BIT = 13
    DATA_TYPE_TINYINT = 14
    DATA_TYPE_INTEGER = 15
    DATA_TYPE_BIGINT = 16
    DATA_TYPE_FLOAT = 17
    DATA_TYPE_REAL = 18
    DATA_TYPE_NUMERIC = 19
    DATA_TYPE_CHAR = 20
    DATA_TYPE_VARCHAR = 21
    DATA_TYPE_LONGVARCHAR = 22
    DATA_TYPE_TIMESTAMP = 23
    DATA_TYPE_NCHAR = 24
    DATA_TYPE_NVARCHAR = 25
    DATA_TYPE_LONGNVARCHAR = 26
    DATA_TYPE_NULL = 27
    DATA_TYPE_OTHER = 28
    DATA_TYPE_JAVA_OBJECT = 29
    DATA_TYPE_DISTINCT = 30
    DATA_TYPE_STRUCT = 31
    DATA_TYPE_ARRAY = 32
    DATA_TYPE_CLOB = 33
    DATA_TYPE_REF = 34
    DATA_TYPE_DATALINK = 35
    DATA_TYPE_ROWID = 36
    DATA_TYPE_BINARY = 37
    DATA_TYPE_VARBINARY = 38
    DATA_TYPE_LONGVARBINARY = 39
    DATA_TYPE_NCLOB = 40
    DATA_TYPE_SQLXML = 41
    DATA_TYPE_REF_CURSOR = 42
    DATA_TYPE_TIME_WITH_TIMEZONE = 43
    DATA_TYPE_TIMESTAMP_WITH_TIMEZONE = 44

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalDetailsValue(_messages.Message):
    r"""The following map contains fields that are not explicitly mentioned
    above,this give connectors the flexibility to add new metadata fields.

    Messages:
      AdditionalProperty: An additional property for a AdditionalDetailsValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalDetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalDetails = _messages.MessageField('AdditionalDetailsValue', 1)
  dataType = _messages.EnumField('DataTypeValueValuesEnum', 2)
  defaultValue = _messages.MessageField('extra_types.JsonValue', 3)
  description = _messages.StringField(4)
  field = _messages.StringField(5)
  key = _messages.BooleanField(6)
  nullable = _messages.BooleanField(7)
  readonly = _messages.BooleanField(8)


class FieldComparison(_messages.Message):
  r"""Field that needs to be compared.

  Enums:
    ComparatorValueValuesEnum: Comparator to use for comparing the field
      value.

  Fields:
    boolValue: Boolean value
    comparator: Comparator to use for comparing the field value.
    intValue: Integer value
    key: Key of the field.
    stringValue: String value
  """

  class ComparatorValueValuesEnum(_messages.Enum):
    r"""Comparator to use for comparing the field value.

    Values:
      COMPARATOR_UNSPECIFIED: The default value.
      EQUALS: The field value must be equal to the specified value.
      NOT_EQUALS: The field value must not be equal to the specified value.
    """
    COMPARATOR_UNSPECIFIED = 0
    EQUALS = 1
    NOT_EQUALS = 2

  boolValue = _messages.BooleanField(1)
  comparator = _messages.EnumField('ComparatorValueValuesEnum', 2)
  intValue = _messages.IntegerField(3)
  key = _messages.StringField(4)
  stringValue = _messages.StringField(5)


class Header(_messages.Message):
  r"""Header details for a given header to be added to Endpoint.

  Fields:
    key: Key of Header.
    value: Value of Header.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class InputParameter(_messages.Message):
  r"""Metadata of an input parameter.

  Enums:
    DataTypeValueValuesEnum: The data type of the Parameter.

  Fields:
    dataType: The data type of the Parameter.
    defaultValue: The following field specifies the default value of the
      Parameter provided by the external system if a value is not provided.
    description: A brief description of the Parameter.
    nullable: Specifies whether a null value is allowed.
    parameter: Name of the Parameter.
  """

  class DataTypeValueValuesEnum(_messages.Enum):
    r"""The data type of the Parameter.

    Values:
      DATA_TYPE_UNSPECIFIED: Data type is not specified.
      DATA_TYPE_INT: DEPRECATED! Use DATA_TYPE_INTEGER.
      DATA_TYPE_SMALLINT: Short integer(int16) data type.
      DATA_TYPE_DOUBLE: Double data type.
      DATA_TYPE_DATE: Date data type.
      DATA_TYPE_DATETIME: DEPRECATED! Use DATA_TYPE_TIMESTAMP.
      DATA_TYPE_TIME: Time data type.
      DATA_TYPE_STRING: DEPRECATED! Use DATA_TYPE_VARCHAR.
      DATA_TYPE_LONG: DEPRECATED! Use DATA_TYPE_BIGINT.
      DATA_TYPE_BOOLEAN: Boolean data type.
      DATA_TYPE_DECIMAL: Decimal data type.
      DATA_TYPE_UUID: DEPRECATED! Use DATA_TYPE_VARCHAR.
      DATA_TYPE_BLOB: UNSUPPORTED! Binary data type.
      DATA_TYPE_BIT: Bit data type.
      DATA_TYPE_TINYINT: Small integer(int8) data type.
      DATA_TYPE_INTEGER: Integer(int32) data type.
      DATA_TYPE_BIGINT: Long integer(int64) data type.
      DATA_TYPE_FLOAT: Float data type.
      DATA_TYPE_REAL: Real data type.
      DATA_TYPE_NUMERIC: Numeric data type.
      DATA_TYPE_CHAR: Char data type.
      DATA_TYPE_VARCHAR: Varchar data type.
      DATA_TYPE_LONGVARCHAR: Longvarchar data type.
      DATA_TYPE_TIMESTAMP: Timestamp data type.
      DATA_TYPE_NCHAR: Nchar data type.
      DATA_TYPE_NVARCHAR: Nvarchar data type.
      DATA_TYPE_LONGNVARCHAR: Longnvarchar data type.
      DATA_TYPE_NULL: Null data type.
      DATA_TYPE_OTHER: UNSUPPORTED! Binary data type.
      DATA_TYPE_JAVA_OBJECT: UNSUPPORTED! Binary data type.
      DATA_TYPE_DISTINCT: UNSUPPORTED! Binary data type.
      DATA_TYPE_STRUCT: UNSUPPORTED! Binary data type.
      DATA_TYPE_ARRAY: UNSUPPORTED! Binary data type.
      DATA_TYPE_CLOB: UNSUPPORTED! Binary data type.
      DATA_TYPE_REF: UNSUPPORTED! Binary data type.
      DATA_TYPE_DATALINK: UNSUPPORTED! Binary data type.
      DATA_TYPE_ROWID: UNSUPPORTED! Row id data type.
      DATA_TYPE_BINARY: UNSUPPORTED! Binary data type.
      DATA_TYPE_VARBINARY: UNSUPPORTED! Variable binary data type.
      DATA_TYPE_LONGVARBINARY: UNSUPPORTED! Long variable binary data type.
      DATA_TYPE_NCLOB: UNSUPPORTED! NCLOB data type.
      DATA_TYPE_SQLXML: UNSUPPORTED! SQL XML data type is not supported.
      DATA_TYPE_REF_CURSOR: UNSUPPORTED! Cursor reference type is not
        supported.
      DATA_TYPE_TIME_WITH_TIMEZONE: UNSUPPORTED! Use TIME or TIMESTAMP
        instead.
      DATA_TYPE_TIMESTAMP_WITH_TIMEZONE: UNSUPPORTED! Use TIMESTAMP instead.
    """
    DATA_TYPE_UNSPECIFIED = 0
    DATA_TYPE_INT = 1
    DATA_TYPE_SMALLINT = 2
    DATA_TYPE_DOUBLE = 3
    DATA_TYPE_DATE = 4
    DATA_TYPE_DATETIME = 5
    DATA_TYPE_TIME = 6
    DATA_TYPE_STRING = 7
    DATA_TYPE_LONG = 8
    DATA_TYPE_BOOLEAN = 9
    DATA_TYPE_DECIMAL = 10
    DATA_TYPE_UUID = 11
    DATA_TYPE_BLOB = 12
    DATA_TYPE_BIT = 13
    DATA_TYPE_TINYINT = 14
    DATA_TYPE_INTEGER = 15
    DATA_TYPE_BIGINT = 16
    DATA_TYPE_FLOAT = 17
    DATA_TYPE_REAL = 18
    DATA_TYPE_NUMERIC = 19
    DATA_TYPE_CHAR = 20
    DATA_TYPE_VARCHAR = 21
    DATA_TYPE_LONGVARCHAR = 22
    DATA_TYPE_TIMESTAMP = 23
    DATA_TYPE_NCHAR = 24
    DATA_TYPE_NVARCHAR = 25
    DATA_TYPE_LONGNVARCHAR = 26
    DATA_TYPE_NULL = 27
    DATA_TYPE_OTHER = 28
    DATA_TYPE_JAVA_OBJECT = 29
    DATA_TYPE_DISTINCT = 30
    DATA_TYPE_STRUCT = 31
    DATA_TYPE_ARRAY = 32
    DATA_TYPE_CLOB = 33
    DATA_TYPE_REF = 34
    DATA_TYPE_DATALINK = 35
    DATA_TYPE_ROWID = 36
    DATA_TYPE_BINARY = 37
    DATA_TYPE_VARBINARY = 38
    DATA_TYPE_LONGVARBINARY = 39
    DATA_TYPE_NCLOB = 40
    DATA_TYPE_SQLXML = 41
    DATA_TYPE_REF_CURSOR = 42
    DATA_TYPE_TIME_WITH_TIMEZONE = 43
    DATA_TYPE_TIMESTAMP_WITH_TIMEZONE = 44

  dataType = _messages.EnumField('DataTypeValueValuesEnum', 1)
  defaultValue = _messages.MessageField('extra_types.JsonValue', 2)
  description = _messages.StringField(3)
  nullable = _messages.BooleanField(4)
  parameter = _messages.StringField(5)


class JwtClaims(_messages.Message):
  r"""JWT claims used for the jwt-bearer authorization grant.

  Fields:
    audience: Value for the "aud" claim.
    issuer: Value for the "iss" claim.
    subject: Value for the "sub" claim.
  """

  audience = _messages.StringField(1)
  issuer = _messages.StringField(2)
  subject = _messages.StringField(3)


class ListConnectionsResponse(_messages.Message):
  r"""Response message for ConnectorsService.ListConnections

  Fields:
    connections: Connections.
    nextPageToken: Next page token.
    unreachable: Locations that could not be reached.
  """

  connections = _messages.MessageField('Connection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListConnectorVersionsResponse(_messages.Message):
  r"""Response message for Connectors.ListConnectorVersions.

  Fields:
    connectorVersions: A list of connector versions.
    nextPageToken: Next page token.
    unreachable: Locations that could not be reached.
  """

  connectorVersions = _messages.MessageField('ConnectorVersion', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListConnectorsResponse(_messages.Message):
  r"""Response message for Connectors.ListConnectors.

  Fields:
    connectors: A list of connectors.
    nextPageToken: Next page token.
    unreachable: Locations that could not be reached.
  """

  connectors = _messages.MessageField('Connector', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListEndpointAttachmentsResponse(_messages.Message):
  r"""Response message for ConnectorsService.ListEndpointAttachments

  Fields:
    endpointAttachments: EndpointAttachments.
    nextPageToken: Next page token.
    unreachable: Locations that could not be reached.
  """

  endpointAttachments = _messages.MessageField('EndpointAttachment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListEventSubscriptionsResponse(_messages.Message):
  r"""Response message for ConnectorsService.ListEventSubscriptions

  Fields:
    eventSubscriptions: Subscriptions.
    nextPageToken: Next page token.
    unreachable: Locations that could not be reached.
  """

  eventSubscriptions = _messages.MessageField('EventSubscription', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListEventTypesResponse(_messages.Message):
  r"""Response message for Connectors.ListEventTypes.

  Fields:
    eventTypes: A list of connector versions.
    nextPageToken: Next page token.
  """

  eventTypes = _messages.MessageField('EventType', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListManagedZonesResponse(_messages.Message):
  r"""Response message for ConnectorsService.ListManagedZones

  Fields:
    managedZones: ManagedZones.
    nextPageToken: Next page token.
  """

  managedZones = _messages.MessageField('ManagedZone', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListProvidersResponse(_messages.Message):
  r"""Response message for Connectors.ListProviders.

  Fields:
    nextPageToken: Next page token.
    providers: A list of providers.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  providers = _messages.MessageField('Provider', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRuntimeActionSchemasResponse(_messages.Message):
  r"""Response message for ConnectorsService.ListRuntimeActionSchemas.

  Fields:
    nextPageToken: Next page token.
    runtimeActionSchemas: Runtime action schemas.
  """

  nextPageToken = _messages.StringField(1)
  runtimeActionSchemas = _messages.MessageField('RuntimeActionSchema', 2, repeated=True)


class ListRuntimeEntitySchemasResponse(_messages.Message):
  r"""Response message for ConnectorsService.ListRuntimeEntitySchemas.

  Fields:
    nextPageToken: Next page token.
    runtimeEntitySchemas: Runtime entity schemas.
  """

  nextPageToken = _messages.StringField(1)
  runtimeEntitySchemas = _messages.MessageField('RuntimeEntitySchema', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LockConfig(_messages.Message):
  r"""Determines whether or no a connection is locked. If locked, a reason
  must be specified.

  Fields:
    locked: Indicates whether or not the connection is locked.
    reason: Describes why a connection is locked.
  """

  locked = _messages.BooleanField(1)
  reason = _messages.StringField(2)


class LogicalExpression(_messages.Message):
  r"""Struct for representing boolean expressions.

  Enums:
    LogicalOperatorValueValuesEnum: The logical operator to use between the
      fields and conditions.

  Fields:
    fieldComparisons: A list of fields to be compared.
    logicalExpressions: A list of nested conditions to be compared.
    logicalOperator: The logical operator to use between the fields and
      conditions.
  """

  class LogicalOperatorValueValuesEnum(_messages.Enum):
    r"""The logical operator to use between the fields and conditions.

    Values:
      OPERATOR_UNSPECIFIED: The default value.
      AND: AND operator; The conditions must all be true.
      OR: OR operator; At least one of the conditions must be true.
    """
    OPERATOR_UNSPECIFIED = 0
    AND = 1
    OR = 2

  fieldComparisons = _messages.MessageField('FieldComparison', 1, repeated=True)
  logicalExpressions = _messages.MessageField('LogicalExpression', 2, repeated=True)
  logicalOperator = _messages.EnumField('LogicalOperatorValueValuesEnum', 3)


class ManagedZone(_messages.Message):
  r"""represents the Connector's Managed Zone resource

  Messages:
    LabelsValue: Optional. Resource labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    createTime: Output only. Created time.
    description: Optional. Description of the resource.
    dns: Required. DNS Name of the resource
    labels: Optional. Resource labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    name: Output only. Resource name of the Managed Zone. Format:
      projects/{project}/locations/global/managedZones/{managed_zone}
    targetProject: Required. The name of the Target Project
    targetVpc: Required. The name of the Target Project VPC Network
    updateTime: Output only. Updated time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user-provided metadata. Refer
    to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  dns = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  targetProject = _messages.StringField(6)
  targetVpc = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class NodeConfig(_messages.Message):
  r"""Node configuration for the connection.

  Fields:
    maxNodeCount: Maximum number of nodes in the runtime nodes.
    minNodeCount: Minimum number of nodes in the runtime nodes.
  """

  maxNodeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minNodeCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Oauth2AuthCodeFlow(_messages.Message):
  r"""Parameters to support Oauth 2.0 Auth Code Grant Authentication. See
  https://www.rfc-editor.org/rfc/rfc6749#section-1.3.1 for more details.

  Fields:
    authCode: Authorization code to be exchanged for access and refresh
      tokens.
    authUri: Auth URL for Authorization Code Flow
    clientId: Client ID for user-provided OAuth app.
    clientSecret: Client secret for user-provided OAuth app.
    enablePkce: Whether to enable PKCE when the user performs the auth code
      flow.
    pkceVerifier: PKCE verifier to be used during the auth code exchange.
    redirectUri: Redirect URI to be provided during the auth code exchange.
    scopes: Scopes the connection will request when the user performs the auth
      code flow.
  """

  authCode = _messages.StringField(1)
  authUri = _messages.StringField(2)
  clientId = _messages.StringField(3)
  clientSecret = _messages.MessageField('Secret', 4)
  enablePkce = _messages.BooleanField(5)
  pkceVerifier = _messages.StringField(6)
  redirectUri = _messages.StringField(7)
  scopes = _messages.StringField(8, repeated=True)


class Oauth2ClientCredentials(_messages.Message):
  r"""Parameters to support Oauth 2.0 Client Credentials Grant Authentication.
  See https://tools.ietf.org/html/rfc6749#section-1.3.4 for more details.

  Fields:
    clientId: The client identifier.
    clientSecret: Secret version reference containing the client secret.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.MessageField('Secret', 2)


class Oauth2JwtBearer(_messages.Message):
  r"""Parameters to support JSON Web Token (JWT) Profile for Oauth 2.0
  Authorization Grant based authentication. See
  https://tools.ietf.org/html/rfc7523 for more details.

  Fields:
    clientKey: Secret version reference containing a PKCS#8 PEM-encoded
      private key associated with the Client Certificate. This private key
      will be used to sign JWTs used for the jwt-bearer authorization grant.
      Specified in the form as: `projects/*/secrets/*/versions/*`.
    jwtClaims: JwtClaims providers fields to generate the token.
  """

  clientKey = _messages.MessageField('Secret', 1)
  jwtClaims = _messages.MessageField('JwtClaims', 2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Provider(_messages.Message):
  r"""Provider indicates the owner who provides the connectors.

  Enums:
    LaunchStageValueValuesEnum: Output only. Flag to mark the version
      indicating the launch stage.

  Messages:
    LabelsValue: Output only. Resource labels to represent user-provided
      metadata. Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources

  Fields:
    createTime: Output only. Created time.
    description: Output only. Description of the resource.
    displayName: Output only. Display name.
    documentationUri: Output only. Link to documentation page.
    externalUri: Output only. Link to external page.
    labels: Output only. Resource labels to represent user-provided metadata.
      Refer to cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    launchStage: Output only. Flag to mark the version indicating the launch
      stage.
    name: Output only. Resource name of the Provider. Format:
      projects/{project}/locations/{location}/providers/{provider} Only global
      location is supported for Provider resource.
    updateTime: Output only. Updated time.
    webAssetsLocation: Output only. Cloud storage location of icons etc
      consumed by UI.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Output only. Flag to mark the version indicating the launch stage.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: LAUNCH_STAGE_UNSPECIFIED.
      PREVIEW: PREVIEW.
      GA: GA.
      DEPRECATED: DEPRECATED.
      PRIVATE_PREVIEW: PRIVATE_PREVIEW.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    PREVIEW = 1
    GA = 2
    DEPRECATED = 3
    PRIVATE_PREVIEW = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Output only. Resource labels to represent user-provided metadata.
    Refer to cloud documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  documentationUri = _messages.StringField(4)
  externalUri = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 7)
  name = _messages.StringField(8)
  updateTime = _messages.StringField(9)
  webAssetsLocation = _messages.StringField(10)


class RefreshConnectionSchemaMetadataRequest(_messages.Message):
  r"""Request message for ConnectorsService.RefreshConnectionSchemaMetadata.
  """



class RepairEventingRequest(_messages.Message):
  r"""Request message for ConnectorsService.RepairEventing"""


class Resource(_messages.Message):
  r"""Resource definition

  Enums:
    TypeValueValuesEnum: Different types of resource supported.

  Fields:
    pathTemplate: Template to uniquely represent a Google Cloud resource in a
      format IAM expects This is a template that can have references to other
      values provided in the config variable template.
    type: Different types of resource supported.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Different types of resource supported.

    Values:
      TYPE_UNSPECIFIED: Value type is not specified.
      GCP_PROJECT: Google Cloud Project Resource.
      GCP_RESOURCE: Any Google Cloud Resource which is identified uniquely by
        IAM.
      GCP_SECRETMANAGER_SECRET: Google Cloud Secret Resource.
      GCP_SECRETMANAGER_SECRET_VERSION: Google Cloud Secret Version Resource.
    """
    TYPE_UNSPECIFIED = 0
    GCP_PROJECT = 1
    GCP_RESOURCE = 2
    GCP_SECRETMANAGER_SECRET = 3
    GCP_SECRETMANAGER_SECRET_VERSION = 4

  pathTemplate = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class ResultMetadata(_messages.Message):
  r"""Metadata of result field.

  Enums:
    DataTypeValueValuesEnum: The data type of the field.

  Fields:
    dataType: The data type of the field.
    description: A brief description of the field.
    field: Name of the result field.
  """

  class DataTypeValueValuesEnum(_messages.Enum):
    r"""The data type of the field.

    Values:
      DATA_TYPE_UNSPECIFIED: Data type is not specified.
      DATA_TYPE_INT: DEPRECATED! Use DATA_TYPE_INTEGER.
      DATA_TYPE_SMALLINT: Short integer(int16) data type.
      DATA_TYPE_DOUBLE: Double data type.
      DATA_TYPE_DATE: Date data type.
      DATA_TYPE_DATETIME: DEPRECATED! Use DATA_TYPE_TIMESTAMP.
      DATA_TYPE_TIME: Time data type.
      DATA_TYPE_STRING: DEPRECATED! Use DATA_TYPE_VARCHAR.
      DATA_TYPE_LONG: DEPRECATED! Use DATA_TYPE_BIGINT.
      DATA_TYPE_BOOLEAN: Boolean data type.
      DATA_TYPE_DECIMAL: Decimal data type.
      DATA_TYPE_UUID: DEPRECATED! Use DATA_TYPE_VARCHAR.
      DATA_TYPE_BLOB: UNSUPPORTED! Binary data type.
      DATA_TYPE_BIT: Bit data type.
      DATA_TYPE_TINYINT: Small integer(int8) data type.
      DATA_TYPE_INTEGER: Integer(int32) data type.
      DATA_TYPE_BIGINT: Long integer(int64) data type.
      DATA_TYPE_FLOAT: Float data type.
      DATA_TYPE_REAL: Real data type.
      DATA_TYPE_NUMERIC: Numeric data type.
      DATA_TYPE_CHAR: Char data type.
      DATA_TYPE_VARCHAR: Varchar data type.
      DATA_TYPE_LONGVARCHAR: Longvarchar data type.
      DATA_TYPE_TIMESTAMP: Timestamp data type.
      DATA_TYPE_NCHAR: Nchar data type.
      DATA_TYPE_NVARCHAR: Nvarchar data type.
      DATA_TYPE_LONGNVARCHAR: Longnvarchar data type.
      DATA_TYPE_NULL: Null data type.
      DATA_TYPE_OTHER: UNSUPPORTED! Binary data type.
      DATA_TYPE_JAVA_OBJECT: UNSUPPORTED! Binary data type.
      DATA_TYPE_DISTINCT: UNSUPPORTED! Binary data type.
      DATA_TYPE_STRUCT: UNSUPPORTED! Binary data type.
      DATA_TYPE_ARRAY: UNSUPPORTED! Binary data type.
      DATA_TYPE_CLOB: UNSUPPORTED! Binary data type.
      DATA_TYPE_REF: UNSUPPORTED! Binary data type.
      DATA_TYPE_DATALINK: UNSUPPORTED! Binary data type.
      DATA_TYPE_ROWID: UNSUPPORTED! Row id data type.
      DATA_TYPE_BINARY: UNSUPPORTED! Binary data type.
      DATA_TYPE_VARBINARY: UNSUPPORTED! Variable binary data type.
      DATA_TYPE_LONGVARBINARY: UNSUPPORTED! Long variable binary data type.
      DATA_TYPE_NCLOB: UNSUPPORTED! NCLOB data type.
      DATA_TYPE_SQLXML: UNSUPPORTED! SQL XML data type is not supported.
      DATA_TYPE_REF_CURSOR: UNSUPPORTED! Cursor reference type is not
        supported.
      DATA_TYPE_TIME_WITH_TIMEZONE: UNSUPPORTED! Use TIME or TIMESTAMP
        instead.
      DATA_TYPE_TIMESTAMP_WITH_TIMEZONE: UNSUPPORTED! Use TIMESTAMP instead.
    """
    DATA_TYPE_UNSPECIFIED = 0
    DATA_TYPE_INT = 1
    DATA_TYPE_SMALLINT = 2
    DATA_TYPE_DOUBLE = 3
    DATA_TYPE_DATE = 4
    DATA_TYPE_DATETIME = 5
    DATA_TYPE_TIME = 6
    DATA_TYPE_STRING = 7
    DATA_TYPE_LONG = 8
    DATA_TYPE_BOOLEAN = 9
    DATA_TYPE_DECIMAL = 10
    DATA_TYPE_UUID = 11
    DATA_TYPE_BLOB = 12
    DATA_TYPE_BIT = 13
    DATA_TYPE_TINYINT = 14
    DATA_TYPE_INTEGER = 15
    DATA_TYPE_BIGINT = 16
    DATA_TYPE_FLOAT = 17
    DATA_TYPE_REAL = 18
    DATA_TYPE_NUMERIC = 19
    DATA_TYPE_CHAR = 20
    DATA_TYPE_VARCHAR = 21
    DATA_TYPE_LONGVARCHAR = 22
    DATA_TYPE_TIMESTAMP = 23
    DATA_TYPE_NCHAR = 24
    DATA_TYPE_NVARCHAR = 25
    DATA_TYPE_LONGNVARCHAR = 26
    DATA_TYPE_NULL = 27
    DATA_TYPE_OTHER = 28
    DATA_TYPE_JAVA_OBJECT = 29
    DATA_TYPE_DISTINCT = 30
    DATA_TYPE_STRUCT = 31
    DATA_TYPE_ARRAY = 32
    DATA_TYPE_CLOB = 33
    DATA_TYPE_REF = 34
    DATA_TYPE_DATALINK = 35
    DATA_TYPE_ROWID = 36
    DATA_TYPE_BINARY = 37
    DATA_TYPE_VARBINARY = 38
    DATA_TYPE_LONGVARBINARY = 39
    DATA_TYPE_NCLOB = 40
    DATA_TYPE_SQLXML = 41
    DATA_TYPE_REF_CURSOR = 42
    DATA_TYPE_TIME_WITH_TIMEZONE = 43
    DATA_TYPE_TIMESTAMP_WITH_TIMEZONE = 44

  dataType = _messages.EnumField('DataTypeValueValuesEnum', 1)
  description = _messages.StringField(2)
  field = _messages.StringField(3)


class RetryEventSubscriptionRequest(_messages.Message):
  r"""Request message for ConnectorsService.RefreshEventSubscription"""


class RoleGrant(_messages.Message):
  r"""This configuration defines all the Cloud IAM roles that needs to be
  granted to a particular Google Cloud resource for the selected principal
  like service account. These configurations will let UI display to customers
  what IAM roles need to be granted by them. Or these configurations can be
  used by the UI to render a 'grant' button to do the same on behalf of the
  user.

  Enums:
    PrincipalValueValuesEnum: Prinicipal/Identity for whom the role need to
      assigned.

  Fields:
    helperTextTemplate: Template that UI can use to provide helper text to
      customers.
    principal: Prinicipal/Identity for whom the role need to assigned.
    resource: Resource on which the roles needs to be granted for the
      principal.
    roles: List of roles that need to be granted.
  """

  class PrincipalValueValuesEnum(_messages.Enum):
    r"""Prinicipal/Identity for whom the role need to assigned.

    Values:
      PRINCIPAL_UNSPECIFIED: Value type is not specified.
      CONNECTOR_SA: Service Account used for Connector workload identity This
        is either the default service account if unspecified or Service
        Account provided by Customers through BYOSA.
    """
    PRINCIPAL_UNSPECIFIED = 0
    CONNECTOR_SA = 1

  helperTextTemplate = _messages.StringField(1)
  principal = _messages.EnumField('PrincipalValueValuesEnum', 2)
  resource = _messages.MessageField('Resource', 3)
  roles = _messages.StringField(4, repeated=True)


class RuntimeActionSchema(_messages.Message):
  r"""Schema of a runtime action.

  Fields:
    action: Output only. Name of the action.
    inputParameters: Output only. List of input parameter metadata for the
      action.
    resultMetadata: Output only. List of result field metadata.
  """

  action = _messages.StringField(1)
  inputParameters = _messages.MessageField('InputParameter', 2, repeated=True)
  resultMetadata = _messages.MessageField('ResultMetadata', 3, repeated=True)


class RuntimeConfig(_messages.Message):
  r"""RuntimeConfig is the singleton resource of each location. It includes
  generic resource configs consumed by control plane and runtime plane like:
  pub/sub topic/subscription resource name, Cloud Storage location storing
  schema etc.

  Enums:
    StateValueValuesEnum: Output only. The state of the location.

  Fields:
    conndSubscription: Output only. Pub/Sub subscription for connd to receive
      message. E.g. projects/{project-id}/subscriptions/{topic-id}
    conndTopic: Output only. Pub/Sub topic for connd to send message. E.g.
      projects/{project-id}/topics/{topic-id}
    controlPlaneSubscription: Output only. Pub/Sub subscription for control
      plane to receive message. E.g. projects/{project-
      id}/subscriptions/{topic-id}
    controlPlaneTopic: Output only. Pub/Sub topic for control plne to send
      message. communication. E.g. projects/{project-id}/topics/{topic-id}
    locationId: Output only. location_id of the runtime location. E.g. "us-
      west1".
    name: Output only. Name of the runtimeConfig resource. Format:
      projects/{project}/locations/{location}/runtimeConfig
    runtimeEndpoint: Output only. The endpoint of the connectors runtime
      ingress.
    schemaGcsBucket: Output only. The Cloud Storage bucket that stores
      connector's schema reports.
    serviceDirectory: Output only. The name of the Service Directory service
      name.
    state: Output only. The state of the location.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the location.

    Values:
      STATE_UNSPECIFIED: STATE_UNSPECIFIED.
      INACTIVE: INACTIVE.
      ACTIVATING: ACTIVATING.
      ACTIVE: ACTIVE.
      CREATING: CREATING.
      DELETING: DELETING.
      UPDATING: UPDATING.
    """
    STATE_UNSPECIFIED = 0
    INACTIVE = 1
    ACTIVATING = 2
    ACTIVE = 3
    CREATING = 4
    DELETING = 5
    UPDATING = 6

  conndSubscription = _messages.StringField(1)
  conndTopic = _messages.StringField(2)
  controlPlaneSubscription = _messages.StringField(3)
  controlPlaneTopic = _messages.StringField(4)
  locationId = _messages.StringField(5)
  name = _messages.StringField(6)
  runtimeEndpoint = _messages.StringField(7)
  schemaGcsBucket = _messages.StringField(8)
  serviceDirectory = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)


class RuntimeEntitySchema(_messages.Message):
  r"""Schema of a runtime entity.

  Fields:
    entity: Output only. Name of the entity.
    fields: Output only. List of fields in the entity.
  """

  entity = _messages.StringField(1)
  fields = _messages.MessageField('Field', 2, repeated=True)


class Secret(_messages.Message):
  r"""Secret provides a reference to entries in Secret Manager.

  Fields:
    secretVersion: The resource name of the secret version in the format,
      format as: `projects/*/secrets/*/versions/*`.
  """

  secretVersion = _messages.StringField(1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class Settings(_messages.Message):
  r"""Global Settings details.

  Fields:
    name: Output only. Resource name of the Connection. Format:
      projects/{project}/locations/global/settings}
    payg: Output only. Flag indicates if user is in PayG model
    tenantProjectId: Output only. Tenant project id of the consumer project.
    vpcsc: Optional. Flag indicates whether vpc-sc is enabled.
  """

  name = _messages.StringField(1)
  payg = _messages.BooleanField(2)
  tenantProjectId = _messages.StringField(3)
  vpcsc = _messages.BooleanField(4)


class Source(_messages.Message):
  r"""Source to extract the backend from.

  Enums:
    SourceTypeValueValuesEnum: Type of the source.

  Fields:
    fieldId: Field identifier. For example config vaiable name.
    sourceType: Type of the source.
  """

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""Type of the source.

    Values:
      SOURCE_TYPE_UNSPECIFIED: Default SOURCE.
      CONFIG_VARIABLE: Config Variable source type.
    """
    SOURCE_TYPE_UNSPECIFIED = 0
    CONFIG_VARIABLE = 1

  fieldId = _messages.StringField(1)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 2)


class SshPublicKey(_messages.Message):
  r"""Parameters to support Ssh public key Authentication.

  Fields:
    certType: Format of SSH Client cert.
    sshClientCert: SSH Client Cert. It should contain both public and private
      key.
    sshClientCertPass: Password (passphrase) for ssh client certificate if it
      has one.
    username: The user account used to authenticate.
  """

  certType = _messages.StringField(1)
  sshClientCert = _messages.MessageField('Secret', 2)
  sshClientCertPass = _messages.MessageField('Secret', 3)
  username = _messages.StringField(4)


class SslConfig(_messages.Message):
  r"""SSL Configuration of a connection

  Enums:
    ClientCertTypeValueValuesEnum: Type of Client Cert (PEM/JKS/.. etc.)
    ServerCertTypeValueValuesEnum: Type of Server Cert (PEM/JKS/.. etc.)
    TrustModelValueValuesEnum: Trust Model of the SSL connection
    TypeValueValuesEnum: Controls the ssl type for the given connector
      version.

  Fields:
    additionalVariables: Additional SSL related field values
    clientCertType: Type of Client Cert (PEM/JKS/.. etc.)
    clientCertificate: Client Certificate
    clientPrivateKey: Client Private Key
    clientPrivateKeyPass: Secret containing the passphrase protecting the
      Client Private Key
    privateServerCertificate: Private Server Certificate. Needs to be
      specified if trust model is `PRIVATE`.
    serverCertType: Type of Server Cert (PEM/JKS/.. etc.)
    trustModel: Trust Model of the SSL connection
    type: Controls the ssl type for the given connector version.
    useSsl: Bool for enabling SSL
  """

  class ClientCertTypeValueValuesEnum(_messages.Enum):
    r"""Type of Client Cert (PEM/JKS/.. etc.)

    Values:
      CERT_TYPE_UNSPECIFIED: Cert type unspecified.
      PEM: Privacy Enhanced Mail (PEM) Type
    """
    CERT_TYPE_UNSPECIFIED = 0
    PEM = 1

  class ServerCertTypeValueValuesEnum(_messages.Enum):
    r"""Type of Server Cert (PEM/JKS/.. etc.)

    Values:
      CERT_TYPE_UNSPECIFIED: Cert type unspecified.
      PEM: Privacy Enhanced Mail (PEM) Type
    """
    CERT_TYPE_UNSPECIFIED = 0
    PEM = 1

  class TrustModelValueValuesEnum(_messages.Enum):
    r"""Trust Model of the SSL connection

    Values:
      PUBLIC: Public Trust Model. Takes the Default Java trust store.
      PRIVATE: Private Trust Model. Takes custom/private trust store.
      INSECURE: Insecure Trust Model. Accept all certificates.
    """
    PUBLIC = 0
    PRIVATE = 1
    INSECURE = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Controls the ssl type for the given connector version.

    Values:
      SSL_TYPE_UNSPECIFIED: No SSL configuration required.
      TLS: TLS Handshake
      MTLS: mutual TLS (MTLS) Handshake
    """
    SSL_TYPE_UNSPECIFIED = 0
    TLS = 1
    MTLS = 2

  additionalVariables = _messages.MessageField('ConfigVariable', 1, repeated=True)
  clientCertType = _messages.EnumField('ClientCertTypeValueValuesEnum', 2)
  clientCertificate = _messages.MessageField('Secret', 3)
  clientPrivateKey = _messages.MessageField('Secret', 4)
  clientPrivateKeyPass = _messages.MessageField('Secret', 5)
  privateServerCertificate = _messages.MessageField('Secret', 6)
  serverCertType = _messages.EnumField('ServerCertTypeValueValuesEnum', 7)
  trustModel = _messages.EnumField('TrustModelValueValuesEnum', 8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)
  useSsl = _messages.BooleanField(10)


class SslConfigTemplate(_messages.Message):
  r"""Ssl config details of a connector version

  Enums:
    ClientCertTypeValueListEntryValuesEnum:
    ServerCertTypeValueListEntryValuesEnum:
    SslTypeValueValuesEnum: Controls the ssl type for the given connector
      version

  Fields:
    additionalVariables: Any additional fields that need to be rendered
    clientCertType: List of supported Client Cert Types
    isTlsMandatory: Boolean for determining if the connector version mandates
      TLS.
    serverCertType: List of supported Server Cert Types
    sslType: Controls the ssl type for the given connector version
  """

  class ClientCertTypeValueListEntryValuesEnum(_messages.Enum):
    r"""ClientCertTypeValueListEntryValuesEnum enum type.

    Values:
      CERT_TYPE_UNSPECIFIED: Cert type unspecified.
      PEM: Privacy Enhanced Mail (PEM) Type
    """
    CERT_TYPE_UNSPECIFIED = 0
    PEM = 1

  class ServerCertTypeValueListEntryValuesEnum(_messages.Enum):
    r"""ServerCertTypeValueListEntryValuesEnum enum type.

    Values:
      CERT_TYPE_UNSPECIFIED: Cert type unspecified.
      PEM: Privacy Enhanced Mail (PEM) Type
    """
    CERT_TYPE_UNSPECIFIED = 0
    PEM = 1

  class SslTypeValueValuesEnum(_messages.Enum):
    r"""Controls the ssl type for the given connector version

    Values:
      SSL_TYPE_UNSPECIFIED: No SSL configuration required.
      TLS: TLS Handshake
      MTLS: mutual TLS (MTLS) Handshake
    """
    SSL_TYPE_UNSPECIFIED = 0
    TLS = 1
    MTLS = 2

  additionalVariables = _messages.MessageField('ConfigVariableTemplate', 1, repeated=True)
  clientCertType = _messages.EnumField('ClientCertTypeValueListEntryValuesEnum', 2, repeated=True)
  isTlsMandatory = _messages.BooleanField(3)
  serverCertType = _messages.EnumField('ServerCertTypeValueListEntryValuesEnum', 4, repeated=True)
  sslType = _messages.EnumField('SslTypeValueValuesEnum', 5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class SupportedRuntimeFeatures(_messages.Message):
  r"""Supported runtime features of a connector version.

  Fields:
    actionApis: Specifies if the connector supports action apis like
      'executeAction'.
    entityApis: Specifies if the connector supports entity apis like
      'createEntity'.
    sqlQuery: Specifies if the connector supports 'ExecuteSqlQuery' operation.
  """

  actionApis = _messages.BooleanField(1)
  entityApis = _messages.BooleanField(2)
  sqlQuery = _messages.BooleanField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UserPassword(_messages.Message):
  r"""Parameters to support Username and Password Authentication.

  Fields:
    password: Secret version reference containing the password.
    username: Username.
  """

  password = _messages.MessageField('Secret', 1)
  username = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

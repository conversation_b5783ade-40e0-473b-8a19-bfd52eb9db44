"""Generated message classes for cloudscheduler version v1alpha1.

Creates and manages jobs run on a regular recurring schedule.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudscheduler'


class AppEngineHttpTarget(_messages.Message):
  r"""App Engine target. The job will be pushed to a job handler by means of
  an HTTP request via an AppEngineHttpTarget.http_method such as HTTP POST,
  HTTP GET, etc. The job is acknowledged by means of an HTTP response code in
  the range [200 - 299]. Error 503 is considered an App Engine system error
  instead of an application error. Requests returning error 503 will be
  retried regardless of retry configuration and not counted against retry
  counts. Any other response code, or a failure to receive a response before
  the deadline, constitutes a failed execution.

  Enums:
    HttpMethodValueValuesEnum: The HTTP method to use for the request. PATCH
      and OPTIONS are not permitted.

  Messages:
    HeadersValue: HTTP request headers. This map contains the header field
      names and values. Headers can be set when the job is created. Cloud
      Scheduler sets some headers to default values: * `User-Agent`: By
      default, this header is `"AppEngine-Google;
      (+http://code.google.com/appengine)"`. This header can be modified, but
      Cloud Scheduler will append `"AppEngine-Google;
      (+http://code.google.com/appengine)"` to the modified `User-Agent`. *
      `X-CloudScheduler`: This header will be set to true. *
      `X-CloudScheduler-JobName`: This header will contain the job name. *
      `X-CloudScheduler-ScheduleTime`: For Cloud Scheduler jobs specified in
      the unix-cron format, this header will contain the job schedule time in
      RFC3339 UTC "Zulu" format. If the job has a body and the following
      headers are not set by the user, Cloud Scheduler sets default values: *
      `Content-Type`: This will be set to `"application/octet-stream"`. You
      can override this default by explicitly setting `Content-Type` to a
      particular media type when creating the job. For example, you can set
      `Content-Type` to `"application/json"`. The headers below are output
      only. They cannot be set or overridden: * `Content-Length`: This is
      computed by Cloud Scheduler. * `X-Google-*`: For Google internal use
      only. * `X-AppEngine-*`: For Google internal use only. In addition, some
      App Engine headers, which contain job-specific information, are also be
      sent to the job handler.

  Fields:
    appEngineRouting: App Engine Routing setting for the job.
    body: Body. HTTP request body. A request body is allowed only if the HTTP
      method is POST or PUT. It will result in an error to set body on a job
      with an incompatible HttpMethod.
    headers: HTTP request headers. This map contains the header field names
      and values. Headers can be set when the job is created. Cloud Scheduler
      sets some headers to default values: * `User-Agent`: By default, this
      header is `"AppEngine-Google; (+http://code.google.com/appengine)"`.
      This header can be modified, but Cloud Scheduler will append
      `"AppEngine-Google; (+http://code.google.com/appengine)"` to the
      modified `User-Agent`. * `X-CloudScheduler`: This header will be set to
      true. * `X-CloudScheduler-JobName`: This header will contain the job
      name. * `X-CloudScheduler-ScheduleTime`: For Cloud Scheduler jobs
      specified in the unix-cron format, this header will contain the job
      schedule time in RFC3339 UTC "Zulu" format. If the job has a body and
      the following headers are not set by the user, Cloud Scheduler sets
      default values: * `Content-Type`: This will be set to
      `"application/octet-stream"`. You can override this default by
      explicitly setting `Content-Type` to a particular media type when
      creating the job. For example, you can set `Content-Type` to
      `"application/json"`. The headers below are output only. They cannot be
      set or overridden: * `Content-Length`: This is computed by Cloud
      Scheduler. * `X-Google-*`: For Google internal use only. *
      `X-AppEngine-*`: For Google internal use only. In addition, some App
      Engine headers, which contain job-specific information, are also be sent
      to the job handler.
    httpMethod: The HTTP method to use for the request. PATCH and OPTIONS are
      not permitted.
    relativeUrl: The relative URL. The relative URL must begin with "/" and
      must be a valid HTTP relative URL. It can contain a path, query string
      arguments, and `#` fragments. If the relative URL is empty, then the
      root path "/" will be used. No spaces are allowed, and the maximum
      length allowed is 2083 characters.
  """

  class HttpMethodValueValuesEnum(_messages.Enum):
    r"""The HTTP method to use for the request. PATCH and OPTIONS are not
    permitted.

    Values:
      HTTP_METHOD_UNSPECIFIED: HTTP method unspecified. Defaults to POST.
      POST: HTTP Post
      GET: HTTP Get
      HEAD: HTTP Head
      PUT: HTTP Put
      DELETE: HTTP Delete
      PATCH: HTTP Patch
      OPTIONS: HTTP Options
    """
    HTTP_METHOD_UNSPECIFIED = 0
    POST = 1
    GET = 2
    HEAD = 3
    PUT = 4
    DELETE = 5
    PATCH = 6
    OPTIONS = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class HeadersValue(_messages.Message):
    r"""HTTP request headers. This map contains the header field names and
    values. Headers can be set when the job is created. Cloud Scheduler sets
    some headers to default values: * `User-Agent`: By default, this header is
    `"AppEngine-Google; (+http://code.google.com/appengine)"`. This header can
    be modified, but Cloud Scheduler will append `"AppEngine-Google;
    (+http://code.google.com/appengine)"` to the modified `User-Agent`. *
    `X-CloudScheduler`: This header will be set to true. * `X-CloudScheduler-
    JobName`: This header will contain the job name. * `X-CloudScheduler-
    ScheduleTime`: For Cloud Scheduler jobs specified in the unix-cron format,
    this header will contain the job schedule time in RFC3339 UTC "Zulu"
    format. If the job has a body and the following headers are not set by the
    user, Cloud Scheduler sets default values: * `Content-Type`: This will be
    set to `"application/octet-stream"`. You can override this default by
    explicitly setting `Content-Type` to a particular media type when creating
    the job. For example, you can set `Content-Type` to `"application/json"`.
    The headers below are output only. They cannot be set or overridden: *
    `Content-Length`: This is computed by Cloud Scheduler. * `X-Google-*`: For
    Google internal use only. * `X-AppEngine-*`: For Google internal use only.
    In addition, some App Engine headers, which contain job-specific
    information, are also be sent to the job handler.

    Messages:
      AdditionalProperty: An additional property for a HeadersValue object.

    Fields:
      additionalProperties: Additional properties of type HeadersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a HeadersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  appEngineRouting = _messages.MessageField('AppEngineRouting', 1)
  body = _messages.BytesField(2)
  headers = _messages.MessageField('HeadersValue', 3)
  httpMethod = _messages.EnumField('HttpMethodValueValuesEnum', 4)
  relativeUrl = _messages.StringField(5)


class AppEngineRouting(_messages.Message):
  r"""App Engine Routing. For more information about services, versions, and
  instances see [An Overview of App
  Engine](https://cloud.google.com/appengine/docs/python/an-overview-of-app-
  engine), [Microservices Architecture on Google App
  Engine](https://cloud.google.com/appengine/docs/python/microservices-on-app-
  engine), [App Engine Standard request
  routing](https://cloud.google.com/appengine/docs/standard/python/how-
  requests-are-routed), and [App Engine Flex request
  routing](https://cloud.google.com/appengine/docs/flexible/python/how-
  requests-are-routed).

  Fields:
    host: Output only. The host that the job is sent to. For more information
      about how App Engine requests are routed, see
      [here](https://cloud.google.com/appengine/docs/standard/python/how-
      requests-are-routed). The host is constructed as: * `host =
      [application_domain_name]` `| [service] + '.' +
      [application_domain_name]` `| [version] + '.' +
      [application_domain_name]` `| [version_dot_service]+ '.' +
      [application_domain_name]` `| [instance] + '.' +
      [application_domain_name]` `| [instance_dot_service] + '.' +
      [application_domain_name]` `| [instance_dot_version] + '.' +
      [application_domain_name]` `| [instance_dot_version_dot_service] + '.' +
      [application_domain_name]` * `application_domain_name` = The domain name
      of the app, for example .appspot.com, which is associated with the job's
      project ID. * `service =` AppEngineRouting.service * `version =`
      AppEngineRouting.version * `version_dot_service =`
      AppEngineRouting.version `+ '.' +` AppEngineRouting.service * `instance
      =` AppEngineRouting.instance * `instance_dot_service =`
      AppEngineRouting.instance `+ '.' +` AppEngineRouting.service *
      `instance_dot_version =` AppEngineRouting.instance `+ '.' +`
      AppEngineRouting.version * `instance_dot_version_dot_service =`
      AppEngineRouting.instance `+ '.' +` AppEngineRouting.version `+ '.' +`
      AppEngineRouting.service If AppEngineRouting.service is empty, then the
      job will be sent to the service which is the default service when the
      job is attempted. If AppEngineRouting.version is empty, then the job
      will be sent to the version which is the default version when the job is
      attempted. If AppEngineRouting.instance is empty, then the job will be
      sent to an instance which is available when the job is attempted. If
      AppEngineRouting.service, AppEngineRouting.version, or
      AppEngineRouting.instance is invalid, then the job will be sent to the
      default version of the default service when the job is attempted.
    instance: App instance. By default, the job is sent to an instance which
      is available when the job is attempted. Requests can only be sent to a
      specific instance if [manual scaling is used in App Engine
      Standard](https://cloud.google.com/appengine/docs/python/an-overview-of-
      app-engine?#scaling_types_and_instance_classes). App Engine Flex does
      not support instances. For more information, see [App Engine Standard
      request
      routing](https://cloud.google.com/appengine/docs/standard/python/how-
      requests-are-routed) and [App Engine Flex request
      routing](https://cloud.google.com/appengine/docs/flexible/python/how-
      requests-are-routed).
    service: App service. By default, the job is sent to the service which is
      the default service when the job is attempted.
    version: App version. By default, the job is sent to the version which is
      the default version when the job is attempted.
  """

  host = _messages.StringField(1)
  instance = _messages.StringField(2)
  service = _messages.StringField(3)
  version = _messages.StringField(4)


class CloudschedulerProjectsLocationsGetRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class CloudschedulerProjectsLocationsJobsCreateRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsJobsCreateRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    parent: Required. The location name. For example:
      `projects/PROJECT_ID/locations/LOCATION_ID`.
  """

  job = _messages.MessageField('Job', 1)
  parent = _messages.StringField(2, required=True)


class CloudschedulerProjectsLocationsJobsDeleteRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsJobsDeleteRequest object.

  Fields:
    legacyAppEngineCron: This field is used to manage the legacy App Engine
      Cron jobs using the Cloud Scheduler API. If the field is set to true,
      the job in the __cron queue with the corresponding name will be deleted
      instead.
    name: Required. The job name. For example:
      `projects/PROJECT_ID/locations/LOCATION_ID/jobs/JOB_ID`.
  """

  legacyAppEngineCron = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class CloudschedulerProjectsLocationsJobsGetRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsJobsGetRequest object.

  Fields:
    name: Required. The job name. For example:
      `projects/PROJECT_ID/locations/LOCATION_ID/jobs/JOB_ID`.
  """

  name = _messages.StringField(1, required=True)


class CloudschedulerProjectsLocationsJobsListRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsJobsListRequest object.

  Fields:
    filter: `filter` can be used to specify a subset of jobs. If `filter`
      equals `target_config="HttpConfig"`, then the http target jobs are
      retrieved. If `filter` equals `target_config="PubSubConfig"`, then the
      Pub/Sub target jobs are retrieved. If `filter` equals `labels.foo=value1
      labels.foo=value2` then only jobs which are labeled with foo=value1 AND
      foo=value2 will be returned.
    legacyAppEngineCron: This field is used to manage the legacy App Engine
      Cron jobs using the Cloud Scheduler API. If the field is set to true,
      the jobs in the __cron queue will be listed instead.
    pageSize: Requested page size. Fewer jobs than requested might be
      returned. The maximum page size is 500. If unspecified, the page size
      will be the maximum. Fewer jobs than requested might be returned, even
      if more jobs exist; use next_page_token to determine if more jobs exist.
    pageToken: A token identifying a page of results the server will return.
      To request the first page results, page_token must be empty. To request
      the next page of results, page_token must be the value of
      ListJobsResponse.next_page_token returned from the previous call to
      CloudScheduler.ListJobs. It is an error to switch the value of
      ListJobsRequest.filter or ListJobsRequest.order_by while iterating
      through pages.
    parent: Required. The location name. For example:
      `projects/PROJECT_ID/locations/LOCATION_ID`.
  """

  filter = _messages.StringField(1)
  legacyAppEngineCron = _messages.BooleanField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudschedulerProjectsLocationsJobsPatchRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsJobsPatchRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    name: Optionally caller-specified in CreateJob, after which it becomes
      output only. The job name. For example:
      `projects/PROJECT_ID/locations/LOCATION_ID/jobs/JOB_ID`. The maximum
      allowed length for `JOB_ID` is 500 characters.
    updateMask: A mask used to specify which fields of the job are being
      updated.
  """

  job = _messages.MessageField('Job', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudschedulerProjectsLocationsJobsPauseRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsJobsPauseRequest object.

  Fields:
    name: Required. The job name. For example:
      `projects/PROJECT_ID/locations/LOCATION_ID/jobs/JOB_ID`.
    pauseJobRequest: A PauseJobRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  pauseJobRequest = _messages.MessageField('PauseJobRequest', 2)


class CloudschedulerProjectsLocationsJobsResumeRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsJobsResumeRequest object.

  Fields:
    name: Required. The job name. For example:
      `projects/PROJECT_ID/locations/LOCATION_ID/jobs/JOB_ID`.
    resumeJobRequest: A ResumeJobRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  resumeJobRequest = _messages.MessageField('ResumeJobRequest', 2)


class CloudschedulerProjectsLocationsJobsRunRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsJobsRunRequest object.

  Fields:
    name: Required. The job name. For example:
      `projects/PROJECT_ID/locations/LOCATION_ID/jobs/JOB_ID`.
    runJobRequest: A RunJobRequest resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  runJobRequest = _messages.MessageField('RunJobRequest', 2)


class CloudschedulerProjectsLocationsListRequest(_messages.Message):
  r"""A CloudschedulerProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class HttpTarget(_messages.Message):
  r"""Http target. The job will be pushed to the job handler by means of an
  HTTP request via an HttpTarget.http_method such as HTTP POST, HTTP GET, etc.
  The job is acknowledged by means of an HTTP response code in the range [200
  - 299]. A failure to receive a response constitutes a failed execution. For
  a redirected request, the response returned by the redirected request is
  considered.

  Enums:
    HttpMethodValueValuesEnum: Which HTTP method to use for the request.

  Messages:
    HeadersValue: HTTP request headers. This map contains the header field
      names and values. The user can specify HTTP request headers to send with
      the job's HTTP request. Repeated headers are not supported, but a header
      value can contain commas. The following headers represent a subset of
      the headers that accompany the job's HTTP request. Some HTTP request
      headers are ignored or replaced. A partial list of headers that are
      ignored or replaced is below: - Host: This will be computed by Cloud
      Scheduler and derived from HttpTarget.url. - Content-Length: This will
      be computed by Cloud Scheduler. - User-Agent: This will be populated by
      Cloud Scheduler. - X-Google-*: Google internal use only. -
      X-AppEngine-*: Google internal use only. - `X-CloudScheduler`: This
      header will be set to true. - `X-CloudScheduler-JobName`: This header
      will contain the job name. - `X-CloudScheduler-ScheduleTime`: For Cloud
      Scheduler jobs specified in the unix-cron format, this header will
      contain the job schedule time in RFC3339 UTC "Zulu" format. If the job
      has a body and the following headers are not set by the user, Cloud
      Scheduler sets default values: * `Content-Type`: This will be set to
      `"application/octet-stream"`. You can override this default by
      explicitly setting `Content-Type` to a particular media type when
      creating the job. For example, you can set `Content-Type` to
      `"application/json"`. The total size of headers must be less than 80KB.

  Fields:
    body: HTTP request body. A request body is allowed only if the HTTP method
      is POST, PUT, or PATCH. It is an error to set body on a job with an
      incompatible HttpMethod.
    headers: HTTP request headers. This map contains the header field names
      and values. The user can specify HTTP request headers to send with the
      job's HTTP request. Repeated headers are not supported, but a header
      value can contain commas. The following headers represent a subset of
      the headers that accompany the job's HTTP request. Some HTTP request
      headers are ignored or replaced. A partial list of headers that are
      ignored or replaced is below: - Host: This will be computed by Cloud
      Scheduler and derived from HttpTarget.url. - Content-Length: This will
      be computed by Cloud Scheduler. - User-Agent: This will be populated by
      Cloud Scheduler. - X-Google-*: Google internal use only. -
      X-AppEngine-*: Google internal use only. - `X-CloudScheduler`: This
      header will be set to true. - `X-CloudScheduler-JobName`: This header
      will contain the job name. - `X-CloudScheduler-ScheduleTime`: For Cloud
      Scheduler jobs specified in the unix-cron format, this header will
      contain the job schedule time in RFC3339 UTC "Zulu" format. If the job
      has a body and the following headers are not set by the user, Cloud
      Scheduler sets default values: * `Content-Type`: This will be set to
      `"application/octet-stream"`. You can override this default by
      explicitly setting `Content-Type` to a particular media type when
      creating the job. For example, you can set `Content-Type` to
      `"application/json"`. The total size of headers must be less than 80KB.
    httpMethod: Which HTTP method to use for the request.
    oauthToken: If specified, an [OAuth
      token](https://developers.google.com/identity/protocols/OAuth2) will be
      generated and attached as an `Authorization` header in the HTTP request.
      This type of authorization should generally only be used when calling
      Google APIs hosted on *.googleapis.com.
    oidcToken: If specified, an
      [OIDC](https://developers.google.com/identity/protocols/OpenIDConnect)
      token will be generated and attached as an `Authorization` header in the
      HTTP request. This type of authorization can be used for many scenarios,
      including calling Cloud Run, or endpoints where you intend to validate
      the token yourself.
    url: Required. The full url path that the request will be sent to. This
      string must begin with either "http://" or "https://". Some examples of
      valid values for HttpTarget.url are: `http://acme.com` and
      `https://acme.com/sales:8080`. Cloud Scheduler will encode some
      characters for safety and compatibility. The maximum allowed URL length
      is 2083 characters after encoding.
  """

  class HttpMethodValueValuesEnum(_messages.Enum):
    r"""Which HTTP method to use for the request.

    Values:
      HTTP_METHOD_UNSPECIFIED: HTTP method unspecified. Defaults to POST.
      POST: HTTP Post
      GET: HTTP Get
      HEAD: HTTP Head
      PUT: HTTP Put
      DELETE: HTTP Delete
      PATCH: HTTP Patch
      OPTIONS: HTTP Options
    """
    HTTP_METHOD_UNSPECIFIED = 0
    POST = 1
    GET = 2
    HEAD = 3
    PUT = 4
    DELETE = 5
    PATCH = 6
    OPTIONS = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class HeadersValue(_messages.Message):
    r"""HTTP request headers. This map contains the header field names and
    values. The user can specify HTTP request headers to send with the job's
    HTTP request. Repeated headers are not supported, but a header value can
    contain commas. The following headers represent a subset of the headers
    that accompany the job's HTTP request. Some HTTP request headers are
    ignored or replaced. A partial list of headers that are ignored or
    replaced is below: - Host: This will be computed by Cloud Scheduler and
    derived from HttpTarget.url. - Content-Length: This will be computed by
    Cloud Scheduler. - User-Agent: This will be populated by Cloud Scheduler.
    - X-Google-*: Google internal use only. - X-AppEngine-*: Google internal
    use only. - `X-CloudScheduler`: This header will be set to true. -
    `X-CloudScheduler-JobName`: This header will contain the job name. -
    `X-CloudScheduler-ScheduleTime`: For Cloud Scheduler jobs specified in the
    unix-cron format, this header will contain the job schedule time in
    RFC3339 UTC "Zulu" format. If the job has a body and the following headers
    are not set by the user, Cloud Scheduler sets default values: * `Content-
    Type`: This will be set to `"application/octet-stream"`. You can override
    this default by explicitly setting `Content-Type` to a particular media
    type when creating the job. For example, you can set `Content-Type` to
    `"application/json"`. The total size of headers must be less than 80KB.

    Messages:
      AdditionalProperty: An additional property for a HeadersValue object.

    Fields:
      additionalProperties: Additional properties of type HeadersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a HeadersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  body = _messages.BytesField(1)
  headers = _messages.MessageField('HeadersValue', 2)
  httpMethod = _messages.EnumField('HttpMethodValueValuesEnum', 3)
  oauthToken = _messages.MessageField('OAuthToken', 4)
  oidcToken = _messages.MessageField('OidcToken', 5)
  url = _messages.StringField(6)


class Job(_messages.Message):
  r"""Configuration for a job. The maximum allowed size for a job is 1MB.

  Enums:
    StateValueValuesEnum: Output only. State of the job. For example: enabled,
      paused, or disabled.

  Fields:
    appEngineHttpTarget: App Engine Http target.
    attemptDeadline: The deadline for job attempts. If the request handler
      does not respond by this deadline then the request is cancelled and the
      attempt is marked as a `DEADLINE_EXCEEDED` failure. The failed attempt
      can be viewed in execution logs. Cloud Scheduler will retry the job
      according to the RetryConfig. The default and the allowed values depend
      on the type of target: * For HTTP targets, the default is 3 minutes. The
      deadline must be in the interval [15 seconds, 30 minutes]. * For App
      Engine HTTP targets, 0 indicates that the request has the default
      deadline. The default deadline depends on the scaling type of the
      service: 10 minutes for standard apps with automatic scaling, 24 hours
      for standard apps with manual and basic scaling, and 60 minutes for flex
      apps. If the request deadline is set, it must be in the interval [15
      seconds, 24 hours 15 seconds]. * For Pub/Sub targets, this field is
      ignored.
    description: Optionally caller-specified in CreateJob or UpdateJob. A
      human-readable description for the job. This string must not contain
      more than 500 characters.
    httpTarget: Http target.
    lastAttemptTime: Output only. The time the last job attempt started.
    legacyAppEngineCron: Immutable. This field is used to manage the legacy
      App Engine Cron jobs using the Cloud Scheduler API. If the field is set
      to true, the job will be considered to be a legacy job. Note that App
      Engine Cron jobs have fewer features than Cloud Scheduler jobs, e.g.,
      are only limited to App Engine targets.
    name: Optionally caller-specified in CreateJob, after which it becomes
      output only. The job name. For example:
      `projects/PROJECT_ID/locations/LOCATION_ID/jobs/JOB_ID`. The maximum
      allowed length for `JOB_ID` is 500 characters.
    nextScheduleTime: Output only. The next time the job is scheduled. Note
      that this may be a retry of a previously failed attempt or the next
      execution time according to the schedule.
    pubsubTarget: Pub/Sub target.
    retryConfig: Settings that determine the retry behavior.
    schedule: Specifies a schedule of start times. This can be used to specify
      complicated and time-zone-aware schedules. A scheduled start time will
      be delayed if the previous execution has not ended when its scheduled
      time occurs. If RetryConfig.retry_count > 0 and a job attempt fails, the
      job will be a total of tried RetryConfig.retry_count times, with
      exponential backoff, until the next scheduled start time.
    state: Output only. State of the job. For example: enabled, paused, or
      disabled.
    status: Output only. The response from the target of the last attempted
      execution.
    userUpdateTime: Output only. The time of the last user update to the job,
      or the creation time if there have been no updates.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the job. For example: enabled, paused, or
    disabled.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ENABLED: The job is executing normally.
      PAUSED: The job is paused by the user. It will not execute. A user can
        intentionally pause the job using PauseJobRequest.
      DISABLED: The job is disabled by the system due to error. The user
        cannot directly set a job to be disabled.
      UPDATE_FAILED: The job state resulting from a failed
        CloudScheduler.UpdateJob operation. To recover a job from this state,
        retry CloudScheduler.UpdateJob until a successful response is
        received.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    PAUSED = 2
    DISABLED = 3
    UPDATE_FAILED = 4

  appEngineHttpTarget = _messages.MessageField('AppEngineHttpTarget', 1)
  attemptDeadline = _messages.StringField(2)
  description = _messages.StringField(3)
  httpTarget = _messages.MessageField('HttpTarget', 4)
  lastAttemptTime = _messages.StringField(5)
  legacyAppEngineCron = _messages.BooleanField(6)
  name = _messages.StringField(7)
  nextScheduleTime = _messages.StringField(8)
  pubsubTarget = _messages.MessageField('PubsubTarget', 9)
  retryConfig = _messages.MessageField('RetryConfig', 10)
  schedule = _messages.MessageField('Schedule', 11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  status = _messages.MessageField('Status', 13)
  userUpdateTime = _messages.StringField(14)


class ListJobsResponse(_messages.Message):
  r"""Response message for listing jobs using CloudScheduler.ListJobs.

  Fields:
    jobs: The list of jobs.
    nextPageToken: A token to retrieve next page of results. Pass this value
      in the ListJobsRequest.page_token field in the subsequent call to
      CloudScheduler.ListJobs to retrieve the next page of results. If this is
      empty it indicates that there are no more results through which to
      paginate. The page token is valid for only 2 hours.
  """

  jobs = _messages.MessageField('Job', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class OAuthToken(_messages.Message):
  r"""Contains information needed for generating an [OAuth
  token](https://developers.google.com/identity/protocols/OAuth2). This type
  of authorization should generally only be used when calling Google APIs
  hosted on *.googleapis.com.

  Fields:
    scope: OAuth scope to be used for generating OAuth access token. If not
      specified, "https://www.googleapis.com/auth/cloud-platform" will be
      used.
    serviceAccountEmail: [Service account
      email](https://cloud.google.com/iam/docs/service-accounts) to be used
      for generating OAuth token. The service account must be within the same
      project as the job. The caller must have iam.serviceAccounts.actAs
      permission for the service account.
  """

  scope = _messages.StringField(1)
  serviceAccountEmail = _messages.StringField(2)


class OidcToken(_messages.Message):
  r"""Contains information needed for generating an [OpenID Connect
  token](https://developers.google.com/identity/protocols/OpenIDConnect). This
  type of authorization can be used for many scenarios, including calling
  Cloud Run, or endpoints where you intend to validate the token yourself.

  Fields:
    audience: Audience to be used when generating OIDC token. If not
      specified, the URI specified in target will be used.
    serviceAccountEmail: [Service account
      email](https://cloud.google.com/iam/docs/service-accounts) to be used
      for generating OIDC token. The service account must be within the same
      project as the job. The caller must have iam.serviceAccounts.actAs
      permission for the service account.
  """

  audience = _messages.StringField(1)
  serviceAccountEmail = _messages.StringField(2)


class PauseJobRequest(_messages.Message):
  r"""Request message for CloudScheduler.PauseJob."""


class PubsubMessage(_messages.Message):
  r"""A message that is published by publishers and consumed by subscribers.
  The message must contain either a non-empty data field or at least one
  attribute. Note that client libraries represent this object differently
  depending on the language. See the corresponding [client library
  documentation](https://cloud.google.com/pubsub/docs/reference/libraries) for
  more information. See [quotas and limits]
  (https://cloud.google.com/pubsub/quotas) for more information about message
  limits.

  Messages:
    AttributesValue: Optional. Attributes for this message. If this field is
      empty, the message must contain non-empty data. This can be used to
      filter messages on the subscription.

  Fields:
    attributes: Optional. Attributes for this message. If this field is empty,
      the message must contain non-empty data. This can be used to filter
      messages on the subscription.
    data: Optional. The message data field. If this field is empty, the
      message must contain at least one attribute.
    messageId: Optional. ID of this message, assigned by the server when the
      message is published. Guaranteed to be unique within the topic. This
      value may be read by a subscriber that receives a `PubsubMessage` via a
      `Pull` call or a push delivery. It must not be populated by the
      publisher in a `Publish` call.
    orderingKey: Optional. If non-empty, identifies related messages for which
      publish order should be respected. If a `Subscription` has
      `enable_message_ordering` set to `true`, messages published with the
      same non-empty `ordering_key` value will be delivered to subscribers in
      the order in which they are received by the Pub/Sub system. All
      `PubsubMessage`s published in a given `PublishRequest` must specify the
      same `ordering_key` value. For more information, see [ordering
      messages](https://cloud.google.com/pubsub/docs/ordering).
    publishTime: Optional. The time at which the message was published,
      populated by the server when it receives the `Publish` call. It must not
      be populated by the publisher in a `Publish` call.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. Attributes for this message. If this field is empty, the
    message must contain non-empty data. This can be used to filter messages
    on the subscription.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  data = _messages.BytesField(2)
  messageId = _messages.StringField(3)
  orderingKey = _messages.StringField(4)
  publishTime = _messages.StringField(5)


class PubsubTarget(_messages.Message):
  r"""Pub/Sub target. Jobs will be delivered by publishing a message to the
  given Pub/Sub topic.

  Messages:
    PubsubMessageValue: Required. This pubsub message is sent when the job is
      attempted. `pubsub_message` should be a google.pubsub.v1.PubsubMessage.

  Fields:
    pubsubMessage: Required. This pubsub message is sent when the job is
      attempted. `pubsub_message` should be a google.pubsub.v1.PubsubMessage.
    topicName: Required. The name of the Cloud Pub/Sub topic to which messages
      will be published when a job is delivered. The topic name must be in the
      same format as required by Pub/Sub's [PublishRequest.name](https://cloud
      .google.com/pubsub/docs/reference/rpc/google.pubsub.v1#publishrequest),
      for example `projects/PROJECT_ID/topics/TOPIC_ID`. The topic must be in
      the same project as the Cloud Scheduler job.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PubsubMessageValue(_messages.Message):
    r"""Required. This pubsub message is sent when the job is attempted.
    `pubsub_message` should be a google.pubsub.v1.PubsubMessage.

    Messages:
      AdditionalProperty: An additional property for a PubsubMessageValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PubsubMessageValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  pubsubMessage = _messages.MessageField('PubsubMessageValue', 1)
  topicName = _messages.StringField(2)


class ResumeJobRequest(_messages.Message):
  r"""Request message for CloudScheduler.ResumeJob."""


class RetryConfig(_messages.Message):
  r"""Settings that determine the retry behavior. By default, if a job does
  not complete successfully (meaning that an acknowledgement is not received
  from the handler, then it will be retried with exponential backoff according
  to the settings in RetryConfig.

  Fields:
    maxBackoffDuration: The maximum amount of time to wait before retrying a
      job after it fails. The default value of this field is 1 hour.
    maxDoublings: The time between retries will double `max_doublings` times.
      A job's retry interval starts at min_backoff_duration, then doubles
      `max_doublings` times, then increases linearly, and finally retries at
      intervals of max_backoff_duration up to retry_count times. For example,
      if min_backoff_duration is 10s, max_backoff_duration is 300s, and
      `max_doublings` is 3, then the a job will first be retried in 10s. The
      retry interval will double three times, and then increase linearly by
      2^3 * 10s. Finally, the job will retry at intervals of
      max_backoff_duration until the job has been attempted retry_count times.
      Thus, the requests will retry at 10s, 20s, 40s, 80s, 160s, 240s, 300s,
      300s, .... The default value of this field is 5.
    maxRetryDuration: The time limit for retrying a failed job, measured from
      time when an execution was first attempted. If specified with
      RetryConfig.retry_count, the job will be retried until both limits are
      reached. The default value for max_retry_duration is zero, which means
      retry duration is unlimited.
    minBackoffDuration: The minimum amount of time to wait before retrying a
      job after it fails. The default value of this field is 5 seconds.
    retryCount: It determines the number attempts that the system will make to
      run a job using the exponential backoff procedure described above. The
      default value of retry_count is zero. If retry_count is zero, a job
      attempt will *not* be retried if it fails. Instead the Cloud Scheduler
      system will wait for the next scheduled execution time. If retry_count
      is set to a non-zero number then Cloud Scheduler will retry failed
      attempts, using exponential backoff, retry_count times, or until the
      next scheduled execution time, whichever comes first. Value greater than
      5 and negative values are not allowed.
  """

  maxBackoffDuration = _messages.StringField(1)
  maxDoublings = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  maxRetryDuration = _messages.StringField(3)
  minBackoffDuration = _messages.StringField(4)
  retryCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class RunJobRequest(_messages.Message):
  r"""Request message for forcing a job to run now using
  CloudScheduler.RunJob.

  Fields:
    legacyAppEngineCron: This field is used to manage the legacy App Engine
      Cron jobs using the Cloud Scheduler API. If the field is set to true,
      the job in the __cron queue with the corresponding name will be forced
      to run instead.
  """

  legacyAppEngineCron = _messages.BooleanField(1)


class Schedule(_messages.Message):
  r"""Scheduler schedule in an English-like format.

  Fields:
    schedule: Required. Scheduler schedules are specified using an English-
      like format. See https://cloud.google.com/cloud-scheduler/docs/running-
      cron-jobs-with-cloud-scheduler#defining_the_job_schedule
    timeZone: Specifies the time zone to be used in interpreting
      Schedule.schedule. The value of this field must be a time zone name from
      the tz database: http://en.wikipedia.org/wiki/Tz_database. Note that
      some time zones include a provision for daylight savings time. The rules
      for daylight saving time are determined by the chosen tz. For UTC use
      the string "utc". If a time zone is not specified, the default will be
      in UTC (also known as GMT).
  """

  schedule = _messages.StringField(1)
  timeZone = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

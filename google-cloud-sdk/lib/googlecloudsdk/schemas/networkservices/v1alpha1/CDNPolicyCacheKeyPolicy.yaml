$schema: "http://json-schema.org/draft-06/schema#"

title: networkservices v1alpha1 CDNPolicyCacheKeyPolicy export schema
description: A gcloud export/import command YAML validation schema.
type: object
additionalProperties: false
properties:
  COMMENT:
    type: object
    description: User specified info ignored by gcloud import.
    additionalProperties: false
    properties:
      template-id:
        type: string
      region:
        type: string
      description:
        type: string
      date:
        type: string
      version:
        type: string
  UNKNOWN:
    type: array
    description: Unknown API fields that cannot be imported.
    items:
      type: string
  excludeHost:
    description: |-
      If `true`, requests to different hosts are cached separately.
      **Important:** Enable this only if the hosts share the same origin and
      content. Removing the host from the cache key might inadvertently result
      in different objects being cached than intended, depending on which route
      the first user matched.
    type: boolean
  excludeQueryString:
    description: |-
      If `true`, exclude query string parameters from the cache key. If `false`
      (the default), include the query string parameters in the cache key
      according to included_query_parameters and excluded_query_parameters. If
      neither is set, the entire query string is included.
    type: boolean
  excludedQueryParameters:
    description: |-
      The names of query string parameters to exclude from cache keys. All other
      parameters are included. Specify either included_query_parameters or
      excluded_query_parameters, not both. `&` and `=` are percent encoded and
      not treated as delimiters. You can exclude up to 20 query parameters. Each
      query parameter name must be between 1 and 32 characters long (inclusive).
    type: array
    items:
      type: string
  includeProtocol:
    description: If `true`, HTTP and HTTPS requests are cached separately.
    type: boolean
  includeQueryString:
    description: |-
      If `true`, include query string parameters in the cache key according
      to included_query_parameters and excluded_query_parameters. If neither
      is set, the entire query string is included. If `false`, the query
      string is excluded from the cache key entirely.
    type: boolean
  includedCookieNames:
    description: |-
      The names of cookies to include in cache keys. The cookie name and
      cookie value of each cookie named is used as part of the cache key.
      The following limitations apply: - Must be valid RFC 6265 "cookie-
      name" tokens - Are case sensitive - Cannot start with "Edge-Cache-"
      (case insensitive) Specifying several cookies or cookies that have a
      large range of values, such as per-user, dramatically impacts the
      cache hit rate, and might result in a higher eviction rate and reduced
      performance. You can specify up to three cookie names.
    type: array
    items:
      type: string
  includedHeaderNames:
    description: |-
      The names of HTTP request headers to include in cache keys. The
      value of the header field is used as part of the cache key. The
      following limitations apply: - Header names must be valid HTTP RFC
      7230 header field values. - Header field names are case
      insensitive - You can specify up to five header names. - To
      include the HTTP method, use `:method` Refer to the documentation
      for the allowed list of header names. Specifying several headers
      or headers that have a large range of values, such as per-user,
      dramatically impacts the cache hit rate, and might result in a
      higher eviction rate and reduced performance.
    type: array
    items:
      type: string
  includedQueryParameters:
    description: |-
      The names of query string parameters to include in cache keys.
      All other parameters are excluded. Specify either
      included_query_parameters or excluded_query_parameters, not
      both. `&` and `=` are percent encoded and not treated as
      delimiters. You can include up to 20 query parameters. Each
      query parameter name must be between 1 and 32 characters long
      (inclusive).
    type: array
    items:
      type: string

- release_tracks: [ALPHA, GA]

  help_text:
    brief: |
      Update a Looker instance.
    description: |
      Update the metadata and/or configuration parameters of a Looker instance.

      This command can fail for the following reasons:
        * The instance specified does not exist.
        * The active account does not have permission to update the given
          instance.
    examples: |
      To update the maintenance window to Sunday at 11:00 PM for a Looker instance with the name
      `my-looker-instance`, run:

        $ {command} my-looker-instance  --maintenance-window-day=sunday --maintenance-window-time='23:00' --async

  arguments:
    resource:
      spec: !REF googlecloudsdk.command_lib.looker.resources:instance
      help_text: |
        Arguments and flags that specify the Looker instance you want
        to update.
    params:
    # Public Enabled Flags
    - arg_name: enable-public-ip
      api_field: instance.enablePublicIp
      release_tracks: [ALPHA]
      type: bool
      help_text: |
        This specifies whether public IP is enabled on the Looker instance.
    - arg_name: public-ip-enabled
      api_field: instance.publicIpEnabled
      release_tracks: [GA]
      type: bool
      help_text: |
        This specifies whether public IP is enabled on the Looker instance.
    # Group for Maintanence Windows
    - group:
        required: false
        help_text: |
          Maintenance Window - Maintenance typically only takes place once every few months, and requires your instance
          to be restarted while updates are made, which disrupts service briefly.
        params:
        - api_field: instance.maintenanceWindow.dayOfWeek
          required: true
          arg_name: maintenance-window-day
          choices:
          - arg_value: monday
            enum_value: MONDAY
          - arg_value: tuesday
            enum_value: TUESDAY
          - arg_value: wednesday
            enum_value: WEDNESDAY
          - arg_value: thursday
            enum_value: THURSDAY
          - arg_value: friday
            enum_value: FRIDAY
          - arg_value: saturday
            enum_value: SATURDAY
          - arg_value: sunday
            enum_value: SUNDAY
          help_text: |
            Day of the week for the maintenance window, in UTC time zone.
        - api_field: instance.maintenanceWindow.startTime
          arg_name: maintenance-window-time
          required: true
          ALPHA:
            processor: googlecloudsdk.command_lib.looker.instances_util:ParseTimeOfDayMainWindowV1Alpha1
          GA:
            processor: googlecloudsdk.command_lib.looker.instances_util:ParseTimeOfDayMainWindowV1
          help_text: |
            Hour of day for maintenance window, in UTC time zone. A valid time of day must be
            specified in 24hr format (ex: 13:00, 17:30, 23:45). Maintenance will be scheduled
            within 60 minutes. To set the maintenance-window-time* attribute:
            * provide the argument *--maintenance-window-time* on the command line.
    # Group for Deny Maintanence period
    - group:
        required: false
        help_text: |
              Deny Maintanence Period - You must allow at least 48 hours of maintenance availability in
              a 60-day rolling window. Only contiguous availability windows of at least two hours are
              considered. When setting a maintenance exclusion please be aware that excluding
              application upgrades cannot exceed 60-days and excluding infrastructure upgrades cannot
              exceed 90-days.
        params:
        - api_field: instance.denyMaintenancePeriod.startDate
          arg_name: deny-maintenance-period-start-date
          required: true
          GA:
            processor: googlecloudsdk.command_lib.looker.instances_util:ParseToDateTypeV1
          ALPHA:
            processor: googlecloudsdk.command_lib.looker.instances_util:ParseToDateTypeV1Alpha1
          help_text: |
                Start date of the deny maintenance period in format: YYYY-MM-DD
        - api_field: instance.denyMaintenancePeriod.endDate
          arg_name: deny-maintenance-period-end-date
          required: true
          GA:
            processor: googlecloudsdk.command_lib.looker.instances_util:ParseToDateTypeV1
          ALPHA:
            processor: googlecloudsdk.command_lib.looker.instances_util:ParseToDateTypeV1Alpha1
          help_text: |
                End date of the deny maintenance period in format: YYYY-MM-DD
        - api_field: instance.denyMaintenancePeriod.time
          required: true
          arg_name: deny-maintenance-period-time
          GA:
            processor: googlecloudsdk.command_lib.looker.instances_util:ParseTimeOfDayDenyPeriodV1
          ALPHA:
            processor: googlecloudsdk.command_lib.looker.instances_util:ParseTimeOfDayDenyPeriodV1Alpha1
          help_text: |
                Time in UTC when the period starts and ends.  A valid time of day must be
                specified in 24hr format (ex: 13:00, 17:30, 23:45).
    # Group for Admin Setting
    - group:
        required: false
        help_text: |
          Email Domain Allowlist for Scheduled Content - Define the email domains to which your
          users can deliver Looker (Google Cloud core) content.
        params:
        - arg_name: allowed-email-domains
          api_field: instance.adminSettings.allowedEmailDomains
          required: true
          help_text: |
            This specifies the entire allowed email domain list.
    # Group for Oauth configuration
    - group:
        required: false
        help_text: |
          OAuth Application Credentials - Looker Instance OAuth login settings. Setup an OAuth
          app that will allow users to authenticate and access the instance. For more
          information see: https://developers.google.com/identity/protocols/oauth2/web-server#creatingcred
        params:
        - arg_name: oauth-client-id
          api_field: instance.oauthConfig.clientId
          required: true
          help_text: |
            The client ID from an external OAuth application.
        - arg_name: oauth-client-secret
          api_field: instance.oauthConfig.clientSecret
          required: true
          help_text: |
            The client secret from an external OAuth application.
    # Group for Users Allocations
    - group:
        required: false
        help_text: |
          User Allocations - There are ten Standard and two Developer users included in the cost of
          the product. You can allocate additional Standard, Viewer, and Developer users for this
          instance. It is an optional step and can be modified later.

          With the Standard edition of Looker (Google Cloud core), you can provision up to 50 total
          users, distributed across Viewer, Standard, and Developer.
        params:
        - arg_name: add-viewer-users
          GA:
            api_field: instance.userMetadata.additionalViewerUserCount
          ALPHA:
            api_field: instance.users.additionalViewerUsers
          help_text: |
            Number of additional Viewer Users to allocate to the Looker Instance.
        - arg_name: add-standard-users
          GA:
            api_field: instance.userMetadata.additionalStandardUserCount
          ALPHA:
            api_field: instance.users.additionalStandardUsers
          help_text: |
            Number of additional Standard Users to allocate to the Looker Instance.
        - arg_name: add-developer-users
          GA:
            api_field: instance.userMetadata.additionalDeveloperUserCount
          ALPHA:
            api_field: instance.users.additionalDeveloperUsers
          help_text: |
            Number of additional Developer Users to allocate to the Looker Instance.
    # Group for Custom Domain
    - group:
        required: false
        help_text: |
          Custom Domains - Looker (Google Cloud core) lets you serve your application through a custom
          domain. If you use a custom domain, Google will provide a managed auto-renewing SSL
          certificate for security.

          DNS changes can take up to 24 hours to take effect. Your SSL certificate will take several
          minutes to activate. Note that you must get the Type A DNS Record from the Google Cloud
          Console and update with your domain registrar for your custom domain to work properly.
        params:
        - arg_name: custom-domain
          api_field: instance.customDomain.domain
          help_text: |
            Domain name wanted to serve the Looker instance.

  request:
    ALPHA:
      api_version: v1alpha1
      modify_request_hooks:
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateOauthClient
      - googlecloudsdk.command_lib.looker.instances_update_util:ModifyAllowedEmailDomains
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateMaintenanceWindow
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateUsersAlpha
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateDenyMaintenancePeriod
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateEnablePublicIpAlpha
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateCustomDomain
    GA:
      api_version: v1
      modify_request_hooks:
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateOauthClient
      - googlecloudsdk.command_lib.looker.instances_update_util:ModifyAllowedEmailDomains
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateMaintenanceWindow
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateUserMetadata
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateDenyMaintenancePeriod
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdatePublicIPEnabled
      - googlecloudsdk.command_lib.looker.instances_update_util:UpdateCustomDomain
    collection: looker.projects.locations.instances

  update:
    disable_auto_field_mask: true

  async:
    collection: looker.projects.locations.operations

  output:
    format: none

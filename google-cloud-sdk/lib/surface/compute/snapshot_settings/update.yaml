- release_tracks: [<PERSON><PERSON><PERSON>]
  help_text:
    brief: "Update snapshot settings."
    description: "Update the snapshot settings of a project."
    examples: |
      To update the snapshot settings and set the storage location policy to
      the nearest multi-region as the source disk, run:

      $ {command} --storage-location-policy=nearest-multi-region

      To update the snapshot settings and set the storage location policy to
      the same region as the source disk, run:

      $ gcloud alpha compute snapshot-settings update \
          --storage-location-policy=local-region

      To update the snapshot settings and set the storage location policy to
      store snapshots in a specific location like `us-west1`, run:

      $ {command} --storage-location-policy=specific-locations --storage-location-names=us-west1

  request:
    collection: compute.snapshotSettings
    api_version: alpha
    modify_request_hooks:
    # `storage-location-names` cannot be validated with an argument processor for some reason,
    # so we do it in a request modification hook
    - googlecloudsdk.api_lib.compute.snapshot_settings.modify_request_hooks:validate_single_location

    - googlecloudsdk.api_lib.compute.snapshot_settings.modify_request_hooks:maybe_add_locations
    - googlecloudsdk.api_lib.compute.snapshot_settings.modify_request_hooks:adjust_storage_location_update_mask

  output:
    format: yaml(storageLocation.policy, storageLocation.locations.list(show="keys"))

  async:
    collection: compute.globalOperations
    response_name_field: selfLink
    state:
      field: status
      success_values: ['DONE']

  arguments:
    resource:
      help_text: |
        The project of the storage pools to list.
      spec: !REF googlecloudsdk.command_lib.compute.resources:compute_project
      override_resource_collection: true

    params:
    - arg_name: storage-location-policy
      api_field: snapshotSettings.storageLocation.policy
      help_text: |
        The storage location policy. For more information,
        refer to the snapshot settings documentation at
        https://cloud.google.com/compute/docs/disks/snapshot-settings.

      choices:
      - arg_value: nearest-multi-region
      - arg_value: local-region
      - arg_value: specific-locations

    - arg_name: storage-location-names
      help_text: |
        The custom storage locations that you specify for the project's
        snapshots. Use this flag only when you use the `SPECIFIC_LOCATIONS`
        value for the `--storage-location-policy` flag. For more information,
        refer to the snapshot settings documentation at https://cloud.google.com/compute/docs/disks/snapshot-settings.
      type: 'googlecloudsdk.calliope.arg_parsers:ArgList:'

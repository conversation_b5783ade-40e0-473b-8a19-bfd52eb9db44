- release_tracks: [ALPHA]
  help_text:
    brief: Describe a Queued Resource.
    description: |
      Get details on a Queued Resource.

      To get a list of Queued Resources to describe in more detail, run:

        $ {parent_command} list
    examples: |
      To describe a Queued Resource with ID `my-queued-resource` in zone `us-central1-b`
      and project 'my-project', run:

        $ {command} my-queued-resource --zone=us-central1-b --project=my-project

  request:
    collection: tpu.projects.locations.queuedResources
    api_version: v2alpha1

  arguments:
    resource:
      help_text: The Queued Resource you want to describe.
      spec: !REF googlecloudsdk.command_lib.compute.tpus.queued_resources.resources:queuedresource

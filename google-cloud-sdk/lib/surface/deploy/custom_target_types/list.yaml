- release_tracks: [ALPHA]
  hidden: true
  help_text:
    brief: List the custom target types.
    description: List the custom target types for a specified region.
    examples: |
      To list the custom target types in region `us-central1`, run:

        $ {command} --region=us-central1

  request:
    collection: clouddeploy.projects.locations.customTargetTypes
    api_version: v1

  response:
    id_field: name

  arguments:
    resource:
      help_text: The region for which you want to list the custom target types.
      spec: !REF googlecloudsdk.command_lib.deploy.resources:location
      is_positional: false

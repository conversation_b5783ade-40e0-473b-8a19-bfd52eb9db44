- release_tracks: [ALPHA, BETA, GA]
  help_text:
    brief: Add IAM policy binding to a Cloud Build connection.
    description: |
      Add IAM policy binding to a Cloud Build connection.
      One binding consists of a member and a role.
    examples: |
      To add an IAM policy binding for the role of 'roles/cloudbuild.connectionViewer'
      for the user '<EMAIL>' on a Connection named 'my-conn',
      run:

      $ {command} my-conn --region=us-central1 \
      --member='user:<EMAIL>' --role='roles/cloudbuild.connectionViewer'

  request:
    collection: cloudbuild.projects.locations.connections
    api_version: v2

  arguments:
    resource:
      help_text: Cloud Build Connection for which to add the IAM policy binding.
      spec: !REF googlecloudsdk.command_lib.cloudbuild.resources_v2:connection

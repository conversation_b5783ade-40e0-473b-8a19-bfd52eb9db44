{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98407d3f464ddadecc277cab31fab74f1f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d511344b7cc81420b2f8e02486ded530", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98777485661d6fc411415eeb20724f6188", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989ecf0969555463fa6e388dbd5e4ef069", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98777485661d6fc411415eeb20724f6188", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824d6e41d6a2dfa6fd800b26818487cb3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f59725c4d55a450848f18747f2afce47", "guid": "bfdfe7dc352907fc980b868725387e9829c4e6aa9c64e20e8aa1683ca3ca0e5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98611f19a3ba5efded97ee2e69235368d0", "guid": "bfdfe7dc352907fc980b868725387e98da17476032967ca1a29d429e6b3994ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd17d52d551ad68533ad71a0f56a41bd", "guid": "bfdfe7dc352907fc980b868725387e98d3a4998dac6703262228dfb2e1b51415"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dbf5b1e79446f7c0f19f57ee573d66f", "guid": "bfdfe7dc352907fc980b868725387e98096940d21822023e5da5d076126c43eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c9ea0af5832b7d9f3c3c2a296ee020", "guid": "bfdfe7dc352907fc980b868725387e98926c0d5b6c467fe98192643ddb0bf6ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e978098c148732234c9fbcd9412e9568", "guid": "bfdfe7dc352907fc980b868725387e983e4024f5601fc3b6d3ba0e97202bf197"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d099e74cf1c7feb6e98def2caba53683", "guid": "bfdfe7dc352907fc980b868725387e9880d3c0216247f1c1e3e7ae216694af83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988319d90a2f0012d17b97eef70f2dc89a", "guid": "bfdfe7dc352907fc980b868725387e98ca3d043a01f418f2ba4fdc6ce0941b1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98618574bab9c898843327d048b8c6f8ab", "guid": "bfdfe7dc352907fc980b868725387e98a4f2431fe9a70ebedce3f4e03cc76cbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ca222d9639e8a606cfdc76d9bdaf995", "guid": "bfdfe7dc352907fc980b868725387e982b7ea63d64d887ba1bd96d0f12630c3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3f204d5335b00a1d8fac3626d2849be", "guid": "bfdfe7dc352907fc980b868725387e981fbd6ef145b1a8655c52e0b8b27689f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6a11febccd95fc951ebc075fb96eb38", "guid": "bfdfe7dc352907fc980b868725387e9870f02e554b7147d0c243abdcc9b08434", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c39ee1599f4ce28d7eeafc7bfeae5c83", "guid": "bfdfe7dc352907fc980b868725387e987202a7e7c1a1326af687f2a402005b77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e1249b2f8836ab4b5de1f3c23e13c0d", "guid": "bfdfe7dc352907fc980b868725387e980921b2f9d3e761697f97762bee791f29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddc875b98239b879166a2cd7e0e2fbe2", "guid": "bfdfe7dc352907fc980b868725387e98fb50c26d972c028a40521d9838efba32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eabde0c2f951016be9b15d23c83bc0ae", "guid": "bfdfe7dc352907fc980b868725387e98b2dfadd7584e75db9512050337f91644"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e71598c5bdf31462e70d8de1c2ff9b2b", "guid": "bfdfe7dc352907fc980b868725387e98ce332d5744a3e974221de444eb584ab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b08282b1f5f246f17c865615b1d8559", "guid": "bfdfe7dc352907fc980b868725387e9835d2a2ba0cc32216a2854b856cbd1114", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852fb22b28885a4ddd0d65c81e19d79c8", "guid": "bfdfe7dc352907fc980b868725387e980583c5628d75c276adb8747c0055229c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ded8f9b3cb43d7a1eab2f409ce69b241", "guid": "bfdfe7dc352907fc980b868725387e984ff3bc20b5a1b756ddb4fe3d55c551c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c0fd53c6259bf3372097da5bb839b9", "guid": "bfdfe7dc352907fc980b868725387e981a956d6474e8d6777c7c3fca2397bdce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da610e99b00cde6609674521fd4d545f", "guid": "bfdfe7dc352907fc980b868725387e9821ea10bd8a2ebe6e0aa3b771d828b5e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815400bc3aacb99f7b685021772eb18b7", "guid": "bfdfe7dc352907fc980b868725387e9887fa3515cdf31ff7b96391c41a3321ea", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989ac8df821ffdf3f24e186a86e83a3451", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb85241997ffd0ff4e767aacdc8ae23d", "guid": "bfdfe7dc352907fc980b868725387e980de9bd17f1a7e953debcbfca0fca1c85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3810c016c1384f0654ad68cd4f3f99a", "guid": "bfdfe7dc352907fc980b868725387e98bb9e6b11406a3d7f0eabb1dd85c68c18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98202d012826dcc161ce2246fbf61a422e", "guid": "bfdfe7dc352907fc980b868725387e984c08fec139e0835e5f71d8fbf2f984cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98529ab969fccde0245b23692f49564642", "guid": "bfdfe7dc352907fc980b868725387e98e5a991bca26838af8b0a213b5349a5a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d739727a623fb83679d5272e0a1d2a08", "guid": "bfdfe7dc352907fc980b868725387e98b1d13aa71a45cc39c221bebbfd0d8b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd66a466acd903737cf64a202dffc0e", "guid": "bfdfe7dc352907fc980b868725387e98f816ce7b960622e48792b9c2795bde40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb430da710aaff92e1ef1ba5a397ed05", "guid": "bfdfe7dc352907fc980b868725387e9854c6bac1aa956da309b194902201b44a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98298cde56101d66146255d9538267a228", "guid": "bfdfe7dc352907fc980b868725387e9814dabb23fa4102ff62a8189d32879653"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b9ed8c3c6259d6f90f333a08229ef8f", "guid": "bfdfe7dc352907fc980b868725387e98337dd916d65e97ff2d5cc0ea489949d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984be6c091f0752e6addbd0e65cf69c948", "guid": "bfdfe7dc352907fc980b868725387e98e11c785e740e78d8d62eeb5dfd9f7830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdf4d38e6fbe71f4c6aaf59ddec5a93e", "guid": "bfdfe7dc352907fc980b868725387e987a1de0eddab2359e095aaf649c8bf2c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987014d1d61f96ca1a413beb68f16c1aea", "guid": "bfdfe7dc352907fc980b868725387e986f5d5748942614fdba23efce80bc6cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d44f32965522b160012bfa8ddf1547", "guid": "bfdfe7dc352907fc980b868725387e98fb73f7146e8cbd3af35826237bf8551d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988332946ce191760ef482c8ea49301a5a", "guid": "bfdfe7dc352907fc980b868725387e988e17989624a4c57ee7fea8595678e6b8"}], "guid": "bfdfe7dc352907fc980b868725387e987f02480b56e3e249ee185dadf3e9c917", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fbcb3dfc70d39bb6555ed44b178e612b", "guid": "bfdfe7dc352907fc980b868725387e9810e0f0bca653a9958e5f5796f02c017a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806a4008a6c105ba8c01111fb0c3201cd", "guid": "bfdfe7dc352907fc980b868725387e98a226487d9606892f85743b96c24d4234"}], "guid": "bfdfe7dc352907fc980b868725387e985e0a2329756666547a225ade7e65d7d7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9807c05e0e284bde5a22e7847675e31e9d", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e989ccdfee514c8facd1a5171665a5c5a8d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}